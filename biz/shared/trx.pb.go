// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: trx.proto

package shared

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type LockStatus int32

const (
	None            LockStatus = 0
	LockHold        LockStatus = 1
	LockWait        LockStatus = 2
	LockHoldAndWait LockStatus = 3
)

var LockStatus_name = map[int32]string{
	0: "None",
	1: "LockHold",
	2: "LockWait",
	3: "LockHoldAndWait",
}

var LockStatus_value = map[string]int32{
	"None":            0,
	"LockHold":        1,
	"LockWait":        2,
	"LockHoldAndWait": 3,
}

func (LockStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{0}
}

// DescribeTrxAndLocks
type DescribeTrxAndLocks struct {
	Offset      int32           `protobuf:"varint,1,opt,name=Offset,proto3" json:"Offset,omitempty"`
	Limit       int32           `protobuf:"varint,2,opt,name=Limit,proto3" json:"Limit,omitempty"`
	TrxExecTime int32           `protobuf:"varint,3,opt,name=TrxExecTime,proto3" json:"TrxExecTime,omitempty"`
	TrxStatus   string          `protobuf:"bytes,4,opt,name=TrxStatus,proto3" json:"TrxStatus,omitempty"`
	LockStatus  string          `protobuf:"bytes,5,opt,name=LockStatus,proto3" json:"LockStatus,omitempty"`
	SortParam   string          `protobuf:"bytes,6,opt,name=SortParam,proto3" json:"SortParam,omitempty"`
	Order       string          `protobuf:"bytes,7,opt,name=Order,proto3" json:"Order,omitempty"`
	QueryFilter *TrxQueryFilter `protobuf:"bytes,8,opt,name=QueryFilter,proto3" json:"QueryFilter,omitempty"`
}

func (m *DescribeTrxAndLocks) Reset()      { *m = DescribeTrxAndLocks{} }
func (*DescribeTrxAndLocks) ProtoMessage() {}
func (*DescribeTrxAndLocks) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{0}
}
func (m *DescribeTrxAndLocks) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeTrxAndLocks) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeTrxAndLocks.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeTrxAndLocks) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeTrxAndLocks.Merge(m, src)
}
func (m *DescribeTrxAndLocks) XXX_Size() int {
	return m.Size()
}
func (m *DescribeTrxAndLocks) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeTrxAndLocks.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeTrxAndLocks proto.InternalMessageInfo

func (m *DescribeTrxAndLocks) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *DescribeTrxAndLocks) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *DescribeTrxAndLocks) GetTrxExecTime() int32 {
	if m != nil {
		return m.TrxExecTime
	}
	return 0
}

func (m *DescribeTrxAndLocks) GetTrxStatus() string {
	if m != nil {
		return m.TrxStatus
	}
	return ""
}

func (m *DescribeTrxAndLocks) GetLockStatus() string {
	if m != nil {
		return m.LockStatus
	}
	return ""
}

func (m *DescribeTrxAndLocks) GetSortParam() string {
	if m != nil {
		return m.SortParam
	}
	return ""
}

func (m *DescribeTrxAndLocks) GetOrder() string {
	if m != nil {
		return m.Order
	}
	return ""
}

func (m *DescribeTrxAndLocks) GetQueryFilter() *TrxQueryFilter {
	if m != nil {
		return m.QueryFilter
	}
	return nil
}

type DescribeTrxAndLocksInfo struct {
	TrxAndLockList []*TrxAndLock   `protobuf:"bytes,1,rep,name=TrxAndLockList,proto3" json:"TrxAndLockList,omitempty"`
	Total          int32           `protobuf:"varint,2,opt,name=Total,proto3" json:"Total,omitempty"`
	Statistics     *LockStatistics `protobuf:"bytes,3,opt,name=Statistics,proto3" json:"Statistics,omitempty"`
}

func (m *DescribeTrxAndLocksInfo) Reset()      { *m = DescribeTrxAndLocksInfo{} }
func (*DescribeTrxAndLocksInfo) ProtoMessage() {}
func (*DescribeTrxAndLocksInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{1}
}
func (m *DescribeTrxAndLocksInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeTrxAndLocksInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeTrxAndLocksInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeTrxAndLocksInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeTrxAndLocksInfo.Merge(m, src)
}
func (m *DescribeTrxAndLocksInfo) XXX_Size() int {
	return m.Size()
}
func (m *DescribeTrxAndLocksInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeTrxAndLocksInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeTrxAndLocksInfo proto.InternalMessageInfo

func (m *DescribeTrxAndLocksInfo) GetTrxAndLockList() []*TrxAndLock {
	if m != nil {
		return m.TrxAndLockList
	}
	return nil
}

func (m *DescribeTrxAndLocksInfo) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DescribeTrxAndLocksInfo) GetStatistics() *LockStatistics {
	if m != nil {
		return m.Statistics
	}
	return nil
}

type TrxAndLockOverview struct {
	TotalHoldLock        int32 `protobuf:"varint,1,opt,name=TotalHoldLock,proto3" json:"TotalHoldLock,omitempty"`
	TotalWaitLock        int32 `protobuf:"varint,2,opt,name=TotalWaitLock,proto3" json:"TotalWaitLock,omitempty"`
	TimeGreaterThanCount int32 `protobuf:"varint,3,opt,name=timeGreaterThanCount,proto3" json:"timeGreaterThanCount,omitempty"`
}

func (m *TrxAndLockOverview) Reset()      { *m = TrxAndLockOverview{} }
func (*TrxAndLockOverview) ProtoMessage() {}
func (*TrxAndLockOverview) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{2}
}
func (m *TrxAndLockOverview) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TrxAndLockOverview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TrxAndLockOverview.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TrxAndLockOverview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrxAndLockOverview.Merge(m, src)
}
func (m *TrxAndLockOverview) XXX_Size() int {
	return m.Size()
}
func (m *TrxAndLockOverview) XXX_DiscardUnknown() {
	xxx_messageInfo_TrxAndLockOverview.DiscardUnknown(m)
}

var xxx_messageInfo_TrxAndLockOverview proto.InternalMessageInfo

func (m *TrxAndLockOverview) GetTotalHoldLock() int32 {
	if m != nil {
		return m.TotalHoldLock
	}
	return 0
}

func (m *TrxAndLockOverview) GetTotalWaitLock() int32 {
	if m != nil {
		return m.TotalWaitLock
	}
	return 0
}

func (m *TrxAndLockOverview) GetTimeGreaterThanCount() int32 {
	if m != nil {
		return m.TimeGreaterThanCount
	}
	return 0
}

type LockStatistics struct {
	InnoDBLockOverview *TrxAndLockOverview `protobuf:"bytes,1,opt,name=InnoDBLockOverview,proto3" json:"InnoDBLockOverview,omitempty"`
	MetaDBLockOverview *TrxAndLockOverview `protobuf:"bytes,2,opt,name=MetaDBLockOverview,proto3" json:"MetaDBLockOverview,omitempty"`
}

func (m *LockStatistics) Reset()      { *m = LockStatistics{} }
func (*LockStatistics) ProtoMessage() {}
func (*LockStatistics) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{3}
}
func (m *LockStatistics) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LockStatistics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LockStatistics.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LockStatistics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockStatistics.Merge(m, src)
}
func (m *LockStatistics) XXX_Size() int {
	return m.Size()
}
func (m *LockStatistics) XXX_DiscardUnknown() {
	xxx_messageInfo_LockStatistics.DiscardUnknown(m)
}

var xxx_messageInfo_LockStatistics proto.InternalMessageInfo

func (m *LockStatistics) GetInnoDBLockOverview() *TrxAndLockOverview {
	if m != nil {
		return m.InnoDBLockOverview
	}
	return nil
}

func (m *LockStatistics) GetMetaDBLockOverview() *TrxAndLockOverview {
	if m != nil {
		return m.MetaDBLockOverview
	}
	return nil
}

type WaitLockDetail struct {
	RequestedTrxId  string `protobuf:"bytes,1,opt,name=RequestedTrxId,proto3" json:"RequestedTrxId,omitempty"`
	RequestedLockId string `protobuf:"bytes,2,opt,name=RequestedLockId,proto3" json:"RequestedLockId,omitempty"`
	SqlText         string `protobuf:"bytes,3,opt,name=SqlText,proto3" json:"SqlText,omitempty"`
	LockInfo        *Lock  `protobuf:"bytes,4,opt,name=LockInfo,proto3" json:"LockInfo,omitempty"`
}

func (m *WaitLockDetail) Reset()      { *m = WaitLockDetail{} }
func (*WaitLockDetail) ProtoMessage() {}
func (*WaitLockDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{4}
}
func (m *WaitLockDetail) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WaitLockDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WaitLockDetail.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WaitLockDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WaitLockDetail.Merge(m, src)
}
func (m *WaitLockDetail) XXX_Size() int {
	return m.Size()
}
func (m *WaitLockDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_WaitLockDetail.DiscardUnknown(m)
}

var xxx_messageInfo_WaitLockDetail proto.InternalMessageInfo

func (m *WaitLockDetail) GetRequestedTrxId() string {
	if m != nil {
		return m.RequestedTrxId
	}
	return ""
}

func (m *WaitLockDetail) GetRequestedLockId() string {
	if m != nil {
		return m.RequestedLockId
	}
	return ""
}

func (m *WaitLockDetail) GetSqlText() string {
	if m != nil {
		return m.SqlText
	}
	return ""
}

func (m *WaitLockDetail) GetLockInfo() *Lock {
	if m != nil {
		return m.LockInfo
	}
	return nil
}

type TrxAndLock struct {
	ProcessId        string          `protobuf:"bytes,1,opt,name=ProcessId,proto3" json:"ProcessId,omitempty"`
	TrxId            string          `protobuf:"bytes,2,opt,name=TrxId,proto3" json:"TrxId,omitempty"`
	TrxStatus        string          `protobuf:"bytes,3,opt,name=TrxStatus,proto3" json:"TrxStatus,omitempty"`
	LockStatus       LockStatus      `protobuf:"varint,4,opt,name=LockStatus,proto3,enum=shared.LockStatus" json:"LockStatus,omitempty"`
	TrxIsoLevel      string          `protobuf:"bytes,5,opt,name=TrxIsoLevel,proto3" json:"TrxIsoLevel,omitempty"`
	TrxStartTime     string          `protobuf:"bytes,6,opt,name=TrxStartTime,proto3" json:"TrxStartTime,omitempty"`
	TrxWaitStartTime string          `protobuf:"bytes,7,opt,name=TrxWaitStartTime,proto3" json:"TrxWaitStartTime,omitempty"`
	SqlBlocked       string          `protobuf:"bytes,8,opt,name=SqlBlocked,proto3" json:"SqlBlocked,omitempty"`
	TrxTablesLocked  int32           `protobuf:"varint,9,opt,name=TrxTablesLocked,proto3" json:"TrxTablesLocked,omitempty"`
	TrxRowsLocked    int32           `protobuf:"varint,10,opt,name=TrxRowsLocked,proto3" json:"TrxRowsLocked,omitempty"`
	TrxRowsModified  int32           `protobuf:"varint,11,opt,name=TrxRowsModified,proto3" json:"TrxRowsModified,omitempty"`
	LockList         []*Lock         `protobuf:"bytes,12,rep,name=LockList,proto3" json:"LockList,omitempty"`
	NodeId           string          `protobuf:"bytes,13,opt,name=NodeId,proto3" json:"NodeId,omitempty"`
	NodeType         string          `protobuf:"bytes,14,opt,name=NodeType,proto3" json:"NodeType,omitempty"`
	WaitLockDetail   *WaitLockDetail `protobuf:"bytes,15,opt,name=WaitLockDetail,proto3" json:"WaitLockDetail,omitempty"`
	BlockTrxId       string          `protobuf:"bytes,16,opt,name=BlockTrxId,proto3" json:"BlockTrxId,omitempty"`
	TrxExecTime      int32           `protobuf:"varint,17,opt,name=TrxExecTime,proto3" json:"TrxExecTime,omitempty"`
	TrxChangeTime    string          `protobuf:"bytes,18,opt,name=TrxChangeTime,proto3" json:"TrxChangeTime,omitempty"`
}

func (m *TrxAndLock) Reset()      { *m = TrxAndLock{} }
func (*TrxAndLock) ProtoMessage() {}
func (*TrxAndLock) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{5}
}
func (m *TrxAndLock) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TrxAndLock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TrxAndLock.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TrxAndLock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrxAndLock.Merge(m, src)
}
func (m *TrxAndLock) XXX_Size() int {
	return m.Size()
}
func (m *TrxAndLock) XXX_DiscardUnknown() {
	xxx_messageInfo_TrxAndLock.DiscardUnknown(m)
}

var xxx_messageInfo_TrxAndLock proto.InternalMessageInfo

func (m *TrxAndLock) GetProcessId() string {
	if m != nil {
		return m.ProcessId
	}
	return ""
}

func (m *TrxAndLock) GetTrxId() string {
	if m != nil {
		return m.TrxId
	}
	return ""
}

func (m *TrxAndLock) GetTrxStatus() string {
	if m != nil {
		return m.TrxStatus
	}
	return ""
}

func (m *TrxAndLock) GetLockStatus() LockStatus {
	if m != nil {
		return m.LockStatus
	}
	return None
}

func (m *TrxAndLock) GetTrxIsoLevel() string {
	if m != nil {
		return m.TrxIsoLevel
	}
	return ""
}

func (m *TrxAndLock) GetTrxStartTime() string {
	if m != nil {
		return m.TrxStartTime
	}
	return ""
}

func (m *TrxAndLock) GetTrxWaitStartTime() string {
	if m != nil {
		return m.TrxWaitStartTime
	}
	return ""
}

func (m *TrxAndLock) GetSqlBlocked() string {
	if m != nil {
		return m.SqlBlocked
	}
	return ""
}

func (m *TrxAndLock) GetTrxTablesLocked() int32 {
	if m != nil {
		return m.TrxTablesLocked
	}
	return 0
}

func (m *TrxAndLock) GetTrxRowsLocked() int32 {
	if m != nil {
		return m.TrxRowsLocked
	}
	return 0
}

func (m *TrxAndLock) GetTrxRowsModified() int32 {
	if m != nil {
		return m.TrxRowsModified
	}
	return 0
}

func (m *TrxAndLock) GetLockList() []*Lock {
	if m != nil {
		return m.LockList
	}
	return nil
}

func (m *TrxAndLock) GetNodeId() string {
	if m != nil {
		return m.NodeId
	}
	return ""
}

func (m *TrxAndLock) GetNodeType() string {
	if m != nil {
		return m.NodeType
	}
	return ""
}

func (m *TrxAndLock) GetWaitLockDetail() *WaitLockDetail {
	if m != nil {
		return m.WaitLockDetail
	}
	return nil
}

func (m *TrxAndLock) GetBlockTrxId() string {
	if m != nil {
		return m.BlockTrxId
	}
	return ""
}

func (m *TrxAndLock) GetTrxExecTime() int32 {
	if m != nil {
		return m.TrxExecTime
	}
	return 0
}

func (m *TrxAndLock) GetTrxChangeTime() string {
	if m != nil {
		return m.TrxChangeTime
	}
	return ""
}

type Lock struct {
	LockProperty       string `protobuf:"bytes,1,opt,name=LockProperty,proto3" json:"LockProperty,omitempty"`
	LockId             string `protobuf:"bytes,2,opt,name=LockId,proto3" json:"LockId,omitempty"`
	LockAssociateIndex string `protobuf:"bytes,3,opt,name=LockAssociateIndex,proto3" json:"LockAssociateIndex,omitempty"`
	LockAssociateTable string `protobuf:"bytes,4,opt,name=LockAssociateTable,proto3" json:"LockAssociateTable,omitempty"`
	LockType           string `protobuf:"bytes,5,opt,name=LockType,proto3" json:"LockType,omitempty"`
	LockModel          string `protobuf:"bytes,6,opt,name=LockModel,proto3" json:"LockModel,omitempty"`
	TrxId              string `protobuf:"bytes,7,opt,name=TrxId,proto3" json:"TrxId,omitempty"`
}

func (m *Lock) Reset()      { *m = Lock{} }
func (*Lock) ProtoMessage() {}
func (*Lock) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{6}
}
func (m *Lock) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Lock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Lock.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Lock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Lock.Merge(m, src)
}
func (m *Lock) XXX_Size() int {
	return m.Size()
}
func (m *Lock) XXX_DiscardUnknown() {
	xxx_messageInfo_Lock.DiscardUnknown(m)
}

var xxx_messageInfo_Lock proto.InternalMessageInfo

func (m *Lock) GetLockProperty() string {
	if m != nil {
		return m.LockProperty
	}
	return ""
}

func (m *Lock) GetLockId() string {
	if m != nil {
		return m.LockId
	}
	return ""
}

func (m *Lock) GetLockAssociateIndex() string {
	if m != nil {
		return m.LockAssociateIndex
	}
	return ""
}

func (m *Lock) GetLockAssociateTable() string {
	if m != nil {
		return m.LockAssociateTable
	}
	return ""
}

func (m *Lock) GetLockType() string {
	if m != nil {
		return m.LockType
	}
	return ""
}

func (m *Lock) GetLockModel() string {
	if m != nil {
		return m.LockModel
	}
	return ""
}

func (m *Lock) GetTrxId() string {
	if m != nil {
		return m.TrxId
	}
	return ""
}

type TrxQueryFilter struct {
	TrxId       string `protobuf:"bytes,1,opt,name=TrxId,proto3" json:"TrxId,omitempty"`
	ProcessId   string `protobuf:"bytes,2,opt,name=ProcessId,proto3" json:"ProcessId,omitempty"`
	SqlBlocked  string `protobuf:"bytes,3,opt,name=SqlBlocked,proto3" json:"SqlBlocked,omitempty"`
	TrxStatus   string `protobuf:"bytes,4,opt,name=TrxStatus,proto3" json:"TrxStatus,omitempty"`
	LockStatus  string `protobuf:"bytes,5,opt,name=LockStatus,proto3" json:"LockStatus,omitempty"`
	TrxExecTime int32  `protobuf:"varint,6,opt,name=TrxExecTime,proto3" json:"TrxExecTime,omitempty"`
	TrxIsoLevel string `protobuf:"bytes,7,opt,name=TrxIsoLevel,proto3" json:"TrxIsoLevel,omitempty"`
	BlockTrxId  string `protobuf:"bytes,8,opt,name=BlockTrxId,proto3" json:"BlockTrxId,omitempty"`
}

func (m *TrxQueryFilter) Reset()      { *m = TrxQueryFilter{} }
func (*TrxQueryFilter) ProtoMessage() {}
func (*TrxQueryFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{7}
}
func (m *TrxQueryFilter) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TrxQueryFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TrxQueryFilter.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TrxQueryFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrxQueryFilter.Merge(m, src)
}
func (m *TrxQueryFilter) XXX_Size() int {
	return m.Size()
}
func (m *TrxQueryFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_TrxQueryFilter.DiscardUnknown(m)
}

var xxx_messageInfo_TrxQueryFilter proto.InternalMessageInfo

func (m *TrxQueryFilter) GetTrxId() string {
	if m != nil {
		return m.TrxId
	}
	return ""
}

func (m *TrxQueryFilter) GetProcessId() string {
	if m != nil {
		return m.ProcessId
	}
	return ""
}

func (m *TrxQueryFilter) GetSqlBlocked() string {
	if m != nil {
		return m.SqlBlocked
	}
	return ""
}

func (m *TrxQueryFilter) GetTrxStatus() string {
	if m != nil {
		return m.TrxStatus
	}
	return ""
}

func (m *TrxQueryFilter) GetLockStatus() string {
	if m != nil {
		return m.LockStatus
	}
	return ""
}

func (m *TrxQueryFilter) GetTrxExecTime() int32 {
	if m != nil {
		return m.TrxExecTime
	}
	return 0
}

func (m *TrxQueryFilter) GetTrxIsoLevel() string {
	if m != nil {
		return m.TrxIsoLevel
	}
	return ""
}

func (m *TrxQueryFilter) GetBlockTrxId() string {
	if m != nil {
		return m.BlockTrxId
	}
	return ""
}

// DescribeDeadlock
type DescribeDeadlock struct {
	InstanceId string `protobuf:"bytes,1,opt,name=InstanceId,proto3" json:"InstanceId,omitempty"`
	TenantId   string `protobuf:"bytes,2,opt,name=TenantId,proto3" json:"TenantId,omitempty"`
}

func (m *DescribeDeadlock) Reset()      { *m = DescribeDeadlock{} }
func (*DescribeDeadlock) ProtoMessage() {}
func (*DescribeDeadlock) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{8}
}
func (m *DescribeDeadlock) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeDeadlock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeDeadlock.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeDeadlock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeDeadlock.Merge(m, src)
}
func (m *DescribeDeadlock) XXX_Size() int {
	return m.Size()
}
func (m *DescribeDeadlock) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeDeadlock.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeDeadlock proto.InternalMessageInfo

func (m *DescribeDeadlock) GetInstanceId() string {
	if m != nil {
		return m.InstanceId
	}
	return ""
}

func (m *DescribeDeadlock) GetTenantId() string {
	if m != nil {
		return m.TenantId
	}
	return ""
}

type DescribeDeadlockInfo struct {
	DeadlockInfoList []*DeadlockInfo `protobuf:"bytes,1,rep,name=DeadlockInfoList,proto3" json:"DeadlockInfoList,omitempty"`
}

func (m *DescribeDeadlockInfo) Reset()      { *m = DescribeDeadlockInfo{} }
func (*DescribeDeadlockInfo) ProtoMessage() {}
func (*DescribeDeadlockInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{9}
}
func (m *DescribeDeadlockInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeDeadlockInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeDeadlockInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeDeadlockInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeDeadlockInfo.Merge(m, src)
}
func (m *DescribeDeadlockInfo) XXX_Size() int {
	return m.Size()
}
func (m *DescribeDeadlockInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeDeadlockInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeDeadlockInfo proto.InternalMessageInfo

func (m *DescribeDeadlockInfo) GetDeadlockInfoList() []*DeadlockInfo {
	if m != nil {
		return m.DeadlockInfoList
	}
	return nil
}

type DeadlockInfo struct {
	DeadlockCollectionTime string      `protobuf:"bytes,1,opt,name=DeadlockCollectionTime,proto3" json:"DeadlockCollectionTime,omitempty"`
	DeadlockTime           string      `protobuf:"bytes,2,opt,name=DeadlockTime,proto3" json:"DeadlockTime,omitempty"`
	DeadlockList           []*Deadlock `protobuf:"bytes,3,rep,name=DeadlockList,proto3" json:"DeadlockList,omitempty"`
	NodeId                 string      `protobuf:"bytes,4,opt,name=NodeId,proto3" json:"NodeId,omitempty"`
}

func (m *DeadlockInfo) Reset()      { *m = DeadlockInfo{} }
func (*DeadlockInfo) ProtoMessage() {}
func (*DeadlockInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{10}
}
func (m *DeadlockInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeadlockInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeadlockInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeadlockInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeadlockInfo.Merge(m, src)
}
func (m *DeadlockInfo) XXX_Size() int {
	return m.Size()
}
func (m *DeadlockInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DeadlockInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DeadlockInfo proto.InternalMessageInfo

func (m *DeadlockInfo) GetDeadlockCollectionTime() string {
	if m != nil {
		return m.DeadlockCollectionTime
	}
	return ""
}

func (m *DeadlockInfo) GetDeadlockTime() string {
	if m != nil {
		return m.DeadlockTime
	}
	return ""
}

func (m *DeadlockInfo) GetDeadlockList() []*Deadlock {
	if m != nil {
		return m.DeadlockList
	}
	return nil
}

func (m *DeadlockInfo) GetNodeId() string {
	if m != nil {
		return m.NodeId
	}
	return ""
}

type Deadlock struct {
	TrxInfo       string `protobuf:"bytes,1,opt,name=TrxInfo,proto3" json:"TrxInfo,omitempty"`
	ProcessId     string `protobuf:"bytes,2,opt,name=ProcessId,proto3" json:"ProcessId,omitempty"`
	ReqType       string `protobuf:"bytes,3,opt,name=ReqType,proto3" json:"ReqType,omitempty"`
	RelateTable   string `protobuf:"bytes,4,opt,name=RelateTable,proto3" json:"RelateTable,omitempty"`
	WaitLock      string `protobuf:"bytes,5,opt,name=WaitLock,proto3" json:"WaitLock,omitempty"`
	WaitIndex     string `protobuf:"bytes,6,opt,name=WaitIndex,proto3" json:"WaitIndex,omitempty"`
	WaitLockMode  string `protobuf:"bytes,7,opt,name=WaitLockMode,proto3" json:"WaitLockMode,omitempty"`
	HoldLock      string `protobuf:"bytes,8,opt,name=HoldLock,proto3" json:"HoldLock,omitempty"`
	HoldLockIndex string `protobuf:"bytes,9,opt,name=HoldLockIndex,proto3" json:"HoldLockIndex,omitempty"`
	HoldLockMode  string `protobuf:"bytes,10,opt,name=HoldLockMode,proto3" json:"HoldLockMode,omitempty"`
	Sql           string `protobuf:"bytes,11,opt,name=Sql,proto3" json:"Sql,omitempty"`
	TrxTreat      string `protobuf:"bytes,12,opt,name=TrxTreat,proto3" json:"TrxTreat,omitempty"`
}

func (m *Deadlock) Reset()      { *m = Deadlock{} }
func (*Deadlock) ProtoMessage() {}
func (*Deadlock) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{11}
}
func (m *Deadlock) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Deadlock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Deadlock.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Deadlock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Deadlock.Merge(m, src)
}
func (m *Deadlock) XXX_Size() int {
	return m.Size()
}
func (m *Deadlock) XXX_DiscardUnknown() {
	xxx_messageInfo_Deadlock.DiscardUnknown(m)
}

var xxx_messageInfo_Deadlock proto.InternalMessageInfo

func (m *Deadlock) GetTrxInfo() string {
	if m != nil {
		return m.TrxInfo
	}
	return ""
}

func (m *Deadlock) GetProcessId() string {
	if m != nil {
		return m.ProcessId
	}
	return ""
}

func (m *Deadlock) GetReqType() string {
	if m != nil {
		return m.ReqType
	}
	return ""
}

func (m *Deadlock) GetRelateTable() string {
	if m != nil {
		return m.RelateTable
	}
	return ""
}

func (m *Deadlock) GetWaitLock() string {
	if m != nil {
		return m.WaitLock
	}
	return ""
}

func (m *Deadlock) GetWaitIndex() string {
	if m != nil {
		return m.WaitIndex
	}
	return ""
}

func (m *Deadlock) GetWaitLockMode() string {
	if m != nil {
		return m.WaitLockMode
	}
	return ""
}

func (m *Deadlock) GetHoldLock() string {
	if m != nil {
		return m.HoldLock
	}
	return ""
}

func (m *Deadlock) GetHoldLockIndex() string {
	if m != nil {
		return m.HoldLockIndex
	}
	return ""
}

func (m *Deadlock) GetHoldLockMode() string {
	if m != nil {
		return m.HoldLockMode
	}
	return ""
}

func (m *Deadlock) GetSql() string {
	if m != nil {
		return m.Sql
	}
	return ""
}

func (m *Deadlock) GetTrxTreat() string {
	if m != nil {
		return m.TrxTreat
	}
	return ""
}

// DescribeDeadlockDetect
type DescribeDeadlockDetect struct {
}

func (m *DescribeDeadlockDetect) Reset()      { *m = DescribeDeadlockDetect{} }
func (*DescribeDeadlockDetect) ProtoMessage() {}
func (*DescribeDeadlockDetect) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{12}
}
func (m *DescribeDeadlockDetect) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeDeadlockDetect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeDeadlockDetect.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeDeadlockDetect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeDeadlockDetect.Merge(m, src)
}
func (m *DescribeDeadlockDetect) XXX_Size() int {
	return m.Size()
}
func (m *DescribeDeadlockDetect) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeDeadlockDetect.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeDeadlockDetect proto.InternalMessageInfo

type DescribeDeadlockDetectInfo struct {
	OnOff bool `protobuf:"varint,1,opt,name=OnOff,proto3" json:"OnOff,omitempty"`
}

func (m *DescribeDeadlockDetectInfo) Reset()      { *m = DescribeDeadlockDetectInfo{} }
func (*DescribeDeadlockDetectInfo) ProtoMessage() {}
func (*DescribeDeadlockDetectInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee904cf3b19d164a, []int{13}
}
func (m *DescribeDeadlockDetectInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeDeadlockDetectInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeDeadlockDetectInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeDeadlockDetectInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeDeadlockDetectInfo.Merge(m, src)
}
func (m *DescribeDeadlockDetectInfo) XXX_Size() int {
	return m.Size()
}
func (m *DescribeDeadlockDetectInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeDeadlockDetectInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeDeadlockDetectInfo proto.InternalMessageInfo

func (m *DescribeDeadlockDetectInfo) GetOnOff() bool {
	if m != nil {
		return m.OnOff
	}
	return false
}

func init() {
	proto.RegisterEnum("shared.LockStatus", LockStatus_name, LockStatus_value)
	proto.RegisterType((*DescribeTrxAndLocks)(nil), "shared.DescribeTrxAndLocks")
	proto.RegisterType((*DescribeTrxAndLocksInfo)(nil), "shared.DescribeTrxAndLocksInfo")
	proto.RegisterType((*TrxAndLockOverview)(nil), "shared.TrxAndLockOverview")
	proto.RegisterType((*LockStatistics)(nil), "shared.LockStatistics")
	proto.RegisterType((*WaitLockDetail)(nil), "shared.WaitLockDetail")
	proto.RegisterType((*TrxAndLock)(nil), "shared.TrxAndLock")
	proto.RegisterType((*Lock)(nil), "shared.Lock")
	proto.RegisterType((*TrxQueryFilter)(nil), "shared.TrxQueryFilter")
	proto.RegisterType((*DescribeDeadlock)(nil), "shared.DescribeDeadlock")
	proto.RegisterType((*DescribeDeadlockInfo)(nil), "shared.DescribeDeadlockInfo")
	proto.RegisterType((*DeadlockInfo)(nil), "shared.DeadlockInfo")
	proto.RegisterType((*Deadlock)(nil), "shared.Deadlock")
	proto.RegisterType((*DescribeDeadlockDetect)(nil), "shared.DescribeDeadlockDetect")
	proto.RegisterType((*DescribeDeadlockDetectInfo)(nil), "shared.DescribeDeadlockDetectInfo")
}

func init() { proto.RegisterFile("trx.proto", fileDescriptor_ee904cf3b19d164a) }

var fileDescriptor_ee904cf3b19d164a = []byte{
	// 1174 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0x3d, 0x6f, 0xe3, 0x46,
	0x13, 0x16, 0x25, 0x5b, 0x12, 0x47, 0x3a, 0x59, 0xef, 0x9e, 0xe1, 0x97, 0x30, 0x0e, 0x84, 0x41,
	0x04, 0x81, 0x70, 0x85, 0x0b, 0xe5, 0x70, 0x08, 0x52, 0x04, 0xf1, 0x47, 0x72, 0x51, 0xe0, 0xaf,
	0xd0, 0x04, 0x92, 0x96, 0x16, 0x57, 0x31, 0x71, 0x34, 0xd7, 0x5e, 0xae, 0x7d, 0x74, 0x97, 0x2a,
	0x75, 0x02, 0xe4, 0x1f, 0x24, 0x08, 0xee, 0x27, 0xe4, 0x27, 0xa4, 0x74, 0x79, 0x65, 0x2c, 0x37,
	0x29, 0x8d, 0xfc, 0x82, 0x60, 0x67, 0xf9, 0xb1, 0xa4, 0x14, 0x5c, 0x91, 0x4a, 0x9c, 0x67, 0x66,
	0x77, 0x47, 0x33, 0xcf, 0x33, 0xbb, 0x60, 0x0a, 0x9e, 0x6e, 0x5f, 0x72, 0x26, 0x18, 0x69, 0x27,
	0xe7, 0x3e, 0xa7, 0x81, 0xf3, 0x53, 0x13, 0x9e, 0xee, 0xd3, 0x64, 0xca, 0xc3, 0x33, 0xea, 0xf1,
	0x74, 0x27, 0x0e, 0x0e, 0xd8, 0xf4, 0x75, 0x42, 0x36, 0xa0, 0x7d, 0x3c, 0x9b, 0x25, 0x54, 0x58,
	0xc6, 0x96, 0x31, 0x5a, 0x75, 0x33, 0x8b, 0xac, 0xc3, 0xea, 0x41, 0x78, 0x11, 0x0a, 0xab, 0x89,
	0xb0, 0x32, 0xc8, 0x16, 0xf4, 0x3c, 0x9e, 0x7e, 0x9e, 0xd2, 0xa9, 0x17, 0x5e, 0x50, 0xab, 0x85,
	0x3e, 0x1d, 0x22, 0xcf, 0xc0, 0xf4, 0x78, 0x7a, 0x2a, 0x7c, 0x71, 0x9d, 0x58, 0x2b, 0x5b, 0xc6,
	0xc8, 0x74, 0x4b, 0x80, 0xd8, 0x00, 0xf2, 0xd8, 0xcc, 0xbd, 0x8a, 0x6e, 0x0d, 0x91, 0xab, 0x4f,
	0x19, 0x17, 0x27, 0x3e, 0xf7, 0x2f, 0xac, 0xb6, 0x5a, 0x5d, 0x00, 0x32, 0xa7, 0x63, 0x1e, 0x50,
	0x6e, 0x75, 0xd0, 0xa3, 0x0c, 0xf2, 0x31, 0xf4, 0xbe, 0xbe, 0xa6, 0xfc, 0xf6, 0x8b, 0x30, 0x12,
	0x94, 0x5b, 0xdd, 0x2d, 0x63, 0xd4, 0x1b, 0x6f, 0x6c, 0xab, 0xff, 0xbd, 0xed, 0xf1, 0x54, 0xf3,
	0xba, 0x7a, 0xa8, 0xf3, 0x8b, 0x01, 0xff, 0x5f, 0x52, 0x93, 0x49, 0x3c, 0x63, 0xe4, 0x13, 0x18,
	0x94, 0xd0, 0x41, 0x98, 0xc8, 0xfa, 0xb4, 0x46, 0xbd, 0x31, 0xd1, 0x36, 0xce, 0xbc, 0x6e, 0x2d,
	0x52, 0xe6, 0xe9, 0x31, 0xe1, 0x47, 0x79, 0xed, 0xd0, 0x20, 0x2f, 0x01, 0xe4, 0xbf, 0x0c, 0x13,
	0x11, 0x4e, 0x13, 0x2c, 0x9d, 0x96, 0x66, 0x5e, 0x03, 0xe5, 0x75, 0xb5, 0x48, 0xe7, 0x67, 0x03,
	0x48, 0x79, 0xc0, 0xf1, 0x0d, 0xe5, 0x37, 0x21, 0x7d, 0x43, 0x3e, 0x80, 0x27, 0xb8, 0xef, 0x97,
	0x2c, 0x42, 0x47, 0xd6, 0xbf, 0x2a, 0x58, 0x44, 0x7d, 0xe3, 0x87, 0x02, 0xa3, 0x9a, 0x5a, 0x54,
	0x0e, 0x92, 0x31, 0xac, 0x8b, 0xf0, 0x82, 0xbe, 0xe2, 0xd4, 0x17, 0x94, 0x7b, 0xe7, 0x7e, 0xbc,
	0xc7, 0xae, 0x63, 0x91, 0xf5, 0x77, 0xa9, 0xcf, 0x79, 0x6b, 0xc0, 0xa0, 0x9a, 0x35, 0xf9, 0x0a,
	0xc8, 0x24, 0x8e, 0xd9, 0xfe, 0xae, 0x9e, 0x28, 0xe6, 0xd5, 0x1b, 0x6f, 0x2e, 0xd6, 0x2d, 0x8f,
	0x70, 0x97, 0xac, 0x92, 0x7b, 0x1d, 0x52, 0xe1, 0xd7, 0xf6, 0x6a, 0xbe, 0x7f, 0xaf, 0xc5, 0x55,
	0xce, 0x6f, 0x06, 0x0c, 0xf2, 0xff, 0xba, 0x4f, 0x85, 0x1f, 0x46, 0xe4, 0x43, 0x18, 0xb8, 0xf4,
	0xea, 0x9a, 0x26, 0x82, 0x06, 0x1e, 0x4f, 0x27, 0x01, 0xa6, 0x69, 0xba, 0x35, 0x94, 0x8c, 0x60,
	0xad, 0x40, 0xe4, 0xf2, 0x49, 0x80, 0x39, 0x98, 0x6e, 0x1d, 0x26, 0x16, 0x74, 0x4e, 0xaf, 0x22,
	0x8f, 0xa6, 0xaa, 0x6c, 0xa6, 0x9b, 0x9b, 0x64, 0x04, 0x5d, 0x8c, 0x89, 0x67, 0x0c, 0x15, 0xd1,
	0x1b, 0xf7, 0xf5, 0xb6, 0xbb, 0x85, 0xd7, 0xf9, 0x75, 0x15, 0xa0, 0xfc, 0x4f, 0x52, 0x0d, 0x27,
	0x9c, 0x4d, 0x69, 0x92, 0x14, 0xf9, 0x95, 0x00, 0xb2, 0x0c, 0x33, 0x57, 0x09, 0x29, 0xa3, 0xaa,
	0xbf, 0x56, 0x5d, 0x7f, 0xe3, 0x8a, 0xfe, 0x64, 0x32, 0x83, 0x92, 0xd1, 0xa5, 0xa7, 0xa2, 0x49,
	0xa5, 0xf9, 0x49, 0xc2, 0x0e, 0xe8, 0x0d, 0x8d, 0x32, 0xd1, 0xea, 0x10, 0x71, 0xa0, 0xaf, 0x8e,
	0xe0, 0x02, 0xc7, 0x82, 0x12, 0x6e, 0x05, 0x23, 0xcf, 0x61, 0xe8, 0xf1, 0x54, 0x76, 0xa1, 0x8c,
	0x53, 0x32, 0x5e, 0xc0, 0xe5, 0x94, 0x38, 0xbd, 0x8a, 0x76, 0x23, 0x36, 0x7d, 0x4d, 0x03, 0x14,
	0xb4, 0xe9, 0x6a, 0x88, 0x6c, 0x8a, 0xc7, 0x53, 0xcf, 0x3f, 0x8b, 0x68, 0x72, 0xa0, 0x82, 0x4c,
	0x64, 0x6a, 0x1d, 0x46, 0xfa, 0xf3, 0xd4, 0x65, 0x6f, 0xf2, 0x38, 0xc8, 0xe8, 0xaf, 0x83, 0xd9,
	0x7e, 0x12, 0x38, 0x64, 0x41, 0x38, 0x0b, 0x69, 0x60, 0xf5, 0x8a, 0xfd, 0x74, 0x38, 0x6f, 0x25,
	0xce, 0x83, 0x3e, 0xce, 0x83, 0x25, 0xad, 0xc4, 0x19, 0xb0, 0x01, 0xed, 0x23, 0x16, 0xd0, 0x49,
	0x60, 0x3d, 0xc1, 0xfc, 0x33, 0x8b, 0x6c, 0x42, 0x57, 0x7e, 0x79, 0xb7, 0x97, 0xd4, 0x1a, 0xa0,
	0xa7, 0xb0, 0xc9, 0xa7, 0x75, 0x9a, 0x5a, 0x6b, 0xd5, 0x29, 0x51, 0xf5, 0xba, 0x75, 0x52, 0xdb,
	0x00, 0x58, 0x22, 0x45, 0x8b, 0xa1, 0xaa, 0x5b, 0x89, 0xd4, 0xa7, 0xf7, 0xff, 0x16, 0xa7, 0xb7,
	0xaa, 0xd7, 0xde, 0xb9, 0x1f, 0x7f, 0x47, 0x31, 0x86, 0xe0, 0x26, 0x55, 0xd0, 0xf9, 0xdb, 0x80,
	0x15, 0x24, 0xa8, 0x03, 0x7d, 0xf9, 0x7b, 0xc2, 0xd9, 0x25, 0xe5, 0xe2, 0x36, 0xe3, 0x68, 0x05,
	0x93, 0x85, 0xa8, 0x08, 0x27, 0xb3, 0xc8, 0x36, 0x10, 0xf9, 0xb5, 0x93, 0x24, 0x6c, 0x1a, 0xfa,
	0x82, 0x4e, 0xe2, 0x80, 0xa6, 0x19, 0x63, 0x97, 0x78, 0x16, 0xe2, 0xb1, 0xcf, 0xd9, 0x0d, 0xb3,
	0xc4, 0x23, 0x0b, 0x2d, 0x51, 0x2c, 0xb4, 0xe2, 0x6c, 0x61, 0x4b, 0x91, 0xc8, 0xef, 0x43, 0x16,
	0xd0, 0x28, 0xbf, 0x66, 0x0a, 0xa0, 0x14, 0x56, 0x47, 0x13, 0x96, 0xf3, 0x43, 0x13, 0x6f, 0x04,
	0xed, 0xfe, 0x28, 0x03, 0x8d, 0x9a, 0x02, 0x4b, 0xd5, 0x36, 0xeb, 0xaa, 0xad, 0x72, 0xbb, 0xb5,
	0xc0, 0xed, 0xff, 0x76, 0x7f, 0xd6, 0x3a, 0xdc, 0x5e, 0xec, 0x70, 0x4d, 0xcd, 0x9d, 0x45, 0x35,
	0x57, 0x59, 0xd4, 0xad, 0xb3, 0xc8, 0x39, 0x82, 0x61, 0x7e, 0x69, 0xee, 0x53, 0x3f, 0x90, 0x0e,
	0xb9, 0x66, 0x12, 0x27, 0xc2, 0x8f, 0xa7, 0xb4, 0x28, 0x87, 0x86, 0xc8, 0x66, 0x78, 0x34, 0xf6,
	0x63, 0x51, 0x94, 0xa4, 0xb0, 0x9d, 0x6f, 0x61, 0xbd, 0xbe, 0x1f, 0xde, 0xc0, 0x9f, 0xc9, 0x73,
	0x4a, 0x5b, 0xbb, 0x83, 0xd7, 0x73, 0x3d, 0xe8, 0x7e, 0x77, 0x21, 0xda, 0xf9, 0xdd, 0x80, 0x7e,
	0x65, 0xcb, 0x97, 0xb0, 0x91, 0xdb, 0x7b, 0x2c, 0x8a, 0xe8, 0x54, 0x84, 0x2c, 0xc6, 0x4a, 0xa9,
	0x94, 0xff, 0xc5, 0x2b, 0x79, 0x9e, 0x7b, 0x30, 0x5a, 0xfd, 0x85, 0x0a, 0x46, 0x5e, 0x94, 0x31,
	0x98, 0x6a, 0x0b, 0x53, 0x1d, 0xd6, 0x53, 0x75, 0x2b, 0x51, 0xda, 0x98, 0x58, 0xd1, 0xc7, 0x84,
	0xf3, 0xd8, 0x84, 0x6e, 0x51, 0x5d, 0x0b, 0x3a, 0xb2, 0xf4, 0xf2, 0xfe, 0x50, 0x79, 0xe6, 0xe6,
	0x7b, 0xb8, 0x66, 0x41, 0xc7, 0xa5, 0x57, 0xa8, 0x80, 0xec, 0x4a, 0xca, 0x4c, 0xc9, 0x02, 0x97,
	0x46, 0x35, 0x15, 0xe9, 0x90, 0xec, 0x58, 0xf1, 0x66, 0xc8, 0xe4, 0x53, 0x3c, 0x17, 0x9e, 0x81,
	0x29, 0xbf, 0x95, 0x62, 0x33, 0xf9, 0x14, 0x80, 0x2c, 0x56, 0x1e, 0x29, 0xf5, 0x94, 0x51, 0xac,
	0x82, 0xc9, 0xdd, 0x8b, 0x77, 0x8b, 0x62, 0x58, 0x57, 0x7f, 0xb2, 0xe4, 0xdf, 0xea, 0x04, 0x53,
	0xcd, 0xa0, 0x0a, 0x28, 0x4f, 0xc9, 0x01, 0x3c, 0x05, 0xd4, 0x29, 0x3a, 0x46, 0x86, 0xd0, 0x3a,
	0xbd, 0x8a, 0x70, 0x96, 0x9b, 0xae, 0xfc, 0x44, 0x1e, 0xf2, 0xd4, 0x93, 0x6f, 0x19, 0xab, 0x9f,
	0xf1, 0x30, 0xb3, 0x1d, 0x4b, 0x92, 0xa3, 0xca, 0xc3, 0x7d, 0x2a, 0xe8, 0x54, 0x38, 0x63, 0xd8,
	0x5c, 0xee, 0xc1, 0x1e, 0xc8, 0x57, 0x69, 0x7c, 0x3c, 0x9b, 0x61, 0x6f, 0xba, 0xae, 0x32, 0x9e,
	0xbf, 0xd2, 0x95, 0x4a, 0xba, 0xb0, 0x72, 0xc4, 0x62, 0x3a, 0x6c, 0x90, 0xbe, 0x1a, 0x4b, 0x32,
	0xcf, 0xa1, 0x91, 0x5b, 0xb2, 0x36, 0xc3, 0x26, 0x79, 0x0a, 0x6b, 0xb9, 0x6f, 0x27, 0x0e, 0x10,
	0x6c, 0xed, 0xbe, 0xb8, 0xbb, 0xb7, 0x1b, 0xef, 0xee, 0xed, 0xc6, 0xe3, 0xbd, 0x6d, 0x7c, 0x3f,
	0xb7, 0x8d, 0xb7, 0x73, 0xdb, 0xf8, 0x63, 0x6e, 0x1b, 0x77, 0x73, 0xdb, 0xf8, 0x73, 0x6e, 0x1b,
	0x7f, 0xcd, 0xed, 0xc6, 0xe3, 0xdc, 0x36, 0x7e, 0x7c, 0xb0, 0x1b, 0x77, 0x0f, 0x76, 0xe3, 0xdd,
	0x83, 0xdd, 0x38, 0x6b, 0xe3, 0xeb, 0xff, 0xa3, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x0b, 0xb1,
	0x5c, 0x14, 0x0a, 0x0c, 0x00, 0x00,
}

func (x LockStatus) String() string {
	s, ok := LockStatus_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *DescribeTrxAndLocks) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeTrxAndLocks)
	if !ok {
		that2, ok := that.(DescribeTrxAndLocks)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Offset != that1.Offset {
		return false
	}
	if this.Limit != that1.Limit {
		return false
	}
	if this.TrxExecTime != that1.TrxExecTime {
		return false
	}
	if this.TrxStatus != that1.TrxStatus {
		return false
	}
	if this.LockStatus != that1.LockStatus {
		return false
	}
	if this.SortParam != that1.SortParam {
		return false
	}
	if this.Order != that1.Order {
		return false
	}
	if !this.QueryFilter.Equal(that1.QueryFilter) {
		return false
	}
	return true
}
func (this *DescribeTrxAndLocksInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeTrxAndLocksInfo)
	if !ok {
		that2, ok := that.(DescribeTrxAndLocksInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.TrxAndLockList) != len(that1.TrxAndLockList) {
		return false
	}
	for i := range this.TrxAndLockList {
		if !this.TrxAndLockList[i].Equal(that1.TrxAndLockList[i]) {
			return false
		}
	}
	if this.Total != that1.Total {
		return false
	}
	if !this.Statistics.Equal(that1.Statistics) {
		return false
	}
	return true
}
func (this *TrxAndLockOverview) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TrxAndLockOverview)
	if !ok {
		that2, ok := that.(TrxAndLockOverview)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TotalHoldLock != that1.TotalHoldLock {
		return false
	}
	if this.TotalWaitLock != that1.TotalWaitLock {
		return false
	}
	if this.TimeGreaterThanCount != that1.TimeGreaterThanCount {
		return false
	}
	return true
}
func (this *LockStatistics) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LockStatistics)
	if !ok {
		that2, ok := that.(LockStatistics)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.InnoDBLockOverview.Equal(that1.InnoDBLockOverview) {
		return false
	}
	if !this.MetaDBLockOverview.Equal(that1.MetaDBLockOverview) {
		return false
	}
	return true
}
func (this *WaitLockDetail) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*WaitLockDetail)
	if !ok {
		that2, ok := that.(WaitLockDetail)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.RequestedTrxId != that1.RequestedTrxId {
		return false
	}
	if this.RequestedLockId != that1.RequestedLockId {
		return false
	}
	if this.SqlText != that1.SqlText {
		return false
	}
	if !this.LockInfo.Equal(that1.LockInfo) {
		return false
	}
	return true
}
func (this *TrxAndLock) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TrxAndLock)
	if !ok {
		that2, ok := that.(TrxAndLock)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.ProcessId != that1.ProcessId {
		return false
	}
	if this.TrxId != that1.TrxId {
		return false
	}
	if this.TrxStatus != that1.TrxStatus {
		return false
	}
	if this.LockStatus != that1.LockStatus {
		return false
	}
	if this.TrxIsoLevel != that1.TrxIsoLevel {
		return false
	}
	if this.TrxStartTime != that1.TrxStartTime {
		return false
	}
	if this.TrxWaitStartTime != that1.TrxWaitStartTime {
		return false
	}
	if this.SqlBlocked != that1.SqlBlocked {
		return false
	}
	if this.TrxTablesLocked != that1.TrxTablesLocked {
		return false
	}
	if this.TrxRowsLocked != that1.TrxRowsLocked {
		return false
	}
	if this.TrxRowsModified != that1.TrxRowsModified {
		return false
	}
	if len(this.LockList) != len(that1.LockList) {
		return false
	}
	for i := range this.LockList {
		if !this.LockList[i].Equal(that1.LockList[i]) {
			return false
		}
	}
	if this.NodeId != that1.NodeId {
		return false
	}
	if this.NodeType != that1.NodeType {
		return false
	}
	if !this.WaitLockDetail.Equal(that1.WaitLockDetail) {
		return false
	}
	if this.BlockTrxId != that1.BlockTrxId {
		return false
	}
	if this.TrxExecTime != that1.TrxExecTime {
		return false
	}
	if this.TrxChangeTime != that1.TrxChangeTime {
		return false
	}
	return true
}
func (this *Lock) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Lock)
	if !ok {
		that2, ok := that.(Lock)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.LockProperty != that1.LockProperty {
		return false
	}
	if this.LockId != that1.LockId {
		return false
	}
	if this.LockAssociateIndex != that1.LockAssociateIndex {
		return false
	}
	if this.LockAssociateTable != that1.LockAssociateTable {
		return false
	}
	if this.LockType != that1.LockType {
		return false
	}
	if this.LockModel != that1.LockModel {
		return false
	}
	if this.TrxId != that1.TrxId {
		return false
	}
	return true
}
func (this *TrxQueryFilter) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TrxQueryFilter)
	if !ok {
		that2, ok := that.(TrxQueryFilter)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TrxId != that1.TrxId {
		return false
	}
	if this.ProcessId != that1.ProcessId {
		return false
	}
	if this.SqlBlocked != that1.SqlBlocked {
		return false
	}
	if this.TrxStatus != that1.TrxStatus {
		return false
	}
	if this.LockStatus != that1.LockStatus {
		return false
	}
	if this.TrxExecTime != that1.TrxExecTime {
		return false
	}
	if this.TrxIsoLevel != that1.TrxIsoLevel {
		return false
	}
	if this.BlockTrxId != that1.BlockTrxId {
		return false
	}
	return true
}
func (this *DescribeDeadlock) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeDeadlock)
	if !ok {
		that2, ok := that.(DescribeDeadlock)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.InstanceId != that1.InstanceId {
		return false
	}
	if this.TenantId != that1.TenantId {
		return false
	}
	return true
}
func (this *DescribeDeadlockInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeDeadlockInfo)
	if !ok {
		that2, ok := that.(DescribeDeadlockInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.DeadlockInfoList) != len(that1.DeadlockInfoList) {
		return false
	}
	for i := range this.DeadlockInfoList {
		if !this.DeadlockInfoList[i].Equal(that1.DeadlockInfoList[i]) {
			return false
		}
	}
	return true
}
func (this *DeadlockInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DeadlockInfo)
	if !ok {
		that2, ok := that.(DeadlockInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.DeadlockCollectionTime != that1.DeadlockCollectionTime {
		return false
	}
	if this.DeadlockTime != that1.DeadlockTime {
		return false
	}
	if len(this.DeadlockList) != len(that1.DeadlockList) {
		return false
	}
	for i := range this.DeadlockList {
		if !this.DeadlockList[i].Equal(that1.DeadlockList[i]) {
			return false
		}
	}
	if this.NodeId != that1.NodeId {
		return false
	}
	return true
}
func (this *Deadlock) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Deadlock)
	if !ok {
		that2, ok := that.(Deadlock)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TrxInfo != that1.TrxInfo {
		return false
	}
	if this.ProcessId != that1.ProcessId {
		return false
	}
	if this.ReqType != that1.ReqType {
		return false
	}
	if this.RelateTable != that1.RelateTable {
		return false
	}
	if this.WaitLock != that1.WaitLock {
		return false
	}
	if this.WaitIndex != that1.WaitIndex {
		return false
	}
	if this.WaitLockMode != that1.WaitLockMode {
		return false
	}
	if this.HoldLock != that1.HoldLock {
		return false
	}
	if this.HoldLockIndex != that1.HoldLockIndex {
		return false
	}
	if this.HoldLockMode != that1.HoldLockMode {
		return false
	}
	if this.Sql != that1.Sql {
		return false
	}
	if this.TrxTreat != that1.TrxTreat {
		return false
	}
	return true
}
func (this *DescribeDeadlockDetect) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeDeadlockDetect)
	if !ok {
		that2, ok := that.(DescribeDeadlockDetect)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *DescribeDeadlockDetectInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeDeadlockDetectInfo)
	if !ok {
		that2, ok := that.(DescribeDeadlockDetectInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.OnOff != that1.OnOff {
		return false
	}
	return true
}
func (this *DescribeTrxAndLocks) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&shared.DescribeTrxAndLocks{")
	s = append(s, "Offset: "+fmt.Sprintf("%#v", this.Offset)+",\n")
	s = append(s, "Limit: "+fmt.Sprintf("%#v", this.Limit)+",\n")
	s = append(s, "TrxExecTime: "+fmt.Sprintf("%#v", this.TrxExecTime)+",\n")
	s = append(s, "TrxStatus: "+fmt.Sprintf("%#v", this.TrxStatus)+",\n")
	s = append(s, "LockStatus: "+fmt.Sprintf("%#v", this.LockStatus)+",\n")
	s = append(s, "SortParam: "+fmt.Sprintf("%#v", this.SortParam)+",\n")
	s = append(s, "Order: "+fmt.Sprintf("%#v", this.Order)+",\n")
	if this.QueryFilter != nil {
		s = append(s, "QueryFilter: "+fmt.Sprintf("%#v", this.QueryFilter)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeTrxAndLocksInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.DescribeTrxAndLocksInfo{")
	if this.TrxAndLockList != nil {
		s = append(s, "TrxAndLockList: "+fmt.Sprintf("%#v", this.TrxAndLockList)+",\n")
	}
	s = append(s, "Total: "+fmt.Sprintf("%#v", this.Total)+",\n")
	if this.Statistics != nil {
		s = append(s, "Statistics: "+fmt.Sprintf("%#v", this.Statistics)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TrxAndLockOverview) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.TrxAndLockOverview{")
	s = append(s, "TotalHoldLock: "+fmt.Sprintf("%#v", this.TotalHoldLock)+",\n")
	s = append(s, "TotalWaitLock: "+fmt.Sprintf("%#v", this.TotalWaitLock)+",\n")
	s = append(s, "TimeGreaterThanCount: "+fmt.Sprintf("%#v", this.TimeGreaterThanCount)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LockStatistics) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.LockStatistics{")
	if this.InnoDBLockOverview != nil {
		s = append(s, "InnoDBLockOverview: "+fmt.Sprintf("%#v", this.InnoDBLockOverview)+",\n")
	}
	if this.MetaDBLockOverview != nil {
		s = append(s, "MetaDBLockOverview: "+fmt.Sprintf("%#v", this.MetaDBLockOverview)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *WaitLockDetail) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.WaitLockDetail{")
	s = append(s, "RequestedTrxId: "+fmt.Sprintf("%#v", this.RequestedTrxId)+",\n")
	s = append(s, "RequestedLockId: "+fmt.Sprintf("%#v", this.RequestedLockId)+",\n")
	s = append(s, "SqlText: "+fmt.Sprintf("%#v", this.SqlText)+",\n")
	if this.LockInfo != nil {
		s = append(s, "LockInfo: "+fmt.Sprintf("%#v", this.LockInfo)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TrxAndLock) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 22)
	s = append(s, "&shared.TrxAndLock{")
	s = append(s, "ProcessId: "+fmt.Sprintf("%#v", this.ProcessId)+",\n")
	s = append(s, "TrxId: "+fmt.Sprintf("%#v", this.TrxId)+",\n")
	s = append(s, "TrxStatus: "+fmt.Sprintf("%#v", this.TrxStatus)+",\n")
	s = append(s, "LockStatus: "+fmt.Sprintf("%#v", this.LockStatus)+",\n")
	s = append(s, "TrxIsoLevel: "+fmt.Sprintf("%#v", this.TrxIsoLevel)+",\n")
	s = append(s, "TrxStartTime: "+fmt.Sprintf("%#v", this.TrxStartTime)+",\n")
	s = append(s, "TrxWaitStartTime: "+fmt.Sprintf("%#v", this.TrxWaitStartTime)+",\n")
	s = append(s, "SqlBlocked: "+fmt.Sprintf("%#v", this.SqlBlocked)+",\n")
	s = append(s, "TrxTablesLocked: "+fmt.Sprintf("%#v", this.TrxTablesLocked)+",\n")
	s = append(s, "TrxRowsLocked: "+fmt.Sprintf("%#v", this.TrxRowsLocked)+",\n")
	s = append(s, "TrxRowsModified: "+fmt.Sprintf("%#v", this.TrxRowsModified)+",\n")
	if this.LockList != nil {
		s = append(s, "LockList: "+fmt.Sprintf("%#v", this.LockList)+",\n")
	}
	s = append(s, "NodeId: "+fmt.Sprintf("%#v", this.NodeId)+",\n")
	s = append(s, "NodeType: "+fmt.Sprintf("%#v", this.NodeType)+",\n")
	if this.WaitLockDetail != nil {
		s = append(s, "WaitLockDetail: "+fmt.Sprintf("%#v", this.WaitLockDetail)+",\n")
	}
	s = append(s, "BlockTrxId: "+fmt.Sprintf("%#v", this.BlockTrxId)+",\n")
	s = append(s, "TrxExecTime: "+fmt.Sprintf("%#v", this.TrxExecTime)+",\n")
	s = append(s, "TrxChangeTime: "+fmt.Sprintf("%#v", this.TrxChangeTime)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Lock) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 11)
	s = append(s, "&shared.Lock{")
	s = append(s, "LockProperty: "+fmt.Sprintf("%#v", this.LockProperty)+",\n")
	s = append(s, "LockId: "+fmt.Sprintf("%#v", this.LockId)+",\n")
	s = append(s, "LockAssociateIndex: "+fmt.Sprintf("%#v", this.LockAssociateIndex)+",\n")
	s = append(s, "LockAssociateTable: "+fmt.Sprintf("%#v", this.LockAssociateTable)+",\n")
	s = append(s, "LockType: "+fmt.Sprintf("%#v", this.LockType)+",\n")
	s = append(s, "LockModel: "+fmt.Sprintf("%#v", this.LockModel)+",\n")
	s = append(s, "TrxId: "+fmt.Sprintf("%#v", this.TrxId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TrxQueryFilter) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&shared.TrxQueryFilter{")
	s = append(s, "TrxId: "+fmt.Sprintf("%#v", this.TrxId)+",\n")
	s = append(s, "ProcessId: "+fmt.Sprintf("%#v", this.ProcessId)+",\n")
	s = append(s, "SqlBlocked: "+fmt.Sprintf("%#v", this.SqlBlocked)+",\n")
	s = append(s, "TrxStatus: "+fmt.Sprintf("%#v", this.TrxStatus)+",\n")
	s = append(s, "LockStatus: "+fmt.Sprintf("%#v", this.LockStatus)+",\n")
	s = append(s, "TrxExecTime: "+fmt.Sprintf("%#v", this.TrxExecTime)+",\n")
	s = append(s, "TrxIsoLevel: "+fmt.Sprintf("%#v", this.TrxIsoLevel)+",\n")
	s = append(s, "BlockTrxId: "+fmt.Sprintf("%#v", this.BlockTrxId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeDeadlock) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.DescribeDeadlock{")
	s = append(s, "InstanceId: "+fmt.Sprintf("%#v", this.InstanceId)+",\n")
	s = append(s, "TenantId: "+fmt.Sprintf("%#v", this.TenantId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeDeadlockInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.DescribeDeadlockInfo{")
	if this.DeadlockInfoList != nil {
		s = append(s, "DeadlockInfoList: "+fmt.Sprintf("%#v", this.DeadlockInfoList)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DeadlockInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.DeadlockInfo{")
	s = append(s, "DeadlockCollectionTime: "+fmt.Sprintf("%#v", this.DeadlockCollectionTime)+",\n")
	s = append(s, "DeadlockTime: "+fmt.Sprintf("%#v", this.DeadlockTime)+",\n")
	if this.DeadlockList != nil {
		s = append(s, "DeadlockList: "+fmt.Sprintf("%#v", this.DeadlockList)+",\n")
	}
	s = append(s, "NodeId: "+fmt.Sprintf("%#v", this.NodeId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Deadlock) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 16)
	s = append(s, "&shared.Deadlock{")
	s = append(s, "TrxInfo: "+fmt.Sprintf("%#v", this.TrxInfo)+",\n")
	s = append(s, "ProcessId: "+fmt.Sprintf("%#v", this.ProcessId)+",\n")
	s = append(s, "ReqType: "+fmt.Sprintf("%#v", this.ReqType)+",\n")
	s = append(s, "RelateTable: "+fmt.Sprintf("%#v", this.RelateTable)+",\n")
	s = append(s, "WaitLock: "+fmt.Sprintf("%#v", this.WaitLock)+",\n")
	s = append(s, "WaitIndex: "+fmt.Sprintf("%#v", this.WaitIndex)+",\n")
	s = append(s, "WaitLockMode: "+fmt.Sprintf("%#v", this.WaitLockMode)+",\n")
	s = append(s, "HoldLock: "+fmt.Sprintf("%#v", this.HoldLock)+",\n")
	s = append(s, "HoldLockIndex: "+fmt.Sprintf("%#v", this.HoldLockIndex)+",\n")
	s = append(s, "HoldLockMode: "+fmt.Sprintf("%#v", this.HoldLockMode)+",\n")
	s = append(s, "Sql: "+fmt.Sprintf("%#v", this.Sql)+",\n")
	s = append(s, "TrxTreat: "+fmt.Sprintf("%#v", this.TrxTreat)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeDeadlockDetect) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.DescribeDeadlockDetect{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeDeadlockDetectInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.DescribeDeadlockDetectInfo{")
	s = append(s, "OnOff: "+fmt.Sprintf("%#v", this.OnOff)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringTrx(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *DescribeTrxAndLocks) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeTrxAndLocks) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeTrxAndLocks) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.QueryFilter != nil {
		{
			size, err := m.QueryFilter.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTrx(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if len(m.Order) > 0 {
		i -= len(m.Order)
		copy(dAtA[i:], m.Order)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.Order)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.SortParam) > 0 {
		i -= len(m.SortParam)
		copy(dAtA[i:], m.SortParam)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.SortParam)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.LockStatus) > 0 {
		i -= len(m.LockStatus)
		copy(dAtA[i:], m.LockStatus)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.LockStatus)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.TrxStatus) > 0 {
		i -= len(m.TrxStatus)
		copy(dAtA[i:], m.TrxStatus)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxStatus)))
		i--
		dAtA[i] = 0x22
	}
	if m.TrxExecTime != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TrxExecTime))
		i--
		dAtA[i] = 0x18
	}
	if m.Limit != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x10
	}
	if m.Offset != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.Offset))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DescribeTrxAndLocksInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeTrxAndLocksInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeTrxAndLocksInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Statistics != nil {
		{
			size, err := m.Statistics.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTrx(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Total != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.Total))
		i--
		dAtA[i] = 0x10
	}
	if len(m.TrxAndLockList) > 0 {
		for iNdEx := len(m.TrxAndLockList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TrxAndLockList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTrx(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TrxAndLockOverview) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrxAndLockOverview) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TrxAndLockOverview) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TimeGreaterThanCount != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TimeGreaterThanCount))
		i--
		dAtA[i] = 0x18
	}
	if m.TotalWaitLock != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TotalWaitLock))
		i--
		dAtA[i] = 0x10
	}
	if m.TotalHoldLock != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TotalHoldLock))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *LockStatistics) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LockStatistics) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LockStatistics) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MetaDBLockOverview != nil {
		{
			size, err := m.MetaDBLockOverview.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTrx(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.InnoDBLockOverview != nil {
		{
			size, err := m.InnoDBLockOverview.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTrx(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WaitLockDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WaitLockDetail) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WaitLockDetail) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LockInfo != nil {
		{
			size, err := m.LockInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTrx(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.SqlText) > 0 {
		i -= len(m.SqlText)
		copy(dAtA[i:], m.SqlText)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.SqlText)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.RequestedLockId) > 0 {
		i -= len(m.RequestedLockId)
		copy(dAtA[i:], m.RequestedLockId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.RequestedLockId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RequestedTrxId) > 0 {
		i -= len(m.RequestedTrxId)
		copy(dAtA[i:], m.RequestedTrxId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.RequestedTrxId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TrxAndLock) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrxAndLock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TrxAndLock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TrxChangeTime) > 0 {
		i -= len(m.TrxChangeTime)
		copy(dAtA[i:], m.TrxChangeTime)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxChangeTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	if m.TrxExecTime != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TrxExecTime))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if len(m.BlockTrxId) > 0 {
		i -= len(m.BlockTrxId)
		copy(dAtA[i:], m.BlockTrxId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.BlockTrxId)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if m.WaitLockDetail != nil {
		{
			size, err := m.WaitLockDetail.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTrx(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x7a
	}
	if len(m.NodeType) > 0 {
		i -= len(m.NodeType)
		copy(dAtA[i:], m.NodeType)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.NodeType)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.NodeId) > 0 {
		i -= len(m.NodeId)
		copy(dAtA[i:], m.NodeId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.NodeId)))
		i--
		dAtA[i] = 0x6a
	}
	if len(m.LockList) > 0 {
		for iNdEx := len(m.LockList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.LockList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTrx(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x62
		}
	}
	if m.TrxRowsModified != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TrxRowsModified))
		i--
		dAtA[i] = 0x58
	}
	if m.TrxRowsLocked != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TrxRowsLocked))
		i--
		dAtA[i] = 0x50
	}
	if m.TrxTablesLocked != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TrxTablesLocked))
		i--
		dAtA[i] = 0x48
	}
	if len(m.SqlBlocked) > 0 {
		i -= len(m.SqlBlocked)
		copy(dAtA[i:], m.SqlBlocked)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.SqlBlocked)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.TrxWaitStartTime) > 0 {
		i -= len(m.TrxWaitStartTime)
		copy(dAtA[i:], m.TrxWaitStartTime)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxWaitStartTime)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.TrxStartTime) > 0 {
		i -= len(m.TrxStartTime)
		copy(dAtA[i:], m.TrxStartTime)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxStartTime)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.TrxIsoLevel) > 0 {
		i -= len(m.TrxIsoLevel)
		copy(dAtA[i:], m.TrxIsoLevel)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxIsoLevel)))
		i--
		dAtA[i] = 0x2a
	}
	if m.LockStatus != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.LockStatus))
		i--
		dAtA[i] = 0x20
	}
	if len(m.TrxStatus) > 0 {
		i -= len(m.TrxStatus)
		copy(dAtA[i:], m.TrxStatus)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxStatus)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.TrxId) > 0 {
		i -= len(m.TrxId)
		copy(dAtA[i:], m.TrxId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ProcessId) > 0 {
		i -= len(m.ProcessId)
		copy(dAtA[i:], m.ProcessId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.ProcessId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Lock) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Lock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Lock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TrxId) > 0 {
		i -= len(m.TrxId)
		copy(dAtA[i:], m.TrxId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxId)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.LockModel) > 0 {
		i -= len(m.LockModel)
		copy(dAtA[i:], m.LockModel)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.LockModel)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.LockType) > 0 {
		i -= len(m.LockType)
		copy(dAtA[i:], m.LockType)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.LockType)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.LockAssociateTable) > 0 {
		i -= len(m.LockAssociateTable)
		copy(dAtA[i:], m.LockAssociateTable)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.LockAssociateTable)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.LockAssociateIndex) > 0 {
		i -= len(m.LockAssociateIndex)
		copy(dAtA[i:], m.LockAssociateIndex)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.LockAssociateIndex)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.LockId) > 0 {
		i -= len(m.LockId)
		copy(dAtA[i:], m.LockId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.LockId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.LockProperty) > 0 {
		i -= len(m.LockProperty)
		copy(dAtA[i:], m.LockProperty)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.LockProperty)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TrxQueryFilter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrxQueryFilter) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TrxQueryFilter) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BlockTrxId) > 0 {
		i -= len(m.BlockTrxId)
		copy(dAtA[i:], m.BlockTrxId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.BlockTrxId)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.TrxIsoLevel) > 0 {
		i -= len(m.TrxIsoLevel)
		copy(dAtA[i:], m.TrxIsoLevel)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxIsoLevel)))
		i--
		dAtA[i] = 0x3a
	}
	if m.TrxExecTime != 0 {
		i = encodeVarintTrx(dAtA, i, uint64(m.TrxExecTime))
		i--
		dAtA[i] = 0x30
	}
	if len(m.LockStatus) > 0 {
		i -= len(m.LockStatus)
		copy(dAtA[i:], m.LockStatus)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.LockStatus)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.TrxStatus) > 0 {
		i -= len(m.TrxStatus)
		copy(dAtA[i:], m.TrxStatus)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxStatus)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.SqlBlocked) > 0 {
		i -= len(m.SqlBlocked)
		copy(dAtA[i:], m.SqlBlocked)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.SqlBlocked)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ProcessId) > 0 {
		i -= len(m.ProcessId)
		copy(dAtA[i:], m.ProcessId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.ProcessId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TrxId) > 0 {
		i -= len(m.TrxId)
		copy(dAtA[i:], m.TrxId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeDeadlock) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeDeadlock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeDeadlock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TenantId) > 0 {
		i -= len(m.TenantId)
		copy(dAtA[i:], m.TenantId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TenantId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.InstanceId) > 0 {
		i -= len(m.InstanceId)
		copy(dAtA[i:], m.InstanceId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.InstanceId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeDeadlockInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeDeadlockInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeDeadlockInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DeadlockInfoList) > 0 {
		for iNdEx := len(m.DeadlockInfoList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.DeadlockInfoList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTrx(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DeadlockInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeadlockInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeadlockInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.NodeId) > 0 {
		i -= len(m.NodeId)
		copy(dAtA[i:], m.NodeId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.NodeId)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.DeadlockList) > 0 {
		for iNdEx := len(m.DeadlockList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.DeadlockList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTrx(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.DeadlockTime) > 0 {
		i -= len(m.DeadlockTime)
		copy(dAtA[i:], m.DeadlockTime)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.DeadlockTime)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.DeadlockCollectionTime) > 0 {
		i -= len(m.DeadlockCollectionTime)
		copy(dAtA[i:], m.DeadlockCollectionTime)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.DeadlockCollectionTime)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Deadlock) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Deadlock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Deadlock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TrxTreat) > 0 {
		i -= len(m.TrxTreat)
		copy(dAtA[i:], m.TrxTreat)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxTreat)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.Sql) > 0 {
		i -= len(m.Sql)
		copy(dAtA[i:], m.Sql)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.Sql)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.HoldLockMode) > 0 {
		i -= len(m.HoldLockMode)
		copy(dAtA[i:], m.HoldLockMode)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.HoldLockMode)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.HoldLockIndex) > 0 {
		i -= len(m.HoldLockIndex)
		copy(dAtA[i:], m.HoldLockIndex)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.HoldLockIndex)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.HoldLock) > 0 {
		i -= len(m.HoldLock)
		copy(dAtA[i:], m.HoldLock)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.HoldLock)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.WaitLockMode) > 0 {
		i -= len(m.WaitLockMode)
		copy(dAtA[i:], m.WaitLockMode)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.WaitLockMode)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.WaitIndex) > 0 {
		i -= len(m.WaitIndex)
		copy(dAtA[i:], m.WaitIndex)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.WaitIndex)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.WaitLock) > 0 {
		i -= len(m.WaitLock)
		copy(dAtA[i:], m.WaitLock)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.WaitLock)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.RelateTable) > 0 {
		i -= len(m.RelateTable)
		copy(dAtA[i:], m.RelateTable)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.RelateTable)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ReqType) > 0 {
		i -= len(m.ReqType)
		copy(dAtA[i:], m.ReqType)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.ReqType)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ProcessId) > 0 {
		i -= len(m.ProcessId)
		copy(dAtA[i:], m.ProcessId)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.ProcessId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TrxInfo) > 0 {
		i -= len(m.TrxInfo)
		copy(dAtA[i:], m.TrxInfo)
		i = encodeVarintTrx(dAtA, i, uint64(len(m.TrxInfo)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeDeadlockDetect) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeDeadlockDetect) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeDeadlockDetect) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *DescribeDeadlockDetectInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeDeadlockDetectInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeDeadlockDetectInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.OnOff {
		i--
		if m.OnOff {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintTrx(dAtA []byte, offset int, v uint64) int {
	offset -= sovTrx(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DescribeTrxAndLocks) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Offset != 0 {
		n += 1 + sovTrx(uint64(m.Offset))
	}
	if m.Limit != 0 {
		n += 1 + sovTrx(uint64(m.Limit))
	}
	if m.TrxExecTime != 0 {
		n += 1 + sovTrx(uint64(m.TrxExecTime))
	}
	l = len(m.TrxStatus)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.LockStatus)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.SortParam)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.Order)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	if m.QueryFilter != nil {
		l = m.QueryFilter.Size()
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *DescribeTrxAndLocksInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TrxAndLockList) > 0 {
		for _, e := range m.TrxAndLockList {
			l = e.Size()
			n += 1 + l + sovTrx(uint64(l))
		}
	}
	if m.Total != 0 {
		n += 1 + sovTrx(uint64(m.Total))
	}
	if m.Statistics != nil {
		l = m.Statistics.Size()
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *TrxAndLockOverview) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TotalHoldLock != 0 {
		n += 1 + sovTrx(uint64(m.TotalHoldLock))
	}
	if m.TotalWaitLock != 0 {
		n += 1 + sovTrx(uint64(m.TotalWaitLock))
	}
	if m.TimeGreaterThanCount != 0 {
		n += 1 + sovTrx(uint64(m.TimeGreaterThanCount))
	}
	return n
}

func (m *LockStatistics) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.InnoDBLockOverview != nil {
		l = m.InnoDBLockOverview.Size()
		n += 1 + l + sovTrx(uint64(l))
	}
	if m.MetaDBLockOverview != nil {
		l = m.MetaDBLockOverview.Size()
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *WaitLockDetail) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RequestedTrxId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.RequestedLockId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.SqlText)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	if m.LockInfo != nil {
		l = m.LockInfo.Size()
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *TrxAndLock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ProcessId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.TrxId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.TrxStatus)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	if m.LockStatus != 0 {
		n += 1 + sovTrx(uint64(m.LockStatus))
	}
	l = len(m.TrxIsoLevel)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.TrxStartTime)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.TrxWaitStartTime)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.SqlBlocked)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	if m.TrxTablesLocked != 0 {
		n += 1 + sovTrx(uint64(m.TrxTablesLocked))
	}
	if m.TrxRowsLocked != 0 {
		n += 1 + sovTrx(uint64(m.TrxRowsLocked))
	}
	if m.TrxRowsModified != 0 {
		n += 1 + sovTrx(uint64(m.TrxRowsModified))
	}
	if len(m.LockList) > 0 {
		for _, e := range m.LockList {
			l = e.Size()
			n += 1 + l + sovTrx(uint64(l))
		}
	}
	l = len(m.NodeId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.NodeType)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	if m.WaitLockDetail != nil {
		l = m.WaitLockDetail.Size()
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.BlockTrxId)
	if l > 0 {
		n += 2 + l + sovTrx(uint64(l))
	}
	if m.TrxExecTime != 0 {
		n += 2 + sovTrx(uint64(m.TrxExecTime))
	}
	l = len(m.TrxChangeTime)
	if l > 0 {
		n += 2 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *Lock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.LockProperty)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.LockId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.LockAssociateIndex)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.LockAssociateTable)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.LockType)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.LockModel)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.TrxId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *TrxQueryFilter) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TrxId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.ProcessId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.SqlBlocked)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.TrxStatus)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.LockStatus)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	if m.TrxExecTime != 0 {
		n += 1 + sovTrx(uint64(m.TrxExecTime))
	}
	l = len(m.TrxIsoLevel)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.BlockTrxId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *DescribeDeadlock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.InstanceId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.TenantId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *DescribeDeadlockInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.DeadlockInfoList) > 0 {
		for _, e := range m.DeadlockInfoList {
			l = e.Size()
			n += 1 + l + sovTrx(uint64(l))
		}
	}
	return n
}

func (m *DeadlockInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.DeadlockCollectionTime)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.DeadlockTime)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	if len(m.DeadlockList) > 0 {
		for _, e := range m.DeadlockList {
			l = e.Size()
			n += 1 + l + sovTrx(uint64(l))
		}
	}
	l = len(m.NodeId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *Deadlock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TrxInfo)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.ProcessId)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.ReqType)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.RelateTable)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.WaitLock)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.WaitIndex)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.WaitLockMode)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.HoldLock)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.HoldLockIndex)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.HoldLockMode)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.Sql)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	l = len(m.TrxTreat)
	if l > 0 {
		n += 1 + l + sovTrx(uint64(l))
	}
	return n
}

func (m *DescribeDeadlockDetect) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *DescribeDeadlockDetectInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.OnOff {
		n += 2
	}
	return n
}

func sovTrx(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozTrx(x uint64) (n int) {
	return sovTrx(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *DescribeTrxAndLocks) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeTrxAndLocks{`,
		`Offset:` + fmt.Sprintf("%v", this.Offset) + `,`,
		`Limit:` + fmt.Sprintf("%v", this.Limit) + `,`,
		`TrxExecTime:` + fmt.Sprintf("%v", this.TrxExecTime) + `,`,
		`TrxStatus:` + fmt.Sprintf("%v", this.TrxStatus) + `,`,
		`LockStatus:` + fmt.Sprintf("%v", this.LockStatus) + `,`,
		`SortParam:` + fmt.Sprintf("%v", this.SortParam) + `,`,
		`Order:` + fmt.Sprintf("%v", this.Order) + `,`,
		`QueryFilter:` + strings.Replace(this.QueryFilter.String(), "TrxQueryFilter", "TrxQueryFilter", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeTrxAndLocksInfo) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForTrxAndLockList := "[]*TrxAndLock{"
	for _, f := range this.TrxAndLockList {
		repeatedStringForTrxAndLockList += strings.Replace(f.String(), "TrxAndLock", "TrxAndLock", 1) + ","
	}
	repeatedStringForTrxAndLockList += "}"
	s := strings.Join([]string{`&DescribeTrxAndLocksInfo{`,
		`TrxAndLockList:` + repeatedStringForTrxAndLockList + `,`,
		`Total:` + fmt.Sprintf("%v", this.Total) + `,`,
		`Statistics:` + strings.Replace(this.Statistics.String(), "LockStatistics", "LockStatistics", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TrxAndLockOverview) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TrxAndLockOverview{`,
		`TotalHoldLock:` + fmt.Sprintf("%v", this.TotalHoldLock) + `,`,
		`TotalWaitLock:` + fmt.Sprintf("%v", this.TotalWaitLock) + `,`,
		`TimeGreaterThanCount:` + fmt.Sprintf("%v", this.TimeGreaterThanCount) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LockStatistics) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LockStatistics{`,
		`InnoDBLockOverview:` + strings.Replace(this.InnoDBLockOverview.String(), "TrxAndLockOverview", "TrxAndLockOverview", 1) + `,`,
		`MetaDBLockOverview:` + strings.Replace(this.MetaDBLockOverview.String(), "TrxAndLockOverview", "TrxAndLockOverview", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *WaitLockDetail) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&WaitLockDetail{`,
		`RequestedTrxId:` + fmt.Sprintf("%v", this.RequestedTrxId) + `,`,
		`RequestedLockId:` + fmt.Sprintf("%v", this.RequestedLockId) + `,`,
		`SqlText:` + fmt.Sprintf("%v", this.SqlText) + `,`,
		`LockInfo:` + strings.Replace(this.LockInfo.String(), "Lock", "Lock", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TrxAndLock) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForLockList := "[]*Lock{"
	for _, f := range this.LockList {
		repeatedStringForLockList += strings.Replace(f.String(), "Lock", "Lock", 1) + ","
	}
	repeatedStringForLockList += "}"
	s := strings.Join([]string{`&TrxAndLock{`,
		`ProcessId:` + fmt.Sprintf("%v", this.ProcessId) + `,`,
		`TrxId:` + fmt.Sprintf("%v", this.TrxId) + `,`,
		`TrxStatus:` + fmt.Sprintf("%v", this.TrxStatus) + `,`,
		`LockStatus:` + fmt.Sprintf("%v", this.LockStatus) + `,`,
		`TrxIsoLevel:` + fmt.Sprintf("%v", this.TrxIsoLevel) + `,`,
		`TrxStartTime:` + fmt.Sprintf("%v", this.TrxStartTime) + `,`,
		`TrxWaitStartTime:` + fmt.Sprintf("%v", this.TrxWaitStartTime) + `,`,
		`SqlBlocked:` + fmt.Sprintf("%v", this.SqlBlocked) + `,`,
		`TrxTablesLocked:` + fmt.Sprintf("%v", this.TrxTablesLocked) + `,`,
		`TrxRowsLocked:` + fmt.Sprintf("%v", this.TrxRowsLocked) + `,`,
		`TrxRowsModified:` + fmt.Sprintf("%v", this.TrxRowsModified) + `,`,
		`LockList:` + repeatedStringForLockList + `,`,
		`NodeId:` + fmt.Sprintf("%v", this.NodeId) + `,`,
		`NodeType:` + fmt.Sprintf("%v", this.NodeType) + `,`,
		`WaitLockDetail:` + strings.Replace(this.WaitLockDetail.String(), "WaitLockDetail", "WaitLockDetail", 1) + `,`,
		`BlockTrxId:` + fmt.Sprintf("%v", this.BlockTrxId) + `,`,
		`TrxExecTime:` + fmt.Sprintf("%v", this.TrxExecTime) + `,`,
		`TrxChangeTime:` + fmt.Sprintf("%v", this.TrxChangeTime) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Lock) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Lock{`,
		`LockProperty:` + fmt.Sprintf("%v", this.LockProperty) + `,`,
		`LockId:` + fmt.Sprintf("%v", this.LockId) + `,`,
		`LockAssociateIndex:` + fmt.Sprintf("%v", this.LockAssociateIndex) + `,`,
		`LockAssociateTable:` + fmt.Sprintf("%v", this.LockAssociateTable) + `,`,
		`LockType:` + fmt.Sprintf("%v", this.LockType) + `,`,
		`LockModel:` + fmt.Sprintf("%v", this.LockModel) + `,`,
		`TrxId:` + fmt.Sprintf("%v", this.TrxId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TrxQueryFilter) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TrxQueryFilter{`,
		`TrxId:` + fmt.Sprintf("%v", this.TrxId) + `,`,
		`ProcessId:` + fmt.Sprintf("%v", this.ProcessId) + `,`,
		`SqlBlocked:` + fmt.Sprintf("%v", this.SqlBlocked) + `,`,
		`TrxStatus:` + fmt.Sprintf("%v", this.TrxStatus) + `,`,
		`LockStatus:` + fmt.Sprintf("%v", this.LockStatus) + `,`,
		`TrxExecTime:` + fmt.Sprintf("%v", this.TrxExecTime) + `,`,
		`TrxIsoLevel:` + fmt.Sprintf("%v", this.TrxIsoLevel) + `,`,
		`BlockTrxId:` + fmt.Sprintf("%v", this.BlockTrxId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeDeadlock) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeDeadlock{`,
		`InstanceId:` + fmt.Sprintf("%v", this.InstanceId) + `,`,
		`TenantId:` + fmt.Sprintf("%v", this.TenantId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeDeadlockInfo) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForDeadlockInfoList := "[]*DeadlockInfo{"
	for _, f := range this.DeadlockInfoList {
		repeatedStringForDeadlockInfoList += strings.Replace(f.String(), "DeadlockInfo", "DeadlockInfo", 1) + ","
	}
	repeatedStringForDeadlockInfoList += "}"
	s := strings.Join([]string{`&DescribeDeadlockInfo{`,
		`DeadlockInfoList:` + repeatedStringForDeadlockInfoList + `,`,
		`}`,
	}, "")
	return s
}
func (this *DeadlockInfo) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForDeadlockList := "[]*Deadlock{"
	for _, f := range this.DeadlockList {
		repeatedStringForDeadlockList += strings.Replace(f.String(), "Deadlock", "Deadlock", 1) + ","
	}
	repeatedStringForDeadlockList += "}"
	s := strings.Join([]string{`&DeadlockInfo{`,
		`DeadlockCollectionTime:` + fmt.Sprintf("%v", this.DeadlockCollectionTime) + `,`,
		`DeadlockTime:` + fmt.Sprintf("%v", this.DeadlockTime) + `,`,
		`DeadlockList:` + repeatedStringForDeadlockList + `,`,
		`NodeId:` + fmt.Sprintf("%v", this.NodeId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Deadlock) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Deadlock{`,
		`TrxInfo:` + fmt.Sprintf("%v", this.TrxInfo) + `,`,
		`ProcessId:` + fmt.Sprintf("%v", this.ProcessId) + `,`,
		`ReqType:` + fmt.Sprintf("%v", this.ReqType) + `,`,
		`RelateTable:` + fmt.Sprintf("%v", this.RelateTable) + `,`,
		`WaitLock:` + fmt.Sprintf("%v", this.WaitLock) + `,`,
		`WaitIndex:` + fmt.Sprintf("%v", this.WaitIndex) + `,`,
		`WaitLockMode:` + fmt.Sprintf("%v", this.WaitLockMode) + `,`,
		`HoldLock:` + fmt.Sprintf("%v", this.HoldLock) + `,`,
		`HoldLockIndex:` + fmt.Sprintf("%v", this.HoldLockIndex) + `,`,
		`HoldLockMode:` + fmt.Sprintf("%v", this.HoldLockMode) + `,`,
		`Sql:` + fmt.Sprintf("%v", this.Sql) + `,`,
		`TrxTreat:` + fmt.Sprintf("%v", this.TrxTreat) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeDeadlockDetect) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeDeadlockDetect{`,
		`}`,
	}, "")
	return s
}
func (this *DescribeDeadlockDetectInfo) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeDeadlockDetectInfo{`,
		`OnOff:` + fmt.Sprintf("%v", this.OnOff) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringTrx(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *DescribeTrxAndLocks) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeTrxAndLocks: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeTrxAndLocks: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxExecTime", wireType)
			}
			m.TrxExecTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrxExecTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxStatus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxStatus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockStatus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockStatus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SortParam", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SortParam = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Order", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Order = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueryFilter", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.QueryFilter == nil {
				m.QueryFilter = &TrxQueryFilter{}
			}
			if err := m.QueryFilter.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeTrxAndLocksInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeTrxAndLocksInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeTrxAndLocksInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxAndLockList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxAndLockList = append(m.TrxAndLockList, &TrxAndLock{})
			if err := m.TrxAndLockList[len(m.TrxAndLockList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Statistics", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Statistics == nil {
				m.Statistics = &LockStatistics{}
			}
			if err := m.Statistics.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrxAndLockOverview) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TrxAndLockOverview: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TrxAndLockOverview: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalHoldLock", wireType)
			}
			m.TotalHoldLock = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalHoldLock |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalWaitLock", wireType)
			}
			m.TotalWaitLock = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalWaitLock |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeGreaterThanCount", wireType)
			}
			m.TimeGreaterThanCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeGreaterThanCount |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LockStatistics) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LockStatistics: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LockStatistics: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InnoDBLockOverview", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.InnoDBLockOverview == nil {
				m.InnoDBLockOverview = &TrxAndLockOverview{}
			}
			if err := m.InnoDBLockOverview.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MetaDBLockOverview", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MetaDBLockOverview == nil {
				m.MetaDBLockOverview = &TrxAndLockOverview{}
			}
			if err := m.MetaDBLockOverview.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WaitLockDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WaitLockDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WaitLockDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestedTrxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequestedTrxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestedLockId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequestedLockId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.LockInfo == nil {
				m.LockInfo = &Lock{}
			}
			if err := m.LockInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrxAndLock) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TrxAndLock: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TrxAndLock: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProcessId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProcessId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxStatus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxStatus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockStatus", wireType)
			}
			m.LockStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LockStatus |= LockStatus(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxIsoLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxIsoLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxStartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxStartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxWaitStartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxWaitStartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlBlocked", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlBlocked = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxTablesLocked", wireType)
			}
			m.TrxTablesLocked = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrxTablesLocked |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxRowsLocked", wireType)
			}
			m.TrxRowsLocked = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrxRowsLocked |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxRowsModified", wireType)
			}
			m.TrxRowsModified = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrxRowsModified |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockList = append(m.LockList, &Lock{})
			if err := m.LockList[len(m.LockList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NodeId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.NodeId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NodeType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.NodeType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitLockDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.WaitLockDetail == nil {
				m.WaitLockDetail = &WaitLockDetail{}
			}
			if err := m.WaitLockDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BlockTrxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BlockTrxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxExecTime", wireType)
			}
			m.TrxExecTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrxExecTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxChangeTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxChangeTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Lock) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Lock: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Lock: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockProperty", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockProperty = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockAssociateIndex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockAssociateIndex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockAssociateTable", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockAssociateTable = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockModel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockModel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrxQueryFilter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TrxQueryFilter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TrxQueryFilter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProcessId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProcessId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlBlocked", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlBlocked = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxStatus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxStatus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockStatus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LockStatus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxExecTime", wireType)
			}
			m.TrxExecTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrxExecTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxIsoLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxIsoLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BlockTrxId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BlockTrxId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeDeadlock) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeDeadlock: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeDeadlock: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeDeadlockInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeDeadlockInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeDeadlockInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeadlockInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeadlockInfoList = append(m.DeadlockInfoList, &DeadlockInfo{})
			if err := m.DeadlockInfoList[len(m.DeadlockInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeadlockInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeadlockInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeadlockInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeadlockCollectionTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeadlockCollectionTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeadlockTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeadlockTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeadlockList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeadlockList = append(m.DeadlockList, &Deadlock{})
			if err := m.DeadlockList[len(m.DeadlockList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NodeId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.NodeId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Deadlock) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Deadlock: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Deadlock: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProcessId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProcessId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReqType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReqType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RelateTable", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RelateTable = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitLock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WaitLock = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitIndex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WaitIndex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitLockMode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WaitLockMode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HoldLock", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HoldLock = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HoldLockIndex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HoldLockIndex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HoldLockMode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HoldLockMode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sql", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sql = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrxTreat", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTrx
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTrx
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrxTreat = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeDeadlockDetect) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeDeadlockDetect: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeDeadlockDetect: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeDeadlockDetectInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeDeadlockDetectInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeDeadlockDetectInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OnOff", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnOff = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipTrx(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTrx
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipTrx(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTrx
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTrx
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthTrx
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupTrx
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthTrx
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthTrx        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTrx          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupTrx = fmt.Errorf("proto: unexpected end of group")
)

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: connections.proto

package shared

import (
	bytes "bytes"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_sortkeys "github.com/gogo/protobuf/sortkeys"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type DataSourceType int32

const (
	NoneType      DataSourceType = 0
	MySQL         DataSourceType = 1
	Postgres      DataSourceType = 2
	Mongo         DataSourceType = 3
	Redis         DataSourceType = 4
	VeDBMySQL     DataSourceType = 5
	MetaRDS       DataSourceType = 6
	MSSQL         DataSourceType = 7
	ByteRDS       DataSourceType = 8
	MySQLSharding DataSourceType = 9
	MetaMySQL     DataSourceType = 10
	ByteDoc       DataSourceType = 11
	DAIR          DataSourceType = 12
)

var DataSourceType_name = map[int32]string{
	0:  "NoneType",
	1:  "MySQL",
	2:  "Postgres",
	3:  "Mongo",
	4:  "Redis",
	5:  "VeDBMySQL",
	6:  "MetaRDS",
	7:  "MSSQL",
	8:  "ByteRDS",
	9:  "MySQLSharding",
	10: "MetaMySQL",
	11: "ByteDoc",
	12: "DAIR",
}

var DataSourceType_value = map[string]int32{
	"NoneType":      0,
	"MySQL":         1,
	"Postgres":      2,
	"Mongo":         3,
	"Redis":         4,
	"VeDBMySQL":     5,
	"MetaRDS":       6,
	"MSSQL":         7,
	"ByteRDS":       8,
	"MySQLSharding": 9,
	"MetaMySQL":     10,
	"ByteDoc":       11,
	"DAIR":          12,
}

func (DataSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{0}
}

type DBEngine int32

const (
	Unknown        DBEngine = 0
	MySQLEngine    DBEngine = 1
	PostgresEngine DBEngine = 2
)

var DBEngine_name = map[int32]string{
	0: "Unknown",
	1: "MySQLEngine",
	2: "PostgresEngine",
}

var DBEngine_value = map[string]int32{
	"Unknown":        0,
	"MySQLEngine":    1,
	"PostgresEngine": 2,
}

func (DBEngine) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{1}
}

type LinkType int32

const (
	Volc       LinkType = 0
	Ecs        LinkType = 1
	Public     LinkType = 2
	PublicZone LinkType = 3
	ByteCloud  LinkType = 4
)

var LinkType_name = map[int32]string{
	0: "Volc",
	1: "Ecs",
	2: "Public",
	3: "PublicZone",
	4: "ByteCloud",
}

var LinkType_value = map[string]int32{
	"Volc":       0,
	"Ecs":        1,
	"Public":     2,
	"PublicZone": 3,
	"ByteCloud":  4,
}

func (LinkType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{2}
}

type EndpointType int32

const (
	Primary   EndpointType = 0
	Secondary EndpointType = 1
	ReadOnly  EndpointType = 2
)

var EndpointType_name = map[int32]string{
	0: "Primary",
	1: "Secondary",
	2: "ReadOnly",
}

var EndpointType_value = map[string]int32{
	"Primary":   0,
	"Secondary": 1,
	"ReadOnly":  2,
}

func (EndpointType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{3}
}

type ConnectionError int32

const (
	ErrOK                      ConnectionError = 0
	ErrDataSource              ConnectionError = 1
	ErrConnectFail             ConnectionError = 2
	ErrConnectionBroken        ConnectionError = 3
	ErrConnectionTimeout       ConnectionError = 4
	ErrConnectionNotExist      ConnectionError = 5
	ErrConnectionAlreadyClosed ConnectionError = 6
	ErrConnectionClosedByActor ConnectionError = 7
)

var ConnectionError_name = map[int32]string{
	0: "ErrOK",
	1: "ErrDataSource",
	2: "ErrConnectFail",
	3: "ErrConnectionBroken",
	4: "ErrConnectionTimeout",
	5: "ErrConnectionNotExist",
	6: "ErrConnectionAlreadyClosed",
	7: "ErrConnectionClosedByActor",
}

var ConnectionError_value = map[string]int32{
	"ErrOK":                      0,
	"ErrDataSource":              1,
	"ErrConnectFail":             2,
	"ErrConnectionBroken":        3,
	"ErrConnectionTimeout":       4,
	"ErrConnectionNotExist":      5,
	"ErrConnectionAlreadyClosed": 6,
	"ErrConnectionClosedByActor": 7,
}

func (ConnectionError) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{4}
}

type CommandError int32

const (
	ErrCommandOK      CommandError = 0
	ErrBadCommand     CommandError = 1
	ErrExeCommand     CommandError = 2
	ErrConnectionLost CommandError = 3
	ErrCursorBusy     CommandError = 4
	ErrCursorReady    CommandError = 5
	ErrCursorMismatch CommandError = 6
	ErrNoCursor       CommandError = 7
)

var CommandError_name = map[int32]string{
	0: "ErrCommandOK",
	1: "ErrBadCommand",
	2: "ErrExeCommand",
	3: "ErrConnectionLost",
	4: "ErrCursorBusy",
	5: "ErrCursorReady",
	6: "ErrCursorMismatch",
	7: "ErrNoCursor",
}

var CommandError_value = map[string]int32{
	"ErrCommandOK":      0,
	"ErrBadCommand":     1,
	"ErrExeCommand":     2,
	"ErrConnectionLost": 3,
	"ErrCursorBusy":     4,
	"ErrCursorReady":    5,
	"ErrCursorMismatch": 6,
	"ErrNoCursor":       7,
}

func (CommandError) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{5}
}

type CommandResultType int32

const (
	Table    CommandResultType = 0
	List     CommandResultType = 1
	String   CommandResultType = 2
	Object   CommandResultType = 3
	TaskId   CommandResultType = 4
	TicketID CommandResultType = 5
)

var CommandResultType_name = map[int32]string{
	0: "Table",
	1: "List",
	2: "String",
	3: "Object",
	4: "TaskId",
	5: "TicketID",
}

var CommandResultType_value = map[string]int32{
	"Table":    0,
	"List":     1,
	"String":   2,
	"Object":   3,
	"TaskId":   4,
	"TicketID": 5,
}

func (CommandResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{6}
}

type LimitType int32

const (
	RowLimit  LimitType = 0
	ByteLimit LimitType = 1
)

var LimitType_name = map[int32]string{
	0: "RowLimit",
	1: "ByteLimit",
}

var LimitType_value = map[string]int32{
	"RowLimit":  0,
	"ByteLimit": 1,
}

func (LimitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{7}
}

type ActorRef struct {
	Kind string `protobuf:"bytes,1,opt,name=kind,proto3" json:"kind,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (m *ActorRef) Reset()      { *m = ActorRef{} }
func (*ActorRef) ProtoMessage() {}
func (*ActorRef) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{0}
}
func (m *ActorRef) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ActorRef) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ActorRef.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ActorRef) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActorRef.Merge(m, src)
}
func (m *ActorRef) XXX_Size() int {
	return m.Size()
}
func (m *ActorRef) XXX_DiscardUnknown() {
	xxx_messageInfo_ActorRef.DiscardUnknown(m)
}

var xxx_messageInfo_ActorRef proto.InternalMessageInfo

func (m *ActorRef) GetKind() string {
	if m != nil {
		return m.Kind
	}
	return ""
}

func (m *ActorRef) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type DataSource struct {
	Type             DataSourceType    `protobuf:"varint,1,opt,name=type,proto3,enum=shared.DataSourceType" json:"type,omitempty"`
	LinkType         LinkType          `protobuf:"varint,2,opt,name=link_type,json=linkType,proto3,enum=shared.LinkType" json:"link_type,omitempty"`
	Address          string            `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	User             string            `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`
	Password         string            `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	ExtraDsn         map[string]string `protobuf:"bytes,6,rep,name=extra_dsn,json=extraDsn,proto3" json:"extra_dsn,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ConnectTimeoutMs int64             `protobuf:"varint,7,opt,name=connect_timeout_ms,json=connectTimeoutMs,proto3" json:"connect_timeout_ms,omitempty"`
	ReadTimeoutMs    int64             `protobuf:"varint,8,opt,name=read_timeout_ms,json=readTimeoutMs,proto3" json:"read_timeout_ms,omitempty"`
	WriteTimeoutMs   int64             `protobuf:"varint,9,opt,name=write_timeout_ms,json=writeTimeoutMs,proto3" json:"write_timeout_ms,omitempty"`
	Db               string            `protobuf:"bytes,10,opt,name=db,proto3" json:"db,omitempty"`
	IdleTimeoutMs    int64             `protobuf:"varint,11,opt,name=idle_timeout_ms,json=idleTimeoutMs,proto3" json:"idle_timeout_ms,omitempty"`
	InstanceId       string            `protobuf:"bytes,12,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	CadidateAddress  string            `protobuf:"bytes,13,opt,name=cadidate_address,json=cadidateAddress,proto3" json:"cadidate_address,omitempty"`
	VpcID            string            `protobuf:"bytes,14,opt,name=VpcID,proto3" json:"VpcID,omitempty"`
	MaxOpenConns     int64             `protobuf:"varint,15,opt,name=max_open_conns,json=maxOpenConns,proto3" json:"max_open_conns,omitempty"`
	MaxIdleConns     int64             `protobuf:"varint,16,opt,name=max_idle_conns,json=maxIdleConns,proto3" json:"max_idle_conns,omitempty"`
	MongoNodeId      string            `protobuf:"bytes,17,opt,name=MongoNodeId,proto3" json:"MongoNodeId,omitempty"`
	NodeId           string            `protobuf:"bytes,18,opt,name=NodeId,proto3" json:"NodeId,omitempty"`
	AuthDb           string            `protobuf:"bytes,19,opt,name=authDb,proto3" json:"authDb,omitempty"`
	EndpointRole     EndpointType      `protobuf:"varint,20,opt,name=endpointRole,proto3,enum=shared.EndpointType" json:"endpointRole,omitempty"`
	Region           string            `protobuf:"bytes,21,opt,name=Region,proto3" json:"Region,omitempty"`
	Psm              string            `protobuf:"bytes,23,opt,name=psm,proto3" json:"psm,omitempty"`
	Gdid             string            `protobuf:"bytes,24,opt,name=gdid,proto3" json:"gdid,omitempty"`
	Tunnel           string            `protobuf:"bytes,25,opt,name=tunnel,proto3" json:"tunnel,omitempty"`
	BranchId         string            `protobuf:"bytes,26,opt,name=branchId,proto3" json:"branchId,omitempty"`
	DbEngine         DBEngine          `protobuf:"varint,27,opt,name=db_engine,json=dbEngine,proto3,enum=shared.DBEngine" json:"db_engine,omitempty"`
}

func (m *DataSource) Reset()      { *m = DataSource{} }
func (*DataSource) ProtoMessage() {}
func (*DataSource) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{1}
}
func (m *DataSource) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DataSource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DataSource.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DataSource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataSource.Merge(m, src)
}
func (m *DataSource) XXX_Size() int {
	return m.Size()
}
func (m *DataSource) XXX_DiscardUnknown() {
	xxx_messageInfo_DataSource.DiscardUnknown(m)
}

var xxx_messageInfo_DataSource proto.InternalMessageInfo

func (m *DataSource) GetType() DataSourceType {
	if m != nil {
		return m.Type
	}
	return NoneType
}

func (m *DataSource) GetLinkType() LinkType {
	if m != nil {
		return m.LinkType
	}
	return Volc
}

func (m *DataSource) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *DataSource) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

func (m *DataSource) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *DataSource) GetExtraDsn() map[string]string {
	if m != nil {
		return m.ExtraDsn
	}
	return nil
}

func (m *DataSource) GetConnectTimeoutMs() int64 {
	if m != nil {
		return m.ConnectTimeoutMs
	}
	return 0
}

func (m *DataSource) GetReadTimeoutMs() int64 {
	if m != nil {
		return m.ReadTimeoutMs
	}
	return 0
}

func (m *DataSource) GetWriteTimeoutMs() int64 {
	if m != nil {
		return m.WriteTimeoutMs
	}
	return 0
}

func (m *DataSource) GetDb() string {
	if m != nil {
		return m.Db
	}
	return ""
}

func (m *DataSource) GetIdleTimeoutMs() int64 {
	if m != nil {
		return m.IdleTimeoutMs
	}
	return 0
}

func (m *DataSource) GetInstanceId() string {
	if m != nil {
		return m.InstanceId
	}
	return ""
}

func (m *DataSource) GetCadidateAddress() string {
	if m != nil {
		return m.CadidateAddress
	}
	return ""
}

func (m *DataSource) GetVpcID() string {
	if m != nil {
		return m.VpcID
	}
	return ""
}

func (m *DataSource) GetMaxOpenConns() int64 {
	if m != nil {
		return m.MaxOpenConns
	}
	return 0
}

func (m *DataSource) GetMaxIdleConns() int64 {
	if m != nil {
		return m.MaxIdleConns
	}
	return 0
}

func (m *DataSource) GetMongoNodeId() string {
	if m != nil {
		return m.MongoNodeId
	}
	return ""
}

func (m *DataSource) GetNodeId() string {
	if m != nil {
		return m.NodeId
	}
	return ""
}

func (m *DataSource) GetAuthDb() string {
	if m != nil {
		return m.AuthDb
	}
	return ""
}

func (m *DataSource) GetEndpointRole() EndpointType {
	if m != nil {
		return m.EndpointRole
	}
	return Primary
}

func (m *DataSource) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *DataSource) GetPsm() string {
	if m != nil {
		return m.Psm
	}
	return ""
}

func (m *DataSource) GetGdid() string {
	if m != nil {
		return m.Gdid
	}
	return ""
}

func (m *DataSource) GetTunnel() string {
	if m != nil {
		return m.Tunnel
	}
	return ""
}

func (m *DataSource) GetBranchId() string {
	if m != nil {
		return m.BranchId
	}
	return ""
}

func (m *DataSource) GetDbEngine() DBEngine {
	if m != nil {
		return m.DbEngine
	}
	return Unknown
}

// open connection
type OpenConnection struct {
	BizContext         string      `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	Source             *DataSource `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	Supervisor         *ActorRef   `protobuf:"bytes,3,opt,name=supervisor,proto3" json:"supervisor,omitempty"`
	IdleTimeoutSeconds int64       `protobuf:"varint,4,opt,name=idle_timeout_seconds,json=idleTimeoutSeconds,proto3" json:"idle_timeout_seconds,omitempty"`
}

func (m *OpenConnection) Reset()      { *m = OpenConnection{} }
func (*OpenConnection) ProtoMessage() {}
func (*OpenConnection) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{2}
}
func (m *OpenConnection) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OpenConnection) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OpenConnection.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OpenConnection) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenConnection.Merge(m, src)
}
func (m *OpenConnection) XXX_Size() int {
	return m.Size()
}
func (m *OpenConnection) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenConnection.DiscardUnknown(m)
}

var xxx_messageInfo_OpenConnection proto.InternalMessageInfo

func (m *OpenConnection) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *OpenConnection) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *OpenConnection) GetSupervisor() *ActorRef {
	if m != nil {
		return m.Supervisor
	}
	return nil
}

func (m *OpenConnection) GetIdleTimeoutSeconds() int64 {
	if m != nil {
		return m.IdleTimeoutSeconds
	}
	return 0
}

type OpenConnectionSuccessful struct {
	Code              ConnectionError `protobuf:"varint,1,opt,name=code,proto3,enum=shared.ConnectionError" json:"code,omitempty"`
	CurrentDb         string          `protobuf:"bytes,2,opt,name=current_db,json=currentDb,proto3" json:"current_db,omitempty"`
	OuterConnectionId string          `protobuf:"bytes,3,opt,name=outer_connection_id,json=outerConnectionId,proto3" json:"outer_connection_id,omitempty"`
}

func (m *OpenConnectionSuccessful) Reset()      { *m = OpenConnectionSuccessful{} }
func (*OpenConnectionSuccessful) ProtoMessage() {}
func (*OpenConnectionSuccessful) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{3}
}
func (m *OpenConnectionSuccessful) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OpenConnectionSuccessful) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OpenConnectionSuccessful.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OpenConnectionSuccessful) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenConnectionSuccessful.Merge(m, src)
}
func (m *OpenConnectionSuccessful) XXX_Size() int {
	return m.Size()
}
func (m *OpenConnectionSuccessful) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenConnectionSuccessful.DiscardUnknown(m)
}

var xxx_messageInfo_OpenConnectionSuccessful proto.InternalMessageInfo

func (m *OpenConnectionSuccessful) GetCode() ConnectionError {
	if m != nil {
		return m.Code
	}
	return ErrOK
}

func (m *OpenConnectionSuccessful) GetCurrentDb() string {
	if m != nil {
		return m.CurrentDb
	}
	return ""
}

func (m *OpenConnectionSuccessful) GetOuterConnectionId() string {
	if m != nil {
		return m.OuterConnectionId
	}
	return ""
}

type OpenConnectionFailed struct {
	Code         ConnectionError `protobuf:"varint,1,opt,name=code,proto3,enum=shared.ConnectionError" json:"code,omitempty"`
	ErrorMessage string          `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (m *OpenConnectionFailed) Reset()      { *m = OpenConnectionFailed{} }
func (*OpenConnectionFailed) ProtoMessage() {}
func (*OpenConnectionFailed) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{4}
}
func (m *OpenConnectionFailed) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OpenConnectionFailed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OpenConnectionFailed.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OpenConnectionFailed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenConnectionFailed.Merge(m, src)
}
func (m *OpenConnectionFailed) XXX_Size() int {
	return m.Size()
}
func (m *OpenConnectionFailed) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenConnectionFailed.DiscardUnknown(m)
}

var xxx_messageInfo_OpenConnectionFailed proto.InternalMessageInfo

func (m *OpenConnectionFailed) GetCode() ConnectionError {
	if m != nil {
		return m.Code
	}
	return ErrOK
}

func (m *OpenConnectionFailed) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

type ConnectionEstablished struct {
	Source             *DataSource `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	ConnectionId       string      `protobuf:"bytes,2,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
	OutterConnectionId string      `protobuf:"bytes,3,opt,name=outter_connection_id,json=outterConnectionId,proto3" json:"outter_connection_id,omitempty"`
}

func (m *ConnectionEstablished) Reset()      { *m = ConnectionEstablished{} }
func (*ConnectionEstablished) ProtoMessage() {}
func (*ConnectionEstablished) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{5}
}
func (m *ConnectionEstablished) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConnectionEstablished) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ConnectionEstablished.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ConnectionEstablished) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectionEstablished.Merge(m, src)
}
func (m *ConnectionEstablished) XXX_Size() int {
	return m.Size()
}
func (m *ConnectionEstablished) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectionEstablished.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectionEstablished proto.InternalMessageInfo

func (m *ConnectionEstablished) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *ConnectionEstablished) GetConnectionId() string {
	if m != nil {
		return m.ConnectionId
	}
	return ""
}

func (m *ConnectionEstablished) GetOutterConnectionId() string {
	if m != nil {
		return m.OutterConnectionId
	}
	return ""
}

// close connection
type CloseConnection struct {
}

func (m *CloseConnection) Reset()      { *m = CloseConnection{} }
func (*CloseConnection) ProtoMessage() {}
func (*CloseConnection) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{6}
}
func (m *CloseConnection) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CloseConnection) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CloseConnection.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CloseConnection) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseConnection.Merge(m, src)
}
func (m *CloseConnection) XXX_Size() int {
	return m.Size()
}
func (m *CloseConnection) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseConnection.DiscardUnknown(m)
}

var xxx_messageInfo_CloseConnection proto.InternalMessageInfo

type ConnectionClosed struct {
	Code         ConnectionError `protobuf:"varint,1,opt,name=code,proto3,enum=shared.ConnectionError" json:"code,omitempty"`
	ErrorMessage string          `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ConnectionId string          `protobuf:"bytes,3,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
}

func (m *ConnectionClosed) Reset()      { *m = ConnectionClosed{} }
func (*ConnectionClosed) ProtoMessage() {}
func (*ConnectionClosed) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{7}
}
func (m *ConnectionClosed) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConnectionClosed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ConnectionClosed.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ConnectionClosed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectionClosed.Merge(m, src)
}
func (m *ConnectionClosed) XXX_Size() int {
	return m.Size()
}
func (m *ConnectionClosed) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectionClosed.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectionClosed proto.InternalMessageInfo

func (m *ConnectionClosed) GetCode() ConnectionError {
	if m != nil {
		return m.Code
	}
	return ErrOK
}

func (m *ConnectionClosed) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

func (m *ConnectionClosed) GetConnectionId() string {
	if m != nil {
		return m.ConnectionId
	}
	return ""
}

type ForceRollback struct {
	BizContext string `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *ForceRollback) Reset()      { *m = ForceRollback{} }
func (*ForceRollback) ProtoMessage() {}
func (*ForceRollback) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{8}
}
func (m *ForceRollback) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ForceRollback) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ForceRollback.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ForceRollback) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForceRollback.Merge(m, src)
}
func (m *ForceRollback) XXX_Size() int {
	return m.Size()
}
func (m *ForceRollback) XXX_DiscardUnknown() {
	xxx_messageInfo_ForceRollback.DiscardUnknown(m)
}

var xxx_messageInfo_ForceRollback proto.InternalMessageInfo

func (m *ForceRollback) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

type ForceRollbackResp struct {
}

func (m *ForceRollbackResp) Reset()      { *m = ForceRollbackResp{} }
func (*ForceRollbackResp) ProtoMessage() {}
func (*ForceRollbackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{9}
}
func (m *ForceRollbackResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ForceRollbackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ForceRollbackResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ForceRollbackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForceRollbackResp.Merge(m, src)
}
func (m *ForceRollbackResp) XXX_Size() int {
	return m.Size()
}
func (m *ForceRollbackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ForceRollbackResp.DiscardUnknown(m)
}

var xxx_messageInfo_ForceRollbackResp proto.InternalMessageInfo

// execute command
type Command struct {
	BizContext         string `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	CommandId          string `protobuf:"bytes,2,opt,name=command_id,json=commandId,proto3" json:"command_id,omitempty"`
	Command            string `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`
	MaxResultCount     int64  `protobuf:"varint,4,opt,name=max_result_count,json=maxResultCount,proto3" json:"max_result_count,omitempty"`
	MaxResultBytes     int64  `protobuf:"varint,5,opt,name=max_result_bytes,json=maxResultBytes,proto3" json:"max_result_bytes,omitempty"`
	MaxResultCellBytes int64  `protobuf:"varint,6,opt,name=max_result_cell_bytes,json=maxResultCellBytes,proto3" json:"max_result_cell_bytes,omitempty"`
	TimeoutMs          int64  `protobuf:"varint,7,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
	SaveMetadb         bool   `protobuf:"varint,8,opt,name=save_metadb,json=saveMetadb,proto3" json:"save_metadb,omitempty"`
}

func (m *Command) Reset()      { *m = Command{} }
func (*Command) ProtoMessage() {}
func (*Command) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{10}
}
func (m *Command) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Command) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Command.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Command) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Command.Merge(m, src)
}
func (m *Command) XXX_Size() int {
	return m.Size()
}
func (m *Command) XXX_DiscardUnknown() {
	xxx_messageInfo_Command.DiscardUnknown(m)
}

var xxx_messageInfo_Command proto.InternalMessageInfo

func (m *Command) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *Command) GetCommandId() string {
	if m != nil {
		return m.CommandId
	}
	return ""
}

func (m *Command) GetCommand() string {
	if m != nil {
		return m.Command
	}
	return ""
}

func (m *Command) GetMaxResultCount() int64 {
	if m != nil {
		return m.MaxResultCount
	}
	return 0
}

func (m *Command) GetMaxResultBytes() int64 {
	if m != nil {
		return m.MaxResultBytes
	}
	return 0
}

func (m *Command) GetMaxResultCellBytes() int64 {
	if m != nil {
		return m.MaxResultCellBytes
	}
	return 0
}

func (m *Command) GetTimeoutMs() int64 {
	if m != nil {
		return m.TimeoutMs
	}
	return 0
}

func (m *Command) GetSaveMetadb() bool {
	if m != nil {
		return m.SaveMetadb
	}
	return false
}

type CommandResult struct {
	BizContext   string                    `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	CommandId    string                    `protobuf:"bytes,2,opt,name=command_id,json=commandId,proto3" json:"command_id,omitempty"`
	Code         CommandError              `protobuf:"varint,3,opt,name=code,proto3,enum=shared.CommandError" json:"code,omitempty"`
	ErrorMessage string                    `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	Type         CommandResultType         `protobuf:"varint,5,opt,name=type,proto3,enum=shared.CommandResultType" json:"type,omitempty"`
	Payload      []*CommandResultChunk     `protobuf:"bytes,6,rep,name=payload,proto3" json:"payload,omitempty"`
	ConnectionId string                    `protobuf:"bytes,7,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
	LimitInfo    []*CommandResultLimitInfo `protobuf:"bytes,8,rep,name=limit_info,json=limitInfo,proto3" json:"limit_info,omitempty"`
}

func (m *CommandResult) Reset()      { *m = CommandResult{} }
func (*CommandResult) ProtoMessage() {}
func (*CommandResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{11}
}
func (m *CommandResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandResult.Merge(m, src)
}
func (m *CommandResult) XXX_Size() int {
	return m.Size()
}
func (m *CommandResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandResult.DiscardUnknown(m)
}

var xxx_messageInfo_CommandResult proto.InternalMessageInfo

func (m *CommandResult) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *CommandResult) GetCommandId() string {
	if m != nil {
		return m.CommandId
	}
	return ""
}

func (m *CommandResult) GetCode() CommandError {
	if m != nil {
		return m.Code
	}
	return ErrCommandOK
}

func (m *CommandResult) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

func (m *CommandResult) GetType() CommandResultType {
	if m != nil {
		return m.Type
	}
	return Table
}

func (m *CommandResult) GetPayload() []*CommandResultChunk {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (m *CommandResult) GetConnectionId() string {
	if m != nil {
		return m.ConnectionId
	}
	return ""
}

func (m *CommandResult) GetLimitInfo() []*CommandResultLimitInfo {
	if m != nil {
		return m.LimitInfo
	}
	return nil
}

type CommandResultChunk struct {
	HasMore bool                      `protobuf:"varint,1,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	Offset  int64                     `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Header  []string                  `protobuf:"bytes,3,rep,name=header,proto3" json:"header,omitempty"`
	Rows    []*CommandResultChunk_Row `protobuf:"bytes,4,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *CommandResultChunk) Reset()      { *m = CommandResultChunk{} }
func (*CommandResultChunk) ProtoMessage() {}
func (*CommandResultChunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{12}
}
func (m *CommandResultChunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandResultChunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandResultChunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandResultChunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandResultChunk.Merge(m, src)
}
func (m *CommandResultChunk) XXX_Size() int {
	return m.Size()
}
func (m *CommandResultChunk) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandResultChunk.DiscardUnknown(m)
}

var xxx_messageInfo_CommandResultChunk proto.InternalMessageInfo

func (m *CommandResultChunk) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *CommandResultChunk) GetOffset() int64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *CommandResultChunk) GetHeader() []string {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *CommandResultChunk) GetRows() []*CommandResultChunk_Row {
	if m != nil {
		return m.Rows
	}
	return nil
}

type CommandResultChunk_Row struct {
	Cells []string `protobuf:"bytes,1,rep,name=cells,proto3" json:"cells,omitempty"`
}

func (m *CommandResultChunk_Row) Reset()      { *m = CommandResultChunk_Row{} }
func (*CommandResultChunk_Row) ProtoMessage() {}
func (*CommandResultChunk_Row) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{12, 0}
}
func (m *CommandResultChunk_Row) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandResultChunk_Row) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandResultChunk_Row.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandResultChunk_Row) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandResultChunk_Row.Merge(m, src)
}
func (m *CommandResultChunk_Row) XXX_Size() int {
	return m.Size()
}
func (m *CommandResultChunk_Row) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandResultChunk_Row.DiscardUnknown(m)
}

var xxx_messageInfo_CommandResultChunk_Row proto.InternalMessageInfo

func (m *CommandResultChunk_Row) GetCells() []string {
	if m != nil {
		return m.Cells
	}
	return nil
}

type CommandResultLimitInfo struct {
	Type       LimitType `protobuf:"varint,1,opt,name=type,proto3,enum=shared.LimitType" json:"type,omitempty"`
	LimitValue int64     `protobuf:"varint,2,opt,name=LimitValue,proto3" json:"LimitValue,omitempty"`
}

func (m *CommandResultLimitInfo) Reset()      { *m = CommandResultLimitInfo{} }
func (*CommandResultLimitInfo) ProtoMessage() {}
func (*CommandResultLimitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{13}
}
func (m *CommandResultLimitInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandResultLimitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandResultLimitInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandResultLimitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandResultLimitInfo.Merge(m, src)
}
func (m *CommandResultLimitInfo) XXX_Size() int {
	return m.Size()
}
func (m *CommandResultLimitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandResultLimitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommandResultLimitInfo proto.InternalMessageInfo

func (m *CommandResultLimitInfo) GetType() LimitType {
	if m != nil {
		return m.Type
	}
	return RowLimit
}

func (m *CommandResultLimitInfo) GetLimitValue() int64 {
	if m != nil {
		return m.LimitValue
	}
	return 0
}

type ConnectionConfigUpdated struct {
	SentryDsn string `protobuf:"bytes,1,opt,name=sentry_dsn,json=sentryDsn,proto3" json:"sentry_dsn,omitempty"`
}

func (m *ConnectionConfigUpdated) Reset()      { *m = ConnectionConfigUpdated{} }
func (*ConnectionConfigUpdated) ProtoMessage() {}
func (*ConnectionConfigUpdated) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{14}
}
func (m *ConnectionConfigUpdated) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConnectionConfigUpdated) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ConnectionConfigUpdated.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ConnectionConfigUpdated) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectionConfigUpdated.Merge(m, src)
}
func (m *ConnectionConfigUpdated) XXX_Size() int {
	return m.Size()
}
func (m *ConnectionConfigUpdated) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectionConfigUpdated.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectionConfigUpdated proto.InternalMessageInfo

func (m *ConnectionConfigUpdated) GetSentryDsn() string {
	if m != nil {
		return m.SentryDsn
	}
	return ""
}

type Ping struct {
	BizContext string `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *Ping) Reset()      { *m = Ping{} }
func (*Ping) ProtoMessage() {}
func (*Ping) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{15}
}
func (m *Ping) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Ping) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Ping.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Ping) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ping.Merge(m, src)
}
func (m *Ping) XXX_Size() int {
	return m.Size()
}
func (m *Ping) XXX_DiscardUnknown() {
	xxx_messageInfo_Ping.DiscardUnknown(m)
}

var xxx_messageInfo_Ping proto.InternalMessageInfo

func (m *Ping) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

type ConnectionInfo struct {
	ConnectionId      string `protobuf:"bytes,1,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
	CurrentDb         string `protobuf:"bytes,2,opt,name=current_db,json=currentDb,proto3" json:"current_db,omitempty"`
	OuterConnectionId string `protobuf:"bytes,3,opt,name=outer_connection_id,json=outerConnectionId,proto3" json:"outer_connection_id,omitempty"`
}

func (m *ConnectionInfo) Reset()      { *m = ConnectionInfo{} }
func (*ConnectionInfo) ProtoMessage() {}
func (*ConnectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{16}
}
func (m *ConnectionInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConnectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ConnectionInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ConnectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectionInfo.Merge(m, src)
}
func (m *ConnectionInfo) XXX_Size() int {
	return m.Size()
}
func (m *ConnectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectionInfo proto.InternalMessageInfo

func (m *ConnectionInfo) GetConnectionId() string {
	if m != nil {
		return m.ConnectionId
	}
	return ""
}

func (m *ConnectionInfo) GetCurrentDb() string {
	if m != nil {
		return m.CurrentDb
	}
	return ""
}

func (m *ConnectionInfo) GetOuterConnectionId() string {
	if m != nil {
		return m.OuterConnectionId
	}
	return ""
}

type GetLocation struct {
}

func (m *GetLocation) Reset()      { *m = GetLocation{} }
func (*GetLocation) ProtoMessage() {}
func (*GetLocation) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{17}
}
func (m *GetLocation) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetLocation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetLocation.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetLocation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLocation.Merge(m, src)
}
func (m *GetLocation) XXX_Size() int {
	return m.Size()
}
func (m *GetLocation) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLocation.DiscardUnknown(m)
}

var xxx_messageInfo_GetLocation proto.InternalMessageInfo

type Location struct {
	Kind   string `protobuf:"bytes,1,opt,name=kind,proto3" json:"kind,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	HostIp string `protobuf:"bytes,3,opt,name=host_ip,json=hostIp,proto3" json:"host_ip,omitempty"`
	PodIp  string `protobuf:"bytes,4,opt,name=pod_ip,json=podIp,proto3" json:"pod_ip,omitempty"`
}

func (m *Location) Reset()      { *m = Location{} }
func (*Location) ProtoMessage() {}
func (*Location) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{18}
}
func (m *Location) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Location) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Location.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Location) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Location.Merge(m, src)
}
func (m *Location) XXX_Size() int {
	return m.Size()
}
func (m *Location) XXX_DiscardUnknown() {
	xxx_messageInfo_Location.DiscardUnknown(m)
}

var xxx_messageInfo_Location proto.InternalMessageInfo

func (m *Location) GetKind() string {
	if m != nil {
		return m.Kind
	}
	return ""
}

func (m *Location) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Location) GetHostIp() string {
	if m != nil {
		return m.HostIp
	}
	return ""
}

func (m *Location) GetPodIp() string {
	if m != nil {
		return m.PodIp
	}
	return ""
}

type Inspect struct {
}

func (m *Inspect) Reset()      { *m = Inspect{} }
func (*Inspect) ProtoMessage() {}
func (*Inspect) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{19}
}
func (m *Inspect) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Inspect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Inspect.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Inspect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Inspect.Merge(m, src)
}
func (m *Inspect) XXX_Size() int {
	return m.Size()
}
func (m *Inspect) XXX_DiscardUnknown() {
	xxx_messageInfo_Inspect.DiscardUnknown(m)
}

var xxx_messageInfo_Inspect proto.InternalMessageInfo

type ActorStateInfo struct {
	State string `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
}

func (m *ActorStateInfo) Reset()      { *m = ActorStateInfo{} }
func (*ActorStateInfo) ProtoMessage() {}
func (*ActorStateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{20}
}
func (m *ActorStateInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ActorStateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ActorStateInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ActorStateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActorStateInfo.Merge(m, src)
}
func (m *ActorStateInfo) XXX_Size() int {
	return m.Size()
}
func (m *ActorStateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ActorStateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ActorStateInfo proto.InternalMessageInfo

func (m *ActorStateInfo) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

type Event struct {
	BizContext string `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	Pkg        string `protobuf:"bytes,2,opt,name=pkg,proto3" json:"pkg,omitempty"`
	Name       string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Payload    []byte `protobuf:"bytes,4,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (m *Event) Reset()      { *m = Event{} }
func (*Event) ProtoMessage() {}
func (*Event) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{21}
}
func (m *Event) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Event) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Event.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Event) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Event.Merge(m, src)
}
func (m *Event) XXX_Size() int {
	return m.Size()
}
func (m *Event) XXX_DiscardUnknown() {
	xxx_messageInfo_Event.DiscardUnknown(m)
}

var xxx_messageInfo_Event proto.InternalMessageInfo

func (m *Event) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *Event) GetPkg() string {
	if m != nil {
		return m.Pkg
	}
	return ""
}

func (m *Event) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Event) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

type ListIndexes struct {
	Db    string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
}

func (m *ListIndexes) Reset()      { *m = ListIndexes{} }
func (*ListIndexes) ProtoMessage() {}
func (*ListIndexes) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{22}
}
func (m *ListIndexes) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListIndexes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListIndexes.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListIndexes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListIndexes.Merge(m, src)
}
func (m *ListIndexes) XXX_Size() int {
	return m.Size()
}
func (m *ListIndexes) XXX_DiscardUnknown() {
	xxx_messageInfo_ListIndexes.DiscardUnknown(m)
}

var xxx_messageInfo_ListIndexes proto.InternalMessageInfo

func (m *ListIndexes) GetDb() string {
	if m != nil {
		return m.Db
	}
	return ""
}

func (m *ListIndexes) GetTable() string {
	if m != nil {
		return m.Table
	}
	return ""
}

type IndexInfo struct {
	Table        string `protobuf:"bytes,1,opt,name=Table,proto3" json:"Table,omitempty"`
	NonUnique    int64  `protobuf:"varint,2,opt,name=NonUnique,proto3" json:"NonUnique,omitempty"`
	KeyName      string `protobuf:"bytes,3,opt,name=KeyName,proto3" json:"KeyName,omitempty"`
	SeqInIndex   int64  `protobuf:"varint,4,opt,name=SeqInIndex,proto3" json:"SeqInIndex,omitempty"`
	ColumnName   string `protobuf:"bytes,5,opt,name=ColumnName,proto3" json:"ColumnName,omitempty"`
	Collation    string `protobuf:"bytes,6,opt,name=Collation,proto3" json:"Collation,omitempty"`
	Cardinality  int64  `protobuf:"varint,7,opt,name=Cardinality,proto3" json:"Cardinality,omitempty"`
	SubPart      int64  `protobuf:"varint,8,opt,name=SubPart,proto3" json:"SubPart,omitempty"`
	Packed       int64  `protobuf:"varint,9,opt,name=Packed,proto3" json:"Packed,omitempty"`
	Null         string `protobuf:"bytes,10,opt,name=Null,proto3" json:"Null,omitempty"`
	IndexType    string `protobuf:"bytes,11,opt,name=IndexType,proto3" json:"IndexType,omitempty"`
	Comment      string `protobuf:"bytes,12,opt,name=Comment,proto3" json:"Comment,omitempty"`
	IndexComment string `protobuf:"bytes,13,opt,name=IndexComment,proto3" json:"IndexComment,omitempty"`
	Visible      string `protobuf:"bytes,14,opt,name=Visible,proto3" json:"Visible,omitempty"`
	Expression   string `protobuf:"bytes,15,opt,name=Expression,proto3" json:"Expression,omitempty"`
}

func (m *IndexInfo) Reset()      { *m = IndexInfo{} }
func (*IndexInfo) ProtoMessage() {}
func (*IndexInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{23}
}
func (m *IndexInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IndexInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IndexInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IndexInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexInfo.Merge(m, src)
}
func (m *IndexInfo) XXX_Size() int {
	return m.Size()
}
func (m *IndexInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexInfo.DiscardUnknown(m)
}

var xxx_messageInfo_IndexInfo proto.InternalMessageInfo

func (m *IndexInfo) GetTable() string {
	if m != nil {
		return m.Table
	}
	return ""
}

func (m *IndexInfo) GetNonUnique() int64 {
	if m != nil {
		return m.NonUnique
	}
	return 0
}

func (m *IndexInfo) GetKeyName() string {
	if m != nil {
		return m.KeyName
	}
	return ""
}

func (m *IndexInfo) GetSeqInIndex() int64 {
	if m != nil {
		return m.SeqInIndex
	}
	return 0
}

func (m *IndexInfo) GetColumnName() string {
	if m != nil {
		return m.ColumnName
	}
	return ""
}

func (m *IndexInfo) GetCollation() string {
	if m != nil {
		return m.Collation
	}
	return ""
}

func (m *IndexInfo) GetCardinality() int64 {
	if m != nil {
		return m.Cardinality
	}
	return 0
}

func (m *IndexInfo) GetSubPart() int64 {
	if m != nil {
		return m.SubPart
	}
	return 0
}

func (m *IndexInfo) GetPacked() int64 {
	if m != nil {
		return m.Packed
	}
	return 0
}

func (m *IndexInfo) GetNull() string {
	if m != nil {
		return m.Null
	}
	return ""
}

func (m *IndexInfo) GetIndexType() string {
	if m != nil {
		return m.IndexType
	}
	return ""
}

func (m *IndexInfo) GetComment() string {
	if m != nil {
		return m.Comment
	}
	return ""
}

func (m *IndexInfo) GetIndexComment() string {
	if m != nil {
		return m.IndexComment
	}
	return ""
}

func (m *IndexInfo) GetVisible() string {
	if m != nil {
		return m.Visible
	}
	return ""
}

func (m *IndexInfo) GetExpression() string {
	if m != nil {
		return m.Expression
	}
	return ""
}

type ListIndexesResult struct {
	Indexes []*IndexInfo `protobuf:"bytes,1,rep,name=indexes,proto3" json:"indexes,omitempty"`
}

func (m *ListIndexesResult) Reset()      { *m = ListIndexesResult{} }
func (*ListIndexesResult) ProtoMessage() {}
func (*ListIndexesResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{24}
}
func (m *ListIndexesResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListIndexesResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListIndexesResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListIndexesResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListIndexesResult.Merge(m, src)
}
func (m *ListIndexesResult) XXX_Size() int {
	return m.Size()
}
func (m *ListIndexesResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ListIndexesResult.DiscardUnknown(m)
}

var xxx_messageInfo_ListIndexesResult proto.InternalMessageInfo

func (m *ListIndexesResult) GetIndexes() []*IndexInfo {
	if m != nil {
		return m.Indexes
	}
	return nil
}

type ListIndexesError struct {
	Code         CommandError `protobuf:"varint,1,opt,name=code,proto3,enum=shared.CommandError" json:"code,omitempty"`
	ErrorMessage string       `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (m *ListIndexesError) Reset()      { *m = ListIndexesError{} }
func (*ListIndexesError) ProtoMessage() {}
func (*ListIndexesError) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{25}
}
func (m *ListIndexesError) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListIndexesError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListIndexesError.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListIndexesError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListIndexesError.Merge(m, src)
}
func (m *ListIndexesError) XXX_Size() int {
	return m.Size()
}
func (m *ListIndexesError) XXX_DiscardUnknown() {
	xxx_messageInfo_ListIndexesError.DiscardUnknown(m)
}

var xxx_messageInfo_ListIndexesError proto.InternalMessageInfo

func (m *ListIndexesError) GetCode() CommandError {
	if m != nil {
		return m.Code
	}
	return ErrCommandOK
}

func (m *ListIndexesError) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

type RegisterShuttleMgr struct {
	VpcId string `protobuf:"bytes,1,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
}

func (m *RegisterShuttleMgr) Reset()      { *m = RegisterShuttleMgr{} }
func (*RegisterShuttleMgr) ProtoMessage() {}
func (*RegisterShuttleMgr) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{26}
}
func (m *RegisterShuttleMgr) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegisterShuttleMgr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegisterShuttleMgr.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegisterShuttleMgr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterShuttleMgr.Merge(m, src)
}
func (m *RegisterShuttleMgr) XXX_Size() int {
	return m.Size()
}
func (m *RegisterShuttleMgr) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterShuttleMgr.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterShuttleMgr proto.InternalMessageInfo

func (m *RegisterShuttleMgr) GetVpcId() string {
	if m != nil {
		return m.VpcId
	}
	return ""
}

type ConnectionRestart struct {
	BizContext   string `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	ConnectionId string `protobuf:"bytes,2,opt,name=ConnectionId,proto3" json:"ConnectionId,omitempty"`
}

func (m *ConnectionRestart) Reset()      { *m = ConnectionRestart{} }
func (*ConnectionRestart) ProtoMessage() {}
func (*ConnectionRestart) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{27}
}
func (m *ConnectionRestart) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConnectionRestart) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ConnectionRestart.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ConnectionRestart) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectionRestart.Merge(m, src)
}
func (m *ConnectionRestart) XXX_Size() int {
	return m.Size()
}
func (m *ConnectionRestart) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectionRestart.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectionRestart proto.InternalMessageInfo

func (m *ConnectionRestart) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *ConnectionRestart) GetConnectionId() string {
	if m != nil {
		return m.ConnectionId
	}
	return ""
}

// cursor realted
type ScanCursorResult struct {
	ConnectionId string `protobuf:"bytes,1,opt,name=ConnectionId,proto3" json:"ConnectionId,omitempty"`
	CommandId    string `protobuf:"bytes,2,opt,name=CommandId,proto3" json:"CommandId,omitempty"`
	TimeOutMs    int64  `protobuf:"varint,3,opt,name=TimeOutMs,proto3" json:"TimeOutMs,omitempty"`
}

func (m *ScanCursorResult) Reset()      { *m = ScanCursorResult{} }
func (*ScanCursorResult) ProtoMessage() {}
func (*ScanCursorResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{28}
}
func (m *ScanCursorResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScanCursorResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScanCursorResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ScanCursorResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanCursorResult.Merge(m, src)
}
func (m *ScanCursorResult) XXX_Size() int {
	return m.Size()
}
func (m *ScanCursorResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanCursorResult.DiscardUnknown(m)
}

var xxx_messageInfo_ScanCursorResult proto.InternalMessageInfo

func (m *ScanCursorResult) GetConnectionId() string {
	if m != nil {
		return m.ConnectionId
	}
	return ""
}

func (m *ScanCursorResult) GetCommandId() string {
	if m != nil {
		return m.CommandId
	}
	return ""
}

func (m *ScanCursorResult) GetTimeOutMs() int64 {
	if m != nil {
		return m.TimeOutMs
	}
	return 0
}

type KeepAliveProbe struct {
}

func (m *KeepAliveProbe) Reset()      { *m = KeepAliveProbe{} }
func (*KeepAliveProbe) ProtoMessage() {}
func (*KeepAliveProbe) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{29}
}
func (m *KeepAliveProbe) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeepAliveProbe) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeepAliveProbe.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KeepAliveProbe) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeepAliveProbe.Merge(m, src)
}
func (m *KeepAliveProbe) XXX_Size() int {
	return m.Size()
}
func (m *KeepAliveProbe) XXX_DiscardUnknown() {
	xxx_messageInfo_KeepAliveProbe.DiscardUnknown(m)
}

var xxx_messageInfo_KeepAliveProbe proto.InternalMessageInfo

type ClearCursor struct {
}

func (m *ClearCursor) Reset()      { *m = ClearCursor{} }
func (*ClearCursor) ProtoMessage() {}
func (*ClearCursor) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{30}
}
func (m *ClearCursor) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ClearCursor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ClearCursor.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ClearCursor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearCursor.Merge(m, src)
}
func (m *ClearCursor) XXX_Size() int {
	return m.Size()
}
func (m *ClearCursor) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearCursor.DiscardUnknown(m)
}

var xxx_messageInfo_ClearCursor proto.InternalMessageInfo

type ErrScanCursorResult struct {
	ConnectionId string `protobuf:"bytes,1,opt,name=ConnectionId,proto3" json:"ConnectionId,omitempty"`
	CommandId    string `protobuf:"bytes,2,opt,name=CommandId,proto3" json:"CommandId,omitempty"`
	ErrorMessage string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (m *ErrScanCursorResult) Reset()      { *m = ErrScanCursorResult{} }
func (*ErrScanCursorResult) ProtoMessage() {}
func (*ErrScanCursorResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_2580df1b6e466890, []int{31}
}
func (m *ErrScanCursorResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ErrScanCursorResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ErrScanCursorResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ErrScanCursorResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ErrScanCursorResult.Merge(m, src)
}
func (m *ErrScanCursorResult) XXX_Size() int {
	return m.Size()
}
func (m *ErrScanCursorResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ErrScanCursorResult.DiscardUnknown(m)
}

var xxx_messageInfo_ErrScanCursorResult proto.InternalMessageInfo

func (m *ErrScanCursorResult) GetConnectionId() string {
	if m != nil {
		return m.ConnectionId
	}
	return ""
}

func (m *ErrScanCursorResult) GetCommandId() string {
	if m != nil {
		return m.CommandId
	}
	return ""
}

func (m *ErrScanCursorResult) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

func init() {
	proto.RegisterEnum("shared.DataSourceType", DataSourceType_name, DataSourceType_value)
	proto.RegisterEnum("shared.DBEngine", DBEngine_name, DBEngine_value)
	proto.RegisterEnum("shared.LinkType", LinkType_name, LinkType_value)
	proto.RegisterEnum("shared.EndpointType", EndpointType_name, EndpointType_value)
	proto.RegisterEnum("shared.ConnectionError", ConnectionError_name, ConnectionError_value)
	proto.RegisterEnum("shared.CommandError", CommandError_name, CommandError_value)
	proto.RegisterEnum("shared.CommandResultType", CommandResultType_name, CommandResultType_value)
	proto.RegisterEnum("shared.LimitType", LimitType_name, LimitType_value)
	proto.RegisterType((*ActorRef)(nil), "shared.ActorRef")
	proto.RegisterType((*DataSource)(nil), "shared.DataSource")
	proto.RegisterMapType((map[string]string)(nil), "shared.DataSource.ExtraDsnEntry")
	proto.RegisterType((*OpenConnection)(nil), "shared.OpenConnection")
	proto.RegisterType((*OpenConnectionSuccessful)(nil), "shared.OpenConnectionSuccessful")
	proto.RegisterType((*OpenConnectionFailed)(nil), "shared.OpenConnectionFailed")
	proto.RegisterType((*ConnectionEstablished)(nil), "shared.ConnectionEstablished")
	proto.RegisterType((*CloseConnection)(nil), "shared.CloseConnection")
	proto.RegisterType((*ConnectionClosed)(nil), "shared.ConnectionClosed")
	proto.RegisterType((*ForceRollback)(nil), "shared.ForceRollback")
	proto.RegisterType((*ForceRollbackResp)(nil), "shared.ForceRollbackResp")
	proto.RegisterType((*Command)(nil), "shared.Command")
	proto.RegisterType((*CommandResult)(nil), "shared.CommandResult")
	proto.RegisterType((*CommandResultChunk)(nil), "shared.CommandResultChunk")
	proto.RegisterType((*CommandResultChunk_Row)(nil), "shared.CommandResultChunk.Row")
	proto.RegisterType((*CommandResultLimitInfo)(nil), "shared.CommandResultLimitInfo")
	proto.RegisterType((*ConnectionConfigUpdated)(nil), "shared.ConnectionConfigUpdated")
	proto.RegisterType((*Ping)(nil), "shared.Ping")
	proto.RegisterType((*ConnectionInfo)(nil), "shared.ConnectionInfo")
	proto.RegisterType((*GetLocation)(nil), "shared.GetLocation")
	proto.RegisterType((*Location)(nil), "shared.Location")
	proto.RegisterType((*Inspect)(nil), "shared.Inspect")
	proto.RegisterType((*ActorStateInfo)(nil), "shared.ActorStateInfo")
	proto.RegisterType((*Event)(nil), "shared.Event")
	proto.RegisterType((*ListIndexes)(nil), "shared.ListIndexes")
	proto.RegisterType((*IndexInfo)(nil), "shared.IndexInfo")
	proto.RegisterType((*ListIndexesResult)(nil), "shared.ListIndexesResult")
	proto.RegisterType((*ListIndexesError)(nil), "shared.ListIndexesError")
	proto.RegisterType((*RegisterShuttleMgr)(nil), "shared.RegisterShuttleMgr")
	proto.RegisterType((*ConnectionRestart)(nil), "shared.ConnectionRestart")
	proto.RegisterType((*ScanCursorResult)(nil), "shared.ScanCursorResult")
	proto.RegisterType((*KeepAliveProbe)(nil), "shared.KeepAliveProbe")
	proto.RegisterType((*ClearCursor)(nil), "shared.ClearCursor")
	proto.RegisterType((*ErrScanCursorResult)(nil), "shared.ErrScanCursorResult")
}

func init() { proto.RegisterFile("connections.proto", fileDescriptor_2580df1b6e466890) }

var fileDescriptor_2580df1b6e466890 = []byte{
	// 2238 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xcf, 0x73, 0x23, 0x47,
	0xf5, 0xd7, 0xe8, 0xb7, 0x9e, 0x6c, 0x79, 0xdc, 0x6b, 0x67, 0x67, 0x9d, 0x6f, 0xf4, 0xdd, 0x9a,
	0x40, 0x30, 0x0e, 0x71, 0x85, 0x4d, 0x0e, 0x5b, 0x40, 0xaa, 0x58, 0x4b, 0x0a, 0xa8, 0x62, 0x7b,
	0xcd, 0x68, 0xb3, 0x45, 0xe5, 0xa2, 0x1a, 0x4d, 0xb7, 0xad, 0x41, 0xa3, 0x6e, 0x65, 0xba, 0x65,
	0x5b, 0x29, 0x0e, 0x1c, 0xb8, 0x70, 0xe3, 0xc4, 0x81, 0xbf, 0x80, 0xa2, 0x38, 0x70, 0xe1, 0xc2,
	0x09, 0x6e, 0x1c, 0xc3, 0x2d, 0x47, 0xd6, 0x7b, 0x80, 0x63, 0xfe, 0x04, 0xea, 0x75, 0xcf, 0x68,
	0x46, 0x2b, 0x87, 0x4d, 0x0a, 0x72, 0xeb, 0xf7, 0x79, 0xef, 0xf5, 0xbc, 0x7e, 0xfd, 0x7e, 0xf5,
	0xc0, 0x76, 0x20, 0x38, 0x67, 0x81, 0x0a, 0x05, 0x97, 0x87, 0xb3, 0x58, 0x28, 0x41, 0xaa, 0x72,
	0xec, 0xc7, 0x8c, 0xba, 0x0f, 0xa0, 0xfe, 0x28, 0x50, 0x22, 0xf6, 0xd8, 0x39, 0x21, 0x50, 0x9e,
	0x84, 0x9c, 0x3a, 0xd6, 0x7d, 0x6b, 0xbf, 0xe1, 0xe9, 0x35, 0x62, 0xdc, 0x9f, 0x32, 0xa7, 0x68,
	0x30, 0x5c, 0xbb, 0x7f, 0xa9, 0x01, 0x74, 0x7d, 0xe5, 0x0f, 0xc4, 0x3c, 0x0e, 0x18, 0x39, 0x80,
	0xb2, 0x5a, 0xcc, 0x98, 0x56, 0x6b, 0x3d, 0x78, 0xe5, 0xd0, 0xec, 0x7c, 0x98, 0x49, 0x3c, 0x59,
	0xcc, 0x98, 0xa7, 0x65, 0xc8, 0x5b, 0xd0, 0x88, 0x42, 0x3e, 0x19, 0x6a, 0x85, 0xa2, 0x56, 0xb0,
	0x53, 0x85, 0xe3, 0x90, 0x4f, 0xb4, 0x68, 0x3d, 0x4a, 0x56, 0xc4, 0x81, 0x9a, 0x4f, 0x69, 0xcc,
	0xa4, 0x74, 0x4a, 0xda, 0x80, 0x94, 0x44, 0xbb, 0xe6, 0x92, 0xc5, 0x4e, 0xd9, 0xd8, 0x85, 0x6b,
	0xb2, 0x07, 0xf5, 0x99, 0x2f, 0xe5, 0x95, 0x88, 0xa9, 0x53, 0xd1, 0xf8, 0x92, 0x26, 0xef, 0x41,
	0x83, 0x5d, 0xab, 0xd8, 0x1f, 0x52, 0xc9, 0x9d, 0xea, 0xfd, 0xd2, 0x7e, 0xf3, 0xc1, 0xfd, 0x75,
	0x4b, 0x0f, 0x7b, 0x28, 0xd3, 0x95, 0xbc, 0xc7, 0x55, 0xbc, 0xf0, 0xea, 0x2c, 0x21, 0xc9, 0x77,
	0x80, 0x24, 0x3e, 0x1c, 0xaa, 0x70, 0xca, 0xc4, 0x5c, 0x0d, 0xa7, 0xd2, 0xa9, 0xdd, 0xb7, 0xf6,
	0x4b, 0x9e, 0x9d, 0x70, 0x9e, 0x18, 0xc6, 0x89, 0x24, 0x6f, 0xc0, 0x56, 0xcc, 0x7c, 0x9a, 0x17,
	0xad, 0x6b, 0xd1, 0x4d, 0x84, 0x33, 0xb9, 0x7d, 0xb0, 0xaf, 0xe2, 0x50, 0xb1, 0xbc, 0x60, 0x43,
	0x0b, 0xb6, 0x34, 0x9e, 0x49, 0xb6, 0xa0, 0x48, 0x47, 0x0e, 0xe8, 0x43, 0x15, 0xe9, 0x08, 0xbf,
	0x10, 0xd2, 0x68, 0x45, 0xb1, 0x69, 0xbe, 0x80, 0x70, 0xa6, 0xf7, 0xff, 0xd0, 0x0c, 0xb9, 0x54,
	0x3e, 0x0f, 0xd8, 0x30, 0xa4, 0xce, 0x86, 0xde, 0x00, 0x52, 0xa8, 0x4f, 0xc9, 0xb7, 0xc1, 0x0e,
	0x7c, 0x1a, 0x52, 0x5f, 0xb1, 0x61, 0xea, 0xea, 0x4d, 0x2d, 0xb5, 0x95, 0xe2, 0x8f, 0x12, 0x97,
	0xef, 0x40, 0xe5, 0xe9, 0x2c, 0xe8, 0x77, 0x9d, 0x96, 0xe6, 0x1b, 0x82, 0x7c, 0x03, 0x5a, 0x53,
	0xff, 0x7a, 0x28, 0x66, 0x8c, 0x0f, 0xd1, 0x11, 0xd2, 0xd9, 0xd2, 0x86, 0x6c, 0x4c, 0xfd, 0xeb,
	0xc7, 0x33, 0xc6, 0x3b, 0x88, 0xa5, 0x52, 0xda, 0x66, 0x23, 0x65, 0x2f, 0xa5, 0xfa, 0x34, 0x62,
	0x46, 0xea, 0x3e, 0x34, 0x4f, 0x04, 0xbf, 0x10, 0xa7, 0x82, 0xb2, 0x3e, 0x75, 0xb6, 0xf5, 0x77,
	0xf2, 0x10, 0x79, 0x05, 0xaa, 0x09, 0x93, 0x68, 0x66, 0x35, 0xc3, 0xfd, 0xb9, 0x1a, 0x77, 0x47,
	0xce, 0x1d, 0x83, 0x1b, 0x8a, 0x3c, 0x84, 0x0d, 0xc6, 0xe9, 0x4c, 0x84, 0x5c, 0x79, 0x22, 0x62,
	0xce, 0x8e, 0x0e, 0xb9, 0x9d, 0xf4, 0xe6, 0x7b, 0x09, 0x4f, 0x87, 0xdd, 0x8a, 0x24, 0xee, 0xe8,
	0xb1, 0x8b, 0x50, 0x70, 0x67, 0xd7, 0xec, 0x68, 0x28, 0x62, 0x43, 0x69, 0x26, 0xa7, 0xce, 0x5d,
	0x0d, 0xe2, 0x12, 0x43, 0xf1, 0x82, 0x86, 0xd4, 0x71, 0x4c, 0x28, 0xe2, 0x1a, 0xb5, 0xd5, 0x9c,
	0x73, 0x16, 0x39, 0xf7, 0x8c, 0xb6, 0xa1, 0x30, 0x44, 0x47, 0xb1, 0xcf, 0x83, 0x71, 0x9f, 0x3a,
	0x7b, 0x26, 0x44, 0x53, 0x1a, 0x73, 0x83, 0x8e, 0x86, 0x8c, 0x5f, 0x84, 0x9c, 0x39, 0xaf, 0xae,
	0xe6, 0x46, 0xf7, 0xa8, 0xa7, 0x71, 0xaf, 0x4e, 0x47, 0x66, 0xb5, 0xf7, 0x7d, 0xd8, 0x5c, 0x89,
	0x56, 0xb4, 0x6c, 0xc2, 0x16, 0x49, 0xf6, 0xe2, 0x12, 0x6f, 0xec, 0xd2, 0x8f, 0xe6, 0x69, 0xf6,
	0x1a, 0xe2, 0x7b, 0xc5, 0x87, 0x96, 0xfb, 0x57, 0x0b, 0x5a, 0xe9, 0xed, 0x98, 0xc2, 0x80, 0xa1,
	0x32, 0x0a, 0x3f, 0xc1, 0xdb, 0x51, 0xec, 0x5a, 0x25, 0xdb, 0xc0, 0x28, 0xfc, 0xa4, 0x63, 0x10,
	0x72, 0x00, 0x55, 0xa9, 0xb3, 0x44, 0x6f, 0xd7, 0x7c, 0x40, 0xd6, 0xf3, 0xc7, 0x4b, 0x24, 0xc8,
	0xdb, 0x00, 0x72, 0x3e, 0x63, 0xf1, 0x65, 0x28, 0x45, 0xac, 0x73, 0xb7, 0x99, 0x1d, 0x26, 0x2d,
	0x38, 0x5e, 0x4e, 0x86, 0xbc, 0x0d, 0x3b, 0x2b, 0x11, 0x2d, 0x59, 0x20, 0x38, 0x95, 0x3a, 0xc1,
	0x4b, 0x1e, 0xc9, 0x85, 0xf5, 0xc0, 0x70, 0xdc, 0xdf, 0x58, 0xe0, 0xac, 0x9e, 0x61, 0x30, 0x0f,
	0x02, 0x26, 0xe5, 0xf9, 0x3c, 0x22, 0x6f, 0x42, 0x39, 0x10, 0x34, 0x2d, 0x4a, 0x77, 0xd3, 0x4f,
	0x67, 0xb2, 0xbd, 0x38, 0x16, 0xb1, 0xa7, 0x85, 0xc8, 0x6b, 0x00, 0xc1, 0x3c, 0x8e, 0x19, 0x57,
	0x43, 0x3a, 0x4a, 0x9c, 0xd5, 0x48, 0x90, 0xee, 0x88, 0x1c, 0xc2, 0x1d, 0x31, 0x57, 0x2c, 0x1e,
	0x66, 0x65, 0x14, 0x93, 0xc9, 0x54, 0xa4, 0x6d, 0xcd, 0xca, 0xf6, 0xed, 0x53, 0x77, 0x0c, 0x3b,
	0xab, 0x76, 0xbd, 0xef, 0x87, 0x11, 0xa3, 0x5f, 0xcd, 0xa6, 0xd7, 0x61, 0x93, 0x21, 0x39, 0x9c,
	0x32, 0x29, 0xfd, 0x8b, 0xf4, 0x0e, 0x37, 0x34, 0x78, 0x62, 0x30, 0xf7, 0xb7, 0x16, 0xec, 0xe6,
	0xd4, 0xa5, 0xf2, 0x47, 0x51, 0x28, 0xc7, 0x8c, 0xe6, 0x2e, 0xcb, 0x7a, 0xe9, 0x65, 0xbd, 0x0e,
	0x9b, 0xab, 0x27, 0x4b, 0x3e, 0x15, 0xe4, 0x0e, 0x85, 0xf7, 0x23, 0xe6, 0xea, 0x8b, 0xbc, 0x40,
	0x0c, 0x6f, 0xc5, 0x0d, 0xdb, 0xb0, 0xd5, 0x89, 0x84, 0x64, 0x19, 0xe8, 0xfe, 0xca, 0x02, 0x3b,
	0x23, 0x35, 0xf7, 0x6b, 0x70, 0xcb, 0xfa, 0x81, 0x4a, 0xeb, 0x07, 0x72, 0xdf, 0x86, 0xcd, 0xf7,
	0x05, 0xba, 0x41, 0x44, 0xd1, 0xc8, 0x0f, 0x26, 0x2f, 0x4d, 0x00, 0xf7, 0x0e, 0x6c, 0xaf, 0x68,
	0x78, 0x4c, 0xce, 0xdc, 0xdf, 0x17, 0xa1, 0xd6, 0x11, 0xd3, 0xa9, 0xcf, 0xe9, 0xcb, 0x53, 0x08,
	0x03, 0xcd, 0xc8, 0x66, 0x6e, 0x6e, 0x24, 0x48, 0x9f, 0x62, 0xbb, 0x4b, 0x88, 0xb4, 0xdd, 0x25,
	0x24, 0x76, 0x0a, 0xac, 0x9f, 0x31, 0x93, 0xf3, 0x48, 0x0d, 0x03, 0x31, 0xe7, 0x2a, 0xc9, 0x0c,
	0xac, 0xab, 0x9e, 0x86, 0x3b, 0x88, 0xbe, 0x20, 0x39, 0x5a, 0x28, 0x26, 0x75, 0x33, 0xcc, 0x4b,
	0x1e, 0x21, 0x4a, 0xbe, 0x0b, 0xbb, 0xf9, 0x3d, 0x59, 0x14, 0x25, 0xe2, 0x55, 0x93, 0x72, 0xd9,
	0xc6, 0x2c, 0x8a, 0x8c, 0xca, 0x6b, 0x00, 0x6b, 0xed, 0xaf, 0xa1, 0xf2, 0xdd, 0x46, 0xfa, 0x97,
	0x6c, 0x38, 0x65, 0xca, 0xa7, 0x23, 0xdd, 0xf3, 0xea, 0x1e, 0x20, 0x74, 0xa2, 0x11, 0xf7, 0x9f,
	0x45, 0xd8, 0x4c, 0x9c, 0x65, 0xb6, 0xfe, 0xaf, 0x5d, 0xb6, 0x9f, 0x04, 0x4f, 0x69, 0xb5, 0xb0,
	0x27, 0x1f, 0xf9, 0x8f, 0x91, 0x53, 0xbe, 0x25, 0x72, 0xde, 0x4a, 0x66, 0x99, 0x8a, 0xde, 0xee,
	0xde, 0x0b, 0xdb, 0x19, 0x9b, 0x73, 0xe3, 0xcc, 0xbb, 0x50, 0x9b, 0xf9, 0x8b, 0x48, 0xf8, 0x34,
	0x99, 0x29, 0xf6, 0x6e, 0xd5, 0xe8, 0x8c, 0xe7, 0x7c, 0xe2, 0xa5, 0xa2, 0xeb, 0xe1, 0x59, 0xbb,
	0x25, 0xdf, 0xde, 0x03, 0x88, 0xc2, 0x69, 0xa8, 0x86, 0x21, 0x3f, 0x17, 0x4e, 0x5d, 0xef, 0xde,
	0xbe, 0x75, 0xf7, 0x63, 0x14, 0xeb, 0xf3, 0x73, 0xe1, 0x35, 0xa2, 0x74, 0xe9, 0xfe, 0xd1, 0x02,
	0xb2, 0x6e, 0x03, 0xb9, 0x07, 0xf5, 0xb1, 0x2f, 0x87, 0x53, 0x11, 0x9b, 0x7c, 0xab, 0x7b, 0xb5,
	0xb1, 0x2f, 0x4f, 0x44, 0xac, 0x1b, 0x9e, 0x38, 0x3f, 0x97, 0x4c, 0x69, 0x27, 0x97, 0xbc, 0x84,
	0x42, 0x7c, 0xcc, 0x7c, 0xca, 0xb0, 0x8c, 0x97, 0xb0, 0x95, 0x19, 0x8a, 0x3c, 0x80, 0x72, 0x2c,
	0xae, 0xb0, 0x40, 0x7f, 0xb1, 0x69, 0xfa, 0xa3, 0x87, 0x9e, 0xb8, 0xf2, 0xb4, 0xec, 0xde, 0xab,
	0x50, 0xf2, 0xc4, 0x15, 0xf6, 0x25, 0x0c, 0x37, 0xe9, 0x58, 0x7a, 0x47, 0x43, 0xb8, 0x43, 0x78,
	0xe5, 0xf6, 0x73, 0x91, 0x6f, 0xae, 0x4c, 0x98, 0xdb, 0xd9, 0xc0, 0x38, 0x0d, 0xf3, 0xb7, 0xd1,
	0x06, 0xd0, 0xd0, 0xd3, 0x65, 0xcf, 0x2b, 0x79, 0x39, 0xc4, 0x7d, 0x08, 0x77, 0x73, 0xc5, 0x47,
	0xf0, 0xf3, 0xf0, 0xe2, 0xc3, 0x19, 0x4e, 0x38, 0x14, 0xa3, 0x4c, 0x32, 0xec, 0xa2, 0x7a, 0x3e,
	0x34, 0x51, 0xd8, 0x30, 0x48, 0x57, 0x72, 0xf7, 0x5b, 0x50, 0x3e, 0x0b, 0xf9, 0xc5, 0xcb, 0x4b,
	0xc4, 0x2f, 0x2d, 0x68, 0xe5, 0x8a, 0x20, 0x1a, 0xbf, 0x76, 0xdb, 0xd6, 0x2d, 0xb7, 0xfd, 0x3f,
	0xee, 0x40, 0x9b, 0xd0, 0xfc, 0x11, 0x53, 0xc7, 0x22, 0xf0, 0x75, 0xd9, 0x1d, 0x41, 0x3d, 0x5d,
	0x7f, 0xd9, 0x21, 0x9f, 0xdc, 0x85, 0xda, 0x58, 0x48, 0x35, 0x0c, 0x67, 0xc9, 0x67, 0xaa, 0x48,
	0xf6, 0x67, 0x64, 0x17, 0xaa, 0x33, 0x41, 0x11, 0x37, 0x09, 0x54, 0x99, 0x09, 0xda, 0x9f, 0xb9,
	0x0d, 0xa8, 0xf5, 0xb9, 0x9c, 0xb1, 0x40, 0xb9, 0x6f, 0x40, 0x4b, 0xb7, 0xf8, 0x81, 0xf2, 0x15,
	0xd3, 0x3e, 0xd8, 0x81, 0x8a, 0x44, 0x22, 0xf9, 0xaa, 0x21, 0xdc, 0x31, 0x54, 0x7a, 0x97, 0x8c,
	0x7f, 0x89, 0x22, 0x80, 0x43, 0xd7, 0xe4, 0x22, 0xb1, 0x0f, 0x97, 0x4b, 0x93, 0x4b, 0x39, 0x93,
	0x9d, 0x2c, 0x1b, 0xd1, 0xb4, 0x8d, 0x65, 0xc6, 0xb9, 0xef, 0x40, 0xf3, 0x38, 0x94, 0xaa, 0xcf,
	0x29, 0xbb, 0x66, 0xe9, 0x34, 0x6d, 0x2d, 0xa7, 0xe9, 0x1d, 0xa8, 0x60, 0xe7, 0x5c, 0xce, 0x49,
	0x9a, 0x70, 0xff, 0x54, 0x82, 0x86, 0xd6, 0x48, 0x8f, 0xf0, 0x44, 0xcb, 0x24, 0x47, 0xd0, 0x04,
	0xf9, 0x3f, 0x68, 0x9c, 0x0a, 0xfe, 0x21, 0x0f, 0x3f, 0x5e, 0x46, 0x5c, 0x06, 0xa0, 0x41, 0x1f,
	0xb0, 0xc5, 0x69, 0x66, 0x67, 0x4a, 0x62, 0xa8, 0x0e, 0xd8, 0xc7, 0x7d, 0xae, 0xf7, 0x4f, 0x2a,
	0x79, 0x0e, 0x41, 0x7e, 0x47, 0x44, 0xf3, 0x29, 0xd7, 0xca, 0xe6, 0x31, 0x93, 0x43, 0xf0, 0xbb,
	0x1d, 0x11, 0x45, 0xfa, 0x4a, 0x75, 0xbd, 0x6e, 0x78, 0x19, 0x80, 0x73, 0x74, 0xc7, 0x8f, 0x69,
	0xc8, 0xfd, 0x28, 0x54, 0x8b, 0xa4, 0x4e, 0xe7, 0x21, 0xb4, 0x6c, 0x30, 0x1f, 0x9d, 0xf9, 0xb1,
	0x4a, 0x5e, 0x26, 0x29, 0x89, 0xe9, 0x7e, 0xe6, 0x07, 0x13, 0x46, 0x93, 0x97, 0x48, 0x42, 0xa1,
	0xc3, 0x4f, 0xe7, 0x51, 0x94, 0xbc, 0x41, 0xf4, 0x1a, 0xad, 0xd0, 0xe6, 0x62, 0x0e, 0xea, 0xf7,
	0x47, 0xc3, 0xcb, 0x00, 0xfc, 0x06, 0xe6, 0x33, 0xe3, 0x2a, 0x79, 0x77, 0xa4, 0x24, 0x71, 0x61,
	0x43, 0x8b, 0xa5, 0x6c, 0xf3, 0xe0, 0x58, 0xc1, 0x50, 0xfb, 0x69, 0x28, 0x43, 0xf4, 0xb8, 0x79,
	0x6f, 0xa4, 0x24, 0xfa, 0xa6, 0x77, 0x3d, 0xc3, 0x27, 0x09, 0x1e, 0x7e, 0xcb, 0xf8, 0x26, 0x43,
	0xdc, 0x1f, 0xc2, 0x76, 0xee, 0xb2, 0x93, 0x3e, 0xf3, 0x26, 0xd4, 0x42, 0x03, 0xe8, 0xa2, 0xd3,
	0xcc, 0xaa, 0xc8, 0xf2, 0x8a, 0xbd, 0x54, 0xc2, 0xf5, 0xc1, 0xce, 0xed, 0xa0, 0x9b, 0xc8, 0xb2,
	0xd1, 0x58, 0x5f, 0xbd, 0xd1, 0xdc, 0x36, 0xb9, 0xbd, 0x09, 0x04, 0x1f, 0x14, 0x52, 0xb1, 0x78,
	0x30, 0x9e, 0x2b, 0x15, 0xb1, 0x93, 0x8b, 0x18, 0x73, 0xeb, 0x72, 0x16, 0x64, 0x45, 0xa2, 0x72,
	0x39, 0x0b, 0xfa, 0xd4, 0xfd, 0x29, 0x6c, 0x67, 0xe9, 0xed, 0x31, 0xa9, 0xf0, 0xa2, 0x5e, 0x9a,
	0x34, 0x2e, 0x6c, 0xe4, 0x8b, 0x42, 0x6a, 0xc6, 0x4a, 0xa1, 0x88, 0xc1, 0x1e, 0x04, 0x3e, 0xef,
	0xcc, 0x63, 0x89, 0x23, 0xb9, 0x76, 0xd5, 0x8b, 0x7a, 0xd6, 0xba, 0x9e, 0x89, 0xbf, 0xa4, 0x07,
	0xa7, 0xe5, 0x6a, 0x09, 0x20, 0x17, 0x67, 0xf5, 0xc7, 0x38, 0x14, 0xe8, 0xc8, 0x2f, 0x79, 0x19,
	0xe0, 0xda, 0xd0, 0xfa, 0x80, 0xb1, 0xd9, 0xa3, 0x28, 0xbc, 0x64, 0x67, 0xb1, 0x18, 0x31, 0x2c,
	0x57, 0x9d, 0x88, 0xf9, 0xb1, 0x31, 0xc3, 0xfd, 0x39, 0xdc, 0xe9, 0xc5, 0xf1, 0xd7, 0x60, 0xd7,
	0xda, 0xcd, 0x94, 0xd6, 0x6f, 0xe6, 0xe0, 0xcf, 0x16, 0xb4, 0x56, 0xff, 0x5d, 0x90, 0x0d, 0xa8,
	0x9f, 0x0a, 0xae, 0xd7, 0x76, 0x81, 0x34, 0xa0, 0x72, 0xb2, 0x18, 0xfc, 0xe4, 0xd8, 0xb6, 0x90,
	0x71, 0x26, 0xa4, 0xba, 0x88, 0x99, 0xb4, 0x8b, 0x9a, 0x81, 0x6f, 0x55, 0xbb, 0x84, 0x4b, 0x8f,
	0xd1, 0x50, 0xda, 0x65, 0xb2, 0x09, 0x8d, 0xa7, 0xac, 0x7b, 0x64, 0x54, 0x2a, 0xa4, 0x09, 0x35,
	0x1c, 0x86, 0xbc, 0xee, 0xc0, 0xae, 0x6a, 0x8d, 0x01, 0xe2, 0x35, 0xc4, 0x71, 0xc6, 0x42, 0xbc,
	0x4e, 0xb6, 0x61, 0x53, 0xcb, 0x0f, 0xc6, 0x3a, 0x67, 0x2f, 0xec, 0x06, 0x6e, 0x83, 0x7a, 0x66,
	0x1b, 0x48, 0xc5, 0xbb, 0x22, 0xb0, 0x9b, 0xa4, 0x0e, 0xe5, 0xee, 0xa3, 0xbe, 0x67, 0x6f, 0x1c,
	0xfc, 0x00, 0xea, 0xe9, 0x53, 0x11, 0x45, 0x3e, 0xe4, 0x13, 0x2e, 0xae, 0xb8, 0x5d, 0x20, 0x5b,
	0xd0, 0xd4, 0xaa, 0x86, 0x67, 0x5b, 0x84, 0x40, 0x2b, 0x35, 0x3d, 0xc1, 0x8a, 0x07, 0x3f, 0x86,
	0x7a, 0xfa, 0x13, 0x06, 0xf7, 0x7c, 0x2a, 0xa2, 0xc0, 0x2e, 0x90, 0x1a, 0x94, 0x7a, 0x81, 0xb4,
	0x2d, 0x02, 0x50, 0x3d, 0x9b, 0x8f, 0xa2, 0x30, 0xb0, 0x8b, 0xa4, 0x05, 0x60, 0xd6, 0x1f, 0x09,
	0xce, 0xec, 0x12, 0x9a, 0x87, 0xf6, 0x74, 0x22, 0x31, 0xa7, 0x76, 0xf9, 0xe0, 0x21, 0x6c, 0xe4,
	0xdf, 0xd6, 0x68, 0xcb, 0x59, 0x1c, 0x4e, 0xfd, 0x78, 0x61, 0x17, 0x50, 0xd6, 0xbc, 0xe1, 0x90,
	0xd4, 0x4e, 0xf4, 0x98, 0x4f, 0x1f, 0xf3, 0x68, 0x61, 0x17, 0x0f, 0xfe, 0x6e, 0xc1, 0xd6, 0x0b,
	0xa3, 0x3f, 0xba, 0xa9, 0x17, 0xc7, 0x8f, 0x3f, 0xb0, 0x0b, 0xe8, 0x99, 0x5e, 0x1c, 0x67, 0xf7,
	0x63, 0x4e, 0xd2, 0x8b, 0xd3, 0xfe, 0x87, 0x4f, 0x2d, 0xbb, 0x48, 0xee, 0xea, 0x10, 0xca, 0xf6,
	0x39, 0x8a, 0xc5, 0x84, 0x71, 0xbb, 0x44, 0x1c, 0xd8, 0x59, 0x61, 0x24, 0x6f, 0x4a, 0xbb, 0x4c,
	0xee, 0xc1, 0xee, 0x0a, 0xe7, 0x54, 0xa8, 0xde, 0x75, 0x28, 0x95, 0x5d, 0x21, 0x6d, 0xd8, 0x5b,
	0x61, 0x3d, 0x8a, 0x62, 0xe6, 0xd3, 0x85, 0x79, 0xbf, 0xd8, 0xd5, 0x35, 0xbe, 0x61, 0x1c, 0x2d,
	0x74, 0x17, 0xb4, 0x6b, 0x07, 0x7f, 0xb0, 0x30, 0x74, 0xb3, 0x42, 0x41, 0x6c, 0xd8, 0xd0, 0x0a,
	0x1a, 0xca, 0x9d, 0xeb, 0xc8, 0xa7, 0x09, 0x68, 0x5b, 0x09, 0xd4, 0xbb, 0x66, 0x29, 0x54, 0x24,
	0xbb, 0xb0, 0xbd, 0xf2, 0xa1, 0x63, 0x21, 0x95, 0x5d, 0x4a, 0x24, 0x4d, 0xb2, 0x1c, 0xcd, 0xe5,
	0xc2, 0x2e, 0xa7, 0x4e, 0x49, 0xf2, 0xc7, 0xa7, 0x0b, 0xbb, 0x92, 0x6a, 0x6b, 0xec, 0x24, 0x94,
	0x53, 0x5f, 0x05, 0x63, 0xbb, 0x8a, 0xa1, 0xd1, 0x8b, 0xe3, 0x53, 0x61, 0x18, 0x76, 0xed, 0xe0,
	0x23, 0x2c, 0x37, 0x2f, 0x0c, 0xbc, 0x78, 0x07, 0xba, 0xe5, 0xd9, 0x05, 0x0c, 0x0d, 0x2c, 0x8f,
	0x26, 0x22, 0x06, 0x2a, 0xc6, 0x00, 0x2d, 0xe2, 0xfa, 0xf1, 0xe8, 0x67, 0x2c, 0x40, 0x83, 0x00,
	0xaa, 0x4f, 0x7c, 0x39, 0xe9, 0x53, 0xbb, 0x8c, 0xd7, 0xfb, 0x24, 0x0c, 0x26, 0x4c, 0xf5, 0xbb,
	0x76, 0xe5, 0x60, 0x1f, 0x1a, 0xcb, 0xb1, 0x4d, 0xdf, 0xbc, 0xb8, 0xd2, 0xb4, 0x09, 0x0b, 0x0c,
	0x21, 0x43, 0x5a, 0x47, 0xef, 0x7e, 0xfa, 0xac, 0x5d, 0xf8, 0xec, 0x59, 0xbb, 0xf0, 0xf9, 0xb3,
	0xb6, 0xf5, 0x8b, 0x9b, 0xb6, 0xf5, 0xbb, 0x9b, 0xb6, 0xf5, 0xb7, 0x9b, 0xb6, 0xf5, 0xe9, 0x4d,
	0xdb, 0xfa, 0xc7, 0x4d, 0xdb, 0xfa, 0xd7, 0x4d, 0xbb, 0xf0, 0xf9, 0x4d, 0xdb, 0xfa, 0xf5, 0xf3,
	0x76, 0xe1, 0xd3, 0xe7, 0xed, 0xc2, 0x67, 0xcf, 0xdb, 0x85, 0x51, 0x55, 0xff, 0xde, 0x7c, 0xe7,
	0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x4b, 0x54, 0x99, 0x53, 0xf3, 0x14, 0x00, 0x00,
}

func (x DataSourceType) String() string {
	s, ok := DataSourceType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x DBEngine) String() string {
	s, ok := DBEngine_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x LinkType) String() string {
	s, ok := LinkType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x EndpointType) String() string {
	s, ok := EndpointType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x ConnectionError) String() string {
	s, ok := ConnectionError_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x CommandError) String() string {
	s, ok := CommandError_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x CommandResultType) String() string {
	s, ok := CommandResultType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x LimitType) String() string {
	s, ok := LimitType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *ActorRef) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ActorRef)
	if !ok {
		that2, ok := that.(ActorRef)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Kind != that1.Kind {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	return true
}
func (this *DataSource) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DataSource)
	if !ok {
		that2, ok := that.(DataSource)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if this.LinkType != that1.LinkType {
		return false
	}
	if this.Address != that1.Address {
		return false
	}
	if this.User != that1.User {
		return false
	}
	if this.Password != that1.Password {
		return false
	}
	if len(this.ExtraDsn) != len(that1.ExtraDsn) {
		return false
	}
	for i := range this.ExtraDsn {
		if this.ExtraDsn[i] != that1.ExtraDsn[i] {
			return false
		}
	}
	if this.ConnectTimeoutMs != that1.ConnectTimeoutMs {
		return false
	}
	if this.ReadTimeoutMs != that1.ReadTimeoutMs {
		return false
	}
	if this.WriteTimeoutMs != that1.WriteTimeoutMs {
		return false
	}
	if this.Db != that1.Db {
		return false
	}
	if this.IdleTimeoutMs != that1.IdleTimeoutMs {
		return false
	}
	if this.InstanceId != that1.InstanceId {
		return false
	}
	if this.CadidateAddress != that1.CadidateAddress {
		return false
	}
	if this.VpcID != that1.VpcID {
		return false
	}
	if this.MaxOpenConns != that1.MaxOpenConns {
		return false
	}
	if this.MaxIdleConns != that1.MaxIdleConns {
		return false
	}
	if this.MongoNodeId != that1.MongoNodeId {
		return false
	}
	if this.NodeId != that1.NodeId {
		return false
	}
	if this.AuthDb != that1.AuthDb {
		return false
	}
	if this.EndpointRole != that1.EndpointRole {
		return false
	}
	if this.Region != that1.Region {
		return false
	}
	if this.Psm != that1.Psm {
		return false
	}
	if this.Gdid != that1.Gdid {
		return false
	}
	if this.Tunnel != that1.Tunnel {
		return false
	}
	if this.BranchId != that1.BranchId {
		return false
	}
	if this.DbEngine != that1.DbEngine {
		return false
	}
	return true
}
func (this *OpenConnection) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*OpenConnection)
	if !ok {
		that2, ok := that.(OpenConnection)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if !this.Supervisor.Equal(that1.Supervisor) {
		return false
	}
	if this.IdleTimeoutSeconds != that1.IdleTimeoutSeconds {
		return false
	}
	return true
}
func (this *OpenConnectionSuccessful) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*OpenConnectionSuccessful)
	if !ok {
		that2, ok := that.(OpenConnectionSuccessful)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Code != that1.Code {
		return false
	}
	if this.CurrentDb != that1.CurrentDb {
		return false
	}
	if this.OuterConnectionId != that1.OuterConnectionId {
		return false
	}
	return true
}
func (this *OpenConnectionFailed) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*OpenConnectionFailed)
	if !ok {
		that2, ok := that.(OpenConnectionFailed)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Code != that1.Code {
		return false
	}
	if this.ErrorMessage != that1.ErrorMessage {
		return false
	}
	return true
}
func (this *ConnectionEstablished) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ConnectionEstablished)
	if !ok {
		that2, ok := that.(ConnectionEstablished)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if this.ConnectionId != that1.ConnectionId {
		return false
	}
	if this.OutterConnectionId != that1.OutterConnectionId {
		return false
	}
	return true
}
func (this *CloseConnection) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CloseConnection)
	if !ok {
		that2, ok := that.(CloseConnection)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *ConnectionClosed) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ConnectionClosed)
	if !ok {
		that2, ok := that.(ConnectionClosed)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Code != that1.Code {
		return false
	}
	if this.ErrorMessage != that1.ErrorMessage {
		return false
	}
	if this.ConnectionId != that1.ConnectionId {
		return false
	}
	return true
}
func (this *ForceRollback) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ForceRollback)
	if !ok {
		that2, ok := that.(ForceRollback)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *ForceRollbackResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ForceRollbackResp)
	if !ok {
		that2, ok := that.(ForceRollbackResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *Command) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Command)
	if !ok {
		that2, ok := that.(Command)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.CommandId != that1.CommandId {
		return false
	}
	if this.Command != that1.Command {
		return false
	}
	if this.MaxResultCount != that1.MaxResultCount {
		return false
	}
	if this.MaxResultBytes != that1.MaxResultBytes {
		return false
	}
	if this.MaxResultCellBytes != that1.MaxResultCellBytes {
		return false
	}
	if this.TimeoutMs != that1.TimeoutMs {
		return false
	}
	if this.SaveMetadb != that1.SaveMetadb {
		return false
	}
	return true
}
func (this *CommandResult) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CommandResult)
	if !ok {
		that2, ok := that.(CommandResult)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.CommandId != that1.CommandId {
		return false
	}
	if this.Code != that1.Code {
		return false
	}
	if this.ErrorMessage != that1.ErrorMessage {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if len(this.Payload) != len(that1.Payload) {
		return false
	}
	for i := range this.Payload {
		if !this.Payload[i].Equal(that1.Payload[i]) {
			return false
		}
	}
	if this.ConnectionId != that1.ConnectionId {
		return false
	}
	if len(this.LimitInfo) != len(that1.LimitInfo) {
		return false
	}
	for i := range this.LimitInfo {
		if !this.LimitInfo[i].Equal(that1.LimitInfo[i]) {
			return false
		}
	}
	return true
}
func (this *CommandResultChunk) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CommandResultChunk)
	if !ok {
		that2, ok := that.(CommandResultChunk)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.HasMore != that1.HasMore {
		return false
	}
	if this.Offset != that1.Offset {
		return false
	}
	if len(this.Header) != len(that1.Header) {
		return false
	}
	for i := range this.Header {
		if this.Header[i] != that1.Header[i] {
			return false
		}
	}
	if len(this.Rows) != len(that1.Rows) {
		return false
	}
	for i := range this.Rows {
		if !this.Rows[i].Equal(that1.Rows[i]) {
			return false
		}
	}
	return true
}
func (this *CommandResultChunk_Row) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CommandResultChunk_Row)
	if !ok {
		that2, ok := that.(CommandResultChunk_Row)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Cells) != len(that1.Cells) {
		return false
	}
	for i := range this.Cells {
		if this.Cells[i] != that1.Cells[i] {
			return false
		}
	}
	return true
}
func (this *CommandResultLimitInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CommandResultLimitInfo)
	if !ok {
		that2, ok := that.(CommandResultLimitInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if this.LimitValue != that1.LimitValue {
		return false
	}
	return true
}
func (this *ConnectionConfigUpdated) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ConnectionConfigUpdated)
	if !ok {
		that2, ok := that.(ConnectionConfigUpdated)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.SentryDsn != that1.SentryDsn {
		return false
	}
	return true
}
func (this *Ping) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Ping)
	if !ok {
		that2, ok := that.(Ping)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *ConnectionInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ConnectionInfo)
	if !ok {
		that2, ok := that.(ConnectionInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.ConnectionId != that1.ConnectionId {
		return false
	}
	if this.CurrentDb != that1.CurrentDb {
		return false
	}
	if this.OuterConnectionId != that1.OuterConnectionId {
		return false
	}
	return true
}
func (this *GetLocation) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetLocation)
	if !ok {
		that2, ok := that.(GetLocation)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *Location) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Location)
	if !ok {
		that2, ok := that.(Location)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Kind != that1.Kind {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if this.HostIp != that1.HostIp {
		return false
	}
	if this.PodIp != that1.PodIp {
		return false
	}
	return true
}
func (this *Inspect) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Inspect)
	if !ok {
		that2, ok := that.(Inspect)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *ActorStateInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ActorStateInfo)
	if !ok {
		that2, ok := that.(ActorStateInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.State != that1.State {
		return false
	}
	return true
}
func (this *Event) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Event)
	if !ok {
		that2, ok := that.(Event)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.Pkg != that1.Pkg {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if !bytes.Equal(this.Payload, that1.Payload) {
		return false
	}
	return true
}
func (this *ListIndexes) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ListIndexes)
	if !ok {
		that2, ok := that.(ListIndexes)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Db != that1.Db {
		return false
	}
	if this.Table != that1.Table {
		return false
	}
	return true
}
func (this *IndexInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*IndexInfo)
	if !ok {
		that2, ok := that.(IndexInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Table != that1.Table {
		return false
	}
	if this.NonUnique != that1.NonUnique {
		return false
	}
	if this.KeyName != that1.KeyName {
		return false
	}
	if this.SeqInIndex != that1.SeqInIndex {
		return false
	}
	if this.ColumnName != that1.ColumnName {
		return false
	}
	if this.Collation != that1.Collation {
		return false
	}
	if this.Cardinality != that1.Cardinality {
		return false
	}
	if this.SubPart != that1.SubPart {
		return false
	}
	if this.Packed != that1.Packed {
		return false
	}
	if this.Null != that1.Null {
		return false
	}
	if this.IndexType != that1.IndexType {
		return false
	}
	if this.Comment != that1.Comment {
		return false
	}
	if this.IndexComment != that1.IndexComment {
		return false
	}
	if this.Visible != that1.Visible {
		return false
	}
	if this.Expression != that1.Expression {
		return false
	}
	return true
}
func (this *ListIndexesResult) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ListIndexesResult)
	if !ok {
		that2, ok := that.(ListIndexesResult)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Indexes) != len(that1.Indexes) {
		return false
	}
	for i := range this.Indexes {
		if !this.Indexes[i].Equal(that1.Indexes[i]) {
			return false
		}
	}
	return true
}
func (this *ListIndexesError) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ListIndexesError)
	if !ok {
		that2, ok := that.(ListIndexesError)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Code != that1.Code {
		return false
	}
	if this.ErrorMessage != that1.ErrorMessage {
		return false
	}
	return true
}
func (this *RegisterShuttleMgr) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*RegisterShuttleMgr)
	if !ok {
		that2, ok := that.(RegisterShuttleMgr)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.VpcId != that1.VpcId {
		return false
	}
	return true
}
func (this *ConnectionRestart) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ConnectionRestart)
	if !ok {
		that2, ok := that.(ConnectionRestart)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.ConnectionId != that1.ConnectionId {
		return false
	}
	return true
}
func (this *ScanCursorResult) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ScanCursorResult)
	if !ok {
		that2, ok := that.(ScanCursorResult)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.ConnectionId != that1.ConnectionId {
		return false
	}
	if this.CommandId != that1.CommandId {
		return false
	}
	if this.TimeOutMs != that1.TimeOutMs {
		return false
	}
	return true
}
func (this *KeepAliveProbe) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*KeepAliveProbe)
	if !ok {
		that2, ok := that.(KeepAliveProbe)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *ClearCursor) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ClearCursor)
	if !ok {
		that2, ok := that.(ClearCursor)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *ErrScanCursorResult) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ErrScanCursorResult)
	if !ok {
		that2, ok := that.(ErrScanCursorResult)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.ConnectionId != that1.ConnectionId {
		return false
	}
	if this.CommandId != that1.CommandId {
		return false
	}
	if this.ErrorMessage != that1.ErrorMessage {
		return false
	}
	return true
}
func (this *ActorRef) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.ActorRef{")
	s = append(s, "Kind: "+fmt.Sprintf("%#v", this.Kind)+",\n")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DataSource) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 30)
	s = append(s, "&shared.DataSource{")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	s = append(s, "LinkType: "+fmt.Sprintf("%#v", this.LinkType)+",\n")
	s = append(s, "Address: "+fmt.Sprintf("%#v", this.Address)+",\n")
	s = append(s, "User: "+fmt.Sprintf("%#v", this.User)+",\n")
	s = append(s, "Password: "+fmt.Sprintf("%#v", this.Password)+",\n")
	keysForExtraDsn := make([]string, 0, len(this.ExtraDsn))
	for k, _ := range this.ExtraDsn {
		keysForExtraDsn = append(keysForExtraDsn, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForExtraDsn)
	mapStringForExtraDsn := "map[string]string{"
	for _, k := range keysForExtraDsn {
		mapStringForExtraDsn += fmt.Sprintf("%#v: %#v,", k, this.ExtraDsn[k])
	}
	mapStringForExtraDsn += "}"
	if this.ExtraDsn != nil {
		s = append(s, "ExtraDsn: "+mapStringForExtraDsn+",\n")
	}
	s = append(s, "ConnectTimeoutMs: "+fmt.Sprintf("%#v", this.ConnectTimeoutMs)+",\n")
	s = append(s, "ReadTimeoutMs: "+fmt.Sprintf("%#v", this.ReadTimeoutMs)+",\n")
	s = append(s, "WriteTimeoutMs: "+fmt.Sprintf("%#v", this.WriteTimeoutMs)+",\n")
	s = append(s, "Db: "+fmt.Sprintf("%#v", this.Db)+",\n")
	s = append(s, "IdleTimeoutMs: "+fmt.Sprintf("%#v", this.IdleTimeoutMs)+",\n")
	s = append(s, "InstanceId: "+fmt.Sprintf("%#v", this.InstanceId)+",\n")
	s = append(s, "CadidateAddress: "+fmt.Sprintf("%#v", this.CadidateAddress)+",\n")
	s = append(s, "VpcID: "+fmt.Sprintf("%#v", this.VpcID)+",\n")
	s = append(s, "MaxOpenConns: "+fmt.Sprintf("%#v", this.MaxOpenConns)+",\n")
	s = append(s, "MaxIdleConns: "+fmt.Sprintf("%#v", this.MaxIdleConns)+",\n")
	s = append(s, "MongoNodeId: "+fmt.Sprintf("%#v", this.MongoNodeId)+",\n")
	s = append(s, "NodeId: "+fmt.Sprintf("%#v", this.NodeId)+",\n")
	s = append(s, "AuthDb: "+fmt.Sprintf("%#v", this.AuthDb)+",\n")
	s = append(s, "EndpointRole: "+fmt.Sprintf("%#v", this.EndpointRole)+",\n")
	s = append(s, "Region: "+fmt.Sprintf("%#v", this.Region)+",\n")
	s = append(s, "Psm: "+fmt.Sprintf("%#v", this.Psm)+",\n")
	s = append(s, "Gdid: "+fmt.Sprintf("%#v", this.Gdid)+",\n")
	s = append(s, "Tunnel: "+fmt.Sprintf("%#v", this.Tunnel)+",\n")
	s = append(s, "BranchId: "+fmt.Sprintf("%#v", this.BranchId)+",\n")
	s = append(s, "DbEngine: "+fmt.Sprintf("%#v", this.DbEngine)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *OpenConnection) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.OpenConnection{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	if this.Supervisor != nil {
		s = append(s, "Supervisor: "+fmt.Sprintf("%#v", this.Supervisor)+",\n")
	}
	s = append(s, "IdleTimeoutSeconds: "+fmt.Sprintf("%#v", this.IdleTimeoutSeconds)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *OpenConnectionSuccessful) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.OpenConnectionSuccessful{")
	s = append(s, "Code: "+fmt.Sprintf("%#v", this.Code)+",\n")
	s = append(s, "CurrentDb: "+fmt.Sprintf("%#v", this.CurrentDb)+",\n")
	s = append(s, "OuterConnectionId: "+fmt.Sprintf("%#v", this.OuterConnectionId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *OpenConnectionFailed) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.OpenConnectionFailed{")
	s = append(s, "Code: "+fmt.Sprintf("%#v", this.Code)+",\n")
	s = append(s, "ErrorMessage: "+fmt.Sprintf("%#v", this.ErrorMessage)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ConnectionEstablished) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.ConnectionEstablished{")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	s = append(s, "ConnectionId: "+fmt.Sprintf("%#v", this.ConnectionId)+",\n")
	s = append(s, "OutterConnectionId: "+fmt.Sprintf("%#v", this.OutterConnectionId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CloseConnection) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.CloseConnection{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ConnectionClosed) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.ConnectionClosed{")
	s = append(s, "Code: "+fmt.Sprintf("%#v", this.Code)+",\n")
	s = append(s, "ErrorMessage: "+fmt.Sprintf("%#v", this.ErrorMessage)+",\n")
	s = append(s, "ConnectionId: "+fmt.Sprintf("%#v", this.ConnectionId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ForceRollback) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.ForceRollback{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ForceRollbackResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.ForceRollbackResp{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Command) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&shared.Command{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "CommandId: "+fmt.Sprintf("%#v", this.CommandId)+",\n")
	s = append(s, "Command: "+fmt.Sprintf("%#v", this.Command)+",\n")
	s = append(s, "MaxResultCount: "+fmt.Sprintf("%#v", this.MaxResultCount)+",\n")
	s = append(s, "MaxResultBytes: "+fmt.Sprintf("%#v", this.MaxResultBytes)+",\n")
	s = append(s, "MaxResultCellBytes: "+fmt.Sprintf("%#v", this.MaxResultCellBytes)+",\n")
	s = append(s, "TimeoutMs: "+fmt.Sprintf("%#v", this.TimeoutMs)+",\n")
	s = append(s, "SaveMetadb: "+fmt.Sprintf("%#v", this.SaveMetadb)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CommandResult) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&shared.CommandResult{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "CommandId: "+fmt.Sprintf("%#v", this.CommandId)+",\n")
	s = append(s, "Code: "+fmt.Sprintf("%#v", this.Code)+",\n")
	s = append(s, "ErrorMessage: "+fmt.Sprintf("%#v", this.ErrorMessage)+",\n")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	if this.Payload != nil {
		s = append(s, "Payload: "+fmt.Sprintf("%#v", this.Payload)+",\n")
	}
	s = append(s, "ConnectionId: "+fmt.Sprintf("%#v", this.ConnectionId)+",\n")
	if this.LimitInfo != nil {
		s = append(s, "LimitInfo: "+fmt.Sprintf("%#v", this.LimitInfo)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CommandResultChunk) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.CommandResultChunk{")
	s = append(s, "HasMore: "+fmt.Sprintf("%#v", this.HasMore)+",\n")
	s = append(s, "Offset: "+fmt.Sprintf("%#v", this.Offset)+",\n")
	s = append(s, "Header: "+fmt.Sprintf("%#v", this.Header)+",\n")
	if this.Rows != nil {
		s = append(s, "Rows: "+fmt.Sprintf("%#v", this.Rows)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CommandResultChunk_Row) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.CommandResultChunk_Row{")
	s = append(s, "Cells: "+fmt.Sprintf("%#v", this.Cells)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CommandResultLimitInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.CommandResultLimitInfo{")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	s = append(s, "LimitValue: "+fmt.Sprintf("%#v", this.LimitValue)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ConnectionConfigUpdated) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.ConnectionConfigUpdated{")
	s = append(s, "SentryDsn: "+fmt.Sprintf("%#v", this.SentryDsn)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Ping) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.Ping{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ConnectionInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.ConnectionInfo{")
	s = append(s, "ConnectionId: "+fmt.Sprintf("%#v", this.ConnectionId)+",\n")
	s = append(s, "CurrentDb: "+fmt.Sprintf("%#v", this.CurrentDb)+",\n")
	s = append(s, "OuterConnectionId: "+fmt.Sprintf("%#v", this.OuterConnectionId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetLocation) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.GetLocation{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Location) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.Location{")
	s = append(s, "Kind: "+fmt.Sprintf("%#v", this.Kind)+",\n")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "HostIp: "+fmt.Sprintf("%#v", this.HostIp)+",\n")
	s = append(s, "PodIp: "+fmt.Sprintf("%#v", this.PodIp)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Inspect) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.Inspect{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ActorStateInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.ActorStateInfo{")
	s = append(s, "State: "+fmt.Sprintf("%#v", this.State)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Event) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.Event{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "Pkg: "+fmt.Sprintf("%#v", this.Pkg)+",\n")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Payload: "+fmt.Sprintf("%#v", this.Payload)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ListIndexes) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.ListIndexes{")
	s = append(s, "Db: "+fmt.Sprintf("%#v", this.Db)+",\n")
	s = append(s, "Table: "+fmt.Sprintf("%#v", this.Table)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *IndexInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 19)
	s = append(s, "&shared.IndexInfo{")
	s = append(s, "Table: "+fmt.Sprintf("%#v", this.Table)+",\n")
	s = append(s, "NonUnique: "+fmt.Sprintf("%#v", this.NonUnique)+",\n")
	s = append(s, "KeyName: "+fmt.Sprintf("%#v", this.KeyName)+",\n")
	s = append(s, "SeqInIndex: "+fmt.Sprintf("%#v", this.SeqInIndex)+",\n")
	s = append(s, "ColumnName: "+fmt.Sprintf("%#v", this.ColumnName)+",\n")
	s = append(s, "Collation: "+fmt.Sprintf("%#v", this.Collation)+",\n")
	s = append(s, "Cardinality: "+fmt.Sprintf("%#v", this.Cardinality)+",\n")
	s = append(s, "SubPart: "+fmt.Sprintf("%#v", this.SubPart)+",\n")
	s = append(s, "Packed: "+fmt.Sprintf("%#v", this.Packed)+",\n")
	s = append(s, "Null: "+fmt.Sprintf("%#v", this.Null)+",\n")
	s = append(s, "IndexType: "+fmt.Sprintf("%#v", this.IndexType)+",\n")
	s = append(s, "Comment: "+fmt.Sprintf("%#v", this.Comment)+",\n")
	s = append(s, "IndexComment: "+fmt.Sprintf("%#v", this.IndexComment)+",\n")
	s = append(s, "Visible: "+fmt.Sprintf("%#v", this.Visible)+",\n")
	s = append(s, "Expression: "+fmt.Sprintf("%#v", this.Expression)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ListIndexesResult) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.ListIndexesResult{")
	if this.Indexes != nil {
		s = append(s, "Indexes: "+fmt.Sprintf("%#v", this.Indexes)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ListIndexesError) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.ListIndexesError{")
	s = append(s, "Code: "+fmt.Sprintf("%#v", this.Code)+",\n")
	s = append(s, "ErrorMessage: "+fmt.Sprintf("%#v", this.ErrorMessage)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *RegisterShuttleMgr) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.RegisterShuttleMgr{")
	s = append(s, "VpcId: "+fmt.Sprintf("%#v", this.VpcId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ConnectionRestart) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.ConnectionRestart{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "ConnectionId: "+fmt.Sprintf("%#v", this.ConnectionId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ScanCursorResult) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.ScanCursorResult{")
	s = append(s, "ConnectionId: "+fmt.Sprintf("%#v", this.ConnectionId)+",\n")
	s = append(s, "CommandId: "+fmt.Sprintf("%#v", this.CommandId)+",\n")
	s = append(s, "TimeOutMs: "+fmt.Sprintf("%#v", this.TimeOutMs)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *KeepAliveProbe) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.KeepAliveProbe{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ClearCursor) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.ClearCursor{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ErrScanCursorResult) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.ErrScanCursorResult{")
	s = append(s, "ConnectionId: "+fmt.Sprintf("%#v", this.ConnectionId)+",\n")
	s = append(s, "CommandId: "+fmt.Sprintf("%#v", this.CommandId)+",\n")
	s = append(s, "ErrorMessage: "+fmt.Sprintf("%#v", this.ErrorMessage)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringConnections(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *ActorRef) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActorRef) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ActorRef) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Kind) > 0 {
		i -= len(m.Kind)
		copy(dAtA[i:], m.Kind)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Kind)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DataSource) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DataSource) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataSource) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DbEngine != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.DbEngine))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd8
	}
	if len(m.BranchId) > 0 {
		i -= len(m.BranchId)
		copy(dAtA[i:], m.BranchId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.BranchId)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd2
	}
	if len(m.Tunnel) > 0 {
		i -= len(m.Tunnel)
		copy(dAtA[i:], m.Tunnel)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Tunnel)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xca
	}
	if len(m.Gdid) > 0 {
		i -= len(m.Gdid)
		copy(dAtA[i:], m.Gdid)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Gdid)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	if len(m.Psm) > 0 {
		i -= len(m.Psm)
		copy(dAtA[i:], m.Psm)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Psm)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xba
	}
	if len(m.Region) > 0 {
		i -= len(m.Region)
		copy(dAtA[i:], m.Region)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Region)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	if m.EndpointRole != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.EndpointRole))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa0
	}
	if len(m.AuthDb) > 0 {
		i -= len(m.AuthDb)
		copy(dAtA[i:], m.AuthDb)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.AuthDb)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if len(m.NodeId) > 0 {
		i -= len(m.NodeId)
		copy(dAtA[i:], m.NodeId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.NodeId)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	if len(m.MongoNodeId) > 0 {
		i -= len(m.MongoNodeId)
		copy(dAtA[i:], m.MongoNodeId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.MongoNodeId)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if m.MaxIdleConns != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.MaxIdleConns))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if m.MaxOpenConns != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.MaxOpenConns))
		i--
		dAtA[i] = 0x78
	}
	if len(m.VpcID) > 0 {
		i -= len(m.VpcID)
		copy(dAtA[i:], m.VpcID)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.VpcID)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.CadidateAddress) > 0 {
		i -= len(m.CadidateAddress)
		copy(dAtA[i:], m.CadidateAddress)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.CadidateAddress)))
		i--
		dAtA[i] = 0x6a
	}
	if len(m.InstanceId) > 0 {
		i -= len(m.InstanceId)
		copy(dAtA[i:], m.InstanceId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.InstanceId)))
		i--
		dAtA[i] = 0x62
	}
	if m.IdleTimeoutMs != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.IdleTimeoutMs))
		i--
		dAtA[i] = 0x58
	}
	if len(m.Db) > 0 {
		i -= len(m.Db)
		copy(dAtA[i:], m.Db)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Db)))
		i--
		dAtA[i] = 0x52
	}
	if m.WriteTimeoutMs != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.WriteTimeoutMs))
		i--
		dAtA[i] = 0x48
	}
	if m.ReadTimeoutMs != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.ReadTimeoutMs))
		i--
		dAtA[i] = 0x40
	}
	if m.ConnectTimeoutMs != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.ConnectTimeoutMs))
		i--
		dAtA[i] = 0x38
	}
	if len(m.ExtraDsn) > 0 {
		for k := range m.ExtraDsn {
			v := m.ExtraDsn[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintConnections(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintConnections(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintConnections(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Password) > 0 {
		i -= len(m.Password)
		copy(dAtA[i:], m.Password)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Password)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.User) > 0 {
		i -= len(m.User)
		copy(dAtA[i:], m.User)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.User)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Address) > 0 {
		i -= len(m.Address)
		copy(dAtA[i:], m.Address)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Address)))
		i--
		dAtA[i] = 0x1a
	}
	if m.LinkType != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.LinkType))
		i--
		dAtA[i] = 0x10
	}
	if m.Type != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *OpenConnection) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenConnection) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OpenConnection) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IdleTimeoutSeconds != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.IdleTimeoutSeconds))
		i--
		dAtA[i] = 0x20
	}
	if m.Supervisor != nil {
		{
			size, err := m.Supervisor.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintConnections(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintConnections(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *OpenConnectionSuccessful) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenConnectionSuccessful) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OpenConnectionSuccessful) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.OuterConnectionId) > 0 {
		i -= len(m.OuterConnectionId)
		copy(dAtA[i:], m.OuterConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.OuterConnectionId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.CurrentDb) > 0 {
		i -= len(m.CurrentDb)
		copy(dAtA[i:], m.CurrentDb)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.CurrentDb)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *OpenConnectionFailed) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenConnectionFailed) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OpenConnectionFailed) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ErrorMessage) > 0 {
		i -= len(m.ErrorMessage)
		copy(dAtA[i:], m.ErrorMessage)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ErrorMessage)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ConnectionEstablished) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConnectionEstablished) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConnectionEstablished) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.OutterConnectionId) > 0 {
		i -= len(m.OutterConnectionId)
		copy(dAtA[i:], m.OutterConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.OutterConnectionId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ConnectionId) > 0 {
		i -= len(m.ConnectionId)
		copy(dAtA[i:], m.ConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ConnectionId)))
		i--
		dAtA[i] = 0x12
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintConnections(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CloseConnection) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CloseConnection) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CloseConnection) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ConnectionClosed) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConnectionClosed) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConnectionClosed) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ConnectionId) > 0 {
		i -= len(m.ConnectionId)
		copy(dAtA[i:], m.ConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ConnectionId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ErrorMessage) > 0 {
		i -= len(m.ErrorMessage)
		copy(dAtA[i:], m.ErrorMessage)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ErrorMessage)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ForceRollback) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ForceRollback) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ForceRollback) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ForceRollbackResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ForceRollbackResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ForceRollbackResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *Command) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Command) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Command) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SaveMetadb {
		i--
		if m.SaveMetadb {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x40
	}
	if m.TimeoutMs != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.TimeoutMs))
		i--
		dAtA[i] = 0x38
	}
	if m.MaxResultCellBytes != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.MaxResultCellBytes))
		i--
		dAtA[i] = 0x30
	}
	if m.MaxResultBytes != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.MaxResultBytes))
		i--
		dAtA[i] = 0x28
	}
	if m.MaxResultCount != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.MaxResultCount))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Command) > 0 {
		i -= len(m.Command)
		copy(dAtA[i:], m.Command)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Command)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.CommandId) > 0 {
		i -= len(m.CommandId)
		copy(dAtA[i:], m.CommandId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.CommandId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CommandResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LimitInfo) > 0 {
		for iNdEx := len(m.LimitInfo) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.LimitInfo[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintConnections(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x42
		}
	}
	if len(m.ConnectionId) > 0 {
		i -= len(m.ConnectionId)
		copy(dAtA[i:], m.ConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ConnectionId)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Payload) > 0 {
		for iNdEx := len(m.Payload) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Payload[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintConnections(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if m.Type != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x28
	}
	if len(m.ErrorMessage) > 0 {
		i -= len(m.ErrorMessage)
		copy(dAtA[i:], m.ErrorMessage)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ErrorMessage)))
		i--
		dAtA[i] = 0x22
	}
	if m.Code != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x18
	}
	if len(m.CommandId) > 0 {
		i -= len(m.CommandId)
		copy(dAtA[i:], m.CommandId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.CommandId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CommandResultChunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandResultChunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandResultChunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintConnections(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Header) > 0 {
		for iNdEx := len(m.Header) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Header[iNdEx])
			copy(dAtA[i:], m.Header[iNdEx])
			i = encodeVarintConnections(dAtA, i, uint64(len(m.Header[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Offset != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Offset))
		i--
		dAtA[i] = 0x10
	}
	if m.HasMore {
		i--
		if m.HasMore {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CommandResultChunk_Row) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandResultChunk_Row) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandResultChunk_Row) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Cells) > 0 {
		for iNdEx := len(m.Cells) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Cells[iNdEx])
			copy(dAtA[i:], m.Cells[iNdEx])
			i = encodeVarintConnections(dAtA, i, uint64(len(m.Cells[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CommandResultLimitInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandResultLimitInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandResultLimitInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LimitValue != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.LimitValue))
		i--
		dAtA[i] = 0x10
	}
	if m.Type != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ConnectionConfigUpdated) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConnectionConfigUpdated) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConnectionConfigUpdated) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SentryDsn) > 0 {
		i -= len(m.SentryDsn)
		copy(dAtA[i:], m.SentryDsn)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.SentryDsn)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Ping) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Ping) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Ping) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ConnectionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConnectionInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConnectionInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.OuterConnectionId) > 0 {
		i -= len(m.OuterConnectionId)
		copy(dAtA[i:], m.OuterConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.OuterConnectionId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.CurrentDb) > 0 {
		i -= len(m.CurrentDb)
		copy(dAtA[i:], m.CurrentDb)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.CurrentDb)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ConnectionId) > 0 {
		i -= len(m.ConnectionId)
		copy(dAtA[i:], m.ConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ConnectionId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetLocation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLocation) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetLocation) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *Location) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Location) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Location) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.PodIp) > 0 {
		i -= len(m.PodIp)
		copy(dAtA[i:], m.PodIp)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.PodIp)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.HostIp) > 0 {
		i -= len(m.HostIp)
		copy(dAtA[i:], m.HostIp)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.HostIp)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Kind) > 0 {
		i -= len(m.Kind)
		copy(dAtA[i:], m.Kind)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Kind)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Inspect) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Inspect) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Inspect) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ActorStateInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActorStateInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ActorStateInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.State) > 0 {
		i -= len(m.State)
		copy(dAtA[i:], m.State)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.State)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Event) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Event) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Payload) > 0 {
		i -= len(m.Payload)
		copy(dAtA[i:], m.Payload)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Payload)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Pkg) > 0 {
		i -= len(m.Pkg)
		copy(dAtA[i:], m.Pkg)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Pkg)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ListIndexes) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListIndexes) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListIndexes) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Table) > 0 {
		i -= len(m.Table)
		copy(dAtA[i:], m.Table)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Table)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Db) > 0 {
		i -= len(m.Db)
		copy(dAtA[i:], m.Db)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Db)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *IndexInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IndexInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IndexInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Expression) > 0 {
		i -= len(m.Expression)
		copy(dAtA[i:], m.Expression)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Expression)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.Visible) > 0 {
		i -= len(m.Visible)
		copy(dAtA[i:], m.Visible)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Visible)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.IndexComment) > 0 {
		i -= len(m.IndexComment)
		copy(dAtA[i:], m.IndexComment)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.IndexComment)))
		i--
		dAtA[i] = 0x6a
	}
	if len(m.Comment) > 0 {
		i -= len(m.Comment)
		copy(dAtA[i:], m.Comment)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Comment)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.IndexType) > 0 {
		i -= len(m.IndexType)
		copy(dAtA[i:], m.IndexType)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.IndexType)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Null) > 0 {
		i -= len(m.Null)
		copy(dAtA[i:], m.Null)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Null)))
		i--
		dAtA[i] = 0x52
	}
	if m.Packed != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Packed))
		i--
		dAtA[i] = 0x48
	}
	if m.SubPart != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.SubPart))
		i--
		dAtA[i] = 0x40
	}
	if m.Cardinality != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Cardinality))
		i--
		dAtA[i] = 0x38
	}
	if len(m.Collation) > 0 {
		i -= len(m.Collation)
		copy(dAtA[i:], m.Collation)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Collation)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.ColumnName) > 0 {
		i -= len(m.ColumnName)
		copy(dAtA[i:], m.ColumnName)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ColumnName)))
		i--
		dAtA[i] = 0x2a
	}
	if m.SeqInIndex != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.SeqInIndex))
		i--
		dAtA[i] = 0x20
	}
	if len(m.KeyName) > 0 {
		i -= len(m.KeyName)
		copy(dAtA[i:], m.KeyName)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.KeyName)))
		i--
		dAtA[i] = 0x1a
	}
	if m.NonUnique != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.NonUnique))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Table) > 0 {
		i -= len(m.Table)
		copy(dAtA[i:], m.Table)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.Table)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ListIndexesResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListIndexesResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListIndexesResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Indexes) > 0 {
		for iNdEx := len(m.Indexes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Indexes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintConnections(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ListIndexesError) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListIndexesError) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListIndexesError) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ErrorMessage) > 0 {
		i -= len(m.ErrorMessage)
		copy(dAtA[i:], m.ErrorMessage)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ErrorMessage)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegisterShuttleMgr) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegisterShuttleMgr) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegisterShuttleMgr) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.VpcId) > 0 {
		i -= len(m.VpcId)
		copy(dAtA[i:], m.VpcId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.VpcId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ConnectionRestart) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConnectionRestart) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConnectionRestart) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ConnectionId) > 0 {
		i -= len(m.ConnectionId)
		copy(dAtA[i:], m.ConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ConnectionId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ScanCursorResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanCursorResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ScanCursorResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TimeOutMs != 0 {
		i = encodeVarintConnections(dAtA, i, uint64(m.TimeOutMs))
		i--
		dAtA[i] = 0x18
	}
	if len(m.CommandId) > 0 {
		i -= len(m.CommandId)
		copy(dAtA[i:], m.CommandId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.CommandId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ConnectionId) > 0 {
		i -= len(m.ConnectionId)
		copy(dAtA[i:], m.ConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ConnectionId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *KeepAliveProbe) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeepAliveProbe) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KeepAliveProbe) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ClearCursor) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearCursor) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ClearCursor) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ErrScanCursorResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ErrScanCursorResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ErrScanCursorResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ErrorMessage) > 0 {
		i -= len(m.ErrorMessage)
		copy(dAtA[i:], m.ErrorMessage)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ErrorMessage)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.CommandId) > 0 {
		i -= len(m.CommandId)
		copy(dAtA[i:], m.CommandId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.CommandId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ConnectionId) > 0 {
		i -= len(m.ConnectionId)
		copy(dAtA[i:], m.ConnectionId)
		i = encodeVarintConnections(dAtA, i, uint64(len(m.ConnectionId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintConnections(dAtA []byte, offset int, v uint64) int {
	offset -= sovConnections(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ActorRef) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Kind)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *DataSource) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovConnections(uint64(m.Type))
	}
	if m.LinkType != 0 {
		n += 1 + sovConnections(uint64(m.LinkType))
	}
	l = len(m.Address)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.User)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Password)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if len(m.ExtraDsn) > 0 {
		for k, v := range m.ExtraDsn {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovConnections(uint64(len(k))) + 1 + len(v) + sovConnections(uint64(len(v)))
			n += mapEntrySize + 1 + sovConnections(uint64(mapEntrySize))
		}
	}
	if m.ConnectTimeoutMs != 0 {
		n += 1 + sovConnections(uint64(m.ConnectTimeoutMs))
	}
	if m.ReadTimeoutMs != 0 {
		n += 1 + sovConnections(uint64(m.ReadTimeoutMs))
	}
	if m.WriteTimeoutMs != 0 {
		n += 1 + sovConnections(uint64(m.WriteTimeoutMs))
	}
	l = len(m.Db)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.IdleTimeoutMs != 0 {
		n += 1 + sovConnections(uint64(m.IdleTimeoutMs))
	}
	l = len(m.InstanceId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.CadidateAddress)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.VpcID)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.MaxOpenConns != 0 {
		n += 1 + sovConnections(uint64(m.MaxOpenConns))
	}
	if m.MaxIdleConns != 0 {
		n += 2 + sovConnections(uint64(m.MaxIdleConns))
	}
	l = len(m.MongoNodeId)
	if l > 0 {
		n += 2 + l + sovConnections(uint64(l))
	}
	l = len(m.NodeId)
	if l > 0 {
		n += 2 + l + sovConnections(uint64(l))
	}
	l = len(m.AuthDb)
	if l > 0 {
		n += 2 + l + sovConnections(uint64(l))
	}
	if m.EndpointRole != 0 {
		n += 2 + sovConnections(uint64(m.EndpointRole))
	}
	l = len(m.Region)
	if l > 0 {
		n += 2 + l + sovConnections(uint64(l))
	}
	l = len(m.Psm)
	if l > 0 {
		n += 2 + l + sovConnections(uint64(l))
	}
	l = len(m.Gdid)
	if l > 0 {
		n += 2 + l + sovConnections(uint64(l))
	}
	l = len(m.Tunnel)
	if l > 0 {
		n += 2 + l + sovConnections(uint64(l))
	}
	l = len(m.BranchId)
	if l > 0 {
		n += 2 + l + sovConnections(uint64(l))
	}
	if m.DbEngine != 0 {
		n += 2 + sovConnections(uint64(m.DbEngine))
	}
	return n
}

func (m *OpenConnection) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.Supervisor != nil {
		l = m.Supervisor.Size()
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.IdleTimeoutSeconds != 0 {
		n += 1 + sovConnections(uint64(m.IdleTimeoutSeconds))
	}
	return n
}

func (m *OpenConnectionSuccessful) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovConnections(uint64(m.Code))
	}
	l = len(m.CurrentDb)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.OuterConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *OpenConnectionFailed) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovConnections(uint64(m.Code))
	}
	l = len(m.ErrorMessage)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *ConnectionEstablished) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.ConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.OutterConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *CloseConnection) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ConnectionClosed) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovConnections(uint64(m.Code))
	}
	l = len(m.ErrorMessage)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.ConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *ForceRollback) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *ForceRollbackResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *Command) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.CommandId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Command)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.MaxResultCount != 0 {
		n += 1 + sovConnections(uint64(m.MaxResultCount))
	}
	if m.MaxResultBytes != 0 {
		n += 1 + sovConnections(uint64(m.MaxResultBytes))
	}
	if m.MaxResultCellBytes != 0 {
		n += 1 + sovConnections(uint64(m.MaxResultCellBytes))
	}
	if m.TimeoutMs != 0 {
		n += 1 + sovConnections(uint64(m.TimeoutMs))
	}
	if m.SaveMetadb {
		n += 2
	}
	return n
}

func (m *CommandResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.CommandId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.Code != 0 {
		n += 1 + sovConnections(uint64(m.Code))
	}
	l = len(m.ErrorMessage)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.Type != 0 {
		n += 1 + sovConnections(uint64(m.Type))
	}
	if len(m.Payload) > 0 {
		for _, e := range m.Payload {
			l = e.Size()
			n += 1 + l + sovConnections(uint64(l))
		}
	}
	l = len(m.ConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if len(m.LimitInfo) > 0 {
		for _, e := range m.LimitInfo {
			l = e.Size()
			n += 1 + l + sovConnections(uint64(l))
		}
	}
	return n
}

func (m *CommandResultChunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HasMore {
		n += 2
	}
	if m.Offset != 0 {
		n += 1 + sovConnections(uint64(m.Offset))
	}
	if len(m.Header) > 0 {
		for _, s := range m.Header {
			l = len(s)
			n += 1 + l + sovConnections(uint64(l))
		}
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovConnections(uint64(l))
		}
	}
	return n
}

func (m *CommandResultChunk_Row) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Cells) > 0 {
		for _, s := range m.Cells {
			l = len(s)
			n += 1 + l + sovConnections(uint64(l))
		}
	}
	return n
}

func (m *CommandResultLimitInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovConnections(uint64(m.Type))
	}
	if m.LimitValue != 0 {
		n += 1 + sovConnections(uint64(m.LimitValue))
	}
	return n
}

func (m *ConnectionConfigUpdated) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SentryDsn)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *Ping) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *ConnectionInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.CurrentDb)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.OuterConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *GetLocation) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *Location) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Kind)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.HostIp)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.PodIp)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *Inspect) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ActorStateInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.State)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *Event) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Pkg)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Payload)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *ListIndexes) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Db)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Table)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *IndexInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Table)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.NonUnique != 0 {
		n += 1 + sovConnections(uint64(m.NonUnique))
	}
	l = len(m.KeyName)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.SeqInIndex != 0 {
		n += 1 + sovConnections(uint64(m.SeqInIndex))
	}
	l = len(m.ColumnName)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Collation)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.Cardinality != 0 {
		n += 1 + sovConnections(uint64(m.Cardinality))
	}
	if m.SubPart != 0 {
		n += 1 + sovConnections(uint64(m.SubPart))
	}
	if m.Packed != 0 {
		n += 1 + sovConnections(uint64(m.Packed))
	}
	l = len(m.Null)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.IndexType)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Comment)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.IndexComment)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Visible)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.Expression)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *ListIndexesResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Indexes) > 0 {
		for _, e := range m.Indexes {
			l = e.Size()
			n += 1 + l + sovConnections(uint64(l))
		}
	}
	return n
}

func (m *ListIndexesError) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovConnections(uint64(m.Code))
	}
	l = len(m.ErrorMessage)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *RegisterShuttleMgr) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.VpcId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *ConnectionRestart) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.ConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func (m *ScanCursorResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.CommandId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	if m.TimeOutMs != 0 {
		n += 1 + sovConnections(uint64(m.TimeOutMs))
	}
	return n
}

func (m *KeepAliveProbe) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ClearCursor) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ErrScanCursorResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ConnectionId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.CommandId)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	l = len(m.ErrorMessage)
	if l > 0 {
		n += 1 + l + sovConnections(uint64(l))
	}
	return n
}

func sovConnections(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozConnections(x uint64) (n int) {
	return sovConnections(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *ActorRef) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ActorRef{`,
		`Kind:` + fmt.Sprintf("%v", this.Kind) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DataSource) String() string {
	if this == nil {
		return "nil"
	}
	keysForExtraDsn := make([]string, 0, len(this.ExtraDsn))
	for k, _ := range this.ExtraDsn {
		keysForExtraDsn = append(keysForExtraDsn, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForExtraDsn)
	mapStringForExtraDsn := "map[string]string{"
	for _, k := range keysForExtraDsn {
		mapStringForExtraDsn += fmt.Sprintf("%v: %v,", k, this.ExtraDsn[k])
	}
	mapStringForExtraDsn += "}"
	s := strings.Join([]string{`&DataSource{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`LinkType:` + fmt.Sprintf("%v", this.LinkType) + `,`,
		`Address:` + fmt.Sprintf("%v", this.Address) + `,`,
		`User:` + fmt.Sprintf("%v", this.User) + `,`,
		`Password:` + fmt.Sprintf("%v", this.Password) + `,`,
		`ExtraDsn:` + mapStringForExtraDsn + `,`,
		`ConnectTimeoutMs:` + fmt.Sprintf("%v", this.ConnectTimeoutMs) + `,`,
		`ReadTimeoutMs:` + fmt.Sprintf("%v", this.ReadTimeoutMs) + `,`,
		`WriteTimeoutMs:` + fmt.Sprintf("%v", this.WriteTimeoutMs) + `,`,
		`Db:` + fmt.Sprintf("%v", this.Db) + `,`,
		`IdleTimeoutMs:` + fmt.Sprintf("%v", this.IdleTimeoutMs) + `,`,
		`InstanceId:` + fmt.Sprintf("%v", this.InstanceId) + `,`,
		`CadidateAddress:` + fmt.Sprintf("%v", this.CadidateAddress) + `,`,
		`VpcID:` + fmt.Sprintf("%v", this.VpcID) + `,`,
		`MaxOpenConns:` + fmt.Sprintf("%v", this.MaxOpenConns) + `,`,
		`MaxIdleConns:` + fmt.Sprintf("%v", this.MaxIdleConns) + `,`,
		`MongoNodeId:` + fmt.Sprintf("%v", this.MongoNodeId) + `,`,
		`NodeId:` + fmt.Sprintf("%v", this.NodeId) + `,`,
		`AuthDb:` + fmt.Sprintf("%v", this.AuthDb) + `,`,
		`EndpointRole:` + fmt.Sprintf("%v", this.EndpointRole) + `,`,
		`Region:` + fmt.Sprintf("%v", this.Region) + `,`,
		`Psm:` + fmt.Sprintf("%v", this.Psm) + `,`,
		`Gdid:` + fmt.Sprintf("%v", this.Gdid) + `,`,
		`Tunnel:` + fmt.Sprintf("%v", this.Tunnel) + `,`,
		`BranchId:` + fmt.Sprintf("%v", this.BranchId) + `,`,
		`DbEngine:` + fmt.Sprintf("%v", this.DbEngine) + `,`,
		`}`,
	}, "")
	return s
}
func (this *OpenConnection) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&OpenConnection{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`Source:` + strings.Replace(this.Source.String(), "DataSource", "DataSource", 1) + `,`,
		`Supervisor:` + strings.Replace(this.Supervisor.String(), "ActorRef", "ActorRef", 1) + `,`,
		`IdleTimeoutSeconds:` + fmt.Sprintf("%v", this.IdleTimeoutSeconds) + `,`,
		`}`,
	}, "")
	return s
}
func (this *OpenConnectionSuccessful) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&OpenConnectionSuccessful{`,
		`Code:` + fmt.Sprintf("%v", this.Code) + `,`,
		`CurrentDb:` + fmt.Sprintf("%v", this.CurrentDb) + `,`,
		`OuterConnectionId:` + fmt.Sprintf("%v", this.OuterConnectionId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *OpenConnectionFailed) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&OpenConnectionFailed{`,
		`Code:` + fmt.Sprintf("%v", this.Code) + `,`,
		`ErrorMessage:` + fmt.Sprintf("%v", this.ErrorMessage) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ConnectionEstablished) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ConnectionEstablished{`,
		`Source:` + strings.Replace(this.Source.String(), "DataSource", "DataSource", 1) + `,`,
		`ConnectionId:` + fmt.Sprintf("%v", this.ConnectionId) + `,`,
		`OutterConnectionId:` + fmt.Sprintf("%v", this.OutterConnectionId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CloseConnection) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CloseConnection{`,
		`}`,
	}, "")
	return s
}
func (this *ConnectionClosed) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ConnectionClosed{`,
		`Code:` + fmt.Sprintf("%v", this.Code) + `,`,
		`ErrorMessage:` + fmt.Sprintf("%v", this.ErrorMessage) + `,`,
		`ConnectionId:` + fmt.Sprintf("%v", this.ConnectionId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ForceRollback) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ForceRollback{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ForceRollbackResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ForceRollbackResp{`,
		`}`,
	}, "")
	return s
}
func (this *Command) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Command{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`CommandId:` + fmt.Sprintf("%v", this.CommandId) + `,`,
		`Command:` + fmt.Sprintf("%v", this.Command) + `,`,
		`MaxResultCount:` + fmt.Sprintf("%v", this.MaxResultCount) + `,`,
		`MaxResultBytes:` + fmt.Sprintf("%v", this.MaxResultBytes) + `,`,
		`MaxResultCellBytes:` + fmt.Sprintf("%v", this.MaxResultCellBytes) + `,`,
		`TimeoutMs:` + fmt.Sprintf("%v", this.TimeoutMs) + `,`,
		`SaveMetadb:` + fmt.Sprintf("%v", this.SaveMetadb) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CommandResult) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForPayload := "[]*CommandResultChunk{"
	for _, f := range this.Payload {
		repeatedStringForPayload += strings.Replace(f.String(), "CommandResultChunk", "CommandResultChunk", 1) + ","
	}
	repeatedStringForPayload += "}"
	repeatedStringForLimitInfo := "[]*CommandResultLimitInfo{"
	for _, f := range this.LimitInfo {
		repeatedStringForLimitInfo += strings.Replace(f.String(), "CommandResultLimitInfo", "CommandResultLimitInfo", 1) + ","
	}
	repeatedStringForLimitInfo += "}"
	s := strings.Join([]string{`&CommandResult{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`CommandId:` + fmt.Sprintf("%v", this.CommandId) + `,`,
		`Code:` + fmt.Sprintf("%v", this.Code) + `,`,
		`ErrorMessage:` + fmt.Sprintf("%v", this.ErrorMessage) + `,`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`Payload:` + repeatedStringForPayload + `,`,
		`ConnectionId:` + fmt.Sprintf("%v", this.ConnectionId) + `,`,
		`LimitInfo:` + repeatedStringForLimitInfo + `,`,
		`}`,
	}, "")
	return s
}
func (this *CommandResultChunk) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRows := "[]*CommandResultChunk_Row{"
	for _, f := range this.Rows {
		repeatedStringForRows += strings.Replace(fmt.Sprintf("%v", f), "CommandResultChunk_Row", "CommandResultChunk_Row", 1) + ","
	}
	repeatedStringForRows += "}"
	s := strings.Join([]string{`&CommandResultChunk{`,
		`HasMore:` + fmt.Sprintf("%v", this.HasMore) + `,`,
		`Offset:` + fmt.Sprintf("%v", this.Offset) + `,`,
		`Header:` + fmt.Sprintf("%v", this.Header) + `,`,
		`Rows:` + repeatedStringForRows + `,`,
		`}`,
	}, "")
	return s
}
func (this *CommandResultChunk_Row) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CommandResultChunk_Row{`,
		`Cells:` + fmt.Sprintf("%v", this.Cells) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CommandResultLimitInfo) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CommandResultLimitInfo{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`LimitValue:` + fmt.Sprintf("%v", this.LimitValue) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ConnectionConfigUpdated) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ConnectionConfigUpdated{`,
		`SentryDsn:` + fmt.Sprintf("%v", this.SentryDsn) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Ping) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Ping{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ConnectionInfo) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ConnectionInfo{`,
		`ConnectionId:` + fmt.Sprintf("%v", this.ConnectionId) + `,`,
		`CurrentDb:` + fmt.Sprintf("%v", this.CurrentDb) + `,`,
		`OuterConnectionId:` + fmt.Sprintf("%v", this.OuterConnectionId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetLocation) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&GetLocation{`,
		`}`,
	}, "")
	return s
}
func (this *Location) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Location{`,
		`Kind:` + fmt.Sprintf("%v", this.Kind) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`HostIp:` + fmt.Sprintf("%v", this.HostIp) + `,`,
		`PodIp:` + fmt.Sprintf("%v", this.PodIp) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Inspect) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Inspect{`,
		`}`,
	}, "")
	return s
}
func (this *ActorStateInfo) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ActorStateInfo{`,
		`State:` + fmt.Sprintf("%v", this.State) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Event) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Event{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`Pkg:` + fmt.Sprintf("%v", this.Pkg) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Payload:` + fmt.Sprintf("%v", this.Payload) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ListIndexes) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ListIndexes{`,
		`Db:` + fmt.Sprintf("%v", this.Db) + `,`,
		`Table:` + fmt.Sprintf("%v", this.Table) + `,`,
		`}`,
	}, "")
	return s
}
func (this *IndexInfo) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&IndexInfo{`,
		`Table:` + fmt.Sprintf("%v", this.Table) + `,`,
		`NonUnique:` + fmt.Sprintf("%v", this.NonUnique) + `,`,
		`KeyName:` + fmt.Sprintf("%v", this.KeyName) + `,`,
		`SeqInIndex:` + fmt.Sprintf("%v", this.SeqInIndex) + `,`,
		`ColumnName:` + fmt.Sprintf("%v", this.ColumnName) + `,`,
		`Collation:` + fmt.Sprintf("%v", this.Collation) + `,`,
		`Cardinality:` + fmt.Sprintf("%v", this.Cardinality) + `,`,
		`SubPart:` + fmt.Sprintf("%v", this.SubPart) + `,`,
		`Packed:` + fmt.Sprintf("%v", this.Packed) + `,`,
		`Null:` + fmt.Sprintf("%v", this.Null) + `,`,
		`IndexType:` + fmt.Sprintf("%v", this.IndexType) + `,`,
		`Comment:` + fmt.Sprintf("%v", this.Comment) + `,`,
		`IndexComment:` + fmt.Sprintf("%v", this.IndexComment) + `,`,
		`Visible:` + fmt.Sprintf("%v", this.Visible) + `,`,
		`Expression:` + fmt.Sprintf("%v", this.Expression) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ListIndexesResult) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForIndexes := "[]*IndexInfo{"
	for _, f := range this.Indexes {
		repeatedStringForIndexes += strings.Replace(f.String(), "IndexInfo", "IndexInfo", 1) + ","
	}
	repeatedStringForIndexes += "}"
	s := strings.Join([]string{`&ListIndexesResult{`,
		`Indexes:` + repeatedStringForIndexes + `,`,
		`}`,
	}, "")
	return s
}
func (this *ListIndexesError) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ListIndexesError{`,
		`Code:` + fmt.Sprintf("%v", this.Code) + `,`,
		`ErrorMessage:` + fmt.Sprintf("%v", this.ErrorMessage) + `,`,
		`}`,
	}, "")
	return s
}
func (this *RegisterShuttleMgr) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&RegisterShuttleMgr{`,
		`VpcId:` + fmt.Sprintf("%v", this.VpcId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ConnectionRestart) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ConnectionRestart{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`ConnectionId:` + fmt.Sprintf("%v", this.ConnectionId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ScanCursorResult) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ScanCursorResult{`,
		`ConnectionId:` + fmt.Sprintf("%v", this.ConnectionId) + `,`,
		`CommandId:` + fmt.Sprintf("%v", this.CommandId) + `,`,
		`TimeOutMs:` + fmt.Sprintf("%v", this.TimeOutMs) + `,`,
		`}`,
	}, "")
	return s
}
func (this *KeepAliveProbe) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&KeepAliveProbe{`,
		`}`,
	}, "")
	return s
}
func (this *ClearCursor) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ClearCursor{`,
		`}`,
	}, "")
	return s
}
func (this *ErrScanCursorResult) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ErrScanCursorResult{`,
		`ConnectionId:` + fmt.Sprintf("%v", this.ConnectionId) + `,`,
		`CommandId:` + fmt.Sprintf("%v", this.CommandId) + `,`,
		`ErrorMessage:` + fmt.Sprintf("%v", this.ErrorMessage) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringConnections(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *ActorRef) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ActorRef: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ActorRef: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Kind", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Kind = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DataSource) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DataSource: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DataSource: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LinkType", wireType)
			}
			m.LinkType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LinkType |= LinkType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Address = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field User", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.User = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExtraDsn", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ExtraDsn == nil {
				m.ExtraDsn = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowConnections
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowConnections
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthConnections
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthConnections
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowConnections
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthConnections
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthConnections
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipConnections(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthConnections
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.ExtraDsn[mapkey] = mapvalue
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectTimeoutMs", wireType)
			}
			m.ConnectTimeoutMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConnectTimeoutMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadTimeoutMs", wireType)
			}
			m.ReadTimeoutMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReadTimeoutMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WriteTimeoutMs", wireType)
			}
			m.WriteTimeoutMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WriteTimeoutMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Db", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Db = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IdleTimeoutMs", wireType)
			}
			m.IdleTimeoutMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IdleTimeoutMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CadidateAddress", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CadidateAddress = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VpcID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.VpcID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxOpenConns", wireType)
			}
			m.MaxOpenConns = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxOpenConns |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxIdleConns", wireType)
			}
			m.MaxIdleConns = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxIdleConns |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MongoNodeId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MongoNodeId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NodeId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.NodeId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AuthDb", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AuthDb = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndpointRole", wireType)
			}
			m.EndpointRole = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndpointRole |= EndpointType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Region = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Psm", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Psm = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Gdid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Gdid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tunnel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tunnel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BranchId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BranchId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 27:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DbEngine", wireType)
			}
			m.DbEngine = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DbEngine |= DBEngine(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenConnection) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OpenConnection: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OpenConnection: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Supervisor", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Supervisor == nil {
				m.Supervisor = &ActorRef{}
			}
			if err := m.Supervisor.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IdleTimeoutSeconds", wireType)
			}
			m.IdleTimeoutSeconds = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IdleTimeoutSeconds |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenConnectionSuccessful) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OpenConnectionSuccessful: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OpenConnectionSuccessful: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= ConnectionError(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentDb", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CurrentDb = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OuterConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OuterConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenConnectionFailed) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OpenConnectionFailed: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OpenConnectionFailed: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= ConnectionError(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrorMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConnectionEstablished) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConnectionEstablished: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConnectionEstablished: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OutterConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutterConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CloseConnection) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CloseConnection: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CloseConnection: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConnectionClosed) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConnectionClosed: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConnectionClosed: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= ConnectionError(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrorMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ForceRollback) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ForceRollback: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ForceRollback: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ForceRollbackResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ForceRollbackResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ForceRollbackResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Command) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Command: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Command: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommandId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommandId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Command", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Command = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxResultCount", wireType)
			}
			m.MaxResultCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxResultCount |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxResultBytes", wireType)
			}
			m.MaxResultBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxResultBytes |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxResultCellBytes", wireType)
			}
			m.MaxResultCellBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxResultCellBytes |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeoutMs", wireType)
			}
			m.TimeoutMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeoutMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SaveMetadb", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SaveMetadb = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommandId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommandId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= CommandError(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrorMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= CommandResultType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Payload", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Payload = append(m.Payload, &CommandResultChunk{})
			if err := m.Payload[len(m.Payload)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LimitInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LimitInfo = append(m.LimitInfo, &CommandResultLimitInfo{})
			if err := m.LimitInfo[len(m.LimitInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandResultChunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandResultChunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandResultChunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HasMore", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasMore = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Header = append(m.Header, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &CommandResultChunk_Row{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandResultChunk_Row) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Row: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Row: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cells", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cells = append(m.Cells, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandResultLimitInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandResultLimitInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandResultLimitInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= LimitType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LimitValue", wireType)
			}
			m.LimitValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitValue |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConnectionConfigUpdated) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConnectionConfigUpdated: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConnectionConfigUpdated: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SentryDsn", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SentryDsn = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Ping) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Ping: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Ping: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConnectionInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConnectionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConnectionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentDb", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CurrentDb = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OuterConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OuterConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLocation) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetLocation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetLocation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Location) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Location: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Location: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Kind", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Kind = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HostIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HostIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PodIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PodIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Inspect) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Inspect: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Inspect: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActorStateInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ActorStateInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ActorStateInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.State = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Event) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Event: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Event: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pkg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Payload", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Payload = append(m.Payload[:0], dAtA[iNdEx:postIndex]...)
			if m.Payload == nil {
				m.Payload = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListIndexes) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListIndexes: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListIndexes: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Db", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Db = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Table", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Table = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IndexInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IndexInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IndexInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Table", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Table = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NonUnique", wireType)
			}
			m.NonUnique = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NonUnique |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeyName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SeqInIndex", wireType)
			}
			m.SeqInIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqInIndex |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ColumnName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ColumnName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Collation", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Collation = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cardinality", wireType)
			}
			m.Cardinality = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cardinality |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SubPart", wireType)
			}
			m.SubPart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubPart |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Packed", wireType)
			}
			m.Packed = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Packed |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Null", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Null = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IndexType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IndexType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Comment = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IndexComment", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IndexComment = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Visible", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Visible = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Expression", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Expression = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListIndexesResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListIndexesResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListIndexesResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Indexes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Indexes = append(m.Indexes, &IndexInfo{})
			if err := m.Indexes[len(m.Indexes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListIndexesError) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListIndexesError: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListIndexesError: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= CommandError(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrorMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegisterShuttleMgr) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegisterShuttleMgr: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegisterShuttleMgr: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VpcId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.VpcId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConnectionRestart) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConnectionRestart: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConnectionRestart: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanCursorResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScanCursorResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScanCursorResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommandId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommandId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeOutMs", wireType)
			}
			m.TimeOutMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeOutMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KeepAliveProbe) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeepAliveProbe: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeepAliveProbe: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearCursor) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ClearCursor: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ClearCursor: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ErrScanCursorResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ErrScanCursorResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ErrScanCursorResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommandId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommandId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConnections
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConnections
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrorMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConnections(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConnections
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipConnections(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowConnections
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowConnections
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthConnections
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupConnections
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthConnections
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthConnections        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowConnections          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupConnections = fmt.Errorf("proto: unexpected end of group")
)

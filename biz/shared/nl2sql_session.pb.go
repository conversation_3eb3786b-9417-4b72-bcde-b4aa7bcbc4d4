// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: nl2sql_session.proto

package shared

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type SessionCacheKey struct {
	TenantID   string `protobuf:"bytes,1,opt,name=TenantID,proto3" json:"TenantID,omitempty"`
	UserID     string `protobuf:"bytes,2,opt,name=UserID,proto3" json:"UserID,omitempty"`
	InstanceID string `protobuf:"bytes,3,opt,name=InstanceID,proto3" json:"InstanceID,omitempty"`
}

func (m *SessionCacheKey) Reset()      { *m = SessionCacheKey{} }
func (*SessionCacheKey) ProtoMessage() {}
func (*SessionCacheKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_323c2e333df38005, []int{0}
}
func (m *SessionCacheKey) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SessionCacheKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SessionCacheKey.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SessionCacheKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SessionCacheKey.Merge(m, src)
}
func (m *SessionCacheKey) XXX_Size() int {
	return m.Size()
}
func (m *SessionCacheKey) XXX_DiscardUnknown() {
	xxx_messageInfo_SessionCacheKey.DiscardUnknown(m)
}

var xxx_messageInfo_SessionCacheKey proto.InternalMessageInfo

func (m *SessionCacheKey) GetTenantID() string {
	if m != nil {
		return m.TenantID
	}
	return ""
}

func (m *SessionCacheKey) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *SessionCacheKey) GetInstanceID() string {
	if m != nil {
		return m.InstanceID
	}
	return ""
}

type SessionCacheValue struct {
	SessionID           string `protobuf:"bytes,1,opt,name=SessionID,proto3" json:"SessionID,omitempty"`
	DefaultConnectionID string `protobuf:"bytes,2,opt,name=DefaultConnectionID,proto3" json:"DefaultConnectionID,omitempty"`
}

func (m *SessionCacheValue) Reset()      { *m = SessionCacheValue{} }
func (*SessionCacheValue) ProtoMessage() {}
func (*SessionCacheValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_323c2e333df38005, []int{1}
}
func (m *SessionCacheValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SessionCacheValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SessionCacheValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SessionCacheValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SessionCacheValue.Merge(m, src)
}
func (m *SessionCacheValue) XXX_Size() int {
	return m.Size()
}
func (m *SessionCacheValue) XXX_DiscardUnknown() {
	xxx_messageInfo_SessionCacheValue.DiscardUnknown(m)
}

var xxx_messageInfo_SessionCacheValue proto.InternalMessageInfo

func (m *SessionCacheValue) GetSessionID() string {
	if m != nil {
		return m.SessionID
	}
	return ""
}

func (m *SessionCacheValue) GetDefaultConnectionID() string {
	if m != nil {
		return m.DefaultConnectionID
	}
	return ""
}

type CreateNL2SQLSessionReq struct {
	NL2SQLSessionCacheKey *SessionCacheKey `protobuf:"bytes,1,opt,name=NL2SQLSessionCacheKey,proto3" json:"NL2SQLSessionCacheKey,omitempty"`
	SessionID             string           `protobuf:"bytes,2,opt,name=SessionID,proto3" json:"SessionID,omitempty"`
}

func (m *CreateNL2SQLSessionReq) Reset()      { *m = CreateNL2SQLSessionReq{} }
func (*CreateNL2SQLSessionReq) ProtoMessage() {}
func (*CreateNL2SQLSessionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_323c2e333df38005, []int{2}
}
func (m *CreateNL2SQLSessionReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateNL2SQLSessionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateNL2SQLSessionReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateNL2SQLSessionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateNL2SQLSessionReq.Merge(m, src)
}
func (m *CreateNL2SQLSessionReq) XXX_Size() int {
	return m.Size()
}
func (m *CreateNL2SQLSessionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateNL2SQLSessionReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateNL2SQLSessionReq proto.InternalMessageInfo

func (m *CreateNL2SQLSessionReq) GetNL2SQLSessionCacheKey() *SessionCacheKey {
	if m != nil {
		return m.NL2SQLSessionCacheKey
	}
	return nil
}

func (m *CreateNL2SQLSessionReq) GetSessionID() string {
	if m != nil {
		return m.SessionID
	}
	return ""
}

type CreateNL2SQLSessionResp struct {
	IsCached bool `protobuf:"varint,1,opt,name=IsCached,proto3" json:"IsCached,omitempty"`
}

func (m *CreateNL2SQLSessionResp) Reset()      { *m = CreateNL2SQLSessionResp{} }
func (*CreateNL2SQLSessionResp) ProtoMessage() {}
func (*CreateNL2SQLSessionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_323c2e333df38005, []int{3}
}
func (m *CreateNL2SQLSessionResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateNL2SQLSessionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateNL2SQLSessionResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateNL2SQLSessionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateNL2SQLSessionResp.Merge(m, src)
}
func (m *CreateNL2SQLSessionResp) XXX_Size() int {
	return m.Size()
}
func (m *CreateNL2SQLSessionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateNL2SQLSessionResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateNL2SQLSessionResp proto.InternalMessageInfo

func (m *CreateNL2SQLSessionResp) GetIsCached() bool {
	if m != nil {
		return m.IsCached
	}
	return false
}

type DescribeNL2SQLSessionReq struct {
	NL2SQLSessionCacheKey *SessionCacheKey `protobuf:"bytes,1,opt,name=NL2SQLSessionCacheKey,proto3" json:"NL2SQLSessionCacheKey,omitempty"`
	BizContext            string           `protobuf:"bytes,4,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *DescribeNL2SQLSessionReq) Reset()      { *m = DescribeNL2SQLSessionReq{} }
func (*DescribeNL2SQLSessionReq) ProtoMessage() {}
func (*DescribeNL2SQLSessionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_323c2e333df38005, []int{4}
}
func (m *DescribeNL2SQLSessionReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeNL2SQLSessionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeNL2SQLSessionReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeNL2SQLSessionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeNL2SQLSessionReq.Merge(m, src)
}
func (m *DescribeNL2SQLSessionReq) XXX_Size() int {
	return m.Size()
}
func (m *DescribeNL2SQLSessionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeNL2SQLSessionReq.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeNL2SQLSessionReq proto.InternalMessageInfo

func (m *DescribeNL2SQLSessionReq) GetNL2SQLSessionCacheKey() *SessionCacheKey {
	if m != nil {
		return m.NL2SQLSessionCacheKey
	}
	return nil
}

func (m *DescribeNL2SQLSessionReq) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

type DescribeNL2SQLSessionResp struct {
	SessionID string `protobuf:"bytes,1,opt,name=SessionID,proto3" json:"SessionID,omitempty"`
}

func (m *DescribeNL2SQLSessionResp) Reset()      { *m = DescribeNL2SQLSessionResp{} }
func (*DescribeNL2SQLSessionResp) ProtoMessage() {}
func (*DescribeNL2SQLSessionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_323c2e333df38005, []int{5}
}
func (m *DescribeNL2SQLSessionResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeNL2SQLSessionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeNL2SQLSessionResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeNL2SQLSessionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeNL2SQLSessionResp.Merge(m, src)
}
func (m *DescribeNL2SQLSessionResp) XXX_Size() int {
	return m.Size()
}
func (m *DescribeNL2SQLSessionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeNL2SQLSessionResp.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeNL2SQLSessionResp proto.InternalMessageInfo

func (m *DescribeNL2SQLSessionResp) GetSessionID() string {
	if m != nil {
		return m.SessionID
	}
	return ""
}

func init() {
	proto.RegisterType((*SessionCacheKey)(nil), "shared.SessionCacheKey")
	proto.RegisterType((*SessionCacheValue)(nil), "shared.SessionCacheValue")
	proto.RegisterType((*CreateNL2SQLSessionReq)(nil), "shared.CreateNL2SQLSessionReq")
	proto.RegisterType((*CreateNL2SQLSessionResp)(nil), "shared.CreateNL2SQLSessionResp")
	proto.RegisterType((*DescribeNL2SQLSessionReq)(nil), "shared.DescribeNL2SQLSessionReq")
	proto.RegisterType((*DescribeNL2SQLSessionResp)(nil), "shared.DescribeNL2SQLSessionResp")
}

func init() { proto.RegisterFile("nl2sql_session.proto", fileDescriptor_323c2e333df38005) }

var fileDescriptor_323c2e333df38005 = []byte{
	// 352 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x52, 0xbf, 0x4f, 0xf2, 0x50,
	0x14, 0xed, 0xe3, 0xfb, 0x42, 0xe0, 0x32, 0x7c, 0xf9, 0xaa, 0x42, 0x35, 0xe6, 0x6a, 0x3a, 0x39,
	0x11, 0x83, 0x3a, 0xb8, 0xda, 0x2e, 0x8d, 0x68, 0x62, 0x51, 0x57, 0xd2, 0x96, 0x6b, 0x68, 0xd2,
	0xbc, 0x42, 0xdf, 0x23, 0x51, 0x26, 0x17, 0x17, 0x27, 0xff, 0x0c, 0xff, 0x14, 0x47, 0x46, 0x46,
	0x79, 0x2c, 0x8e, 0xfc, 0x09, 0xc6, 0x16, 0x11, 0x08, 0xba, 0x39, 0x9e, 0x73, 0xde, 0xbb, 0xe7,
	0xdc, 0x1f, 0xb0, 0xce, 0xa3, 0x9a, 0xe8, 0x46, 0x4d, 0x41, 0x42, 0x84, 0x31, 0xaf, 0x76, 0x92,
	0x58, 0xc6, 0x7a, 0x5e, 0xb4, 0xbd, 0x84, 0x5a, 0x26, 0xc1, 0xbf, 0x46, 0x26, 0x58, 0x5e, 0xd0,
	0xa6, 0x53, 0xba, 0xd3, 0xb7, 0xa0, 0x70, 0x49, 0xdc, 0xe3, 0xd2, 0xb1, 0x0d, 0xb6, 0xcb, 0xf6,
	0x8a, 0xee, 0x0c, 0xeb, 0x65, 0xc8, 0x5f, 0x09, 0x4a, 0x1c, 0xdb, 0xc8, 0xa5, 0xca, 0x14, 0xe9,
	0x08, 0xe0, 0x70, 0x21, 0x3d, 0x1e, 0x90, 0x63, 0x1b, 0x7f, 0x52, 0x6d, 0x8e, 0x31, 0x03, 0xf8,
	0x3f, 0x6f, 0x73, 0xed, 0x45, 0x3d, 0xd2, 0xb7, 0xa1, 0x38, 0x25, 0x67, 0x4e, 0x5f, 0x84, 0xbe,
	0x0f, 0x6b, 0x36, 0xdd, 0x78, 0xbd, 0x48, 0x5a, 0x31, 0xe7, 0x14, 0xc8, 0xec, 0x5d, 0xe6, 0xbb,
	0x4a, 0x32, 0x1f, 0x18, 0x94, 0xad, 0x84, 0x3c, 0x49, 0xe7, 0xf5, 0x5a, 0xe3, 0xa2, 0x3e, 0xad,
	0xe5, 0x52, 0x57, 0x3f, 0x83, 0x8d, 0x05, 0xee, 0xb3, 0xd9, 0xd4, 0xb6, 0x54, 0xab, 0x54, 0xb3,
	0x71, 0x54, 0x97, 0x64, 0x77, 0xf5, 0xaf, 0xc5, 0xe4, 0xb9, 0xa5, 0xe4, 0xe6, 0x11, 0x54, 0x56,
	0xc6, 0x10, 0x9d, 0x8f, 0xd9, 0x3a, 0x22, 0x2d, 0xd3, 0x4a, 0xad, 0x0b, 0xee, 0x0c, 0x9b, 0x8f,
	0x0c, 0x0c, 0x9b, 0x44, 0x90, 0x84, 0xfe, 0xaf, 0x37, 0xb0, 0x03, 0x25, 0x3f, 0xec, 0x37, 0x83,
	0x98, 0x4b, 0xba, 0x95, 0xc6, 0xdf, 0x6c, 0x61, 0x7e, 0xd8, 0xb7, 0x32, 0xc6, 0x3c, 0x86, 0xcd,
	0x6f, 0xb2, 0x88, 0xce, 0xcf, 0x8b, 0x3b, 0x39, 0x1c, 0x8c, 0x50, 0x1b, 0x8e, 0x50, 0x9b, 0x8c,
	0x90, 0xdd, 0x2b, 0x64, 0xcf, 0x0a, 0xd9, 0x8b, 0x42, 0x36, 0x50, 0xc8, 0x5e, 0x15, 0xb2, 0x37,
	0x85, 0xda, 0x44, 0x21, 0x7b, 0x1a, 0xa3, 0x36, 0x18, 0xa3, 0x36, 0x1c, 0xa3, 0xe6, 0xe7, 0xd3,
	0xbb, 0x3c, 0x78, 0x0f, 0x00, 0x00, 0xff, 0xff, 0x23, 0x4c, 0x36, 0x88, 0xaf, 0x02, 0x00, 0x00,
}

func (this *SessionCacheKey) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SessionCacheKey)
	if !ok {
		that2, ok := that.(SessionCacheKey)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TenantID != that1.TenantID {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if this.InstanceID != that1.InstanceID {
		return false
	}
	return true
}
func (this *SessionCacheValue) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SessionCacheValue)
	if !ok {
		that2, ok := that.(SessionCacheValue)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.SessionID != that1.SessionID {
		return false
	}
	if this.DefaultConnectionID != that1.DefaultConnectionID {
		return false
	}
	return true
}
func (this *CreateNL2SQLSessionReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CreateNL2SQLSessionReq)
	if !ok {
		that2, ok := that.(CreateNL2SQLSessionReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.NL2SQLSessionCacheKey.Equal(that1.NL2SQLSessionCacheKey) {
		return false
	}
	if this.SessionID != that1.SessionID {
		return false
	}
	return true
}
func (this *CreateNL2SQLSessionResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CreateNL2SQLSessionResp)
	if !ok {
		that2, ok := that.(CreateNL2SQLSessionResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.IsCached != that1.IsCached {
		return false
	}
	return true
}
func (this *DescribeNL2SQLSessionReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeNL2SQLSessionReq)
	if !ok {
		that2, ok := that.(DescribeNL2SQLSessionReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.NL2SQLSessionCacheKey.Equal(that1.NL2SQLSessionCacheKey) {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *DescribeNL2SQLSessionResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeNL2SQLSessionResp)
	if !ok {
		that2, ok := that.(DescribeNL2SQLSessionResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.SessionID != that1.SessionID {
		return false
	}
	return true
}
func (this *SessionCacheKey) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.SessionCacheKey{")
	s = append(s, "TenantID: "+fmt.Sprintf("%#v", this.TenantID)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	s = append(s, "InstanceID: "+fmt.Sprintf("%#v", this.InstanceID)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SessionCacheValue) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.SessionCacheValue{")
	s = append(s, "SessionID: "+fmt.Sprintf("%#v", this.SessionID)+",\n")
	s = append(s, "DefaultConnectionID: "+fmt.Sprintf("%#v", this.DefaultConnectionID)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CreateNL2SQLSessionReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.CreateNL2SQLSessionReq{")
	if this.NL2SQLSessionCacheKey != nil {
		s = append(s, "NL2SQLSessionCacheKey: "+fmt.Sprintf("%#v", this.NL2SQLSessionCacheKey)+",\n")
	}
	s = append(s, "SessionID: "+fmt.Sprintf("%#v", this.SessionID)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CreateNL2SQLSessionResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.CreateNL2SQLSessionResp{")
	s = append(s, "IsCached: "+fmt.Sprintf("%#v", this.IsCached)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeNL2SQLSessionReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.DescribeNL2SQLSessionReq{")
	if this.NL2SQLSessionCacheKey != nil {
		s = append(s, "NL2SQLSessionCacheKey: "+fmt.Sprintf("%#v", this.NL2SQLSessionCacheKey)+",\n")
	}
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeNL2SQLSessionResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.DescribeNL2SQLSessionResp{")
	s = append(s, "SessionID: "+fmt.Sprintf("%#v", this.SessionID)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringNl2SqlSession(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *SessionCacheKey) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SessionCacheKey) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SessionCacheKey) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.InstanceID) > 0 {
		i -= len(m.InstanceID)
		copy(dAtA[i:], m.InstanceID)
		i = encodeVarintNl2SqlSession(dAtA, i, uint64(len(m.InstanceID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintNl2SqlSession(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TenantID) > 0 {
		i -= len(m.TenantID)
		copy(dAtA[i:], m.TenantID)
		i = encodeVarintNl2SqlSession(dAtA, i, uint64(len(m.TenantID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SessionCacheValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SessionCacheValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SessionCacheValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DefaultConnectionID) > 0 {
		i -= len(m.DefaultConnectionID)
		copy(dAtA[i:], m.DefaultConnectionID)
		i = encodeVarintNl2SqlSession(dAtA, i, uint64(len(m.DefaultConnectionID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.SessionID) > 0 {
		i -= len(m.SessionID)
		copy(dAtA[i:], m.SessionID)
		i = encodeVarintNl2SqlSession(dAtA, i, uint64(len(m.SessionID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateNL2SQLSessionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateNL2SQLSessionReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateNL2SQLSessionReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SessionID) > 0 {
		i -= len(m.SessionID)
		copy(dAtA[i:], m.SessionID)
		i = encodeVarintNl2SqlSession(dAtA, i, uint64(len(m.SessionID)))
		i--
		dAtA[i] = 0x12
	}
	if m.NL2SQLSessionCacheKey != nil {
		{
			size, err := m.NL2SQLSessionCacheKey.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintNl2SqlSession(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateNL2SQLSessionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateNL2SQLSessionResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateNL2SQLSessionResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsCached {
		i--
		if m.IsCached {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DescribeNL2SQLSessionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeNL2SQLSessionReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeNL2SQLSessionReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintNl2SqlSession(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0x22
	}
	if m.NL2SQLSessionCacheKey != nil {
		{
			size, err := m.NL2SQLSessionCacheKey.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintNl2SqlSession(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeNL2SQLSessionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeNL2SQLSessionResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeNL2SQLSessionResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SessionID) > 0 {
		i -= len(m.SessionID)
		copy(dAtA[i:], m.SessionID)
		i = encodeVarintNl2SqlSession(dAtA, i, uint64(len(m.SessionID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintNl2SqlSession(dAtA []byte, offset int, v uint64) int {
	offset -= sovNl2SqlSession(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *SessionCacheKey) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TenantID)
	if l > 0 {
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	l = len(m.InstanceID)
	if l > 0 {
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	return n
}

func (m *SessionCacheValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SessionID)
	if l > 0 {
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	l = len(m.DefaultConnectionID)
	if l > 0 {
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	return n
}

func (m *CreateNL2SQLSessionReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.NL2SQLSessionCacheKey != nil {
		l = m.NL2SQLSessionCacheKey.Size()
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	l = len(m.SessionID)
	if l > 0 {
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	return n
}

func (m *CreateNL2SQLSessionResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.IsCached {
		n += 2
	}
	return n
}

func (m *DescribeNL2SQLSessionReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.NL2SQLSessionCacheKey != nil {
		l = m.NL2SQLSessionCacheKey.Size()
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	return n
}

func (m *DescribeNL2SQLSessionResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SessionID)
	if l > 0 {
		n += 1 + l + sovNl2SqlSession(uint64(l))
	}
	return n
}

func sovNl2SqlSession(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozNl2SqlSession(x uint64) (n int) {
	return sovNl2SqlSession(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *SessionCacheKey) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SessionCacheKey{`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`InstanceID:` + fmt.Sprintf("%v", this.InstanceID) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SessionCacheValue) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SessionCacheValue{`,
		`SessionID:` + fmt.Sprintf("%v", this.SessionID) + `,`,
		`DefaultConnectionID:` + fmt.Sprintf("%v", this.DefaultConnectionID) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CreateNL2SQLSessionReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CreateNL2SQLSessionReq{`,
		`NL2SQLSessionCacheKey:` + strings.Replace(this.NL2SQLSessionCacheKey.String(), "SessionCacheKey", "SessionCacheKey", 1) + `,`,
		`SessionID:` + fmt.Sprintf("%v", this.SessionID) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CreateNL2SQLSessionResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CreateNL2SQLSessionResp{`,
		`IsCached:` + fmt.Sprintf("%v", this.IsCached) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeNL2SQLSessionReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeNL2SQLSessionReq{`,
		`NL2SQLSessionCacheKey:` + strings.Replace(this.NL2SQLSessionCacheKey.String(), "SessionCacheKey", "SessionCacheKey", 1) + `,`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeNL2SQLSessionResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeNL2SQLSessionResp{`,
		`SessionID:` + fmt.Sprintf("%v", this.SessionID) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringNl2SqlSession(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *SessionCacheKey) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNl2SqlSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SessionCacheKey: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SessionCacheKey: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNl2SqlSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SessionCacheValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNl2SqlSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SessionCacheValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SessionCacheValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SessionID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SessionID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultConnectionID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DefaultConnectionID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNl2SqlSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateNL2SQLSessionReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNl2SqlSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateNL2SQLSessionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateNL2SQLSessionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NL2SQLSessionCacheKey", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NL2SQLSessionCacheKey == nil {
				m.NL2SQLSessionCacheKey = &SessionCacheKey{}
			}
			if err := m.NL2SQLSessionCacheKey.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SessionID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SessionID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNl2SqlSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateNL2SQLSessionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNl2SqlSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateNL2SQLSessionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateNL2SQLSessionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsCached", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCached = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipNl2SqlSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeNL2SQLSessionReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNl2SqlSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeNL2SQLSessionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeNL2SQLSessionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NL2SQLSessionCacheKey", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NL2SQLSessionCacheKey == nil {
				m.NL2SQLSessionCacheKey = &SessionCacheKey{}
			}
			if err := m.NL2SQLSessionCacheKey.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNl2SqlSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeNL2SQLSessionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNl2SqlSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeNL2SQLSessionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeNL2SQLSessionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SessionID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SessionID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNl2SqlSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthNl2SqlSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipNl2SqlSession(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowNl2SqlSession
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNl2SqlSession
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthNl2SqlSession
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupNl2SqlSession
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthNl2SqlSession
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthNl2SqlSession        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowNl2SqlSession          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupNl2SqlSession = fmt.Errorf("proto: unexpected end of group")
)

package command

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/protoactor-go/actor"
	"errors"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
	"testing"
	"time"
)

type CommandSetResultBufferActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *CommandSetResultBufferActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *CommandSetResultBufferActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestCommandSetResultBufferActorSuite(t *testing.T) {
	suite.Run(t, new(CommandSetResultBufferActorSuite))
}

func (suite *CommandSetResultBufferActorSuite) TestNewCommandSetResultBufferActor() {
	ret := NewCommandSetResultBufferActor(NewCommandSetResultBufferActorIn{
		In: dig.In{},
	})
	suite.NotEmpty(ret)
}

func (suite *CommandSetResultBufferActorSuite) TestProcess() {
	ctx := mocks.NewMockContext(suite.ctrl)
	bufferActor := &CommandSetResultBufferActor{
		cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
	}

	ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
	ctx.EXPECT().GetName().Return("test").AnyTimes()
	ctx.EXPECT().Self().Return(nil).AnyTimes()
	ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	ctx.EXPECT().Stop(gomock.Any()).AnyTimes()

	// Mock cmdRepo for Started message
	bufferActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().GetCommandSet(ctx, "test").Return(&entity.CommandSet{}, nil).AnyTimes()

	// Test Started message
	ctx.EXPECT().Message().Return(&actor.Started{})
	bufferActor.Process(ctx)

	// Test Stopped message
	ctx.EXPECT().Message().Return(&actor.Stopped{})
	bufferActor.Process(ctx)

	// Test ReceiveTimeout message
	ctx.EXPECT().Message().Return(&actor.ReceiveTimeout{})
	bufferActor.Process(ctx)

	// Test ResultObject slice message
	ctx.EXPECT().Message().Return([]*shared.ResultObject{})
	bufferActor.Process(ctx)

	// Test FetchBufferedResults message
	ctx.EXPECT().Message().Return(&shared.FetchBufferedResults{})
	ctx.EXPECT().Respond(gomock.Any())
	bufferActor.Process(ctx)

	// Test unhandled message
	ctx.EXPECT().Message().Return("unknown")
	bufferActor.Process(ctx)
}

func (suite *CommandSetResultBufferActorSuite) TestOnStart() {
	ctx := mocks.NewMockContext(suite.ctrl)
	bufferActor := &CommandSetResultBufferActor{
		cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
	}
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
	ctx.EXPECT().GetName().Return("test").AnyTimes()

	// Test successful case
	bufferActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().GetCommandSet(ctx, "test").Return(&entity.CommandSet{}, nil)
	bufferActor.onStart(ctx)
	suite.NotNil(bufferActor.state)

	// Test error case
	bufferActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().GetCommandSet(ctx, "test").Return(nil, errors.New("not found"))
	bufferActor.onStart(ctx)
}

func (suite *CommandSetResultBufferActorSuite) TestBufferResult() {
	ctx := mocks.NewMockContext(suite.ctrl)
	bufferActor := &CommandSetResultBufferActor{}
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	// Test empty result
	bufferActor.BufferResult(ctx, []*shared.ResultObject{})

	// Test nil state
	bufferActor.state = nil
	resultObj := []*shared.ResultObject{{CommandStr: "test"}}
	bufferActor.BufferResult(ctx, resultObj)
	suite.NotNil(bufferActor.state)
	suite.Equal(resultObj, bufferActor.buffer)

	// Test normal case
	bufferActor.state = &bufferState{createdAt: time.Now(), hasResponded: true}
	newResultObj := []*shared.ResultObject{{CommandStr: "test2"}}
	bufferActor.BufferResult(ctx, newResultObj)
	suite.Equal(newResultObj, bufferActor.buffer)
	suite.False(bufferActor.state.hasResponded)
}

func (suite *CommandSetResultBufferActorSuite) TestGetResults() {
	ctx := mocks.NewMockContext(suite.ctrl)
	bufferActor := &CommandSetResultBufferActor{}
	msg := &shared.FetchBufferedResults{}
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	// Test nil state
	ctx.EXPECT().Respond(&shared.ErrNoBuffer{
		ErrorMessage: "buffer state not initialized",
	})
	bufferActor.GetResults(ctx, msg)

	// Test already responded
	bufferActor.state = &bufferState{hasResponded: true}
	ctx.EXPECT().Respond(&shared.ErrNoBuffer{
		ErrorMessage: "CommandSet Results have been retrieved. To fetch the data, please rerun DataExecCommandSetAsync",
	})
	bufferActor.GetResults(ctx, msg)

	// Test successful case with buffer
	bufferActor.state = &bufferState{hasResponded: false}
	bufferActor.buffer = []*shared.ResultObject{{CommandStr: "test"}}
	ctx.EXPECT().Respond(bufferActor.buffer)
	ctx.EXPECT().Send(gomock.Any(), &actor.Stopped{})
	ctx.EXPECT().Self().Return(nil)
	bufferActor.GetResults(ctx, msg)
	suite.Nil(bufferActor.buffer)
	suite.True(bufferActor.state.hasResponded)

	// Test no buffer case
	bufferActor.state = &bufferState{hasResponded: false}
	bufferActor.buffer = nil
	ctx.EXPECT().Respond(&shared.ErrNoBuffer{
		ErrorMessage: "No results found now, pls wait commands been executed.",
	})
	bufferActor.GetResults(ctx, msg)
}

func (suite *CommandSetResultBufferActorSuite) TestDoWhenIdle() {
	ctx := mocks.NewMockContext(suite.ctrl)
	bufferActor := &CommandSetResultBufferActor{}
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	// Test nil state
	bufferActor.doWhenIdle(ctx)

	// Test not timeout
	bufferActor.state = &bufferState{createdAt: time.Now()}
	bufferActor.doWhenIdle(ctx)

	// Test timeout
	bufferActor.state = &bufferState{createdAt: time.Now().Add(-10 * time.Minute)}
	bufferActor.buffer = []*shared.ResultObject{{CommandStr: "test"}}
	ctx.EXPECT().Send(gomock.Any(), &actor.Stopped{})
	ctx.EXPECT().Self().Return(nil)
	bufferActor.doWhenIdle(ctx)
	suite.True(bufferActor.state.hasResponded)
	suite.Nil(bufferActor.buffer)
}

func (suite *CommandSetResultBufferActorSuite) TestGetCommandSetID() {
	ctx := mocks.NewMockContext(suite.ctrl)
	bufferActor := &CommandSetResultBufferActor{}

	ctx.EXPECT().GetName().Return("test-id")
	result := bufferActor.GetCommandSetID(ctx)
	suite.Equal("test-id", result)
}

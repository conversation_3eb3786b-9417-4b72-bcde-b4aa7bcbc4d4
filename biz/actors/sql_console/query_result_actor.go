package sql_console

import (
	"encoding/json"
	"github.com/qjpcpu/fp"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/actors"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"go.uber.org/dig"
)

type sessionStateSnapshot struct {
	DataSource          *shared.DataSource
	Conn                []*actors.Connection
	ConnLock            map[string]*actors.CommandCursor
	IgnoreSecurityCheck bool
	SessionID           string
}

type NewQueryResultActorIn struct {
	dig.In
	CmdRepo     repository.CommandRepo
	Config      config.ConfigProvider
	PriSvc      usermgmt.PrivilegeServiceInterface
	ActorClient cli.ActorClient
}

func NewQueryResultActor(p NewQueryResultActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.QueryResultActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &QueryResultActor{
				cnf:      p.Config,
				PriSvc:   p.PriSvc,
				cmdRepo:  p.CmdRepo,
				actorCli: p.ActorClient,
			}
		}),
	}
}

type QueryResultActor struct {
	state    *sessionStateSnapshot
	PriSvc   usermgmt.PrivilegeServiceInterface
	cnf      config.ConfigProvider
	cmdRepo  repository.CommandRepo
	actorCli cli.ActorClient
}

const (
	queryResultTimeIntervalSeconds int64 = 30
)

func (a *QueryResultActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(time.Duration(queryResultTimeIntervalSeconds) * time.Second)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		a.onStart(ctx)
	case *actor.Stopped:
		log.Info(ctx, "actor stopped for connection: %s", ctx.GetName())
		ctx.Stop(ctx.Self())
	case *actor.ReceiveTimeout:
		a.doWhenIdle(ctx)
	case *shared.ScanCursorResult:
		a.handleScanCursorResult(ctx, msg)
	case *shared.CloseConnection:
		log.Info(ctx, "receive close connection message, actor will close.")
		ctx.Send(ctx.Self(), &actor.Stopped{})
	default:
		log.Warn(ctx, "receive unhandled message type: %T", msg)
	}
}

func (a *QueryResultActor) GetConnectionID(ctx types.Context) string {
	return ctx.GetName()
}

func (a *QueryResultActor) onStart(ctx types.Context) {
	log.Info(ctx, "started for connection: %s", ctx.GetName())
	a.state = &sessionStateSnapshot{}
}

func (a *QueryResultActor) handleScanCursorResult(ctx types.Context, msg *shared.ScanCursorResult) {
	cs, err := a.cmdRepo.GetCommandSetByCmdID(ctx, msg.CommandId)
	if err != nil {
		log.Warn(ctx, "get command set by cmd id %s fail %v", msg.CommandId, err)
		ctx.Respond(&shared.ErrScanCursorResult{
			ConnectionId: msg.GetConnectionId(),
			CommandId:    msg.GetCommandId(),
			ErrorMessage: err.Error(),
		})
		return
	}
	sessionID := cs.SessionID
	a.state.SessionID = sessionID

	// fetch session state from session actor
	resp, err := a.actorCli.KindOf(consts.SessionActorKind).
		Call(ctx, sessionID, &shared.Inspect{})
	if err != nil {
		log.Warn(ctx, "failed to inspect session state from %s: %v", sessionID, err)
		ctx.Respond(&shared.ErrScanCursorResult{
			ConnectionId: msg.GetConnectionId(),
			CommandId:    msg.GetCommandId(),
			ErrorMessage: err.Error(),
		})
		return
	}
	a.fillSessionState(ctx, resp, a.state)

	cmdResult, err := a.fetchCmdResultFromConn(ctx, msg)
	if err != nil {
		ctx.Respond(&shared.ErrScanCursorResult{
			ConnectionId: msg.GetConnectionId(),
			CommandId:    msg.GetCommandId(),
			ErrorMessage: err.Error(),
		})
		return
	}

	// 更新查询次数
	err = a.PriSvc.UpdateCurExecuteCount(ctx, a.state.DataSource)
	if err != nil {
		log.Warn(ctx, "UpdateCurExecuteCount fail %v", err)
		ctx.Respond(&shared.ErrScanCursorResult{
			ConnectionId: msg.GetConnectionId(),
			CommandId:    msg.GetCommandId(),
			ErrorMessage: err.Error(),
		})
		return
	}
	a.onCommandMeta(ctx, cmdResult)

	// 更新查询结果条数
	count := fp.StreamOf(cmdResult.Payload).
		FlatMap(func(chunk *shared.CommandResultChunk) fp.Stream {
			return fp.StreamOf(chunk.Rows).
				Reject(func(r *shared.CommandResultChunk_Row) bool {
					return r == nil || len(r.Cells) == 0
				})
		}).
		Count()
	if count != 0 {
		err := a.PriSvc.UpdateCurResultCount(ctx, count, a.state.DataSource)
		if err != nil {
			log.Warn(ctx, "UpdateCurExecuteCount fail %v", err)
			ctx.Respond(&shared.ErrScanCursorResult{
				ConnectionId: msg.GetConnectionId(),
				CommandId:    msg.GetCommandId(),
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	log.Info(ctx, "fetch %d command result", count)
	a.onCursorFinished(ctx, cmdResult)
	ctx.Respond(cmdResult)
}

func (a *QueryResultActor) fetchCmdResultFromConn(ctx types.Context, msg *shared.ScanCursorResult) (*shared.CommandResult, error) {
	resp, err := ctx.ClientOf(consts.ConnectionActorKind).Call(ctx, msg.ConnectionId, msg)
	if err != nil {
		log.Warn(ctx, "call connection actor ScanCursorResult error: %v, CommandID:[%s]",
			err, msg.GetCommandId())
		return nil, consts.ErrorWithParam(model.ErrorCode_ParamError, err.Error())
	}

	cmdResult, ok := resp.(*shared.CommandResult)
	if !ok {
		log.Warn(ctx, "unexpected resp type %T, expected *shared.CommandResult, CommandID:[%s]",
			resp, msg.GetCommandId())
		return nil, consts.ErrorWithParam(model.ErrorCode_ParamError, "unexpected response type")
	}

	switch cmdResult.Code {
	case shared.ErrCommandOK:
		log.Info(ctx, "CommandID:[%s] fetch result from cursor successfully, payload length is %d",
			msg.CommandId, len(cmdResult.Payload))
		return cmdResult, nil
	default:
		log.Warn(ctx, "CommandID[%s], CommandResult error, error code: %s",
			msg.GetCommandId(), cmdResult.Code.String())
		return nil, consts.ErrorWithParam(model.ErrorCode_ParamError, cmdResult.Code.String())
	}
}

func (a *QueryResultActor) onCommandMeta(ctx types.Context, msg *shared.CommandResult) {
	cs, err := a.cmdRepo.GetCommandSetByCmdID(ctx, msg.CommandId)
	if err != nil {
		log.Warn(ctx, "get command set by cmd id %s fail %v", msg.CommandId, err)
		return
	}
	if len(msg.Payload) > 0 {
		cs.SetCommandResultMeta(msg.CommandId, entity.TableResult, msg.Payload[0].Header)
		cs.SetCommandConnCurrentDB(msg.CommandId, a.getConnCurrentDB(ctx, msg.ConnectionId))
		a.cmdRepo.SaveCommandSet(ctx, cs)
	}
}

func (a *QueryResultActor) onCursorFinished(ctx types.Context, msg *shared.CommandResult) {
	// 后处理 sessionactor execute下一个command
	err := a.actorCli.KindOf(consts.SessionActorKind).
		Send(ctx, a.state.SessionID, &shared.FinishCursor{
			ConnectionId: msg.ConnectionId,
			CommandId:    msg.CommandId,
		})
	if err != nil {
		log.Warn(ctx, "failed to send FinishCursor message to session actor %s, error: %v", a.state.SessionID, err)
	}
}

func (a *QueryResultActor) doWhenIdle(ctx types.Context) {
	a.checkConnectionAlive(ctx)
}

func (a *QueryResultActor) checkConnectionAlive(ctx types.Context) {
	resp, err := ctx.ClientOf(consts.ConnectionActorKind).Call(ctx, a.GetConnectionID(ctx), &shared.KeepAliveProbe{})
	if err != nil {
		log.Warn(ctx, "failed to ping connection[%s], error: %v, assuming it's dead, actor will stop.", a.GetConnectionID(ctx), err)
		ctx.Send(ctx.Self(), &actor.Stopped{})
	} else {
		switch rsp := resp.(type) {
		case *shared.OK:
			log.Info(ctx, "check connection[%s] ok", a.GetConnectionID(ctx))
		case *shared.ConnectionClosed:
			log.Info(ctx, "connection[%s] closed, actor will stop.", a.GetConnectionID(ctx))
			ctx.Send(ctx.Self(), &actor.Stopped{})
		default:
			log.Warn(ctx, "check connection [%s] fail, unknown response %#v, actor will stop.", a.GetConnectionID(ctx), rsp)
			ctx.Send(ctx.Self(), &actor.Stopped{})
		}
	}
}

func (a *QueryResultActor) getConnCurrentDB(ctx types.Context, connID string) (dbName string) {
	dbName = a.state.DataSource.Db
	for _, c := range a.state.Conn {
		if c.ID == connID {
			dbName = c.CurrentDB
			return
		}
	}
	return
}

func (a *QueryResultActor) fillSessionState(ctx types.Context, resp interface{}, state *sessionStateSnapshot) {
	stateInfo, ok := resp.(*shared.ActorStateInfo)
	if !ok {
		log.Warn(ctx, "unexpected response type from Inspect: %T", resp)
		return
	}
	if err := json.Unmarshal([]byte(stateInfo.State), state); err != nil {
		log.Warn(ctx, "failed to unmarshal session state: %v", err)
	}
}

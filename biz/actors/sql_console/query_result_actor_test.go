package sql_console

import (
	"code.byted.org/infcs/dbw-mgr/biz/actors"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/protoactor-go/actor"
	"errors"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
	"testing"
)

type QueryResultActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *QueryResultActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *QueryResultActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestQueryResultActorSuite(t *testing.T) {
	suite.Run(t, new(QueryResultActorSuite))
}

func (suite *QueryResultActorSuite) TestNewQueryResultActor() {
	ret := NewQueryResultActor(NewQueryResultActorIn{
		In: dig.In{},
	})
	suite.NotEmpty(ret)
}

func (suite *QueryResultActorSuite) TestProcess() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{}

	ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
	ctx.EXPECT().GetName().Return("test").AnyTimes()
	ctx.EXPECT().Self().Return(nil).AnyTimes()
	ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	ctx.EXPECT().Stop(gomock.Any()).AnyTimes()

	// Test Started message
	ctx.EXPECT().Message().Return(&actor.Started{})
	queryResultActor.Process(ctx)

	// Test Stopped message
	ctx.EXPECT().Message().Return(&actor.Stopped{})
	queryResultActor.Process(ctx)

	// Test ReceiveTimeout message
	ctx.EXPECT().Message().Return(&actor.ReceiveTimeout{})
	kc := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc)
	kc.EXPECT().Call(ctx, "test", gomock.Any()).Return(&shared.OK{}, nil)
	queryResultActor.Process(ctx)

	// Test CloseConnection message
	ctx.EXPECT().Message().Return(&shared.CloseConnection{})
	queryResultActor.Process(ctx)

	// Test unhandled message
	ctx.EXPECT().Message().Return("unknown")
	queryResultActor.Process(ctx)
}

func (suite *QueryResultActorSuite) TestOnStart() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
	queryResultActor := &QueryResultActor{}
	ctx.EXPECT().GetName().Return("test").Times(1)
	queryResultActor.onStart(ctx)
	suite.NotNil(queryResultActor.state)
}

func (suite *QueryResultActorSuite) TestHandleScanCursorResult_Success() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{
		cmdRepo:  mocks.NewMockCommandRepo(suite.ctrl),
		actorCli: mocks.NewMockActorClient(suite.ctrl),
		PriSvc:   mocks.NewMockPrivilegeServiceInterface(suite.ctrl),
		state:    &sessionStateSnapshot{},
	}
	msg := &shared.ScanCursorResult{CommandId: "cmd1", ConnectionId: "conn1"}

	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	// Step 1: Mock GetCommandSetByCmdID - called multiple times
	cs := &entity.CommandSet{
		ID:        "cs1",
		SessionID: "session1",
		Commands:  []*entity.Command{{ID: "cmd1"}},
	}
	queryResultActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().GetCommandSetByCmdID(ctx, "cmd1").Return(cs, nil).AnyTimes()

	// Step 2: Mock session state fetch
	sessionKind := mocks.NewMockKindClient(suite.ctrl)
	queryResultActor.actorCli.(*mocks.MockActorClient).EXPECT().KindOf(consts.SessionActorKind).Return(sessionKind).Times(2)
	sessionKind.EXPECT().Call(ctx, "session1", gomock.Any()).Return(&shared.ActorStateInfo{State: `{"DataSource": {"Db": "test"}}`}, nil)

	// Step 3: Mock connection call
	connKind := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(connKind)
	connKind.EXPECT().Call(ctx, "conn1", msg).Return(&shared.CommandResult{
		Code:         shared.ErrCommandOK,
		CommandId:    "cmd1",
		ConnectionId: "conn1",
		Payload:      []*shared.CommandResultChunk{{Header: []string{"col1"}, Rows: []*shared.CommandResultChunk_Row{}}},
	}, nil)

	// Step 4: Mock privilege service calls
	queryResultActor.PriSvc.(*mocks.MockPrivilegeServiceInterface).EXPECT().UpdateCurExecuteCount(ctx, gomock.Any()).Return(nil)
	queryResultActor.PriSvc.(*mocks.MockPrivilegeServiceInterface).EXPECT().UpdateCurResultCount(ctx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	// Step 5: Mock command set save (called in onCommandMeta)
	queryResultActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().SaveCommandSet(ctx, cs)

	// Step 6: Mock finish cursor
	sessionKind.EXPECT().Send(ctx, "session1", gomock.Any()).Return(nil)

	// Step 7: Mock response
	ctx.EXPECT().Respond(gomock.Any())

	// Execute the method
	queryResultActor.handleScanCursorResult(ctx, msg)
}

func (suite *QueryResultActorSuite) TestHandleScanCursorResult_ErrorCases() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{
		cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
	}
	msg := &shared.ScanCursorResult{CommandId: "cmd1", ConnectionId: "conn1"}

	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	// Test command set not found
	queryResultActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().GetCommandSetByCmdID(ctx, "cmd1").Return(nil, errors.New("not found"))
	ctx.EXPECT().Respond(&shared.ErrScanCursorResult{
		ConnectionId: "conn1",
		CommandId:    "cmd1",
		ErrorMessage: "not found",
	})

	queryResultActor.handleScanCursorResult(ctx, msg)
}

func (suite *QueryResultActorSuite) TestFetchCmdResultFromConn() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{}
	msg := &shared.ScanCursorResult{CommandId: "cmd1", ConnectionId: "conn1"}

	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	// Success
	kc1 := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc1)
	kc1.EXPECT().Call(ctx, "conn1", msg).Return(&shared.CommandResult{Code: shared.ErrCommandOK}, nil)
	res, err := queryResultActor.fetchCmdResultFromConn(ctx, msg)
	suite.NoError(err)
	suite.NotNil(res)

	// Error calling
	kc2 := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc2)
	kc2.EXPECT().Call(ctx, "conn1", msg).Return(nil, errors.New("error"))
	_, err = queryResultActor.fetchCmdResultFromConn(ctx, msg)
	suite.Error(err)

	// Unexpected type
	kc3 := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc3)
	kc3.EXPECT().Call(ctx, "conn1", msg).Return("wrong", nil)
	_, err = queryResultActor.fetchCmdResultFromConn(ctx, msg)
	suite.Error(err)

	// Error code
	kc4 := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc4)
	kc4.EXPECT().Call(ctx, "conn1", msg).Return(&shared.CommandResult{Code: shared.ErrBadCommand}, nil)
	_, err = queryResultActor.fetchCmdResultFromConn(ctx, msg)
	suite.Error(err)
}

func (suite *QueryResultActorSuite) TestOnCommandMeta() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{
		cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
		state:   &sessionStateSnapshot{DataSource: &shared.DataSource{Db: "default"}},
	}
	msg := &shared.CommandResult{
		CommandId:    "cmd1",
		ConnectionId: "conn1",
		Payload:      []*shared.CommandResultChunk{{Header: []string{"col1"}}},
	}

	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	cs := &entity.CommandSet{
		ID:       "cs1",
		Commands: []*entity.Command{{ID: "cmd1"}},
	}
	queryResultActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().GetCommandSetByCmdID(ctx, "cmd1").Return(cs, nil)
	queryResultActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().SaveCommandSet(ctx, cs)

	queryResultActor.onCommandMeta(ctx, msg)
}

func (suite *QueryResultActorSuite) TestOnCursorFinished() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{
		actorCli: mocks.NewMockActorClient(suite.ctrl),
		state:    &sessionStateSnapshot{SessionID: "session1"},
	}
	msg := &shared.CommandResult{
		CommandId:    "cmd1",
		ConnectionId: "conn1",
	}

	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	sessionKind := mocks.NewMockKindClient(suite.ctrl)
	queryResultActor.actorCli.(*mocks.MockActorClient).EXPECT().KindOf(consts.SessionActorKind).Return(sessionKind)
	sessionKind.EXPECT().Send(ctx, "session1", &shared.FinishCursor{
		ConnectionId: "conn1",
		CommandId:    "cmd1",
	}).Return(nil)

	queryResultActor.onCursorFinished(ctx, msg)
}

func (suite *QueryResultActorSuite) TestDoWhenIdle() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{}

	kc := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc)
	ctx.EXPECT().GetName().Return("conn1").AnyTimes()
	ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	ctx.EXPECT().Self().Return(nil).AnyTimes()
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	// Mock successful response
	kc.EXPECT().Call(ctx, "conn1", gomock.Any()).Return(&shared.OK{}, nil)

	queryResultActor.doWhenIdle(ctx)
}

func (suite *QueryResultActorSuite) TestCheckConnectionAlive() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{}

	ctx.EXPECT().GetName().Return("conn1").AnyTimes()
	ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	ctx.EXPECT().Self().Return(nil).AnyTimes()
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

	// Success OK
	kc1 := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc1)
	kc1.EXPECT().Call(ctx, "conn1", gomock.Any()).Return(&shared.OK{}, nil)
	queryResultActor.checkConnectionAlive(ctx)

	// Error calling
	kc2 := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc2)
	kc2.EXPECT().Call(ctx, "conn1", gomock.Any()).Return(nil, errors.New("error"))
	queryResultActor.checkConnectionAlive(ctx)

	// ConnectionClosed
	kc3 := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc3)
	kc3.EXPECT().Call(ctx, "conn1", gomock.Any()).Return(&shared.ConnectionClosed{}, nil)
	queryResultActor.checkConnectionAlive(ctx)

	// Unknown response
	kc4 := mocks.NewMockKindClient(suite.ctrl)
	ctx.EXPECT().ClientOf(consts.ConnectionActorKind).Return(kc4)
	kc4.EXPECT().Call(ctx, "conn1", gomock.Any()).Return("unknown", nil)
	queryResultActor.checkConnectionAlive(ctx)
}

func (suite *QueryResultActorSuite) TestGetConnCurrentDB() {
	queryResultActor := &QueryResultActor{
		state: &sessionStateSnapshot{
			DataSource: &shared.DataSource{Db: "default"},
			Conn:       []*actors.Connection{{ID: "conn1", CurrentDB: "db1"}, {ID: "conn2", CurrentDB: "db2"}},
		},
	}

	// Found
	db := queryResultActor.getConnCurrentDB(nil, "conn1")
	suite.Equal("db1", db)

	// Not found
	db = queryResultActor.getConnCurrentDB(nil, "conn3")
	suite.Equal("default", db)
}

func (suite *QueryResultActorSuite) TestFillSessionState() {
	ctx := mocks.NewMockContext(suite.ctrl)
	queryResultActor := &QueryResultActor{}
	state := &sessionStateSnapshot{}
	ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
	// Success
	validJSON := `{"DataSource": {}}`
	queryResultActor.fillSessionState(ctx, &shared.ActorStateInfo{State: validJSON}, state)
	suite.NotNil(state.DataSource)

	// Invalid JSON
	invalidJSON := `invalid`
	queryResultActor.fillSessionState(ctx, &shared.ActorStateInfo{State: invalidJSON}, state)

	// Wrong type
	queryResultActor.fillSessionState(ctx, "wrong", state)
}

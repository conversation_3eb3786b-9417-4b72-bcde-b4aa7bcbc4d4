package actors

import (
    "errors"
    "testing"
    "time"

    "code.byted.org/infcs/dbw-mgr/biz/entity"
    "code.byted.org/infcs/dbw-mgr/biz/shared"
    "code.byted.org/infcs/dbw-mgr/biz/test/mocks"
    "code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
    "github.com/golang/mock/gomock"
    "github.com/stretchr/testify/suite"
)

type OnCmdResultTestSuite struct {
    suite.Suite
    ctrl *gomock.Controller
}

func (suite *OnCmdResultTestSuite) SetupTest() {
    suite.ctrl = gomock.NewController(suite.T())
}

func (suite *OnCmdResultTestSuite) TearDownTest() {
    suite.ctrl.Finish()
}

func TestOnCmdResultTestSuite(t *testing.T) {
    suite.Run(t, new(OnCmdResultTestSuite))
}

func (suite *OnCmdResultTestSuite) TestOnCommandCursorReady() {
    ctx := mocks.NewMockContext(suite.ctrl)
    sessionActor := &SessionActor{
        cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
    }

    ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

    // Test case 1: GetCommandSetByCmdID fails
    msg := &shared.CommandResult{
        CommandId: "cmd1",
        Payload: []*shared.CommandResultChunk{
            {Header: []string{"col1", "col2"}},
        },
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(nil, errors.New("command set not found"))
    
    sessionActor.onCommandCursorReady(ctx, msg)

    // Test case 2: Command not found in command set
    cs := &entity.CommandSet{
        ID:       "cs1",
        Commands: []*entity.Command{},
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(cs, nil)
    
    sessionActor.onCommandCursorReady(ctx, msg)

    // Test case 3: Command not in executing state
    cs = &entity.CommandSet{
        ID: "cs1",
        Commands: []*entity.Command{
            {ID: "cmd1", State: entity.CommandPending},
        },
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(cs, nil)
    
    sessionActor.onCommandCursorReady(ctx, msg)

    // Test case 4: Successful case
    cs = &entity.CommandSet{
        ID: "cs1",
        Commands: []*entity.Command{
            {ID: "cmd1", State: entity.CommandExecuting},
        },
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(cs, nil)
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        SaveCommandSet(ctx, cs).
        Return(nil)
    
    sessionActor.onCommandCursorReady(ctx, msg)

    // Test case 5: Empty payload
    msgEmpty := &shared.CommandResult{
        CommandId: "cmd1",
        Payload:   []*shared.CommandResultChunk{},
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(cs, nil)
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        SaveCommandSet(ctx, cs).
        Return(nil)
    
    sessionActor.onCommandCursorReady(ctx, msgEmpty)
}

func (suite *OnCmdResultTestSuite) TestOnCursorFinished() {
    ctx := mocks.NewMockContext(suite.ctrl)
    sessionActor := &SessionActor{
        cmdRepo:                mocks.NewMockCommandRepo(suite.ctrl),
        PriSvc:                 mocks.NewMockPrivilegeServiceInterface(suite.ctrl),
        OperateRecordService:   mocks.NewMockOperateRecordServiceInterface(suite.ctrl),
        state: &sessionState{
            UserID:     "user1",
            TenantID:   "tenant1",
            DataSource: &shared.DataSource{Db: "testdb"},
            Conn: []*Connection{
                {ID: "conn1", CurrentDB: "db1"},
            },
        },
    }

    ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
    ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()

    // Test case 1: GetCommandSetByCmdID fails
    msg := &shared.FinishCursor{
        CommandId:    "cmd1",
        ConnectionId: "conn1",
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(nil, errors.New("command set not found"))
    
    sessionActor.onCursorFinished(ctx, msg)

    // Test case 2: Command not found in command set
    cs := &entity.CommandSet{
        ID:       "cs1",
        Commands: []*entity.Command{},
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(cs, nil)
    
    sessionActor.onCursorFinished(ctx, msg)

    // Test case 3: Command not in terminated state
    cs = &entity.CommandSet{
        ID: "cs1",
        Commands: []*entity.Command{
            {ID: "cmd1", State: entity.CommandExecuting},
        },
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(cs, nil)
    
    sessionActor.onCursorFinished(ctx, msg)

    // Test case 4: No next command - all finished
    cs = &entity.CommandSet{
        ID: "cs1",
        Commands: []*entity.Command{
            {ID: "cmd1", State: entity.CommandTerminated},
        },
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(cs, nil)
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        SaveCommandSet(ctx, cs).
        Return(nil)
    ctx.EXPECT().SaveState().Return()
    sessionActor.OperateRecordService.(*mocks.MockOperateRecordServiceInterface).EXPECT().
        CreateConsoleRecordBySQLExecute(ctx, "cmd1", model.ConsoleOperationStatus_SUCCESS, sessionActor.state.DataSource, "db1").
        Return(nil)
    
    sessionActor.onCursorFinished(ctx, msg)

    // Test case 5: Has next pending command - security check fails
    cs = &entity.CommandSet{
        ID: "cs1",
        Commands: []*entity.Command{
            {ID: "cmd1", State: entity.CommandTerminated},
            {ID: "cmd2", State: entity.CommandPending, Content: "SELECT * FROM table", Extra: map[string]interface{}{"SqlExecuteType": "Connection"}},
        },
    }
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        GetCommandSetByCmdID(ctx, "cmd1").
        Return(cs, nil)
    sessionActor.PriSvc.(*mocks.MockPrivilegeServiceInterface).EXPECT().
        SecurityCheck(ctx, "SELECT * FROM table", gomock.Any(), false, model.SqlExecutionType_SqlQuery).
        Return(errors.New("security check failed"))
    ctx.EXPECT().Send(ctx, gomock.Any()).Return()
    ctx.EXPECT().Self().Return(nil)
    sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
        SaveCommandSet(ctx, cs).
        Return(nil)
    sessionActor.OperateRecordService.(*mocks.MockOperateRecordServiceInterface).EXPECT().
        CreateConsoleRecordBySQLExecute(ctx, "cmd1", model.ConsoleOperationStatus_SUCCESS, sessionActor.state.DataSource, "db1").
        Return(nil)
    
    sessionActor.onCursorFinished(ctx, msg)
}
// ignore_security_alert_file SQL_INJECTION
package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	serviceParser "code.byted.org/infcs/dbw-mgr/biz/service/parser"
	sqltask_svc "code.byted.org/infcs/dbw-mgr/biz/service/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"errors"
	"fmt"
	_ "github.com/pingcap/tidb/types/parser_driver"
	"github.com/qjpcpu/fp"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"go.uber.org/dig"
	"os"
	"runtime/debug"
	"time"
)

var (
	ExecuteTimeoutError = errors.New("ticket: command exceed the max time")
)

const (
	// actor receive timeout配置
	DefaultTimePeriod               = 100
	FreeLockTicketTimeout           = time.Duration(DefaultTimePeriod) * time.Millisecond
	FreeLockTicketBeforeStopTimeout = time.Duration(60) * time.Second

	// 用来判断当前批次是执行select找边界，还是真正执行对应的DML语句的
	SQLTypeSelect = "SQLType_Select"
	SQLTypeDML    = "SQLType_DML"

	// 特定的报错信息
	ReachTicketEndTime        = "cannot execute ticket,because current time is exceed ticket endtime"
	ReachTicketEndTimeSuccess = "current time is exceed ticket endtime，stop ticket by dbw successfully"

	// 工单来源
	TicketFromOpenAPI     = "openapi"
	TicketFromDataArchive = "data_archive_inner"

	// 判断工单是否是delete语句，对于delete语句，为了兼容小基架和sharding，要特殊处理
	DeleteType = "Delete"
	NormalType = "Normal"

	// 单批次执行超时时间默认5分钟,超过的话，就报任务状态异常
	SingleBatchTimeLimit = 5
)

type FreeLockDMLActorIn struct {
	dig.In
	Conf           config.ConfigProvider
	WorkflowDal    dal.WorkflowDAL
	IdgenSvc       idgen.Service
	Sp             serviceParser.CommandParser
	CmdRepo        repository.CommandRepo
	CrRepo         repository.CommandResultRepo
	TicketService  workflow.TicketService
	SqlTaskSvc     sqltask_svc.SqlTaskService
	Ds             datasource.DataSourceService
	C3ConfProvider c3.ConfigProvider
}

// NewFreeLockDMLActor 执行工单的actor
func NewFreeLockDMLActor(p FreeLockDMLActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.FreeLockDMLActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &FreeLockDMLActor{
				state:          newFreeLockDMLActorState(state),
				cnf:            p.Conf,
				workflowDal:    p.WorkflowDal,
				idgenSvc:       p.IdgenSvc,
				sp:             p.Sp,
				cmdRepo:        p.CmdRepo,
				crRepo:         p.CrRepo,
				ticketService:  p.TicketService,
				sqlTaskSvc:     p.SqlTaskSvc,
				ds:             p.Ds,
				c3ConfProvider: p.C3ConfProvider,
			}
		}),
	}
}

func newFreeLockDMLActorState(bytes []byte) *FreeLockDMLActorState {
	ts := &FreeLockDMLActorState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return nil
	}
	return ts
}

type FreeLockDMLActorState struct {
	SessionId                string                        `json:"session_id"`
	ConnectionId             string                        `json:"connection_id"`
	CsID                     string                        `json:"command_set_id"` // 当前正在处理的CommandSet
	Ds                       *shared.DataSource            `json:"datasource"`
	CurrentAction            model.FreeLockDMLTicketAction `json:"current_action"`
	TenantId                 string                        `json:"tenant_id"`
	UserId                   string                        `json:"user_id"`
	Tables                   []string                      `json:"tables"`
	FreeLockExecInfo         []*FreeLockExecInfo           `json:"freelock_exec_info"`           // 索引最大值、最小值、当前批次左值、右值
	TableIndexInfo           []*datasource.TableIndexInfo  `json:"table_index_info"`             // 表的主键或者非空唯一索引信息
	IsLastBatch              bool                          `json:"is_last_batch"`                // 当前批次是否是最后一批
	BatchNum                 int64                         `json:"batch_num"`                    // 当前已经执行的批次
	TotalBatchNum            int64                         `json:"total_batch_num"`              // 根据Explain结果和用户输入的批次,计算一个总的执行批次
	AffectedRows             int64                         `json:"affected_rows"`                // 已经执行的行数
	IsFreeLockDMLTaskCreated bool                          `json:"is_freelock_dml_task_created"` // 是否已经创建了FreeLockDML任务
	CreateFromType           string                        `json:"create_from_type"`             // CreateFromType
	SQLType                  string                        `json:"sql_type"`                     // sqltype
	CurrentExecSQL           string                        `json:"current_exec_sql"`
	ExecRetryTimes           int                           `json:"exec_retry_times"`
	Hint                     string                        `json:"hint"`
}

type FreeLockExecInfo struct {
	MinValue   string `json:"min_value"`   // 最小主键值
	MaxValue   string `json:"max_value"`   // 最大主键值
	BatchLeft  string `json:"batch_left"`  // 左边界值
	BatchRight string `json:"batch_right"` // 右边界值
	IndexName  string `json:"index_name"`  // 索引名字
}

type FreeLockDMLActor struct {
	state          *FreeLockDMLActorState
	cnf            config.ConfigProvider
	workflowDal    dal.WorkflowDAL
	idgenSvc       idgen.Service
	sp             serviceParser.CommandParser
	cmdRepo        repository.CommandRepo
	crRepo         repository.CommandResultRepo
	ticketService  workflow.TicketService
	sqlTaskSvc     sqltask_svc.SqlTaskService
	ds             datasource.DataSourceService
	c3ConfProvider c3.ConfigProvider
	client         tls.Client
}

type IndexInfo struct {
	IndexName  string
	IndexValue string
}

func (f *FreeLockDMLActor) GetState() []byte {
	state, _ := json.Marshal(f.state)
	return state
}

func (f *FreeLockDMLActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		f.protectUserCall(ctx, func() {
			f.OnStart(ctx)
		})
	case *shared.ExecFreeLockDMLTicket:
		f.protectUserCall(ctx, func() {
			f.ExecFreeLockDMLTicket(ctx, msg)
		})
	case *shared.StopTicket:
		f.protectUserCall(ctx, func() {
			f.StopTicket(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		f.protectUserCall(ctx, func() {
			f.GetFreeLockDMLTicketStatus(ctx)
			ctx.SetReceiveTimeout(FreeLockTicketTimeout)
		})
	case *actor.Stopped:
		log.Info(ctx, "FreeLockDMLActor %s stop", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
	return
}

// protectUserCall 防止panic之后程序挂掉
func (f *FreeLockDMLActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call FreeLockDMLActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

// OnStart FreeLockDMLActor启动
func (f *FreeLockDMLActor) OnStart(ctx types.Context) {
	if f.state == nil {
		f.state = new(FreeLockDMLActorState)
	}
	log.Info(ctx, "FreeLockDMLActor %s start,CurrentAction is %s,tenant is %s",
		ctx.GetName(), f.state.CurrentAction.String(), fwctx.GetTenantID(ctx))

	// 1、启动一个tls的client,为后面写工单日志做准备
	f.client = f.createTlsClient(ctx)
	f.BuildCtx(ctx)
	// 2、修复状态
	f.repairActorState(ctx)
	return
}

// createTlsClient 创建Tls客户端
func (f *FreeLockDMLActor) createTlsClient(ctx types.Context) tls.Client {
	c3Cfg := f.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	regionId := os.Getenv(`BDC_REGION_ID`)
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
	return tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)
}

// repairActorState 这里考虑重新启动之后的动作
func (f *FreeLockDMLActor) repairActorState(ctx types.Context) {
	switch f.state.CurrentAction {
	case model.FreeLockDMLTicketAction_Started: // 刚启动,什么也不做,直接执行后续的消息
		return
	case model.FreeLockDMLTicketAction_ReceiveCommand, // 刚收到消息就挂了
		model.FreeLockDMLTicketAction_GetMinMaxBoundaryValue, // 计算完索引边界值之后挂了
		model.FreeLockDMLTicketAction_CreateSession,          // 创建完session挂了
		model.FreeLockDMLTicketAction_ExecuteCommand:         // 开始执行命令后挂了
		log.Info(ctx, "actor restart, try to exec ticket %s again", ctx.GetName())
		if f.state == nil || (f.state != nil && f.state.Ds == nil) {
			log.Warn(ctx, "actor state is empty,can't recover ticket %s", ctx.GetName())
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		ticket, err := f.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
		if err != nil {
			log.Error(ctx, "actor restart find ticket error:%s", err.Error())
			return
		}
		ctx.Send(ctx.Self(), &shared.ExecFreeLockDMLTicket{
			TenantID:    f.state.TenantId,
			UserID:      f.state.UserId,
			Source:      f.state.Ds,
			TicketType:  int32(ticket.TicketType),
			ExecuteType: int32(ticket.ExecuteType),
			SqlText:     ticket.SqlText,
		})
		return
	case model.FreeLockDMLTicketAction_ExecuteFinished: // 工单执行完成
		f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketFinished)})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	case model.FreeLockDMLTicketAction_ExecuteFailed: // 工单执行失败
		f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError)})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
	return
}

func (f *FreeLockDMLActor) ExecFreeLockDMLTicket(ctx types.Context, msg *shared.ExecFreeLockDMLTicket) {
	log.Info(ctx, "ticket: actor receive command,datasource is %v,msg tenant id is %s",
		msg.Source, msg.TenantID)
	f.state.Hint = msg.Hint
	// 工单类型:前端、OpenAPI、数据归档
	f.state.CreateFromType = msg.CreateFrom

	// 这里新增一个校验,同一时间段内,只能有一个执行中的任务
	if f.IsInstanceExistRunningFreeLockDMLTicket(ctx, msg) {
		log.Warn(ctx, "ticket %v there are tickets running in this instance", ctx.GetName())
		ctx.Respond(&shared.TicketExecuted{
			TicketId: ctx.GetName(),
			Code:     shared.ExecutedFail,
			Message:  fmt.Sprintf("there are tickets running in this instance,please check"),
		})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}

	// 0、初始化state并更新当前的actor状态为ReceiveCommand
	if f.state.CurrentAction == model.FreeLockDMLTicketAction_Started {
		err := f.initState(ctx, msg)
		if err != nil {
			log.Warn(ctx, "initState error:%s", err.Error())
			f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
				Description: err.Error()})
			ctx.Respond(&shared.TicketExecuted{
				TicketId: ctx.GetName(),
				Code:     shared.ExecutedFail,
				Message:  err.Error(),
			})
			f.updateActorAction(ctx, model.FreeLockDMLTicketAction_ExecuteFailed)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		f.updateActorAction(ctx, model.FreeLockDMLTicketAction_ReceiveCommand)
	}

	// 1、返回给接口任务已经发起
	// 1.1 无锁SQL变更工单审批过后如果已经到达了截止时间,则直接提示工单超过截止时间，关闭即可
	if IsNowReachExecEndTime(msg.ExecutableEndTime) {
		log.Warn(ctx, "ticket: %s out of execute time window", ctx.GetName())
		f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
			Description: ReachTicketEndTime})
		ctx.Respond(&shared.TicketExecuted{
			TicketId: ctx.GetName(),
			Code:     shared.ExecutedFail,
			Message:  ReachTicketEndTime,
		})
		f.updateActorAction(ctx, model.FreeLockDMLTicketAction_ExecuteFailed)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	// 1.2 无锁SQL变更工单审批过后时间正常,则直接返回任务提交成功
	ctx.Respond(&shared.TicketExecuted{
		TicketId: ctx.GetName(),
		Code:     shared.ExecutedStart,
		Message:  "Ticket task initiated successfully",
	})

	// 2、获取索引(主键或者非空唯一索引)最大最小的记录值,并记录到state里面
	if f.state.CurrentAction == model.FreeLockDMLTicketAction_ReceiveCommand {
		// FixMe 这块现在区分一下SQL语句的类型,对于Delete语句和非Delete语句的处理方案不同
		TicketSQLType, _ := GetSQLTextType(msg.SqlText)
		switch TicketSQLType {
		case DeleteType:
			f.state.SQLType = DeleteType
			log.Info(ctx, "ticket %v is a delete type sql ", ctx.GetName())
			freeLockExecInfo, err := f.GetTableIndexBorderValueForDelete(ctx, msg)
			if err != nil {
				log.Warn(ctx, "ticket %s,get index border error:%s", ctx.GetName(), err.Error())
				f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
					Description: err.Error()})
				ctx.Respond(&shared.TicketExecuted{
					TicketId: ctx.GetName(),
					Code:     shared.ExecutedFail,
					Message:  err.Error(),
				})
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			}
			f.state.FreeLockExecInfo = freeLockExecInfo
		default:
			f.state.SQLType = NormalType
			log.Info(ctx, "ticket %v is a normal type sql", ctx.GetName())
			freeLockExecInfo, err := f.GetTableIndexBorderValue(ctx, msg)
			if err != nil {
				log.Warn(ctx, "ticket %s,get index border error:%s", ctx.GetName(), err.Error())
				f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
					Description: err.Error()})
				ctx.Respond(&shared.TicketExecuted{
					TicketId: ctx.GetName(),
					Code:     shared.ExecutedFail,
					Message:  err.Error(),
				})
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			}
			f.state.FreeLockExecInfo = freeLockExecInfo
		}
		f.updateActorAction(ctx, model.FreeLockDMLTicketAction_GetMinMaxBoundaryValue)
	}

	// 3、创建session
	if f.state.CurrentAction == model.FreeLockDMLTicketAction_GetMinMaxBoundaryValue {
		// 3.1 定时DML工单如果未到执行时间,计算一下和起始时间的时间差,并设置为ctx的ReceiveTimeOut
		if NotReachCronStartTime(msg.ExecuteType, msg.ExecutableStartTime) {
			timeDelta := int64(msg.ExecutableStartTime) - time.Now().Unix()
			if timeDelta < 0 {
				timeDelta = 0
			}
			log.Info(ctx, "ticket: %s timeDelta is %v s", ctx.GetName(), timeDelta)
			ctx.SetReceiveTimeout(time.Duration(timeDelta+1) * time.Second)
			ctx.Respond(&shared.TicketExecuted{
				TicketId: ctx.GetName(),
				Code:     shared.ExecutedStart,
				Message:  "execute free lock dml ticket task submit success",
			})
			return
		}
		// 开始进入ReceiveTimeOut
		f.updateActorAction(ctx, model.FreeLockDMLTicketAction_CreateSession)
	}
	// 标记任务已经创建
	f.state.IsFreeLockDMLTaskCreated = true
	f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketExecute)})
	ctx.SetReceiveTimeout(FreeLockTicketTimeout)
	return

}

// ExecuteCommand 执行命令
func (f *FreeLockDMLActor) ExecuteCommand(ctx types.Context, msg *shared.ExecuteCommand) error {
	log.Info(ctx, "ticket: actor execute command,connection is %v,commandSet id is %v", msg.ConnectionId, msg.CommandSetId)
	f.updateActorAction(ctx, model.FreeLockDMLTicketAction_ExecuteCommand)

	// 这里给 session actor发送ExecuteCommand消息
	resp, err := ctx.ClientOf(consts.SessionActorKind).
		Call(ctx, ctx.GetName(), &shared.ExecuteCommand{
			ConnectionId:        msg.ConnectionId,
			CommandSetId:        msg.CommandSetId,
			IgnoreSecurityCheck: true,
		})
	if err != nil {
		log.Warn(ctx, "ticket:%s  Execute Command err:%s", ctx.GetName(), err.Error())
		f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError), Description: model.ErrorCode_CreateSessionError.String()})
		return err
	}
	switch rsp := resp.(type) {
	case *shared.CommandAccepted:
		log.Info(ctx, "ticket:%s command set %s submitted ok", ctx.GetName(), msg.CommandSetId)
		return nil
	case *shared.CommandRejected:
		log.Warn(ctx, "ticket:command set %s rejected by %v", msg.CommandSetId, rsp.Reason)
		f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError), Description: model.ErrorCode_CreateSessionError.String()})
		return fmt.Errorf("ExecuteCommand return CommandRejected")
	default:
		log.Warn(ctx, "ExecuteCommand err,rsp type is:%v", rsp)
		f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError), Description: model.ErrorCode_CreateSessionError.String()})
		return fmt.Errorf("Unknown  ExecuteCommand resp ")
	}
}

func (f *FreeLockDMLActor) explainCommand(ctx types.Context, typ *shared.DataSource, req *model.ExecuteCommandSetReq) ([]*serviceParser.Command, error) {
	cmdList, err := f.sp.Explain(ctx, typ, req.GetCommandSetContent(), model.SqlExecuteType_Connection)
	if err != nil {
		log.Warn(ctx, "ticket: split command %s fail %v", req.GetCommandSetContent(), err)
		cmdList = []*serviceParser.Command{serviceParser.NewCommand(req.GetCommandSetContent(), nil)}
	}
	if len(cmdList) == 0 {
		log.Info(ctx, "ticket: command need not split")
		cmdList = []*serviceParser.Command{serviceParser.NewCommand(req.GetCommandSetContent(), nil)}
	}
	return cmdList, nil
}

// convertToEntity 申请这一组CommandSet的Id,并将CommandSet结构拼接完整
func (f *FreeLockDMLActor) convertToEntity(ctx types.Context, req *model.ExecuteCommandSetReq, cmdList []*serviceParser.Command) (*entity.CommandSet, error) {
	ids, err := f.idgenSvc.NextStr(ctx, len(cmdList)+1)
	if err != nil {
		log.Info(ctx, "gen id fail %v", err)
		return nil, err
	}
	csID := ids[len(ids)-1]
	cs := &entity.CommandSet{
		ID:           csID,
		SessionID:    req.GetSessionId(),
		ConnectionID: req.GetConnectionId(),
		CreateTimeMS: time.Now().Unix() * 1000,
		Content:      req.GetCommandSetContent(),
	}
	fp.StreamOf(ids).
		Zip(fp.StreamOf(cmdList), func(id string, cmd *serviceParser.Command) *entity.Command {
			return &entity.Command{
				ID:           id,
				CommandSetID: csID,
				State:        entity.CommandPending,
				Content:      cmd.Text(),
				Extra:        cmd.Extra(),
			}
		}).
		ToSlice(&cs.Commands)
	return cs, nil
}

func (f *FreeLockDMLActor) GetFreeLockDMLTicketStatus(ctx types.Context) {
	// 这里需要根据Actor的状态来获取当前命令的执行结果
	log.Info(ctx, "FreeLockDMLActor receive timeout,ticket %v", ctx.GetName())
	// 1、数据库查询工单信息
	f.BuildCtx(ctx)
	sharedTicket, err := f.GetSharedTicket(ctx)
	if err != nil {
		log.Warn(ctx, "ticket:%s get shared ticket error", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	// 1.1、判断一下当前时间是否超过了结束时间,如果超过了,就停止执行,并且更新数据库
	if sharedTicket.ExecEndTime > 0 && time.Now().Unix() > int64(sharedTicket.ExecEndTime) {
		log.Info(ctx, "ticket:%s reach ticket endtime, begin to stop ticket", ctx.GetName())
		if err = f.StopTicket(ctx, &shared.StopTicket{}); err != nil {
			log.Warn(ctx, "ticket: stop ticket %s execute err:%s", ctx.GetName(), err.Error())
		}
		f.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketFinished),
			Description: ReachTicketEndTimeSuccess})
		return
	}
	// 1.2、如果还未开始执行,则发送执行的消息
	if !f.state.IsFreeLockDMLTaskCreated {
		log.Info(ctx, "IsFreeLockDMLTaskCreated is %v", f.state.IsFreeLockDMLTaskCreated)
		f.ExecFreeLockDMLTicket(ctx, &shared.ExecFreeLockDMLTicket{
			TenantID:            f.state.TenantId,
			UserID:              f.state.UserId,
			Source:              f.state.Ds,
			TicketType:          sharedTicket.TicketType,
			ExecuteType:         sharedTicket.ExecuteType,
			ExecutableStartTime: sharedTicket.ExecStartTime,
			ExecutableEndTime:   sharedTicket.ExecEndTime,
			SqlText:             sharedTicket.SqlText,
		})
	}
	// 2、这里做一个优化,如果判断影响行数很小, 只需要执行一次，则直接提交SQL即可,不需要生成select的语句了,并且标记这一批为最后一批
	if f.state.TotalBatchNum == 1 {
		if err = f.ExecSQLDirectly(ctx, sharedTicket); err != nil {
			log.Warn(ctx, "direct execute sql error:%s", err.Error())
		}
		f.TicketSucceed(ctx, sharedTicket)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}

	f.doWhenBatchGap(ctx, sharedTicket)

	// 4、这里开始执行Select语句,查询这一批次的右边界
	// 对于delete语句和其他语句的处理稍有不同
	var selectSQL string
	switch f.state.SQLType {
	case DeleteType:
		selectSQL = f.GenerateNextBatchSelectSQLForDelete(ctx, sharedTicket)
	default:
		selectSQL = f.GenerateNextBatchSelectSQL(ctx, sharedTicket)
	}
	log.Info(ctx, "ticket %v select sql is [%v]", ctx.GetName(), selectSQL)

	if err := f.execCommand(ctx, sharedTicket, selectSQL, SQLTypeSelect); err != nil {
		log.Warn(ctx, "execute command %s err %s", selectSQL, err)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}

	// 6、根据Select语句求出的边界，开始执行DML语句
	var execSQL string
	switch f.state.SQLType {
	case DeleteType:
		execSQL = f.GenerateNextBatchExecSQLForDelete(ctx, sharedTicket)
	default:
		execSQL = f.GenerateNextBatchExecSQL(ctx, sharedTicket)
	}
	log.Info(ctx, "ticket %v exec sql is [%v]", ctx.GetName(), execSQL)

	if err := f.execCommand(ctx, sharedTicket, execSQL, SQLTypeDML); err != nil {
		log.Warn(ctx, "execute command %s err %s", execSQL, err)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
	// 8、判断是否是最后一批
	log.Info(ctx, "IsLastBatch is %v", f.state.IsLastBatch)
	if f.state.IsLastBatch {
		f.TicketSucceed(ctx, sharedTicket)
		time.Sleep(FreeLockTicketBeforeStopTimeout)
		log.Info(ctx, "last batch execute success,actor suicide")
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	// 9、继续执行下一批
	switch f.state.SQLType {
	case DeleteType:
	default:
		f.updateIndex(ctx)
	}
	return
}

func (f *FreeLockDMLActor) execCommand(ctx types.Context, ticket *shared.Ticket, sqlText string, sqlType string) (err error) {
	// 这里针对DQL和DML分别处理
	switch sqlType {
	case SQLTypeSelect:
		switch f.state.SQLType {
		case DeleteType:
			var dqlRes *datasource.ExecuteDQLResp
			var connErr error
			for i := 0; i < 5; i++ {
				dqlRes, connErr = f.ds.ExecuteDQL(ctx, &datasource.ExecuteDQLReq{
					Source:  f.state.Ds,
					Command: sqlText,
					Columns: GenerateColumnList(f.state.TableIndexInfo),
				})
				if connErr != nil {
					log.Warn(ctx, "ticket %v execute dql error %v", ctx.GetName(), err)
					// 如果没有连接上,有可能是发生了主从切换,获取一下最新的地址,再次尝试连接
					f.ds.GetDatasourceAddress(ctx, f.state.Ds)
					continue
				} else {
					break
				}
			}
			// 重试之后还是错误
			if connErr != nil {
				f.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
					TicketId:     ticket.TicketId,
					TicketStatus: int8(model.TicketStatus_TicketError),
					Description:  connErr.Error(),
				})
				return connErr
			}
			//  这里将Select的执行结果赋给right值,并生成当前批次的update语句
			res := make([]string, 0)
			for _, val := range dqlRes.ColumnValue {
				res = append(res, val)
			}
			log.Info(ctx, "ticket %v select sql  [%v] value result is %v", ctx.GetName(), sqlText, res)
			// 这里如果是最后一批,那么计算出来的value就是一个空：column name is ["id"],column value is [""]
			f.SaveResToLeft(ctx, res, f.state.FreeLockExecInfo)
			f.UpdateProgress(ctx, ticket)
		default:
			var dqlRes *datasource.ExecuteDQLResp
			var connErr error
			columns := GenerateColumnList(f.state.TableIndexInfo)
			for i := 0; i < 5; i++ {
				dqlRes, connErr = f.ds.ExecuteDQL(ctx, &datasource.ExecuteDQLReq{
					Source:  f.state.Ds,
					Command: sqlText,
					Columns: append(columns, "isNeedDML"),
				})
				if connErr != nil {
					log.Warn(ctx, "ticket %v execute dql error %v", ctx.GetName(), err)
					continue
				} else {
					break
				}
			}
			// 重试之后还是错误
			if connErr != nil {
				log.Warn(ctx, "ticket %v conn error is %v", ctx.GetName(), connErr.Error())
				f.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
					TicketId:     ticket.TicketId,
					TicketStatus: int8(model.TicketStatus_TicketError),
					Description:  connErr.Error(),
				})
				return connErr
			}
			// 如果返回值长度为0,说明这一批次没有符合条件的记录，已经是最后一批了
			log.Info(ctx, "ticket %v select sql result is %v", ctx.GetName(), utils.Show(dqlRes))
			if IsEmptyResult(dqlRes.ColumnValue) {
				log.Info(ctx, "ticket %v get dql result is empty,this is last batch")
				f.LastBatch(ctx, f.state.FreeLockExecInfo)
				f.UpdateProgress(ctx, ticket)
				return nil
			}
			// 如果isNeedDML为1,说明这一批需要处理
			if len(dqlRes.ColumnValue) > 0 && dqlRes.ColumnValue[len(dqlRes.ColumnValue)-1] == "1" {
				log.Info(ctx, "ticket %v need dml", ctx.GetName())
				//  这里将Select的执行结果赋给right值,并生成当前批次的update语句
				f.NeedDml(ctx, dqlRes.ColumnValue, f.state.FreeLockExecInfo)
				f.UpdateProgress(ctx, ticket)
				return nil
			}
			// 最后一行MySQL输入的是"Empty set (0.00 sec)",使用代码输出的东西是:column name is [\"id\",\"isNeedDml\"],column value is [\"\",\"\"]"
			// 此时需要判断是最后一批,然后把最大值
			log.Info(ctx, "ticket %v isNeedDML is [%s] ", ctx.GetName(), dqlRes.ColumnValue[len(dqlRes.ColumnValue)-1])
			// isNeedDml为Null,说明这一批不需要处理,直接将输出的值作为左值,并重新计算右值
			f.NeedDml(ctx, dqlRes.ColumnValue, f.state.FreeLockExecInfo)
			return nil
		}

	case SQLTypeDML: // 如果是DML语句,需要判断是delete语句还是其他dml语句
		affectedRows, err := f.ds.ExecuteDMLAndGetAffectedRows(ctx, &datasource.ExecuteDMLAndGetAffectedRowsReq{
			Source:  f.state.Ds,
			Command: sqlText,
		})
		if err != nil {
			log.Warn(ctx, "ticket %v execute dml and get affected rows error %v", ctx.GetName(), err)
			return err
		}
		log.Info(ctx, "ticket %v affected rows is %v", ctx.GetName(), affectedRows)
		// 如果最后一批执行的影响行数小于批次大小,说明已经是最后一批了
		if affectedRows < ticket.BatchSize {
			log.Info(ctx, "ticket %v current batch size is %v,batch size is %v, this is last batch ", ctx.GetName(), affectedRows, ticket.BatchSize)
			f.state.IsLastBatch = true
		}
		// 这里,将上面的执行结果,Update到ticket表里面
		f.state.AffectedRows += affectedRows
		f.workflowDal.UpdateTicketAffectedRows(ctx, &dao.Ticket{
			TicketId:     ticket.TicketId,
			AffectedRows: fmt.Sprintf("%v/%v", f.state.AffectedRows, f.state.AffectedRows),
		})
		return nil
	}
	return nil
}

// ExecSQLDirectly 直接执行
func (f *FreeLockDMLActor) ExecSQLDirectly(ctx types.Context, ticket *shared.Ticket) error {
	log.Info(ctx, "total batch is 1 ,ticket %s execute [%v] directly", ctx.GetName(), ticket.SqlText)
	f.state.IsLastBatch = true
	affectedRows, err := f.ds.ExecuteDMLAndGetAffectedRows(ctx, &datasource.ExecuteDMLAndGetAffectedRowsReq{
		Source:  f.state.Ds,
		Command: ticket.SqlText,
	})
	if err != nil {
		log.Warn(ctx, "ticket %v: submit command to connection error:%s ", ctx.GetName(), err.Error())
		return err
	}
	log.Info(ctx, "ticket %v affectedRows is %v", ctx.GetName(), affectedRows)
	// 这里更新一下数据库的影响行数
	f.workflowDal.UpdateTicketAffectedRows(ctx, &dao.Ticket{
		TicketId:     ticket.TicketId,
		AffectedRows: fmt.Sprintf("%v/%v", affectedRows, affectedRows),
	})
	return nil
}

func (f *FreeLockDMLActor) StopTicket(ctx types.Context, msg *shared.StopTicket) error {
	// 停止命令
	// 给session_actor发消息停止命令
	if f.state == nil || f.state.CsID == "" || f.state.SessionId == "" || f.state.ConnectionId == "" {
		return nil
	}
	f.updateActorAction(ctx, model.FreeLockDMLTicketAction_ExecuteFinished)
	// 不走session之后,这里修改成停止Actor,更新工单状态就行
	f.UpdateTicketRepo(ctx,
		&dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketFinished),
			Description: "this ticket is normally stopped by user",
		})
	ctx.Respond(&shared.StopTicketResp{
		TicketId:   ctx.GetName(),
		ErrCode:    "200",
		ErrMessage: "stop ticket success",
		Status:     consts.StatusSuccess,
	})
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	return nil
}

func IsEmptyResult(res []string) bool {
	if len(res) == 0 {
		return true
	}
	for _, val := range res {
		if val != "" {
			return false
		}
	}
	return true
}

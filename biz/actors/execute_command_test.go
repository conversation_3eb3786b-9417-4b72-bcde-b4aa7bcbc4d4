package actors

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	. "code.byted.org/gopkg/mockito"
	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/sqltask"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ExecuteCommandSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *ExecuteCommandSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *ExecuteCommandSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestExecuteCommandSuite(t *testing.T) {
	suite.Run(t, new(ExecuteCommandSuite))
}

type mockConfigProviderImpl struct {
}

func (self *mockConfigProviderImpl) Get(ctx context.Context) *config.Config {
	return &config.Config{}
}

func (self *mockConfigProviderImpl) Update(ctx context.Context, cnf *config.Config) error {
	return nil
}

func (self *mockConfigProviderImpl) Refresh(ctx context.Context) {

}

func (self *mockConfigProviderImpl) AddUpdateHook(fn func(*shared.ConfigUpdated)) {

}

func mockConfigRes() *config.Config {
	return &config.Config{
		MgrPodCIDR: "172.0.0.0/8,11.0.0.0/8,100.64.0.0/10",
	}
}

func (suite *ExecuteCommandSuite) TestSubmitToConnection() {
	PatchConvey("submit successed", suite.T(), func() {
		mgrClient := &mocks.MockMgrClient{}
		mock1 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock3.UnPatch()

		cli := mocks.NewMockActorClient(suite.ctrl)
		actorCtx := mocks.NewMockContext(suite.ctrl)
		kindClient := mocks.NewMockKindClient(suite.ctrl)

		kindClient.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		actorCtx.EXPECT().ClientOf(gomock.Any()).Return(kindClient).AnyTimes()
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()

		impl := SessionActor{
			cnf:      &mockConfigProviderImpl{},
			actorCli: cli,
		}
		cmd := &entity.Command{
			ID:           "xx",
			CommandSetID: "xx",
			State:        0,
			Reason:       nil,
			Content:      "xx",
			StartTimeMS:  0,
			EndTimeMS:    0,
			ResultType:   nil,
			Header:       nil,
			ReasonDetail: "xx",
			Extra:        nil,
			TenantID:     "xx",
		}
		impl.submitToConnection(actorCtx, cmd, "xxx")
	})
}

func (suite *ExecuteCommandSuite) TestSubmitToOnlineDDLTask() {
	PatchConvey("submit DDLTask successed", suite.T(), func() {
		actorCtx := mocks.NewMockContext(suite.ctrl)
		cli := mocks.NewMockActorClient(suite.ctrl)
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"Type\":\"1\",\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"**********\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
		state := &sessionState{}
		json.Unmarshal([]byte(rawState), state)

		kindClient := mocks.NewMockKindClient(suite.ctrl)
		kindClient.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		actorCtx.EXPECT().ClientOf(gomock.Any()).Return(kindClient).AnyTimes()
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		actorCtx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
		actorCtx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
		actorCtx.EXPECT().SaveState().AnyTimes()

		mock2 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
		defer mock2.UnPatch()
		mgrClient := &mocks.MockMgrClient{}
		mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock4.UnPatch()
		mock5 := mockey.Mock((*sqltask.MockSqlTaskService).CreateOnlineDDLSqlTask).Return(utils.StringRef("test"), nil).Build()
		defer mock5.UnPatch()
		mock6 := mockey.Mock((*sqltask.MockSqlTaskService).DescribeSqlTasks).Return([]*model.SqlTask{
			{
				OrderId:       "1",
				InstanceId:    "1",
				SqlTaskId:     "1",
				DBName:        "1",
				TableName:     "1",
				ExecSQL:       "1",
				Comment:       "1",
				CreateTime:    1,
				FinishTime:    1,
				SqlTaskStatus: model.SqlTaskStatus_Success,
				Result_:       "1",
				Progress:      1,
			},
		}, nil).Build()
		defer mock6.UnPatch()

		impl := SessionActor{
			cnf:        &mockConfigProviderImpl{},
			actorCli:   cli,
			state:      state,
			sqlTaskSvc: &sqltask.MockSqlTaskService{},
		}
		cmd := &entity.Command{
			ID:           "xx",
			CommandSetID: "xx",
			State:        0,
			Reason:       nil,
			Content:      "xx",
			StartTimeMS:  0,
			EndTimeMS:    0,
			ResultType:   nil,
			Header:       nil,
			ReasonDetail: "xx",
			Extra:        nil,
			TenantID:     "xx",
		}
		impl.submitToOnlineDDLTask(actorCtx, cmd, "xxx")
	})
	PatchConvey("submit DDLTask Stop", suite.T(), func() {
		actorCtx := mocks.NewMockContext(suite.ctrl)
		cli := mocks.NewMockActorClient(suite.ctrl)
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"Type\":\"1\",\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"**********\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
		state := &sessionState{}
		json.Unmarshal([]byte(rawState), state)

		kindClient := mocks.NewMockKindClient(suite.ctrl)
		kindClient.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		actorCtx.EXPECT().ClientOf(gomock.Any()).Return(kindClient).AnyTimes()
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		actorCtx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
		actorCtx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
		actorCtx.EXPECT().Respond(gomock.Any()).AnyTimes()
		actorCtx.EXPECT().SaveState().AnyTimes()

		mock2 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
		defer mock2.UnPatch()
		mgrClient := &mocks.MockMgrClient{}
		mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock4.UnPatch()
		mock5 := mockey.Mock((*sqltask.MockSqlTaskService).CreateOnlineDDLSqlTask).Return(utils.StringRef("test"), nil).Build()
		defer mock5.UnPatch()
		mock6 := mockey.Mock((*sqltask.MockSqlTaskService).DescribeSqlTasks).Return([]*model.SqlTask{
			{
				OrderId:       "1",
				InstanceId:    "1",
				SqlTaskId:     "1",
				DBName:        "1",
				TableName:     "1",
				ExecSQL:       "1",
				Comment:       "1",
				CreateTime:    1,
				FinishTime:    1,
				SqlTaskStatus: model.SqlTaskStatus_Stop,
				Result_:       "1",
				Progress:      1,
			},
		}, errors.New("task stop")).Build()
		defer mock6.UnPatch()

		impl := SessionActor{
			cnf:        &mockConfigProviderImpl{},
			actorCli:   cli,
			state:      state,
			sqlTaskSvc: &sqltask.MockSqlTaskService{},
		}
		cmd := &entity.Command{
			ID:           "xx",
			CommandSetID: "xx",
			State:        0,
			Reason:       nil,
			Content:      "xx",
			StartTimeMS:  0,
			EndTimeMS:    0,
			ResultType:   nil,
			Header:       nil,
			ReasonDetail: "xx",
			Extra: map[string]interface{}{
				"IsCreateShardingTable": true,
				"ShardingKeyName":       "xx",
				"ShardingKeyType":       "xx",
				"KillLongTxn":           false,
				"RenameDisallowWindow":  "xx",
				"RplDelayCheckRule":     "xx",
			},
			TenantID: "xx",
		}
		impl.submitToOnlineDDLTask(actorCtx, cmd, "xxx")
	})
}

func (suite *ExecuteCommandSuite) TestPollingSqlTask() {
	PatchConvey("submit DDLTask successed", suite.T(), func() {
		actorCtx := mocks.NewMockContext(suite.ctrl)
		cli := mocks.NewMockActorClient(suite.ctrl)
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		//rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"Type\":\"1\",\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"**********\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
		//state := &sessionState{}
		//json.Unmarshal([]byte(rawState), state)
		state := &sessionState{
			DataSource: &shared.DataSource{
				Type: shared.MySQL,
			},
			SqlTaskRunning: true,
			SqlTaskInfo: SqlTaskInfo{
				SqlTaskId:     "172343535",
				CommandId:     "172343535",
				ConnectionId:  "172343535",
				SqlTaskStatus: model.SqlTaskStatus_Success,
			},
		}
		kindClient := mocks.NewMockKindClient(suite.ctrl)
		kindClient.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		actorCtx.EXPECT().ClientOf(gomock.Any()).Return(kindClient).AnyTimes()
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		actorCtx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
		actorCtx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
		actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
		actorCtx.EXPECT().SaveState().AnyTimes()
		actorCtx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()

		mock1 := mockey.Mock(fwctx.GetTenantID).Return("**********").Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
		defer mock2.UnPatch()
		mgrClient := &mocks.MockMgrClient{}
		mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock4.UnPatch()
		//mock5 := mockey.Mock((*sqltask.MockSqlTaskService).CreateOnlineDDLSqlTask).Return(utils.StringRef("test"), nil).Build()
		//defer mock5.UnPatch()
		mock6 := mockey.Mock((*sqltask.MockSqlTaskService).DescribeSqlTasks).Return([]*model.SqlTask{
			{
				OrderId:       "1",
				InstanceId:    "1",
				SqlTaskId:     "1",
				DBName:        "1",
				TableName:     "1",
				ExecSQL:       "1",
				Comment:       "1",
				CreateTime:    1,
				FinishTime:    1,
				SqlTaskStatus: model.SqlTaskStatus_Success,
				Result_:       "1",
				Progress:      1,
			},
		}, nil).Build()
		defer mock6.UnPatch()

		impl := SessionActor{
			cnf:        &mockConfigProviderImpl{},
			actorCli:   cli,
			state:      state,
			sqlTaskSvc: &sqltask.MockSqlTaskService{},
		}
		impl.pollingSqlTask(actorCtx)
	})
	PatchConvey("submit DDLTask desc failed", suite.T(), func() {
		actorCtx := mocks.NewMockContext(suite.ctrl)
		cli := mocks.NewMockActorClient(suite.ctrl)
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		//rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"Type\":\"1\",\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"**********\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
		//state := &sessionState{}
		//json.Unmarshal([]byte(rawState), state)
		state := &sessionState{
			DataSource: &shared.DataSource{
				Type: shared.MySQL,
			},
			SqlTaskRunning: true,
			SqlTaskInfo: SqlTaskInfo{
				SqlTaskId:     "172343535",
				CommandId:     "172343535",
				ConnectionId:  "172343535",
				SqlTaskStatus: model.SqlTaskStatus_Success,
			},
		}
		kindClient := mocks.NewMockKindClient(suite.ctrl)
		kindClient.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		actorCtx.EXPECT().ClientOf(gomock.Any()).Return(kindClient).AnyTimes()
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		actorCtx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
		actorCtx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
		actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
		actorCtx.EXPECT().SaveState().AnyTimes()
		actorCtx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()

		mock1 := mockey.Mock(fwctx.GetTenantID).Return("**********").Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
		defer mock2.UnPatch()
		mgrClient := &mocks.MockMgrClient{}
		mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock4.UnPatch()
		//mock5 := mockey.Mock((*sqltask.MockSqlTaskService).CreateOnlineDDLSqlTask).Return(utils.StringRef("test"), nil).Build()
		//defer mock5.UnPatch()
		mock6 := mockey.Mock((*sqltask.MockSqlTaskService).DescribeSqlTasks).Return(nil, errors.New("error")).Build()
		defer mock6.UnPatch()

		impl := SessionActor{
			cnf:        &mockConfigProviderImpl{},
			actorCli:   cli,
			state:      state,
			sqlTaskSvc: &sqltask.MockSqlTaskService{},
		}
		impl.pollingSqlTask(actorCtx)
	})
	PatchConvey("submit DDLTask running", suite.T(), func() {
		actorCtx := mocks.NewMockContext(suite.ctrl)
		cli := mocks.NewMockActorClient(suite.ctrl)
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		//rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"Type\":\"1\",\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"**********\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
		//state := &sessionState{}
		//json.Unmarshal([]byte(rawState), state)
		state := &sessionState{
			DataSource: &shared.DataSource{
				Type: shared.MySQL,
			},
			SqlTaskRunning: true,
			SqlTaskInfo: SqlTaskInfo{
				SqlTaskId:     "172343535",
				CommandId:     "172343535",
				ConnectionId:  "172343535",
				SqlTaskStatus: model.SqlTaskStatus_Running,
			},
		}
		kindClient := mocks.NewMockKindClient(suite.ctrl)
		kindClient.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		actorCtx.EXPECT().ClientOf(gomock.Any()).Return(kindClient).AnyTimes()
		actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
		actorCtx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
		actorCtx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
		actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
		actorCtx.EXPECT().SaveState().AnyTimes()
		actorCtx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()

		mock1 := mockey.Mock(fwctx.GetTenantID).Return("**********").Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
		defer mock2.UnPatch()
		mgrClient := &mocks.MockMgrClient{}
		mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock4.UnPatch()
		//mock5 := mockey.Mock((*sqltask.MockSqlTaskService).CreateOnlineDDLSqlTask).Return(utils.StringRef("test"), nil).Build()
		//defer mock5.UnPatch()
		mock6 := mockey.Mock((*sqltask.MockSqlTaskService).DescribeSqlTasks).Return([]*model.SqlTask{
			{
				OrderId:       "1",
				InstanceId:    "1",
				SqlTaskId:     "1",
				DBName:        "1",
				TableName:     "1",
				ExecSQL:       "1",
				Comment:       "1",
				CreateTime:    1,
				FinishTime:    1,
				SqlTaskStatus: model.SqlTaskStatus_Success,
				Result_:       "1",
				Progress:      1,
			},
		}, nil).Build()
		defer mock6.UnPatch()

		impl := SessionActor{
			cnf:        &mockConfigProviderImpl{},
			actorCli:   cli,
			state:      state,
			sqlTaskSvc: &sqltask.MockSqlTaskService{},
		}
		impl.pollingSqlTask(actorCtx)
	})
}

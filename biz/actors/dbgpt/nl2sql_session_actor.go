package dbgpt

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/patrickmn/go-cache"
	"go.uber.org/dig"
)

type NL2SQLSessionActor struct {
	actorClient  cli.ActorClient
	sessionCache *cache.Cache
}

type NL2SQLSessionActorIn struct {
	dig.In
	ActorClient cli.ActorClient
}

func NewNL2SQLSessionActor(in NL2SQLSessionActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.NL2SQLSessionActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &NL2SQLSessionActor{
				actorClient:  in.ActorClient,
				sessionCache: cache.New(consts.NL2SQLSessionCacheExpirationDuration, consts.NL2SQLSessionCacheCleanupInterval),
			}
		}),
	}
}

func (a *NL2SQLSessionActor) Process(ctx types.Context) {
	log.Info(ctx, "nl2sql session actor received a request message: %s", utils.Show(ctx.Message()))

	switch msg := ctx.Message().(type) {
	case actor.Started:
	case *shared.CreateNL2SQLSessionReq:
		a.CreateNL2SQLSession(ctx, msg)
	case *shared.DescribeNL2SQLSessionReq:
		a.DescribeNL2SQLSession(ctx, msg)
	case *actor.ReceiveTimeout:
	}
}

func (a *NL2SQLSessionActor) CreateNL2SQLSession(ctx types.Context, msg *shared.CreateNL2SQLSessionReq) {
	a.sessionCache.Set(convertSessionCacheKeyToString(msg.NL2SQLSessionCacheKey), msg.SessionID, consts.NL2SQLSessionCacheExpirationDuration)
	ctx.Respond(&shared.CreateNL2SQLSessionResp{IsCached: true})
}

func (a *NL2SQLSessionActor) DescribeNL2SQLSession(ctx types.Context, msg *shared.DescribeNL2SQLSessionReq) {
	sessionCacheVal, ok := a.sessionCache.Get(convertSessionCacheKeyToString(msg.NL2SQLSessionCacheKey))
	if !ok {
		ctx.Respond(&shared.DescribeNL2SQLSessionResp{SessionID: ""})
		return
	}

	sessionID, ok := sessionCacheVal.(string)
	if !ok {
		ctx.Respond(&shared.DescribeNL2SQLSessionResp{SessionID: ""})
		return
	}

	ctx.Respond(&shared.DescribeNL2SQLSessionResp{SessionID: sessionID})
}

func convertSessionCacheKeyToString(sessionCacheKey *shared.SessionCacheKey) string {
	if sessionCacheKey == nil {
		return ""
	}

	return sessionCacheKey.TenantID + "/" + sessionCacheKey.UserID + "/" + sessionCacheKey.InstanceID
}

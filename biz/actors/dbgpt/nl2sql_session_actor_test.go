package dbgpt

import (
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"github.com/golang/mock/gomock"
	"github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/suite"
)

type NL2SQLSessionActorTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
	ctx  *mocks.MockContext

	actor cli.ActorClient

	NL2SQLSessionActor *NL2SQLSessionActor
}

func (suite *NL2SQLSessionActorTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.ctx = mocks.NewMockContext(suite.ctrl)

	suite.actor = mocks.NewMockActorClient(suite.ctrl)

	suite.NL2SQLSessionActor = &NL2SQLSessionActor{
		actorClient:  suite.actor,
		sessionCache: cache.New(consts.NL2SQLSessionCacheExpirationDuration, consts.NL2SQLSessionCacheCleanupInterval),
	}
}

func (suite *NL2SQLSessionActorTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestNL2SQLSessionActorTestSuite(t *testing.T) {
	suite.Run(t, new(NL2SQLSessionActorTestSuite))
}

func (suite *NL2SQLSessionActorTestSuite) TestNewNL2SQLSessionActor() {
	NewNL2SQLSessionActor(NL2SQLSessionActorIn{
		ActorClient: suite.actor,
	})
}

func (suite *NL2SQLSessionActorTestSuite) TestCreateNL2SQLSession() {
	suite.ctx.EXPECT().Respond(gomock.Any()).Return().AnyTimes()
	suite.NL2SQLSessionActor.CreateNL2SQLSession(suite.ctx, &shared.CreateNL2SQLSessionReq{
		NL2SQLSessionCacheKey: &shared.SessionCacheKey{
			TenantID:   "123",
			UserID:     "123",
			InstanceID: "mysql-abc",
		},
		SessionID: "123",
	})
}

func (suite *NL2SQLSessionActorTestSuite) TestDescribeNL2SQLSession() {
	suite.ctx.EXPECT().Respond(gomock.Any()).Return().AnyTimes()

	sessionCacheKey := &shared.SessionCacheKey{
		TenantID:   "123",
		UserID:     "123",
		InstanceID: "mysql-abc",
	}

	suite.NL2SQLSessionActor.DescribeNL2SQLSession(suite.ctx, &shared.DescribeNL2SQLSessionReq{
		NL2SQLSessionCacheKey: sessionCacheKey,
	})

	suite.NL2SQLSessionActor.CreateNL2SQLSession(suite.ctx, &shared.CreateNL2SQLSessionReq{
		NL2SQLSessionCacheKey: sessionCacheKey,
		SessionID:             "123",
	})
	suite.NL2SQLSessionActor.DescribeNL2SQLSession(suite.ctx, &shared.DescribeNL2SQLSessionReq{
		NL2SQLSessionCacheKey: sessionCacheKey,
	})
}

func (suite *NL2SQLSessionActorTestSuite) TestConvertSessionCacheKeyToString() {
	var sessionCacheKey *shared.SessionCacheKey

	keyStr := convertSessionCacheKeyToString(sessionCacheKey)
	suite.Equal("", keyStr)

	keyStr = convertSessionCacheKeyToString(&shared.SessionCacheKey{
		TenantID:   "123",
		UserID:     "123",
		InstanceID: "mysql-abc",
	})
	suite.Equal("123/123/mysql-abc", keyStr)

}

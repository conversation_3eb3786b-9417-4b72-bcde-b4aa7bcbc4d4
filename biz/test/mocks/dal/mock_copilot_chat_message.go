// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/GolandProjects/dbw-mgr/biz/dal/copilot_chat_message.go

// Package mocks is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	dal "code.byted.org/infcs/dbw-mgr/biz/dal"
	dao "code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	model "code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	gomock "github.com/golang/mock/gomock"
)

// MockCopilotChatMessageDAL is a mock of CopilotChatMessageDAL interface.
type MockCopilotChatMessageDAL struct {
	ctrl     *gomock.Controller
	recorder *MockCopilotChatMessageDALMockRecorder
}

// MockCopilotChatMessageDALMockRecorder is the mock recorder for MockCopilotChatMessageDAL.
type MockCopilotChatMessageDALMockRecorder struct {
	mock *MockCopilotChatMessageDAL
}

// NewMockCopilotChatMessageDAL creates a new mock instance.
func NewMockCopilotChatMessageDAL(ctrl *gomock.Controller) *MockCopilotChatMessageDAL {
	mock := &MockCopilotChatMessageDAL{ctrl: ctrl}
	mock.recorder = &MockCopilotChatMessageDALMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCopilotChatMessageDAL) EXPECT() *MockCopilotChatMessageDALMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockCopilotChatMessageDAL) Create(ctx context.Context, CopilotChatMessage *dao.CopilotChatMessage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, CopilotChatMessage)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockCopilotChatMessageDALMockRecorder) Create(ctx, CopilotChatMessage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).Create), ctx, CopilotChatMessage)
}

// DeleteMessageByChatID mocks base method.
func (m *MockCopilotChatMessageDAL) DeleteMessageByChatID(ctx context.Context, TenantId, chatID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMessageByChatID", ctx, TenantId, chatID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteMessageByChatID indicates an expected call of DeleteMessageByChatID.
func (mr *MockCopilotChatMessageDALMockRecorder) DeleteMessageByChatID(ctx, TenantId, chatID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMessageByChatID", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).DeleteMessageByChatID), ctx, TenantId, chatID)
}

// QueryMessageByCondition mocks base method.
func (m *MockCopilotChatMessageDAL) QueryMessageByCondition(ctx context.Context, TenantId, chatID string, condition dal.CopilotChatMessageParam) ([]*dao.CopilotChatMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryMessageByCondition", ctx, TenantId, chatID, condition)
	ret0, _ := ret[0].([]*dao.CopilotChatMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryMessageByCondition indicates an expected call of QueryMessageByCondition.
func (mr *MockCopilotChatMessageDALMockRecorder) QueryMessageByCondition(ctx, TenantId, chatID, condition interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryMessageByCondition", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).QueryMessageByCondition), ctx, TenantId, chatID, condition)
}

// QueryMessageByMessageID mocks base method.
func (m *MockCopilotChatMessageDAL) QueryMessageByMessageID(ctx context.Context, TenantId, messageId string) (*dao.CopilotChatMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryMessageByMessageID", ctx, TenantId, messageId)
	ret0, _ := ret[0].(*dao.CopilotChatMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryMessageByMessageID indicates an expected call of QueryMessageByMessageID.
func (mr *MockCopilotChatMessageDALMockRecorder) QueryMessageByMessageID(ctx, TenantId, messageId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryMessageByMessageID", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).QueryMessageByMessageID), ctx, TenantId, messageId)
}

// QueryMessageListByAfterMessageID mocks base method.
func (m *MockCopilotChatMessageDAL) QueryMessageListByAfterMessageID(ctx context.Context, TenantId, messageID, chatID string) ([]*dao.CopilotChatMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryMessageListByAfterMessageID", ctx, TenantId, messageID, chatID)
	ret0, _ := ret[0].([]*dao.CopilotChatMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryMessageListByAfterMessageID indicates an expected call of QueryMessageListByAfterMessageID.
func (mr *MockCopilotChatMessageDALMockRecorder) QueryMessageListByAfterMessageID(ctx, TenantId, messageID, chatID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryMessageListByAfterMessageID", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).QueryMessageListByAfterMessageID), ctx, TenantId, messageID, chatID)
}

// QueryMessageListByChatID mocks base method.
func (m *MockCopilotChatMessageDAL) QueryMessageListByChatID(ctx context.Context, TenantId, chatID string) ([]*dao.CopilotChatMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryMessageListByChatID", ctx, TenantId, chatID)
	ret0, _ := ret[0].([]*dao.CopilotChatMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryMessageListByChatID indicates an expected call of QueryMessageListByChatID.
func (mr *MockCopilotChatMessageDALMockRecorder) QueryMessageListByChatID(ctx, TenantId, chatID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryMessageListByChatID", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).QueryMessageListByChatID), ctx, TenantId, chatID)
}

// UpdateExtraInfo mocks base method.
func (m *MockCopilotChatMessageDAL) UpdateExtraInfo(ctx context.Context, messageId, extraInfo string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExtraInfo", ctx, messageId, extraInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateExtraInfo indicates an expected call of UpdateExtraInfo.
func (mr *MockCopilotChatMessageDALMockRecorder) UpdateExtraInfo(ctx, messageId, extraInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExtraInfo", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).UpdateExtraInfo), ctx, messageId, extraInfo)
}

// UpdateRateType mocks base method.
func (m *MockCopilotChatMessageDAL) UpdateRateType(ctx context.Context, messageId string, ratedType model.RateModelReplyEnum) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRateType", ctx, messageId, ratedType)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRateType indicates an expected call of UpdateRateType.
func (mr *MockCopilotChatMessageDALMockRecorder) UpdateRateType(ctx, messageId, ratedType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRateType", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).UpdateRateType), ctx, messageId, ratedType)
}

// UpdateTaskID mocks base method.
func (m *MockCopilotChatMessageDAL) UpdateTaskID(ctx context.Context, messageId, taskID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskID", ctx, messageId, taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaskID indicates an expected call of UpdateTaskID.
func (mr *MockCopilotChatMessageDALMockRecorder) UpdateTaskID(ctx, messageId, taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskID", reflect.TypeOf((*MockCopilotChatMessageDAL)(nil).UpdateTaskID), ctx, messageId, taskID)
}

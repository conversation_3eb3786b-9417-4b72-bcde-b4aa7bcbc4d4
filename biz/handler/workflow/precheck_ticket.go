package workflow

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
)

func NewPreCheckTicketHandler(service workflow.TicketService) handler.HandlerImplementationEnvolope {
	h := &PreCheckTicketHandler{
		service: service,
	}
	return handler.NewHandler(h.PreCheckTicket)
}

type PreCheckTicketHandler struct {
	service workflow.TicketService
}

// PreCheckTicket 预检查
func (h *PreCheckTicketHandler) PreCheckTicket(ctx context.Context, req *model.PreCheckTicketReq) (*model.PreCheckTicketResp, error) {
	// 检查用户信息，用户是否存在，用户租户是否和工单租户是一样的
	if err := h.service.CheckTicketUserInfo(ctx, conv.StrToInt64(req.TicketId, 0)); err != nil {
		return &model.PreCheckTicketResp{}, err
	}
	return h.service.PreCheck(ctx, req)
}

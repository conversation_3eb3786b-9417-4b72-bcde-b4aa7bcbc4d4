package data_migration

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_migration"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/iam"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	pg_parser "github.com/pganalyze/pg_query_go/v6"
	"go.uber.org/dig"
)

type CreateMigrationTicketHandler struct {
	actorClient          cli.ActorClient
	dsSvc                datasource.DataSourceService
	cnf                  config.ConfigProvider
	c3Conf               c3.ConfigProvider
	crossAuthSvc         crossauth.CrossServiceAuthorizationService
	iam                  iam.IAM
	migRepo              repository.MigrationRepo
	dataMigrationService data_migration.DataMigrationService
	idgen                idgen.Service
	workflowService      workflow.TicketWorkflowService
	approvalFlowService  approval_flow.ApprovalFlowService
	ticketService        workflow.TicketService
}

type CreateMigrationTicketHandlerIn struct {
	dig.In
	Handler              *CreateDbExportTaskHandler
	Cnf                  config.ConfigProvider
	DsSvc                datasource.DataSourceService
	CrossAuthSvc         crossauth.CrossServiceAuthorizationService
	C3                   c3.ConfigProvider
	IAM                  iam.IAM
	MigRepo              repository.MigrationRepo
	ActorClient          cli.ActorClient
	DataMigrationService data_migration.DataMigrationService
	IdGen                idgen.Service
	WorkflowService      workflow.TicketWorkflowService
	ApprovalFlowService  approval_flow.ApprovalFlowService
	TicketService        workflow.TicketService
}

func NewCreateMigrationTicketHandler(d CreateMigrationTicketHandlerIn) handler.HandlerImplementationEnvolope {
	h := &CreateMigrationTicketHandler{
		actorClient:          d.ActorClient,
		dsSvc:                d.DsSvc,
		crossAuthSvc:         d.CrossAuthSvc,
		cnf:                  d.Cnf,
		c3Conf:               d.C3,
		iam:                  d.IAM,
		migRepo:              d.MigRepo,
		dataMigrationService: d.DataMigrationService,
		idgen:                d.IdGen,
		workflowService:      d.WorkflowService,
		approvalFlowService:  d.ApprovalFlowService,
		ticketService:        d.TicketService,
	}
	return handler.NewHandler(h.CreateMigrationTicket)
}

func (h *CreateMigrationTicketHandler) CreateMigrationTicket(ctx context.Context, req *model.CreateMigrationTicketReq) (*model.CreateMigrationTicketResp, error) {
	log.Info(ctx, "CreateMigrationTicketReq is %s", req)
	// 1、check req params
	if err := h.checkReq(ctx, req); err != nil {
		log.Warn(ctx, "CreateMigrationTicketReq is err %v", err)
		return nil, err
	}

	// 2、检查实例是否是安全管控实例
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if userId == "" {
		userId = tenantId
	}
	// 3、检查用户是不是存在，不存在/已删除/禁用 不能发起工单
	isExist, err := h.dataMigrationService.IsUserExists(ctx, userId, tenantId)
	if err != nil || !isExist {
		return &model.CreateMigrationTicketResp{Code: model.ErrCode_Error, ErrMsg: "get user error"}, consts.ErrorOf(model.ErrorCode_UserNotJoinUserMgmt)
	}
	// 4、检查实例，需要从实例数据库中获取，需要判断是不是接入安全管控
	isInstanceAvailable, err := h.dataMigrationService.IsInstanceAvailable(ctx, tenantId, req.InstanceId)
	if err != nil || !isInstanceAvailable {
		return &model.CreateMigrationTicketResp{Code: model.ErrCode_Error, ErrMsg: "get instance info error"}, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 5、创建工单
	// 5.1 生成工单ID
	ticketId, err := h.idgen.NextID(ctx)
	if err != nil {
		errMsg := fmt.Sprintf("generate ticket id err:%v", err)
		log.Warn(ctx, errMsg)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 5.2 生成工单记录
	var (
		workflowId         int64 // 小基架工单这个字段走BPM，有值；火山工单没有值
		approvalFlowId     int64
		approvalTemplateId int64
	)
	rreq := &model.CreateTicketReq{
		InstanceType: req.InstanceType,
		InstanceId:   req.InstanceId,
	}
	approvalFlowId, approvalTemplateId, err = h.createApprovalFlow(ctx, rreq, tenantId)
	if err != nil {
		log.Warn(ctx, "createApprovalFlow error: %s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "create approval flow error")
	}
	migrationTicketInfo := &model.MigrationTicketInfo{
		ApprovalFlowId:     utils.Int64ToStr(approvalFlowId),
		ApprovalTemplateId: utils.Int64ToStr(approvalTemplateId),
		WorkflowId:         utils.Int64ToStr(workflowId),
		TicketId:           utils.Int64ToStr(ticketId),
		TenantId:           tenantId,
		UserId:             userId,
	}
	if _, err := h.dataMigrationService.CreateMigrationTicket(ctx, req, migrationTicketInfo); err != nil {
		return &model.CreateMigrationTicketResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	h.preCheck(ctx, utils.Int64ToStr(ticketId))
	return &model.CreateMigrationTicketResp{Code: model.ErrCode_Success, MigrationTicketId: utils.Int64ToStr(ticketId)}, nil
}

func (h *CreateMigrationTicketHandler) createApprovalFlow(ctx context.Context, req *model.CreateTicketReq, tenantId string) (int64, int64, error) {
	instanceInfo, err := h.approvalFlowService.GetInstanceInfo(ctx, req.InstanceId, req.InstanceType.String(), tenantId)
	if err != nil {
		log.Warn(ctx, "GetInstanceInfo error: %s", err.Error())
		return 0, 0, err
	}
	// 获取工单的审批模板id ticketApprovalTemplateId,这个东西是从dbw_instance的配置中获取的
	/*
		MySQL [dbwmgr]> select * from dbw_approval_flow_config where config_id=0;
		+-----------+-------------------------+-------------+------------------------------------------+-----------+----------------+------------------+----------------+-------------+---------------+------+---------+
		| config_id | config_name             | config_type | flow_scenes                              | tenant_id | create_user_id | create_user_name | modify_user_id | create_time | modify_time   | memo | deleted |
		+-----------+-------------------------+-------------+------------------------------------------+-----------+----------------+------------------+----------------+-------------+---------------+------+---------+
		|         0 | System Default Template |           0 | [{"TemplateId":0,"ScenesType":"Ticket"}] |           |                | NULL             |                |  1720582332 | 1721025512870 |      | 0       |
		+-----------+-------------------------+-------------+------------------------------------------+-----------+----------------+------------------+----------------+-------------+---------------+------+---------+
		1 row in set (0.01 sec)
	*/
	flowConfig, err := h.approvalFlowService.GetApprovalFlowConfig(ctx, instanceInfo.ApprovalFlowConfigId)
	if err != nil {
		log.Warn(ctx, "ListApprovalFlowConfig error: %s", err.Error())
		return 0, 0, err
	}
	if flowConfig == nil {
		log.Warn(ctx, "there is no flowConfig configId:%d", instanceInfo.ApprovalFlowConfigId)
		return 0, 0, fmt.Errorf(fmt.Sprintf("there is no flowConfig configId:%d", instanceInfo.ApprovalFlowConfigId))
	}
	ticketApprovalTemplateId := h.getTicketApprovalTemplateID(flowConfig)
	approvalFlowId, err := h.approvalFlowService.CreatApprovalFlowRecord(ctx, fmt.Sprintf("%d", ticketApprovalTemplateId), req.InstanceId)
	if err != nil {
		log.Warn(ctx, "CreatApprovalFlowRecord error: %s", err.Error())
		return 0, 0, err
	}
	return approvalFlowId, ticketApprovalTemplateId, nil
}

func (h *CreateMigrationTicketHandler) checkReq(ctx context.Context, req *model.CreateMigrationTicketReq) error {
	if err := req.IsValid(); err != nil {
		log.Warn(ctx, "Param is Invalid:[%+v]", err)
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if req.GetInstanceId() == "" || req.GetDatabaseName() == "" {
		log.Warn(ctx, "param instanceId is [%v],dbName is [%v] ", req.GetInstanceId(), req.GetDatabaseName())
		return consts.ErrorWithParam(model.ErrorCode_ParamError, "param instanceId or databaseName is empty")
	}
	switch req.GetTicketType() {
	case model.TicketType_DataMigrationExportSqlResult:
		if req.SqlResultExportParam == nil {
			return consts.ErrorWithParam(model.ErrorCode_InputParamError, "empty SqlResultExportParam ")
		}
		// 如果是sql结果集导出的情况，导出类型不是CSV，或者sql文本没有内容，那么返回报错
		if req.SqlResultExportParam.GetFileType() == model.FileType_EXCEL ||
			req.SqlResultExportParam.GetSqlText() == "" {
			return consts.ErrorOf(model.ErrorCode_ExportSqlResultParamError)
		}
		// 这里的解析,有一点点问题,就是对于with开头的MySQL命令,解析器解析不了,但是本身MySQL是可以执行的
		if err := h.checkSqlText(ctx, req); err != nil {
			log.Warn(ctx, "wrong sql text,err is %v", err)
			return consts.ErrorWithParam(model.ErrorCode_InputParamError, "wrong sql text,err is", err)
		}
		if req.SqlResultExportParam.GetFileType() == model.FileType_CLOUD_DOC {
			// 检查当前用户，当天的导出次数
			if err := h.checkReqLimit(ctx, req); err != nil {
				return err
			}
		}
	case model.TicketType_DataMigrationImport:
		if req.ImportParam == nil {
			return consts.ErrorWithParam(model.ErrorCode_InputParamError, "empty ImportParam")
		}
	case model.TicketType_DataMigrationExportDB:
		if req.DBExportParam == nil {
			return consts.ErrorWithParam(model.ErrorCode_InputParamError, "empty DBExportParam")
		}
		// CSV格式,不支持导出数据结构
		if req.DBExportParam.FileType == model.FileType_CSV &&
			strings.Contains(req.DBExportParam.ContentFormat.String(), "STRUCT") {
			log.Warn(ctx, "Export CSV not support table struct")
			return consts.ErrorOf(model.ErrorCode_ExportContentParamError)
		}
	default:
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "unknown migration type")
	}
	return nil
}

// 针对Cloud_Doc的SQL结果集导出选项,需要校验请求次数,默认24小时内只能请求10次
func (h *CreateMigrationTicketHandler) checkReqLimit(ctx context.Context, req *model.CreateMigrationTicketReq) error {
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	startTime := todayStart.UnixMilli()
	endTime := todayStart.UnixMilli() + 86400000
	todayCount, err := h.migRepo.CountUserFinishedTasks(ctx, req.GetCreateUser(), startTime, endTime, "SqlResultExport", req.InstanceId)
	if err != nil {
		// 加日志，改一下报错
		log.Warn(ctx, "get current user today success task error: %s", err.Error())
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	conf := h.cnf.Get(ctx)
	if todayCount >= int64(conf.InfieldSqlResultExportTaskLimit) {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "Exceeded the export task limit for the day, the maximum number of times is 10")
	}
	return nil
}

func (h *CreateMigrationTicketHandler) getTicketApprovalTemplateID(flowConfig *entity.ApprovalFlowConfig) int64 {
	/*
		select * from dbw_approval_flow_config
		flow_scenes: [{"TemplateId":1896360104861638656,"ScenesType":"Ticket"}]
	*/
	for _, flowScenes := range flowConfig.FlowScenes {
		if flowScenes.ScenesType == entity.TicketFlowType {
			return flowScenes.TemplateId
		}
	}
	return 0
}

func (h *CreateMigrationTicketHandler) preCheck(ctx context.Context, ticketId string) {
	go func() {
		_, _ = h.dataMigrationService.PreCheckMigrationTicket(ctx, &model.PreCheckMigrationTicketReq{TicketId: ticketId})
	}()
}

func (h *CreateMigrationTicketHandler) checkSqlText(ctx context.Context, req *model.CreateMigrationTicketReq) error {
	log.Info(ctx, "instance %v use explain command to check sql text", req.InstanceId)
	// NOTE 这里有一个问题,就是我们工具目前不支持with这类的语法,就算预检查通过了,执行也会失败,所以这里不需要解析,因为解析的结果受制于工具
	//return h.ExplainSQL(ctx, req)
	return nil
}

func (h *CreateMigrationTicketHandler) ExplainSQL(ctx context.Context, req *model.CreateMigrationTicketReq) error {
	cnf := h.cnf.Get(ctx)
	ds := &shared.DataSource{
		Type:             shared.DataSourceType(req.InstanceType),
		LinkType:         shared.Volc,
		ConnectTimeoutMs: cnf.TicketConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.TicketReadTimeout * 1000,
		WriteTimeoutMs:   cnf.TicketWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.TicketIdleTimeout * 1000,
		InstanceId:       req.InstanceId,
		Db:               req.DatabaseName,
	}
	// 获取安全管控的账号密码
	ds, err := h.ticketService.GetDBAccount(ctx, ds)
	if err != nil {
		log.Warn(ctx, "ticket: get db account err: %v", err.Error())
		return consts.ErrorOf(model.ErrorCode_ListAccountFail)
	}
	err = h.dsSvc.GetDatasourceAddress(ctx, ds)
	if err != nil {
		log.Warn(ctx, "ticket: get db address err: %v", err.Error())
		return consts.ErrorOf(model.ErrorCode_GetInstanceAddressFailed)
	}
	_, err = h.dsSvc.ExplainCommand(ctx, &datasource.ExplainCommandReq{
		Source:  ds,
		Command: req.SqlResultExportParam.GetSqlText(),
	})
	if err != nil {
		log.Warn(ctx, "ticket: explain command err: %v", err.Error())
		return consts.ErrorOf(model.ErrorCode_ExplainCommandError)
	}
	return nil
}

func checkPgFormat(ctx context.Context, sql string) error {
	// 0、解析SQL
	res, err := pg_parser.Parse(sql)
	if err != nil {
		log.Warn(ctx, "SQL statement %v error or unsupported, reason: %v", sql, err)
		return err
	}
	if len(res.Stmts) == 0 {
		log.Warn(ctx, fmt.Sprintf("SQL statement %v error or unsupported, reason: empty stmts", sql))
		return err
	}
	return nil
}

package data_copilot

import (
	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_copilot"
	consts2 "code.byted.org/infcs/dbw-mgr/biz/service/data_copilot/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"go.uber.org/dig"
)

type CopilotButtonGenerationHandler struct {
	CopilotChatMessageDal  dal.CopilotChatMessageDAL
	CopilotChatTaskDAL     dal.CopilotChatTaskDAL
	CopilotFunctionService data_copilot.CopilotFunctionService
	IdgenSvc               idgen.Service
	FornaxService          data_copilot.FornaxService
}

type CopilotButtonGenerationHandlerIn struct {
	dig.In
	CopilotChatMessageDal  dal.CopilotChatMessageDAL
	CopilotChatTaskDAL     dal.CopilotChatTaskDAL
	CopilotFunctionService data_copilot.CopilotFunctionService
	IdgenSvc               idgen.Service
	FornaxService          data_copilot.FornaxService
}

func NewCopilotButtonGenerationHandler(in CopilotButtonGenerationHandlerIn) handler.HandlerImplementationEnvolope {
	h := &CopilotButtonGenerationHandler{
		CopilotChatMessageDal:  in.CopilotChatMessageDal,
		CopilotChatTaskDAL:     in.CopilotChatTaskDAL,
		CopilotFunctionService: in.CopilotFunctionService,
		IdgenSvc:               in.IdgenSvc,
		FornaxService:          in.FornaxService,
	}

	return handler.NewHandler(h.CopilotButtonGeneration)
}

func (self *CopilotButtonGenerationHandler) CopilotButtonGeneration(ctx context.Context, req *model.CopilotButtonGenerationReq) (*model.CopilotButtonGenerationResp, error) {
	if strings.IsEmpty(req.MessageId) || strings.IsEmpty(req.ChatId) {
		return &model.CopilotButtonGenerationResp{}, nil
	}

	messageList, _ := self.BuildFunctionCallChatMessageList(ctx, req.ChatId, req.MessageId)

	//接下来，根据消息链，调用模型，获取到返回的信息，将这部分信息返回给前端
	variables := make(map[string]interface{})
	variables["historyMessage"] = messageList
	message := self.FornaxService.ExecutePrompt(ctx, nil, "dbw.copilot.button_generation", variables)
	if message == nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_InternalError)
	}

	if len(message.ToolCalls) == 0 {
		return &model.CopilotButtonGenerationResp{}, nil
	}

	//在返回给前端时，找到ExtraInfo信息，返回给前端，用作补充信息
	extraInfo := &model.CopilotExtraInfo{}
	for _, messageInfo := range messageList {
		if messageInfo.Extra != nil {
			extraInfo = messageInfo.Extra
		}
	}

	buttonInfos := []*model.ExtraButtonInfo{}
	//接下来，向ExtraInfo信息中补充Button的信息进去
	for _, toolCall := range message.ToolCalls {
		if toolCall.FunctionCall != nil {
			buttonInfos = append(buttonInfos, &model.ExtraButtonInfo{
				ButtonName: &toolCall.FunctionCall.Name,
				Arguments:  &toolCall.FunctionCall.Arguments,
				IsClick:    false,
			})
		}
	}
	extraInfo.ExtraButtons = buttonInfos

	//最后，再将Button的信息保存到本次消息的Message里面，用于后续再次查询的时候使用，在此之前，找到最后一条模型发起的MessageID然后保存
	messages, err := self.CopilotChatMessageDal.QueryMessageListByAfterMessageID(ctx, fwctx.GetTenantID(ctx), req.MessageId, req.ChatId)
	if err != nil {
		return nil, err
	}
	assistantMessage := messages[len(messages)-1]
	if assistantMessage.Role == string(consts2.RoleTypeAssistant) {
		self.CopilotChatMessageDal.UpdateExtraInfo(ctx, assistantMessage.ID, utils.Show(extraInfo))
	}

	return &model.CopilotButtonGenerationResp{
		ExtraInfo: extraInfo,
	}, nil
}

func (self *CopilotButtonGenerationHandler) BuildFunctionCallChatMessageList(ctx context.Context, chatID string, messageID string) ([]*consts2.ChatMessage, string) {
	tenantID := fwctx.GetTenantID(ctx)
	//根据MessageID，向前找到最近的一个user，那么这中间的所有内容就是本次的上下文信息
	//首先，我们查询最近1条用户发起的消息的记录
	param := dal.CopilotChatMessageParam{
		Role:  string(consts2.RoleTypeUser),
		Limit: 1,
	}
	messageLists, err := self.CopilotChatMessageDal.QueryMessageByCondition(ctx, fwctx.GetTenantID(ctx), chatID, param)
	if err != nil || len(messageLists) == 0 {
		log.Warn(ctx, "ExecutePrompt QueryMessageByCopilotMaxChatContextLength failed,  chatID is:%v, err is:%v", chatID, err)
		return nil, ""
	}
	log.Info(ctx, "buildFunctionCallChatMessageList QueryMessageByCondition messageLists result is:%v", utils.Show(messageLists))

	CreateTimeParam := dal.CopilotChatMessageParam{
		CreateTime: &messageLists[0].CreateTime,
	}
	ChatMessageList, err := self.CopilotChatMessageDal.QueryMessageByCondition(ctx, fwctx.GetTenantID(ctx), chatID, CreateTimeParam)
	if err != nil {
		log.Warn(ctx, "ExecutePrompt QueryMessageByCopilotMaxChatContextLength failed,  chatID is:%v, err is:%v", chatID, err)
		return nil, ""
	}
	log.Info(ctx, "buildFunctionCallChatMessageList QueryMessageByCondition ChatMessageList result is:%v", utils.Show(ChatMessageList))

	var taskIDList []string
	//接下来遍历所有的messageList，获取到里面所有的TaskID，去重后获得所有的taskID的集合
	for _, message := range ChatMessageList {
		taskIDList = append(taskIDList, message.TaskID)
	}

	//再根据TaskID找到所有的Task信息集合（获取FunctionCall的结果信息）
	taskInfoList, err := self.CopilotChatTaskDAL.QueryMessageListByTaskIDList(ctx, tenantID, taskIDList)
	if err != nil {
		return nil, ""
	}
	//然后，将所有的Task消息，按照TaskID进行分组，形成一个Map结构
	taskInfoMap := make(map[string][]*dao.CopilotChatTask)
	for _, task := range taskInfoList {
		if _, exists := taskInfoMap[task.TaskID]; !exists {
			taskInfoMap[task.TaskID] = []*dao.CopilotChatTask{}
		}
		// 将当前元素添加到对应键的切片中
		taskInfoMap[task.TaskID] = append(taskInfoMap[task.TaskID], task)
	}

	chatList := []*consts2.ChatMessage{}
	for _, message := range ChatMessageList {
		var toolCalls []*consts2.ToolCall
		if len(message.ToolCalls) > 0 {
			err := json.Unmarshal([]byte(message.ToolCalls), &toolCalls)
			if err != nil {
				log.Warn(ctx, "DescribeChatMessages Unmarshal extraInfo failed, extraInfo is %v", utils.Show(message.ToolCalls))
			}
		}
		extraInfo := &model.CopilotExtraInfo{}
		if message.ExtraInfo != "" {
			err := json.Unmarshal([]byte(message.ExtraInfo), extraInfo)
			if err != nil {
				log.Warn(ctx, "DescribeChatMessages Unmarshal extraInfo failed, extraInfo is %v", utils.Show(message.ExtraInfo))
			}
		}
		chatList = append(chatList, &consts2.ChatMessage{
			Role:      consts2.ParseRoleType(message.Role),
			Content:   message.Content,
			ToolCalls: toolCalls,
			Extra:     extraInfo,
		})

		//接下来，在这里判断一下是否存在taskID。如果存在，则说明需要将TaskID的结果补充到这里，作为内容输入
		if len(message.ToolCalls) > 0 && len(taskInfoMap[message.TaskID]) > 0 {
			tasks := taskInfoMap[message.TaskID]
			for _, taskInfo := range tasks {
				//只需要type=assistant，且content不为空的那部分数据，其他的数据都不需要
				if taskInfo.Role == string(consts2.RoleTypeAssistant) && !strings.IsEmpty(taskInfo.Content) {
					extraInfo := &model.CopilotExtraInfo{}
					if taskInfo.ExtraInfo != "" {
						err := json.Unmarshal([]byte(taskInfo.ExtraInfo), extraInfo)
						if err != nil {
							log.Warn(ctx, "DescribeChatMessages Unmarshal extraInfo failed, extraInfo is %v", utils.Show(taskInfo.ExtraInfo))
						}
					}

					//这部分数据由于源于Task部分，因此在作为上下文时，作为system类型返回出去
					chatList = append(chatList, &consts2.ChatMessage{
						Role:       consts2.RoleTypeTool,
						Content:    taskInfo.Content,
						ToolCallID: taskInfo.ToolCallID,
						Extra:      extraInfo,
					})
				}
			}
		}

	}

	log.Info(ctx, "buildFunctionCallChatMessageList finished, chatList is:%v, messageID is:%v", utils.Show(chatList), messageID)
	//由于在本次调用中，属于拼接产生的结果，因此不存在新的message消息，这里将入参的message返回出去
	return chatList, messageID
}

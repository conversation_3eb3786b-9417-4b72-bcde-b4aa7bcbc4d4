package data_copilot

import (
	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	consts2 "code.byted.org/infcs/dbw-mgr/biz/service/data_copilot/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"go.uber.org/dig"
)

type DescribeChatMessagesHandler struct {
	CopilotChatMessageDal dal.CopilotChatMessageDAL
	CopilotChatTaskDal    dal.CopilotChatTaskDAL
}

type DescribeChatMessagesIn struct {
	dig.In
	CopilotChatMessageDal dal.CopilotChatMessageDAL
	CopilotChatTaskDal    dal.CopilotChatTaskDAL
}

func NewDescribeChatMessageHandler(in DescribeChatMessagesIn) handler.HandlerImplementationEnvolope {
	h := &DescribeChatMessagesHandler{
		CopilotChatMessageDal: in.CopilotChatMessageDal,
		CopilotChatTaskDal:    in.CopilotChatTaskDal,
	}
	return handler.NewHandler(h.DescribeChatMessages)
}

func (self *DescribeChatMessagesHandler) DescribeChatMessages(ctx context.Context, req *model.DescribeChatMessagesReq) (*model.DescribeChatMessagesResp, error) {
	var messages []*model.ChatMessage
	if req.MessageID == nil && req.ChatID == nil {
		return &model.DescribeChatMessagesResp{}, nil
	}

	//如果MessageID为空，那就说明要查询所有的会话数据
	if req.MessageID == nil {
		messageList, err := self.CopilotChatMessageDal.QueryMessageListByChatID(ctx, fwctx.GetTenantID(ctx), *req.ChatID)
		if err != nil {
			log.Info(ctx, "DescribeChatMessages QueryMessageListByChatID failed, chatID is:%v, err is:%v", *req.ChatID, err)
			return &model.DescribeChatMessagesResp{}, err
		}
		//遍历所有查询到的messageList，组合信息
		for _, ms := range messageList {
			extraInfo := &model.CopilotExtraInfo{}
			if ms.ExtraInfo != "" {
				err := json.Unmarshal([]byte(ms.ExtraInfo), extraInfo)
				if err != nil {
					log.Warn(ctx, "DescribeChatMessages Unmarshal extraInfo failed, extraInfo is %v", utils.Show(ms.ExtraInfo))
				}
			}
			info := &model.ChatMessage{
				MessageID:    ms.ID,
				ChatID:       &ms.ChatID,
				TaskID:       &ms.TaskID,
				Content:      ms.Content,
				ChatType:     ms.Role,
				FunctionName: &ms.FunctionName,
				ExtraInfo:    extraInfo,
				UpdateTime:   &ms.UpdateTime,
			}
			if !strings.IsEmpty(ms.RatedType) {
				reply, _ := model.RateModelReplyEnumFromString(ms.RatedType)
				info.Reply = &reply
			}
			messages = append(messages, info)

			//这里，对message再做一次判断，如果本次message存在taskID，那么就需要将Task中，涉及到图标部分的信息都拿到列表页展示给前端，用于前端生成图表
			list, _ := self.QueryTaskListByMessage(ctx, ms)
			messages = append(messages, list...)
		}
		return &model.DescribeChatMessagesResp{
			MessageList: messages,
		}, nil
	}

	//如果messageID不为空，那么就说明要查询的是对应的那一条对话之后的所有数据
	messageList, err := self.CopilotChatMessageDal.QueryMessageListByAfterMessageID(ctx, fwctx.GetTenantID(ctx), *req.MessageID, *req.ChatID)
	if err != nil {
		log.Info(ctx, "DescribeChatMessages QueryMessageListByChatID failed, chatID is:%v, err is:%v", *req.ChatID, err)
		return &model.DescribeChatMessagesResp{}, err
	}
	for _, ms := range messageList {
		extraInfo := &model.CopilotExtraInfo{}
		if ms.ExtraInfo != "" {
			err := json.Unmarshal([]byte(ms.ExtraInfo), extraInfo)
			if err != nil {
				log.Warn(ctx, "DescribeChatMessages Unmarshal extraInfo failed, extraInfo is %v", utils.Show(ms.ExtraInfo))
			}
		}

		info := &model.ChatMessage{
			MessageID:    ms.ID,
			ChatID:       &ms.ChatID,
			TaskID:       &ms.TaskID,
			Content:      ms.Content,
			ChatType:     ms.Role,
			FunctionName: &ms.FunctionName,
			ExtraInfo:    extraInfo,
			UpdateTime:   &ms.UpdateTime,
		}
		if !strings.IsEmpty(ms.RatedType) {
			reply, _ := model.RateModelReplyEnumFromString(ms.RatedType)
			info.Reply = &reply
		}
		messages = append(messages, info)

		//这里，对message再做一次判断，如果本次message存在taskID，那么就需要将Task中，涉及到图标部分的信息都拿到列表页展示给前端，用于前端生成图表
		list, _ := self.QueryTaskListByMessage(ctx, ms)
		messages = append(messages, list...)
	}
	return &model.DescribeChatMessagesResp{
		MessageList: messages,
	}, nil
}

// QueryTaskListByMessage 根据Message信息，找到Task信息后，拼接成Message返回
func (self *DescribeChatMessagesHandler) QueryTaskListByMessage(ctx context.Context, messageInfo *dao.CopilotChatMessage) ([]*model.ChatMessage, error) {

	//首先，先判断当前Message的role是否为assistant，且Content为空，tools不为空，因为只有这条数据后面，才能跟Task的信息
	if messageInfo.Role != string(consts2.RoleTypeAssistant) || !strings.IsEmpty(messageInfo.Content) || strings.IsEmpty(messageInfo.ToolCalls) {
		return nil, nil
	}

	//如果找到这条数据了，那么就根据TaskID，查询所有的Task信息
	var taskIDList []string
	taskIDList = append(taskIDList, messageInfo.TaskID)
	taskInfoList, err := self.CopilotChatTaskDal.QueryMessageListByTaskIDList(ctx, fwctx.GetTenantID(ctx), taskIDList)
	if err != nil {
		log.Info(ctx, "DescribeChatMessages QueryTaskListByMessage failed, taskID is:%v, err is:%v", messageInfo.TaskID, err)
		return nil, nil
	}

	var messages []*model.ChatMessage
	//遍历TaskInfoList，这时候只需要拿role为tool的那部分结果拼接返回即可
	for _, task := range taskInfoList {
		if task.Role != string(consts2.RoleTypeTool) {
			continue
		}

		extraInfo := &model.CopilotExtraInfo{}
		if task.ExtraInfo != "" {
			err := json.Unmarshal([]byte(task.ExtraInfo), extraInfo)
			if err != nil {
				log.Warn(ctx, "DescribeChatMessages Unmarshal extraInfo failed, extraInfo is %v", utils.Show(task.ExtraInfo))
			}
		}
		info := &model.ChatMessage{
			MessageID:    messageInfo.ID,
			ChatID:       &messageInfo.ChatID,
			Content:      task.Content,
			ChatType:     task.Role,
			FunctionName: &task.FunctionName,
			ExtraInfo:    extraInfo,
		}
		messages = append(messages, info)
	}

	return messages, nil
}

package data_copilot

import (
	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"go.uber.org/dig"
)

type TriggerButtonHandler struct {
	CopilotChatMessageDAL dal.CopilotChatMessageDAL
}

type TriggerButtonIn struct {
	dig.In
	CopilotChatMessageDAL dal.CopilotChatMessageDAL
}

func NewTriggerButtonHandler(in TriggerButtonIn) handler.HandlerImplementationEnvolope {
	h := &TriggerButtonHandler{
		CopilotChatMessageDAL: in.CopilotChatMessageDAL,
	}
	return handler.NewHandler(h.Trigger<PERSON>on)
}

// TriggerButton Copilot的按钮触发，当用户点击了按钮后，可以调用该接口，或者创建了工单的时候，可以调用该接口，将工单ID进行保存
func (self *TriggerButtonHandler) TriggerButton(ctx context.Context, req *model.TriggerButtonReq) (*model.TriggerButtonResp, error) {

	if strings.IsEmpty(req.GetMessageID()) {
		return &model.TriggerButtonResp{}, nil
	}

	//接下来，根据messageID查询消息，获取到消息中的extraInfo信息
	message, err := self.CopilotChatMessageDAL.QueryMessageByMessageID(ctx, fwctx.GetTenantID(ctx), req.GetMessageID())
	if err != nil {
		return &model.TriggerButtonResp{}, nil
	}

	//获取并解析extraInfo中的内容
	extraInfo := &model.CopilotExtraInfo{}
	if message == nil || message.ExtraInfo == "" {
		return &model.TriggerButtonResp{}, nil
	}

	err = json.Unmarshal([]byte(message.ExtraInfo), extraInfo)
	if err != nil {
		log.Warn(ctx, "DescribeChatMessages Unmarshal extraInfo failed, extraInfo is %v", utils.Show(message.ExtraInfo))
	}

	//从内容中提取到按钮相关的信息，找到本次用户操作的按钮，对信息进行保存
	orderID := req.GetOrderID()
	for _, val := range extraInfo.ExtraButtons {
		if val.GetButtonName() == req.GetButtonName() {
			val.IsClick = true
			val.OrderID = &orderID
		}
	}

	//最终，再将信息更新回Message中
	self.CopilotChatMessageDAL.UpdateExtraInfo(ctx, req.GetMessageID(), utils.Show(extraInfo))

	return &model.TriggerButtonResp{}, nil
}

package data_copilot

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	mock_dal "code.byted.org/infcs/dbw-mgr/biz/test/mocks/dal"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"github.com/bytedance/mockey"
	"testing"
)

func TestDescribeActions(t *testing.T) {

	handler := TriggerButtonHandler{
		CopilotChatMessageDAL: &mock_dal.MockCopilotChatMessageDAL{},
	}

	message := &dao.CopilotChatMessage{
		ID:        "112321",
		ChatID:    "2",
		ExtraInfo: "{\"InstanceID\":\"mysql-f0ab3186f7be\",\"InstanceType\":\"MySQL\",\"ExtraButtons\":[{\"ButtonName\":\"SQL_Kill\"}]}",
	}

	mock1 := mockey.Mock((*mock_dal.MockCopilotChatMessageDAL).QueryMessageByMessageID).Return(message, nil).Build()
	defer mock1.UnPatch()
	i18nMock := mockey.Mock((*mock_dal.MockCopilotChatMessageDAL).UpdateExtraInfo).Return(nil).Build()
	defer i18nMock.UnPatch()
	req := &model.TriggerButtonReq{
		MessageID:  "112321",
		ButtonName: "SQL_Kill",
	}
	ctx := context.Background()
	handler.TriggerButton(ctx, req)
}

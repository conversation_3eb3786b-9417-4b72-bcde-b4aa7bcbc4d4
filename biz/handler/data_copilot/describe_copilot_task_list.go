package data_copilot

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	consts2 "code.byted.org/infcs/dbw-mgr/biz/service/data_copilot/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"go.uber.org/dig"
)

type DescribeCopilotTaskListHandler struct {
	CopilotChatDAL     dal.CopilotChatDAL
	CopilotChatTaskDAL dal.CopilotChatTaskDAL
}

type DescribeCopilotTaskListIn struct {
	dig.In
	CopilotChatDAL     dal.CopilotChatDAL
	CopilotChatTaskDAL dal.CopilotChatTaskDAL
}

func NewDescribeCopilotTaskListHandler(in DescribeCopilotTaskListIn) handler.HandlerImplementationEnvolope {
	h := &DescribeCopilotTaskListHandler{
		CopilotChatDAL:     in.CopilotChatDAL,
		CopilotChatTaskDAL: in.CopilotChatTaskDAL,
	}
	return handler.NewHandler(h.DescribeCopilotTaskList)
}

func (self *DescribeCopilotTaskListHandler) DescribeCopilotTaskList(ctx context.Context, req *model.DescribeCopilotTaskListReq) (*model.DescribeCopilotTaskListResp, error) {

	//首先，先获取到tenantID
	tenantId := fwctx.GetTenantID(ctx)
	taskMessageList, err := self.CopilotChatTaskDAL.QueryMessageListByTaskIDList(ctx, tenantId, req.TaskIDList)
	if err != nil {
		return nil, err
	}
	if len(taskMessageList) == 0 {
		return &model.DescribeCopilotTaskListResp{}, nil
	}

	messageResp := []*model.ChatMessage{}
	for _, val := range taskMessageList {
		extraInfo := &model.CopilotExtraInfo{}
		if val.ExtraInfo != "" {
			err := json.Unmarshal([]byte(val.ExtraInfo), extraInfo)
			if err != nil {
				log.Warn(ctx, "DescribeChatMessages Unmarshal extraInfo failed, extraInfo is %v", utils.Show(val.ExtraInfo))
			}
		}

		agentNameEnum := consts2.AgentNameMap[val.AgentName]
		info := &model.ChatMessage{
			MessageID:    val.ID,
			ChatID:       &val.ChatID,
			TaskID:       &val.TaskID,
			Content:      val.Content,
			ChatType:     val.Role,
			FunctionName: &val.FunctionName,
			ExtraInfo:    extraInfo,
			AgentName:    &agentNameEnum,
			UpdateTime:   &val.UpdateTime,
		}
		messageResp = append(messageResp, info)
	}

	return &model.DescribeCopilotTaskListResp{
		MessageList: messageResp,
	}, nil
}

package diagnosis

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/space_analysis"
	"code.byted.org/infcs/ds-lib/common/log"

	_ "code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"context"
	"go.uber.org/dig"
)

type DescribeDataBaseTablesHandlerIn struct {
	dig.In
	DataSource    datasource.DataSourceService
	ActorClient   cli.ActorClient
	SpaceAnalysis space_analysis.SqlSpaceAnalysisService
}

func NewDescribeDataBaseTablesHandler(in DescribeDataBaseTablesHandlerIn) handler.HandlerImplementationEnvolope {
	h := DescribeDataBaseTablesHandler{
		ds:            in.DataSource,
		actorClient:   in.ActorClient,
		spaceAnalysis: in.SpaceAnalysis,
	}
	return handler.NewHandler(h.DescribeDataBaseTables)
}

type DescribeDataBaseTablesHandler struct {
	ds            datasource.DataSourceService
	actorClient   cli.ActorClient
	spaceAnalysis space_analysis.SqlSpaceAnalysisService
}

func (h *DescribeDataBaseTablesHandler) DescribeDataBaseTables(ctx context.Context, req *model.DescribeDataBaseTablesReq) (*model.DescribeDataBaseTablesResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	return h.listTables(ctx, req)
}

func (h *DescribeDataBaseTablesHandler) checkReq(ctx context.Context, req *model.DescribeDataBaseTablesReq) error {
	if !h.ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedTypeV2(req.InstanceType)) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	if !req.IsSetPageSize() {
		req.PageSize = utils.Int32Ref(10)
	}
	if !req.IsSetPageNumber() {
		req.PageNumber = utils.Int32Ref(1)
	}
	return nil
}

func (h *DescribeDataBaseTablesHandler) listTables(ctx context.Context,
	req *model.DescribeDataBaseTablesReq) (ret *model.DescribeDataBaseTablesResp, err error) {
	resp, err := h.spaceAnalysis.DescribeDataBaseTables(ctx, req)
	if err != nil {
		log.Warn(ctx, "listTables error:%s", err.Error())
		return nil, nil
	}
	if resp != nil {
		ret = &model.DescribeDataBaseTablesResp{
			Total: utils.Int32Ref(int32(resp.Total)),
			Items: resp.Items,
		}
	} else {
		ret = &model.DescribeDataBaseTablesResp{
			Total: utils.Int32Ref(0),
			Items: []string{},
		}
	}
	return
}

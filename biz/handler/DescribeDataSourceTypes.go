package handler

import (
	"context"

	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/qjpcpu/fp"
)

func (h *DescribeDataSourceTypesHandler) DescribeDataSourceTypes(ctx context.Context, req *model.DescribeDataSourceTypesReq) (ret *model.DescribeDataSourceTypesResp, err error) {
	ret = &model.DescribeDataSourceTypesResp{
		DSTypes: []model.DSType{
			model.DSType_MySQL,
			model.DSType_VeDBMySQL,
			model.DSType_Redis,
			model.DSType_MetaRDS,
			model.DSType_Mongo,
			model.DSType_Postgres,
			model.DSType_MSSQL,
			model.DSType_ByteRDS,
			model.DSType_MySQLSharding,
			model.DSType_MetaMySQL,
			model.DSType_ByteDoc,
			model.DSType_DAIR,
		},
	}

	fp.StreamOf(ret.DSTypes).
		Reject(func(t model.DSType) bool {
			return t == model.DSType_MetaRDS && !h.isInternalAccount(ctx, fwctx.GetTenantID(ctx))
		}).
		Reject(func(t model.DSType) bool {
			//return t == model.DSType_MetaMySQL &&(fwctx.GetTenantID(ctx) != "1" || !h.isInternalAccount(ctx, fwctx.GetTenantID(ctx)))
			return t == model.DSType_MetaMySQL && (fwctx.GetTenantID(ctx) != h.Cp.Get(ctx).MetaMySQLTenantId)
		}).
		Reject(func(t model.DSType) bool {
			return t == model.DSType_VeDBMySQL && !h.Cp.Get(ctx).EnableVeDB
		}).
		Reject(func(t model.DSType) bool {
			return t == model.DSType_Postgres && h.Cp.Get(ctx).DisablePG
		}).
		//Reject(func(t model.DSType) bool {
		//	return t == model.DSType_DAIR && !h.Cp.Get(ctx).EnableDAIR
		//}).
		Reject(func(t model.DSType) bool {
			return t == model.DSType_ByteRDS && !h.Cp.Get(ctx).EnableByteRDS
		}).
		Reject(func(t model.DSType) bool {
			return t == model.DSType_ByteDoc && !h.Cp.Get(ctx).EnableByteRDS
		}).
		Reject(func(t model.DSType) bool {
			return t == model.DSType_MySQLSharding && !h.Cp.Get(ctx).EnableSharding
		}).
		ToSlice(&ret.DSTypes)
	return
}

func (h *DescribeDataSourceTypesHandler) isInternalAccount(ctx context.Context, tenantID string) bool {
	return fp.StreamOf(h.Cp.Get(ctx).InternalAccounts).
		ContainsBy(func(account string) bool {
			return account == tenantID
		})
}

type DescribeDataSourceTypesHandler struct {
	Cp config.ConfigProvider
}

func NewDescribeDataSourceTypesHandler(cp config.ConfigProvider) HandlerImplementationEnvolope {
	hder := &DescribeDataSourceTypesHandler{Cp: cp}
	return NewHandler(hder.DescribeDataSourceTypes)
}

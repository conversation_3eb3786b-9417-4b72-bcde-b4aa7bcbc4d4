package handler

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"math"
	"time"

	dbwInsSvc "code.byted.org/infcs/dbw-mgr/biz/service/dbwinstance"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"

	"code.byted.org/infcs/dbw-mgr/biz/dal"
	instance_entity "code.byted.org/infcs/dbw-mgr/biz/entity/instance"

	"gorm.io/gorm"

	dbwerrors "code.byted.org/infcs/dbw-mgr/biz/errors"
	"code.byted.org/infcs/dbw-mgr/biz/instance/control"
	"code.byted.org/infcs/dbw-mgr/biz/privilege/database"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/iam"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	dsutils "code.byted.org/infcs/ds-lib/common/utils"

	"github.com/qjpcpu/fp"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/entity/privilege"
	"code.byted.org/infcs/dbw-mgr/biz/privilege/user"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/metrics"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/group"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

func (h *CreateSessionHandler) CreateSession(ctx context.Context, req *model.CreateSessionReq) (ret *model.CreateSessionResp, err error) {
	if err = h.checkReq(ctx, req); err != nil {
		return
	}
	if err = h.checkSSL(ctx, req); err != nil {
		return
	}
	if err = h.checkConnectionThreshold(ctx, req); err != nil {
		return
	}
	if tenant.IsRDSMultiCloudPlatform(ctx, h.cnf) && req.DataSource.LinkType == model.LinkType_Volc {
		log.Debug(ctx, "multiplatform tenant %s", h.cnf.Get(ctx).RDSMultiPlatformTenants)
		if fwctx.GetUserID(ctx) != "" && !tenant.IsSuperAdminUser(ctx, h.cnf) {
			if req.DataSource.Type == model.DSType_Mongo {
				if err = h.fillMultiCloudPasswordForMongo(ctx, req); err != nil {
					return
				}
			} else {
				if err = h.handleMultiCloudRequest(ctx, req); err != nil {
					return
				}
			}
		}
	}
	ins, err := h.crtlInstanceManager.GetInstance(ctx, req.DataSource.GetInstanceId(), req.DataSource.LinkType.String())
	if err != nil {
		if err != gorm.ErrRecordNotFound && req.DataSource.LinkType != model.LinkType_Volc {
			err = consts.ErrorWithParam(model.ErrorCode_InstanceNotFound, req.DataSource.GetInstanceId())
			return
		}
		if req.DataSource.GetPassword() == "" {
			if err := h.tryFillPassword(ctx, req); err != nil {
				return nil, err
			}
		}
		if err = h.checkConn(ctx, req); err != nil {
			err = h.wrapError(ctx, req, err)
			return
		}
		if req.DataSource.Type == model.DSType_ByteRDS {
			err := h.handleByteCloudRDS(ctx, req)
			if err != nil {
				return nil, err
			}
		}
		ret, err = h.createSession(ctx, req)
		return
	} else {
		if req.DataSource.Type == model.DSType_ByteRDS {
			err := h.handleByteCloudRDS(ctx, req)
			if err != nil {
				return nil, err
			}
		}
		if err = h.checkPriv(ctx, req); err != nil {
			return
		}
	}
	// 检查当前用户是否为免密登录用户(仅校验MySQL)
	if req.DataSource.GetType() == model.DSType_MySQL ||
		req.DataSource.GetType() == model.DSType_VeDBMySQL ||
		req.DataSource.GetType() == model.DSType_Postgres ||
		req.DataSource.GetType() == model.DSType_ByteRDS ||
		req.DataSource.GetType() == model.DSType_MetaMySQL {
		if err = h.CheckFreePwdUser(ctx, req); err != nil {
			return
		}
		req.DataSource.Address = dsutils.StringRef(ins.Address)
	}
	if err = h.checkConn(ctx, req); err != nil {
		err = h.wrapError(ctx, req, err)
		return
	}
	ret, err = h.createSession(ctx, req)
	if err != nil {
		return
	}

	return
}

func (h *CreateSessionHandler) checkPriv(ctx context.Context, req *model.CreateSessionReq) error {
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	// 检查主账号是否落库
	h.userSvc.AddRootAccount(ctx, fwctx.GetTenantID(ctx))
	if tenant.IsRDSMultiCloudPlatform(ctx, h.cnf) && tenant.IsSuperAdminUser(ctx, h.cnf) {
		return nil
	}
	isUserManaged, err := h.userSvc.CheckUserIsManaged(ctx, userId, fwctx.GetTenantID(ctx))
	if err != nil {
		return err
	}
	if !isUserManaged {
		return consts.ErrorWithParam(model.ErrorCode_UserNotJoinUserMgmt, userId, req.DataSource.GetInstanceId())
	}
	isUpperAccount, err := h.workflowDal.IsUpperAccount(ctx, fwctx.GetTenantID(ctx), userId, req.DataSource.GetInstanceId())
	if err != nil {
		log.Warn(ctx, "获取创建用户角色失败，err：%v", err)
		return consts.ErrorWithParam(model.ErrorCode_InitControlInstanceFailed, userId, req.DataSource.GetInstanceId())
	}
	if !isUpperAccount {
		// 检查是否具有查询权限
		isGranted := h.userSvc.CheckUserHasSpecificPrivilege(ctx, req.DataSource.GetInstanceId(), userId, fwctx.GetTenantID(ctx), model.DbwPrivilegeType_QUERY)
		if !isGranted {
			return consts.ErrorWithParam(model.ErrorCode_PrivilegesNotFound, userId, req.DataSource.GetInstanceId())
		}
	}
	isAssociated, err := h.userSvc.CheckInstanceAssociatedSecGroup(ctx, req.DataSource)
	if err != nil {
		return err
	}
	if !isAssociated {
		return consts.ErrorWithParam(model.ErrorCode_NotAssociatedSecGroup, userId, req.DataSource.GetInstanceId())
	}
	return nil
}

func (h *CreateSessionHandler) fillMultiCloudPasswordForMongo(ctx context.Context, req *model.CreateSessionReq) error {
	return h.ensureDatabaseAccount(ctx, req, "dbOwner")
}

func (h *CreateSessionHandler) tryFillPassword(ctx context.Context, req *model.CreateSessionReq) error {
	pwd, err := h.getSavedPassword(ctx, req.DataSource.GetInstanceId(), req.DataSource.GetUsername())
	if err != nil {
		if err == gorm.ErrRecordNotFound {
		} else {
			return consts.ErrorWithParam(model.ErrorCode_CreateSessionError, " with ", req.DataSource.GetInstanceId(), err.Error())
		}
	} else {
		req.DataSource.Password = &pwd
	}
	return nil
}

func (h *CreateSessionHandler) checkConnectionThreshold(ctx context.Context, req *model.CreateSessionReq) error {
	cnf := h.cnf.Get(ctx)
	if cnf.MaxConnectionPerDataSource == 0 {
		return nil
	}
	connections, err := h.threshold.GetActiveConnections(ctx, conv.ToSharedDataSource(cnf, req.DataSource))
	if err != nil {
		log.Warn(ctx, "get active connections fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_CreateSessionError, " with ", req.DataSource.GetInstanceId(), err.Error())
	}
	if len(connections) >= int(cnf.MaxConnectionPerDataSource) {
		log.Warn(ctx, "%v,当前连接数%v,最大连接数%v", req.DataSource, len(connections), cnf.MaxConnectionPerDataSource)
		return consts.ErrorOf(model.ErrorCode_TooManyConnectionsError)
	}
	return nil
}

func (h *CreateSessionHandler) createSession(ctx context.Context, req *model.CreateSessionReq) (*model.CreateSessionResp, error) {
	sessionID, err := h.idg.NextIDStr(ctx)
	if err != nil {
		log.Warn(ctx, "get next id error:%s", err.Error())
		return nil, consts.BuildDBErrorWithParam(model.ErrorCode_CreateSessionError, req.DataSource.GetInstanceId(), err.Error())
	}

	cnf := h.cnf.Get(ctx)
	timeoutSeconds := cnf.SessionTimeout
	if req.TimeoutSeconds != nil {
		timeoutSeconds = req.GetTimeoutSeconds()
	}
	msg := &shared.CreateSession{
		Source:                conv.ToSharedDataSource(cnf, h.addDsn(req.DataSource)),
		SessionTimeoutSeconds: timeoutSeconds,
		DefaultConnectionName: req.GetDefaultConnectionName(),
	}
	actorClient := h.actorClient.KindOf(consts.SessionActorKind)
	callOpts := actorClient.NewCallOpts().WithTimeout(25 * time.Second).WithRetry(1)
	resp, err := actorClient.Call(ctx, sessionID, msg, callOpts)
	if err != nil {
		log.Warn(ctx, "create session error %s", err)
		return nil, consts.BuildDBErrorWithParam(model.ErrorCode_CreateSessionError, req.DataSource.GetInstanceId(), err.Error())
	}
	switch rsp := resp.(type) {
	case *shared.SessionCreated:
		if rsp.Code == shared.CreatedOK {
			log.Info(ctx, "create session success! %v", rsp.ErrorMessage)
			return &model.CreateSessionResp{
				SessionId: &sessionID,
				DefaultConnection: &model.ConnectionInfo{
					Id:        &rsp.DefaultConnectionId,
					Name:      &rsp.DefaultConnectionName,
					CurrentDB: &rsp.DefaultConnectionCurrentDb,
				},
			}, nil
		}
		log.Warn(ctx, "create session fail %v", rsp.ErrorMessage)
		return nil, consts.TranslateSharedToStandardError(rsp.StandardError)
	}
	log.Warn(ctx, "create session fail %#v", resp)
	return nil, consts.BuildDBErrorWithParam(model.ErrorCode_CreateSessionError, req.DataSource.GetInstanceId(), err.Error())
}

func (h *CreateSessionHandler) CheckFreePwdUser(ctx context.Context, req *model.CreateSessionReq) error {
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	// 检查主账号是否落库
	h.userSvc.AddRootAccount(ctx, fwctx.GetTenantID(ctx))
	// 检查是否为管控用户
	if tenant.IsRDSMultiCloudPlatform(ctx, h.cnf) && req.DataSource.LinkType == model.LinkType_Volc {
		log.Debug(ctx, "multiplatform tenant %s", h.cnf.Get(ctx).RDSMultiPlatformTenants)
		if fwctx.GetUserID(ctx) != "" && !tenant.IsSuperAdminUser(ctx, h.cnf) {
			return h.handleMultiCloudRequest(ctx, req)
		}
		return nil
	}
	//if req.DataSource.Type == model.DSType_ByteRDS {
	//	return h.handleByteCloudRDS(ctx, req)
	//}
	// i dont want to edit the old code
	isUserManaged, err := h.userSvc.CheckUserIsManaged(ctx, userId, fwctx.GetTenantID(ctx))
	if err != nil {
		return err
	}

	// 检查是否为管控实例
	isInstanceManaged, err := h.userSvc.CheckInstanceIsManaged(ctx, req.DataSource)
	if err != nil {
		return err
	}
	if isUserManaged && isInstanceManaged {
		// 高权限账号跳过权限校验
		isUpperAccount, err := h.workflowDal.IsUpperAccount(ctx, fwctx.GetTenantID(ctx), userId, req.DataSource.GetInstanceId())
		if err != nil {
			log.Warn(ctx, "获取创建用户角色失败，err：%v", err)
			return consts.ErrorOf(model.ErrorCode_UserMgmtPermissionDeny)
		}
		if !isUpperAccount {
			// 检查是否具有查询权限
			isGranted := h.userSvc.CheckUserHasSpecificPrivilege(ctx, req.DataSource.GetInstanceId(), userId, fwctx.GetTenantID(ctx), model.DbwPrivilegeType_QUERY)
			if !isGranted {
				return consts.ErrorOf(model.ErrorCode_PrivilegesNotFound)
			}
		}

		// 检查是否绑定安全规则
		isAssociated, err := h.userSvc.CheckInstanceAssociatedSecGroup(ctx, req.DataSource)
		if err != nil {
			return err
		}
		if !isAssociated {
			return consts.ErrorOf(model.ErrorCode_NotAssociatedSecGroup)
		}
		if err := h.userSvc.GetDBAccount(ctx, req.DataSource); err != nil {
			return consts.ErrorOf(model.ErrorCode_InstanceNotInSecureMode)
		}
	} else {
		if req.DataSource.GetUsername() == "" && req.DataSource.GetPassword() == "" {
			if !isUserManaged {
				return consts.ErrorOf(model.ErrorCode_UserNotJoinUserMgmt)
			} else {
				log.Info(ctx, "missing credentials")
				return consts.ErrorOf(model.ErrorCode_ParamError)
			}
		}
	}
	return nil
}

func (h *CreateSessionHandler) handleByteCloudRDS(ctx context.Context, req *model.CreateSessionReq) error {
	if err := h.addByteCloudInstanceToManaged(ctx, req); err != nil {
		return err
	}
	if err := h.addByteCloudUserToManaged(ctx); err != nil {
		return err
	}
	if err := h.grantBytecloudInstancePrivilege(ctx, req); err != nil {
		return err
	}
	//if err := h.grantByteCloudDatabasePrivilege(ctx, req); err != nil {
	//	return err
	//}
	return nil
}

func (h *CreateSessionHandler) getSavedPassword(ctx context.Context, instanceId, accountName string) (string, error) {
	env, err := h.consoleConnEnvRepo.GetByAccountName(ctx, instanceId, accountName)
	if err != nil {
		log.Warn(ctx, "get env error %s", err)
		return "", err
	}
	return env.Password, nil
}

func (h *CreateSessionHandler) handleMultiCloudRequest(ctx context.Context, req *model.CreateSessionReq) error {

	if err := h.addInstanceToManaged(ctx, req); err != nil {
		return err
	}
	if err := h.addUserToManaged(ctx); err != nil {
		return err
	}
	if err := h.grantDatabasePrivilege(ctx, req); err != nil {
		return err
	}
	if err := h.grantInstancePrivilege(ctx, req); err != nil {
		return err
	}
	if err := h.ensureDatabaseAccount(ctx, req, datasource.ReadOnly.String()); err != nil {
		return err
	}
	return nil
}

func (h *CreateSessionHandler) ensureDatabaseAccount(ctx context.Context, req *model.CreateSessionReq, priv string) error {
	pass, err := h.ds.CheckPrivilege(
		ctx,
		*req.DataSource.InstanceId,
		*req.DataSource.DBName,
		utils.GenDBUsername(*req.DataSource.DBName),
		priv,
		conv.ToSharedType(req.DataSource.Type))
	if err != nil {
		log.Warn(ctx, "check privilege error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_CreateSessionError, " with ", req.DataSource.GetInstanceId(), err.Error())
	}
	if !pass {
		if err := h.ds.CreateAccountAndGrant(
			ctx,
			*req.DataSource.InstanceId,
			utils.GenDBUsername(*req.DataSource.DBName),
			utils.GenDBPassword(*req.DataSource.DBName),
			*req.DataSource.DBName,
			priv,
			conv.ToSharedType(req.DataSource.Type),
		); err != nil {
			log.Warn(ctx, "create account or grant error %s", err)
			return consts.ErrorOf(model.ErrorCode_AddPrivilegeToDBFailed)
		}
	}
	req.DataSource.Username = dsutils.StringRef(utils.GenDBUsername(*req.DataSource.DBName))
	req.DataSource.Password = dsutils.StringRef(utils.GenDBPassword(*req.DataSource.DBName))
	return nil
}

func (h *CreateSessionHandler) addInstanceToManaged(ctx context.Context, req *model.CreateSessionReq) error {
	inst, err := h.dbwInstance.Get(ctx, req.DataSource.GetInstanceId(), req.DataSource.GetType().String(), req.DataSource.GetLinkType().String(), fwctx.GetTenantID(ctx), "")
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			err := h.addInstance(ctx, req.DataSource)
			if err != nil {
				log.Warn(ctx, "add instance error %s", err)
				return consts.ErrorWithParam(model.ErrorCode_SystemError, " add instance to Managed Error with ", req.DataSource.GetInstanceId(), err.Error())
			}
			return nil
		}
		log.Warn(ctx, "get instance error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, " add instance to Managed Error with ", req.DataSource.GetInstanceId(), err.Error())
	}
	//初始化放在前面判断，用于补充新规则之后，补充更新

	//初始化规则的时候，把此处逻辑放前面，这样可以针对没有安全规则的场景进行更新
	secId, err := h.securitySvc.InitSecurityGroup(ctx, *req.DataSource.InstanceId, conv.ToSharedType(req.DataSource.Type))
	if err != nil {
		log.Warn(ctx, "init security template error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, " add instance to Managed Error with ", req.DataSource.GetInstanceId(), err.Error())
	}

	if inst.ControlMode == 1 {
		return nil
	}
	inst.DatabaseUser = utils.GenDBUsername(*req.DataSource.DBName)
	inst.DatabasePassword = utils.EncryptData(utils.GenDBPassword(*req.DataSource.DBName), *req.DataSource.InstanceId)
	inst.ControlMode = 1
	inst.ApprovalFlowConfigId = 0
	inst.SecurityGroupId = secId
	if err := h.dbwInstance.UpdateInstance(ctx, inst); err != nil {
		return err
	}

	return nil
}

func (h *CreateSessionHandler) addByteCloudInstanceToManaged(ctx context.Context, req *model.CreateSessionReq) error {
	isManaged, err := h.userSvc.CheckInstanceIsManaged(ctx, req.DataSource)
	if err != nil {
		log.Warn(ctx, "check instance managed error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_InternalError, "check instance managed error")
	}

	//初始化规则的时候，把此处逻辑放前面，这样可以针对没有安全规则的场景进行更新
	secId, err := h.securitySvc.InitSecurityGroup(ctx, *req.DataSource.InstanceId, conv.ToSharedType(req.DataSource.Type))
	if err != nil {
		log.Warn(ctx, "init security template error %s", err)
		return err
	}

	if isManaged {
		return nil
	}
	ID, err := h.idg.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "generate id error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "generate id error")
	}
	var opts []instance_entity.Option
	opts = append(opts, instance_entity.WithSource(req.DataSource.LinkType))
	ctrlInstance := instance_entity.NewCtrlInstance(
		ID,
		*req.DataSource.InstanceId,
		req.DataSource.Type.String(),
		fwctx.GetTenantID(ctx),
		"",
		"",
		0,
		secId,
		opts...,
	)
	if err := h.crtlInstanceManager.AddInstance(ctx, ctrlInstance); err != nil {
		return err
	}
	return nil
}

func (h *CreateSessionHandler) addUserToManaged(ctx context.Context) error {
	userManaged, err := h.userSvc.CheckUserIsManaged(ctx, fwctx.GetUserID(ctx), fwctx.GetTenantID(ctx))
	//userManaged, err := h.userSvc.CheckUserIsManaged(ctx, "213877", fwctx.GetTenantID(ctx))
	if err != nil {
		log.Warn(ctx, "check user error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "check user error")
	}
	if userManaged {
		return nil
	}
	ID, err := h.idg.NextID(ctx)
	if err != nil {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "add User To Managed error")
	}
	userId := fwctx.GetUserID(ctx)
	userName, err := h.iam.GetUserNameById(ctx, "", userId, "DbwIAMUserReadonlyRole")
	if err != nil {
		log.Warn(ctx, "get username error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "add User To Managed error")
	}
	groupUser := privilege.NewDefaultGroupUser(ID, userId, userName, fwctx.GetTenantID(ctx))
	if err := h.userManager.AddUser(ctx, groupUser); err != nil {
		log.Warn(ctx, "add user error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "add User To Managed error")
	}
	return nil
}

func (h *CreateSessionHandler) addByteCloudUserToManaged(ctx context.Context) error {
	userManaged, err := h.userSvc.CheckUserIsManaged(ctx, fwctx.GetUserID(ctx), fwctx.GetTenantID(ctx))
	if err != nil {
		log.Warn(ctx, "check user error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "check user error")
	}
	if userManaged {
		return nil
	}
	ID, err := h.idg.NextID(ctx)
	if err != nil {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "addByteCloudUserToManaged error")
	}
	groupUser := privilege.NewDefaultGroupUser(ID, fwctx.GetUserID(ctx), fwctx.GetUserID(ctx), fwctx.GetTenantID(ctx))
	if err := h.userManager.AddUser(ctx, groupUser); err != nil {
		log.Warn(ctx, "add user error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "addByteCloudUserToManaged error")
	}
	return nil
}

// keep code
//func (h *CreateSessionHandler) grantInstancePrivilege(ctx context.Context, req *model.CreateSessionReq) error {
//	ID, err := h.idg.NextID(ctx)
//	if err != nil {
//		return consts.ErrorOf(model.ErrorCode_InternalError)
//	}
//	userId := fwctx.GetUserID(ctx)
//	userName, err := h.iam.GetUserNameById(ctx, "", userId, "DbwIAMUserReadonlyRole")
//	if err != nil {
//		log.Warn(ctx, "get user name error %s", err)
//		return consts.ErrorOf(model.ErrorCode_InternalError)
//	}
//	_ = &privilege.InstancePrivilege{
//		ID:            ID,
//		TenantID:      fwctx.GetTenantID(ctx),
//		InstanceId:    *req.DataSource.InstanceId,
//		InstanceType:  req.DataSource.Type.String(),
//		UserID:        userId,
//		UserName:      userName,
//		PrivilegeType: model.DbwPrivilegeType_QUERY.String(),
//		Grantor:       fwctx.GetTenantID(ctx),
//		ExpiredAt:     math.MaxInt32,
//	}
//	return nil
//}

func (h *CreateSessionHandler) grantDatabasePrivilege(ctx context.Context, req *model.CreateSessionReq) error {
	hasPriv := h.userSvc.CheckDatabasePrivilege(
		ctx,
		*req.DataSource.InstanceId,
		*req.DataSource.DBName,
		fwctx.GetUserID(ctx),
		fwctx.GetTenantID(ctx),
		model.DbwPrivilegeType_QUERY,
		false)
	if hasPriv {
		return nil
	} else {
		sensitiveDB, err := h.sensitiveDatabaseRepo.Get(ctx, *req.DataSource.InstanceId, *req.DataSource.DBName)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Warn(ctx, "get sensitive database error %s", err)
				return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
			}
		}
		log.Info(ctx, "is sensitive database")
		if sensitiveDB != nil {
			return nil
		}
	}
	ID, err := h.idg.NextID(ctx)
	if err != nil {
		return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
	}
	userId := fwctx.GetUserID(ctx)
	userName, err := h.iam.GetUserNameById(ctx, "", userId, "DbwIAMUserReadonlyRole")
	if err != nil {
		log.Warn(ctx, "get user name error %s", err)
		return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
	}
	databasePrivilege := &privilege.DatabasePrivilege{
		ID:            ID,
		TenantID:      fwctx.GetTenantID(ctx),
		InstanceId:    req.DataSource.GetInstanceId(),
		InstanceType:  req.DataSource.Type.String(),
		DbName:        req.DataSource.GetDBName(),
		UserID:        userId,
		UserName:      userName,
		PrivilegeType: model.DbwPrivilegeType_QUERY.String(),
		Grantor:       fwctx.GetTenantID(ctx),
		ExpiredAt:     math.MaxInt32,
	}
	if err := h.databasePrivilegeManager.AddPrivilege(ctx, databasePrivilege); err != nil {
		log.Warn(ctx, "add database privilege error %s", err)
		return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
	}
	return nil
}

func (h *CreateSessionHandler) grantInstancePrivilege(ctx context.Context, req *model.CreateSessionReq) error {
	now := time.Now().Unix()
	var priList []*dao.InstancePrivilege
	userId := fwctx.GetUserID(ctx)
	userName, err := h.iam.GetUserNameById(ctx, "", userId, "DbwIAMUserReadonlyRole")
	if err != nil {
		log.Warn(ctx, "get user name error %s", err)
		return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
	}
	for _, priType := range []string{model.DbwPrivilegeType_DAS_READ.String(), model.DbwPrivilegeType_DAS_READWRITE.String()} {
		existResources, err := h.instancePermDal.ListByResource(ctx, req.DataSource.GetInstanceId(), priType, fwctx.GetTenantID(ctx), true, []string{fwctx.GetUserID(ctx)}, []string{})
		if err != nil {
			log.Warn(ctx, "CreateSessionHandler ListByResource failed, err=%v", err)
			return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
		}
		if existResources == nil || len(existResources) == 0 {
			Id, err := h.idg.NextID(ctx)
			if err != nil {
				log.Warn(ctx, "Generate ID failed %+v", err)
				return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
			}
			priList = append(priList, &dao.InstancePrivilege{
				ID:            Id,
				TenantID:      fwctx.GetTenantID(ctx),
				InstanceId:    req.DataSource.GetInstanceId(),
				InstanceType:  req.DataSource.GetType().String(),
				UserID:        userId,
				UserName:      userName,
				GroupId:       0,
				PrivilegeType: priType,
				State:         model.PermState_NORMAL.String(),
				Grantor:       fwctx.GetUserID(ctx),
				CreatedAt:     now,
				ExpiredAt:     math.MaxInt32,
				UpdatedAt:     now,
			})
		}
	}
	err = h.instancePermDal.BatchSave(ctx, priList)
	if err != nil {
		log.Warn(ctx, "CreateSessionHandler BatchSave failed, err=%v", err)
	}
	return nil
}

func (h *CreateSessionHandler) grantBytecloudInstancePrivilege(ctx context.Context, req *model.CreateSessionReq) error {
	now := time.Now().Unix()
	var priList []*dao.InstancePrivilege
	userId := fwctx.GetUserID(ctx)
	userName := userId
	for _, priType := range []string{model.DbwPrivilegeType_QUERY.String(), model.DbwPrivilegeType_DAS_READ.String(), model.DbwPrivilegeType_DAS_READWRITE.String()} {
		existResources, err := h.instancePermDal.ListByResource(ctx, req.DataSource.GetInstanceId(), priType, fwctx.GetTenantID(ctx), true, []string{fwctx.GetUserID(ctx)}, []string{})
		if err != nil {
			log.Warn(ctx, "CreateSessionHandler ListByResource failed, err=%v", err)
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		if existResources == nil || len(existResources) == 0 {
			Id, err := h.idg.NextID(ctx)
			if err != nil {
				log.Warn(ctx, "Generate ID failed %+v", err)
				return consts.ErrorOf(model.ErrorCode_InternalError)
			}
			priList = append(priList, &dao.InstancePrivilege{
				ID:            Id,
				TenantID:      fwctx.GetTenantID(ctx),
				InstanceId:    req.DataSource.GetInstanceId(),
				InstanceType:  req.DataSource.GetType().String(),
				UserID:        userId,
				UserName:      userName,
				GroupId:       0,
				PrivilegeType: priType,
				State:         model.PermState_NORMAL.String(),
				Grantor:       fwctx.GetUserID(ctx),
				CreatedAt:     now,
				ExpiredAt:     math.MaxInt32,
				UpdatedAt:     now,
			})
		}
	}
	err := h.instancePermDal.BatchSave(ctx, priList)
	if err != nil {
		log.Warn(ctx, "CreateSessionHandler BatchSave failed, err=%v", err)
		return err
	}
	return nil
}

func (h *CreateSessionHandler) grantByteCloudDatabasePrivilege(ctx context.Context, req *model.CreateSessionReq) error {
	hasPriv := h.userSvc.CheckDatabasePrivilege(
		ctx,
		*req.DataSource.InstanceId,
		*req.DataSource.DBName,
		fwctx.GetUserID(ctx),
		fwctx.GetTenantID(ctx),
		model.DbwPrivilegeType_QUERY,
		false)
	if hasPriv {
		return nil
	}
	ID, err := h.idg.NextID(ctx)
	if err != nil {
		return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
	}
	userId := fwctx.GetUserID(ctx)
	userName := userId
	databasePrivilege := &privilege.DatabasePrivilege{
		ID:            ID,
		TenantID:      fwctx.GetTenantID(ctx),
		InstanceId:    req.DataSource.GetInstanceId(),
		InstanceType:  req.DataSource.Type.String(),
		DbName:        req.DataSource.GetDBName(),
		UserID:        userId,
		UserName:      userName,
		PrivilegeType: model.DbwPrivilegeType_QUERY.String(),
		Grantor:       fwctx.GetTenantID(ctx),
		ExpiredAt:     math.MaxInt32,
	}
	if err := h.databasePrivilegeManager.AddPrivilege(ctx, databasePrivilege); err != nil {
		log.Warn(ctx, "add database privilege error %s", err)
		return consts.BuildDBErrorWithParam(model.ErrorCode_AddPrivilegeToDBFailed, req.DataSource.GetInstanceId(), err.Error())
	}
	return nil
}

func (h *CreateSessionHandler) cacheRedisDomain(ctx context.Context, req *model.CreateSessionReq) {
	err := h.actorClient.KindOf(consts.DiscoActorKind).
		Send(ctx, consts.SingletonActorName, &shared.CacheDomain{
			Info: &shared.UserInfo{
				InstanceId: *req.DataSource.InstanceId,
				TenantId:   fwctx.GetTenantID(ctx),
			},
		})
	if err != nil {
		log.Warn(ctx, "Send Disco Actor Cache Instance %s Domain Failed", *req.DataSource.InstanceId)
	}
}

func (h *CreateSessionHandler) checkReq(ctx context.Context, req *model.CreateSessionReq) error {
	if req == nil || req.DataSource == nil {
		log.Warn(ctx, "request is empty")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	ds := req.DataSource
	if ds.LinkType == model.LinkType_Volc && ds.GetInstanceId() == "" {
		log.Warn(ctx, "missing instance id")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	//if ds.GetUsername() == "" || ds.GetPassword() == "" {
	//	log.Info(ctx, "missing credentials")
	//	return consts.ErrorOf(model.ErrorCode_ParamError)
	//}
	return nil
}

func (h *CreateSessionHandler) addDsn(ds *model.DataSource) *model.DataSource {
	if ds == nil {
		return ds
	}
	if ds.ExtraDsn == nil {
		ds.ExtraDsn = map[string]string{}
	}
	switch ds.Type {
	case model.DSType_MySQL:
		ds.ExtraDsn["multiStatements"] = "true"
	}
	return ds
}

func (h *CreateSessionHandler) checkConn(ctx context.Context, req *model.CreateSessionReq) error {
	if req.DataSource.Type == model.DSType_MSSQL {
		return nil
	}
	conf := h.cnf.Get(ctx)
	counter := h.getCounterName(ctx, req)
	if resp, err := h.actorClient.KindOf(consts.CounterKind).Call(ctx, counter, &shared.GetCounter{}); err == nil {
		switch msg := resp.(type) {
		case *shared.CounterInfo:
			if msg.Count >= conf.MaxLoginFailCount {
				log.Warn(ctx, "login failed for %v times", msg.Count)
				return consts.BuildDBErrorWithParam(model.ErrorCode_ReachMaxRetryTimes, req.DataSource.GetInstanceId(), msg.Count)
			}
		}
	}
	if err := h.ds.CheckConn(ctx, conv.ToSharedDataSource(conf, req.DataSource)); err != nil {
		h.actorClient.KindOf(consts.CounterKind).Send(ctx, counter, &shared.IncrCounter{Num: 1})
		return err
	}
	h.actorClient.KindOf(consts.CounterKind).Send(ctx, counter, &shared.ResetCounter{})
	return nil
}

func (h *CreateSessionHandler) getCounterName(ctx context.Context, req *model.CreateSessionReq) string {
	ds := req.DataSource
	tokens := []string{
		ds.GetType().String(),
		ds.GetLinkType().String(),
		ds.GetAddress(),
		ds.GetInstanceId(),
		ds.GetRegionId(),
		ds.GetECSInstanceId(),
	}
	return fmt.Sprintf(`%x`, md5.Sum([]byte(fp.StreamOf(tokens).JoinStrings(","))))
}

func (h *CreateSessionHandler) checkSSL(ctx context.Context, req *model.CreateSessionReq) error {
	if req.DataSource.LinkType != model.LinkType_Volc {
		return nil
	}
	sslInfo, err := h.ds.DescribeDBInstanceSSL(ctx, &datasource.DescribeDBInstanceSSLReq{
		InstanceId: *req.DataSource.InstanceId,
		Type:       shared.DataSourceType(int32(req.DataSource.Type)),
	})
	if err != nil {
		log.Warn(ctx, "DescribeDBInstanceSSL error: %s", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if sslInfo.SSLEnable {
		log.Warn(ctx, "DescribeDBInstanceSSL %+v", sslInfo)
		return consts.ErrorOf(model.ErrorCode_NotSupportSsl)
	}
	return nil
}

func (h *CreateSessionHandler) addInstance(ctx context.Context, ds *model.DataSource) error {
	secId, err := h.securitySvc.InitSecurityGroup(ctx, ds.GetInstanceId(), conv.ToSharedType(ds.Type))
	if err != nil {
		log.Warn(ctx, "init security template error %s", err)
		return err
	}
	ID, err := h.idg.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "generate id error %s", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	var opts []instance_entity.Option
	opts = append(opts, instance_entity.WithSource(model.LinkType_Volc))
	ctrlInstance := instance_entity.NewCtrlInstance(
		ID,
		ds.GetInstanceId(),
		ds.Type.String(),
		fwctx.GetTenantID(ctx),
		utils.GenDBUsername(*ds.DBName),
		utils.EncryptData(utils.GenDBPassword(*ds.DBName), *ds.InstanceId),
		0,
		secId,
		opts...,
	)
	if err := h.crtlInstanceManager.AddInstance(ctx, ctrlInstance); err != nil {
		return err
	}
	return nil
}

func (h *CreateSessionHandler) wrapError(ctx context.Context, req *model.CreateSessionReq, err error) error {
	if err == nil {
		return nil
	}
	if dbwerrors.IsWhiteIPError(err) {
		return consts.ErrorOf(model.ErrorCode_AllowlistError)
	}
	if dbwerrors.IsEmptyVPCError(err) {
		return consts.ErrorOf(model.ErrorCode_NotSupprotNoVpcInstance)
	}
	if (dbwerrors.IsAccessDeniedError(err) || dbwerrors.IsUsernamePasswordError(err)) && isRDS(req.DataSource.Type) {
		return consts.ErrorOf(model.ErrorCode_CheckRDSConnectionFailed)
	} else if dbwerrors.IsUsernamePasswordError(err) {
		return consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
	}
	if dbwerrors.IsOperateAllowlistError(err) {
		return consts.ErrorOf(model.ErrorCode_ModifyInstanceAllowListFailed)
	}
	if dbwerrors.IsLoginFailedError(err) {
		return consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
	}
	if dbwerrors.IsPSMAuthFailed(err) {
		return consts.ErrorOf(model.ErrorCode_PSMAuthFailed)
	}
	if standardErr, ok := err.(consts.StandardError); ok {
		errorCode := model.ErrorCode(standardErr.GetCode())
		if errorCode == model.ErrorCode_ReachMaxRetryTimes {
			return err
		}
	}
	return consts.ErrorOf(model.ErrorCode_CreateSessionError)
}

func (h *CreateSessionHandler) dsIsSupportControlMode(dsType model.DSType) bool {
	supportedDs := []model.DSType{model.DSType_MySQL, model.DSType_VeDBMySQL, model.DSType_Postgres, model.DSType_MySQLSharding, model.DSType_MSSQL, model.DSType_ByteRDS}
	return slices.Contains(supportedDs, dsType)
}

func isRDS(dsType model.DSType) bool {
	switch dsType {
	case model.DSType_MySQL:
		return true
	default:
		return false
	}
}

type CreateSessionHandler struct {
	cnf                      config.ConfigProvider
	actorClient              cli.ActorClient
	threshold                metrics.ConnectionMetrics
	idg                      idgen.Service
	ds                       datasource.DataSourceService
	userSvc                  usermgmt.UserService
	userManager              user.Manager
	databasePrivilegeManager database.Manager
	crtlInstanceManager      control.InstanceManager
	iam                      iam.IAM
	sensitiveDatabaseRepo    repository.SensitiveDatabaseRepo
	workflowDal              dal.WorkflowDAL
	securitySvc              group.MultiCloudGroupService
	consoleConnEnvRepo       repository.ConsoleConnEnvRepo
	instancePermDal          dal.InstancePrivilegeDAL
	dbwInstance              dal.DbwInstanceDAL
	dbwInstanceSvc           dbwInsSvc.DbwInstanceInterface
}

type CreateSessionHandlerOut struct {
	dig.Out
	HandlerImplEnvolope HandlerImplementationEnvolope
	Handler             *CreateSessionHandler
}

func NewCreateSessionHandler(
	cnf config.ConfigProvider,
	actorClient cli.ActorClient,
	threshold metrics.ConnectionMetrics,
	idg idgen.Service,
	ds datasource.DataSourceService,
	userSvc usermgmt.UserService,
	workflowDal dal.WorkflowDAL,
	sensitiveDatabaseRepo repository.SensitiveDatabaseRepo,
	crtlInstanceManager control.InstanceManager,
	iam iam.IAM,
	userManager user.Manager,
	databasePrivilegeManager database.Manager,
	securitySvc group.MultiCloudGroupService,
	consoleConnEnvRepo repository.ConsoleConnEnvRepo,
	instancePermDal dal.InstancePrivilegeDAL,
	dbwInstance dal.DbwInstanceDAL,
	dbwInstanceSvc dbwInsSvc.DbwInstanceInterface,
) CreateSessionHandlerOut {
	hder := &CreateSessionHandler{
		cnf:         cnf,
		actorClient: actorClient,
		threshold:   threshold,
		idg:         idg, ds: ds,
		userSvc:                  userSvc,
		workflowDal:              workflowDal,
		sensitiveDatabaseRepo:    sensitiveDatabaseRepo,
		crtlInstanceManager:      crtlInstanceManager,
		iam:                      iam,
		userManager:              userManager,
		databasePrivilegeManager: databasePrivilegeManager,
		securitySvc:              securitySvc,
		consoleConnEnvRepo:       consoleConnEnvRepo,
		instancePermDal:          instancePermDal,
		dbwInstance:              dbwInstance,
		dbwInstanceSvc:           dbwInstanceSvc,
	}
	return CreateSessionHandlerOut{
		HandlerImplEnvolope: NewHandler(hder.CreateSession),
		Handler:             hder,
	}
}

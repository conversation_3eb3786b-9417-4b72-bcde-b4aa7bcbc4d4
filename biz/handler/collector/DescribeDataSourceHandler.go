package collector

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/conv"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
)

type DescribeDataSourceHandler struct {
	idSvc       idgen.Service
	actorClient cli.ActorClient
	loc         location.Location
	source      datasource.DataSourceService
	c3Conf      c3.ConfigProvider
	cnf         config.ConfigProvider
}

func NewDescribeDataSourceHandlerHandler(IdSvc idgen.Service, ActorClient cli.ActorClient, Source datasource.DataSourceService,
	Loc location.Location, C3Conf c3.ConfigProvider, Cnf config.ConfigProvider) handler.HandlerImplementationEnvolope {
	d := &DescribeDataSourceHandler{
		idSvc:       IdSvc,
		actorClient: ActorClient,
		loc:         Loc,
		source:      Source,
		c3Conf:      C3Conf,
		cnf:         Cnf,
	}
	return handler.NewHandler(d.DescribeDataSource)
}

func (d *DescribeDataSourceHandler) DescribeDataSource(ctx context.Context, req *model.DescribeDataSourceReq) (*model.DescribeDataSourceResp, error) {
	resp, err := d.createConnection(ctx, req.InstanceId, req.InstanceType)
	if err != nil {
		return nil, err
	}

	return &model.DescribeDataSourceResp{
		DataSourceList: resp,
	}, nil
}

func (d *DescribeDataSourceHandler) createConnection(ctx context.Context, InstanceId string, InstanceType model.InstanceType) ([]*model.DescribeDataSourceInfo, error) {
	c3Cfg := d.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	cnf := d.cnf.Get(ctx)
	ds := &shared.DataSource{
		Type:             shared.DataSourceType(InstanceType),
		LinkType:         shared.Volc,
		User:             c3Cfg.DBWAccountName,
		Password:         d.getAccountPassword(c3Cfg.DbwAccountPasswordGenKey, InstanceId),
		ConnectTimeoutMs: cnf.ConnectionConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.ConnectionReadTimeout * 1000,
		WriteTimeoutMs:   cnf.ConnectionWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.ConnectionIdleTimeout * 1000,
		InstanceId:       InstanceId,
	}

	//首先先查询一下当前实例是否存在，不存在的话直接返回NotFound
	_, err := d.source.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: InstanceId,
		Type:       shared.DataSourceType(InstanceType),
	})
	if err != nil {
		log.Warn(ctx, "DescribeDataSourceHandler DescribeDBInstanceDetail of Instance %s err: %v", InstanceId, err)
		if strings.Contains(err.Error(), "InstanceNotFound") || strings.Contains(err.Error(), "InvalidInstanceId.NotFound") {
			return nil, consts.ErrorWithParam(model.ErrorCode_InstanceNotFound, InstanceId)
		}
		return nil, err
	}

	// 走直连不需要加白名单
	//_, err = d.source.AddWhiteList(ctx, "", ds)
	//if err != nil {
	//	log.Warn(ctx, "DescribeDataSourceHandler createConnection AddWhiteList of Instance %s err: %v", InstanceId, err)
	//	return nil, err
	//}

	if err := d.source.EnsureAccount(ctx, &datasource.EnsureAccountReq{Source: ds}); err != nil {
		log.Info(ctx, "DescribeDataSourceHandler ensureAccount err: %v", err)
		return nil, err
	}

	var datasourceList []*model.DescribeDataSourceInfo
	switch InstanceType {
	case model.InstanceType_MySQL:
		// 节点级
		req := &datasource.DescribeInstanceAddressReq{
			InstanceId: InstanceId,
			Type:       shared.DataSourceType(InstanceType),
			LinkType:   shared.Volc,
		}
		addressList, err := d.source.DescribeInstanceAddressList(ctx, req)
		if err != nil {
			log.Warn(ctx, "DescribeDataSourceHandler createConnection DescribeInstanceAddressList of Instance %s err: %v", InstanceId, err)
			return nil, err
		}

		for _, val := range addressList {
			address := fmt.Sprintf("%s:%d", val.IP, val.Port)
			dataSource := &model.DataSource{
				Type:        model.DSType(ds.Type),
				LinkType:    conv.ToModelLinkType(ds.LinkType),
				Address:     &address,
				Username:    &ds.User,
				Password:    &ds.Password,
				InstanceId:  &ds.InstanceId,
				DBName:      &ds.Db,
				VpcId:       &ds.VpcID,
				MongoNodeId: &ds.MongoNodeId,
				ExtraDsn:    ds.ExtraDsn,
			}

			sourceInfo := &model.DescribeDataSourceInfo{
				DataSource: dataSource,
				NodeId:     val.NodeId,
			}
			datasourceList = append(datasourceList, sourceInfo)
		}
	case model.InstanceType_VeDBMySQL:
		// 节点级(通用,不再走proxy hint)
		req := &datasource.DescribeInstanceAddressReq{
			InstanceId: InstanceId,
			Type:       shared.DataSourceType(InstanceType),
			LinkType:   shared.Volc,
		}
		addressList, err := d.source.DescribeInstanceAddressList(ctx, req)
		if err != nil {
			log.Warn(ctx, "DescribeDataSourceHandler DescribeInstanceAddressList of Instance %s err: %v", InstanceId, err)
			return nil, err
		}

		for _, val := range addressList {
			address := fmt.Sprintf("%s:%d", val.IP, val.Port)
			dataSource := &model.DataSource{
				Type:        model.DSType(ds.Type),
				LinkType:    conv.ToModelLinkType(ds.LinkType),
				Address:     &address,
				Username:    &ds.User,
				Password:    &ds.Password,
				InstanceId:  &ds.InstanceId,
				DBName:      &ds.Db,
				VpcId:       &ds.VpcID,
				MongoNodeId: &ds.MongoNodeId,
				ExtraDsn:    ds.ExtraDsn,
			}

			sourceInfo := &model.DescribeDataSourceInfo{
				DataSource: dataSource,
				NodeId:     val.NodeId,
			}
			datasourceList = append(datasourceList, sourceInfo)
		}
	case model.InstanceType_MySQLSharding:
		req := &datasource.DescribeInstanceAddressReq{
			InstanceId: InstanceId,
			Type:       shared.DataSourceType(InstanceType),
			LinkType:   shared.Volc,
		}
		addressList, err := d.source.DescribeInstanceAddressList(ctx, req)
		if err != nil {
			log.Info(ctx, "DescribeDataSourceHandler createConnection DescribeInstanceAddressList of Instance %s err: %v", InstanceId, err)
			return nil, err
		}

		for _, val := range addressList {
			address := fmt.Sprintf("%s:%d", val.IP, val.Port)
			dataSource := &model.DataSource{
				Type:        model.DSType(ds.Type),
				LinkType:    conv.ToModelLinkType(ds.LinkType),
				Address:     &address,
				Username:    &ds.User,
				Password:    &ds.Password,
				InstanceId:  &ds.InstanceId,
				DBName:      &ds.Db,
				VpcId:       &ds.VpcID,
				MongoNodeId: &ds.MongoNodeId,
				ExtraDsn:    ds.ExtraDsn,
			}

			sourceInfo := &model.DescribeDataSourceInfo{
				DataSource: dataSource,
				NodeId:     val.NodeId,
			}
			datasourceList = append(datasourceList, sourceInfo)
		}
	case model.InstanceType_Postgres:
		// 节点级
		req := &datasource.DescribeInstanceAddressReq{
			InstanceId: InstanceId,
			Type:       shared.DataSourceType(InstanceType),
			LinkType:   shared.Volc,
		}
		addressList, err := d.source.DescribeInstanceAddressList(ctx, req)
		if err != nil {
			log.Warn(ctx, "DescribeDataSourceHandler createConnection DescribeInstanceAddressList of Instance %s err: %v", InstanceId, err)
			return nil, err
		}

		for _, val := range addressList {
			address := fmt.Sprintf("%s:%d", val.IP, val.Port)
			dataSource := &model.DataSource{
				Type:       model.DSType(ds.Type),
				LinkType:   conv.ToModelLinkType(ds.LinkType),
				Address:    &address,
				Username:   &ds.User,
				Password:   &ds.Password,
				InstanceId: &ds.InstanceId,
				DBName:     &ds.Db,
			}

			sourceInfo := &model.DescribeDataSourceInfo{
				DataSource: dataSource,
				NodeId:     val.NodeId,
			}
			datasourceList = append(datasourceList, sourceInfo)
		}
	case model.InstanceType_MetaMySQL:
		//是通过proxy访问,借助hint实现对指定节点的查询
		err = d.source.FillInnerDataSource(ctx, ds)
		if err != nil {
			log.Info(ctx, "DescribeDataSourceHandler createConnection FillInnerDataSource of Instance %s err: %v", InstanceId, err)
			return nil, err
		}

		dataSource := &model.DataSource{
			Type:        model.DSType(ds.Type),
			LinkType:    conv.ToModelLinkType(ds.LinkType),
			Address:     &ds.Address,
			Username:    &ds.User,
			Password:    &ds.Password,
			InstanceId:  &ds.InstanceId,
			DBName:      &ds.Db,
			VpcId:       &ds.VpcID,
			MongoNodeId: &ds.MongoNodeId,
			ExtraDsn:    ds.ExtraDsn,
		}

		sourceInfo := &model.DescribeDataSourceInfo{
			DataSource: dataSource,
			NodeId:     ds.InstanceId,
		}
		datasourceList = append(datasourceList, sourceInfo)
	case model.InstanceType_Mongo:
		// 节点级
		req := &datasource.DescribeInstanceAddressReq{
			InstanceId: InstanceId,
			Type:       shared.DataSourceType(InstanceType),
			LinkType:   shared.Volc,
		}
		addressList, err := d.source.DescribeInstanceAddressList(ctx, req)
		if err != nil {
			log.Warn(ctx, "DescribeDataSourceHandler DescribeInstanceAddressList of Instance %s err: %v", InstanceId, err)
			return nil, err
		}

		for _, val := range addressList {
			address := fmt.Sprintf("%s:%d", val.IP, val.Port)
			dataSource := &model.DataSource{
				Type:        model.DSType(ds.Type),
				LinkType:    conv.ToModelLinkType(ds.LinkType),
				Address:     &address,
				Username:    &ds.User,
				Password:    &ds.Password,
				InstanceId:  &ds.InstanceId,
				DBName:      &ds.Db,
				MongoNodeId: &ds.MongoNodeId,
				ExtraDsn:    ds.ExtraDsn,
			}
			sourceInfo := &model.DescribeDataSourceInfo{
				DataSource: dataSource,
				NodeId:     val.NodeId,
			}
			datasourceList = append(datasourceList, sourceInfo)
		}
	}

	return datasourceList, nil
}

func (d *DescribeDataSourceHandler) getAccountPassword(key string, instanceId string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(instanceId))
	expectedMac := hex.EncodeToString(mac.Sum(nil))
	return "Dbw_" + expectedMac[:26]
}

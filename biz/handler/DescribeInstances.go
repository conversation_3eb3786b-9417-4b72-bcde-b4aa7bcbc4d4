package handler

import (
	"context"
	"encoding/json"
	"math"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	dbwInsSvc "code.byted.org/infcs/dbw-mgr/biz/service/dbwinstance"

	"code.byted.org/gopkg/lang/slices"
	volcobserveopen "code.byted.org/iaasng/volcstack-go-inner-sdk/service_open/volcobserve"
	mq "code.byted.org/inf/metrics-query"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/cloudmonitor"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/metrics"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/qjpcpu/fp"
)

func (h *DescribeInstancesHandler) DescribeInstances(ctx context.Context, req *model.DescribeInstancesReq) (ret *model.DescribeInstancesResp, err error) {
	var instanceList []*model.InstanceInfo
	var metaInstanceList []*model.InstanceInfo
	var total int64
	//testUser := h.cfg.Get(ctx).SyncTestTenantIds
	if req.GetInstancesVersion() == model.InstancesVersion_v2 {
		instanceList, err = h.dbwInstanceSvc.DescribeInstances(ctx, req)
		//if len(testUser) > 0 {
		//	log.Info(ctx, "testUser: %s", testUser)
		//	if slices.Contains(testUser, fwctx.GetTenantID(ctx)) {
		//		if h.cfg.Get(ctx).LocalInstances {
		//			instanceList, err = h.dbwInstanceSvc.DescribeInstances(ctx, req)
		//		} else {
		//			instanceList, total, err = h.getAllInstances(ctx, req)
		//		}
		//	} else {
		//		instanceList, total, err = h.getAllInstances(ctx, req)
		//	}
		//} else {
		//	if h.cfg.Get(ctx).LocalInstances {
		//		instanceList, err = h.dbwInstanceSvc.DescribeInstances(ctx, req)
		//	} else {
		//		instanceList, total, err = h.getAllInstances(ctx, req)
		//	}
		//}
	} else {
		instanceList, total, err = h.getAllInstances(ctx, req)

	}
	if err != nil {
		log.Warn(ctx, "getAllInstances error:%s", err.Error())
		return nil, err
	}
	if req.GetDSType() == model.DSType_MetaMySQL {
		metaInstanceList, total, err = h.getAllInstances(ctx, req)
	}
	if err != nil {
		log.Warn(ctx, "getAllInstances for mateMysql error:%s", err.Error())
		return nil, err
	}
	if req.GetDSType() == model.DSType_ByteRDS || req.GetDSType() == model.DSType_ByteDoc {
		return &model.DescribeInstancesResp{
			Total:     utils.Int32Ref(int32(total)),
			Instances: instanceList,
		}, nil
	}
	done := make(chan struct{}, 1)
	timer := time.NewTimer(20 * time.Second)
	log.Info(ctx, "FillInstanceMetricData in: %s", instanceList)
	go func() {
		if req.GetInstancesVersion() == model.InstancesVersion_v2 {
			instanceList, err = h.FillInstanceInspection(ctx, req, instanceList)
			//if len(testUser) > 0 {
			//	log.Info(ctx, "testUser: %s", testUser)
			//	if slices.Contains(testUser, fwctx.GetTenantID(ctx)) {
			//		if h.cfg.Get(ctx).LocalInstances {
			//			instanceList, err = h.FillInstanceInspection(ctx, req, instanceList)
			//		} else {
			//			instanceList, err = h.FillInstanceMetricData(ctx, req, instanceList)
			//		}
			//	} else {
			//		instanceList, err = h.FillInstanceMetricData(ctx, req, instanceList)
			//	}
			//} else {
			//	if h.cfg.Get(ctx).LocalInstances {
			//		instanceList, err = h.FillInstanceInspection(ctx, req, instanceList)
			//	} else {
			//		instanceList, err = h.FillInstanceMetricData(ctx, req, instanceList)
			//	}
			//}
		} else {
			instanceList, err = h.FillInstanceMetricData(ctx, req, instanceList)
		}

		if req.GetDSType() == model.DSType_MetaMySQL {
			metaInstanceList, err = h.FillInstanceMetricData(ctx, req, metaInstanceList)
			instanceList = metaInstanceList
		}
		if err != nil {
			log.Error(ctx, "fill instanceMetricData err: %v", err)
		}
		done <- struct{}{}
	}()
	select {
	case <-done:
		log.Info(ctx, "Get instanceMetricData success")
	case <-timer.C:
		log.Info(ctx, "Get instanceMetricData timeout")
	}
	ret = &model.DescribeInstancesResp{
		Total:     utils.Int32Ref(int32(len(instanceList))),
		Instances: instanceList,
	}

	// pagination
	if req.GetPageNumber() > 0 || req.GetPageSize() > 0 {
		begIdx := fp.MinInt32((req.GetPageNumber()-1)*req.GetPageSize(), int32(len(instanceList)))
		endIdx := fp.MinInt32(begIdx+req.GetPageSize(), int32(len(instanceList)))
		ret.Instances = instanceList[begIdx:endIdx]
	}
	return ret, nil
}

// judgeInstanceTypeSupport 根据数据库类型判断当前region的支持情况，如果当前region不支持该数据库类型则返回false
func (h *DescribeInstancesHandler) judgeInstanceTypeSupport(ctx context.Context, DsType model.DSType) bool {
	types, err := h.describeDataSourceTypesHandler.DescribeDataSourceTypes(ctx, nil)
	if err != nil {
		return false
	}
	var typesStr []string
	fp.StreamOf(types.DSTypes).
		Map(func(t model.DSType) string {
			return t.String()
		}).
		ToSlice(&typesStr)
	return slices.Contains(typesStr, DsType.String())
}

func (h *DescribeInstancesHandler) getAllInstances(ctx context.Context, req *model.DescribeInstancesReq) ([]*model.InstanceInfo, int64, error) {
	support := h.judgeInstanceTypeSupport(ctx, req.GetDSType())
	if !support {
		return nil, 0, nil
	}

	var (
		instanceList    []*model.InstanceInfo
		DefaultPageSize int32
		wg              sync.WaitGroup
	)
	if req.GetDSType() == model.DSType_ByteRDS || req.GetDSType() == model.DSType_ByteDoc {
		query := &datasource.ListInstanceReq{
			Type:            conv.ToSharedType(req.GetDSType()),
			LinkType:        shared.Volc,
			RegionId:        req.GetRegionId(),
			InstanceName:    req.GetInstanceName(),
			InstanceId:      req.GetInstanceId(),
			InstanceStatus:  req.GetInstanceStatus(),
			DBEngineVersion: req.GetDBEngineVersion(),
			ZoneId:          req.GetZoneId(),
			TenantId:        fwctx.GetTenantID(ctx),
			PageNumber:      req.GetPageNumber(),
			PageSize:        req.GetPageSize(),
			UserId:          fwctx.GetUserID(ctx),
		}
		if req.IsSetQueryInstanceFilter() {
			if req.GetQueryInstanceFilter().IsSetOwned() {
				query.Owned = req.GetQueryInstanceFilter().GetOwned()
				query.UserId = fwctx.GetUserID(ctx)
			}
			if req.GetQueryInstanceFilter().IsSetFavor() {
				query.Favor = req.GetQueryInstanceFilter().GetFavor()
				query.UserId = fwctx.GetUserID(ctx)
			}
		}
		resp, err := h.dsSvc.ListInstance(ctx, query)
		if err != nil {
			log.Warn(ctx, "failed to list instance, err=%v", err)
			return nil, 0, err
		}

		return resp.InstanceList, resp.Total, nil
	}
	if fwctx.GetTenantID(ctx) == "1" || fwctx.GetTenantID(ctx) == "0" {
		DefaultPageSize = int32(h.cfg.Get(ctx).MaxDescribeDBInstancePageSize) // 默认为500
	} else {
		if req.GetDSType() == model.DSType_Mongo {
			DefaultPageSize = 50 // mongoDB查询性能pageSize=100性能差(当异常实例比较多的时候)
		} else {
			DefaultPageSize = 100
		}
	}
	query := &datasource.ListInstanceReq{
		Type:            conv.ToSharedType(req.GetDSType()),
		LinkType:        shared.Volc,
		RegionId:        h.loc.RegionID(),
		InstanceName:    req.GetInstanceName(),
		InstanceId:      req.GetInstanceId(),
		InstanceStatus:  req.GetInstanceStatus(),
		DBEngineVersion: req.GetDBEngineVersion(),
		ZoneId:          req.GetZoneId(),
		TenantId:        fwctx.GetTenantID(ctx),
		PageNumber:      1,
		PageSize:        10,
		ProjectName:     req.GetProjectName(),
	}
	// 仅对metaMySQL生效
	if req.IsSetOwndInstancePrivilege() {
		query.OwndInstancePrivilege = req.GetOwndInstancePrivilege()
	} else {
		// 默认返回有权限的metaMySQL实例
		query.OwndInstancePrivilege = true
	}
	if req.LinkType == nil {
		query.LinkType = shared.Volc
	} else {
		query.LinkType = conv.ToSharedLinkType(req.GetLinkType())
	}
	if req.GetDSType() == model.DSType_MSSQL && req.GetInstanceId() != "" {
		query.InstanceId = "" //sqlserver不支持实例id模糊查询,忽略传参
	}
	if req.IsSetSubInstanceType() {
		query.SubInstanceType = req.GetSubInstanceType().String()
	}
	if req.IsSetQueryInstanceFilter() {
		if req.GetQueryInstanceFilter().IsSetOwned() {
			query.Owned = req.GetQueryInstanceFilter().GetOwned()
			query.UserId = fwctx.GetUserID(ctx)
		}
		if req.GetQueryInstanceFilter().IsSetFavor() {
			query.Favor = req.GetQueryInstanceFilter().GetFavor()
			query.UserId = fwctx.GetUserID(ctx)
		}
	}
	log.Info(ctx, "tenantId is %s userId is %s", fwctx.GetTenantID(ctx), fwctx.GetUserID(ctx))
	resp, err := h.dsSvc.ListInstance(ctx, query)
	if err != nil {
		log.Warn(ctx, "failed to list instance, err=%v", err)
		return nil, 0, err
	}
	instancesChannel := make(chan *model.InstanceInfo, resp.Total)
	totalPages := int32(math.Ceil(float64(resp.Total) / float64(DefaultPageSize))) // 获取总页数
	if totalPages >= 1 {
		for pageNumber := int32(1); pageNumber <= totalPages; pageNumber++ {
			wg.Add(1)
			go func(pageNumber int32) {
				defer wg.Done()
				query := &datasource.ListInstanceReq{
					Type:            conv.ToSharedType(req.GetDSType()),
					RegionId:        h.loc.RegionID(),
					InstanceName:    req.GetInstanceName(),
					InstanceId:      req.GetInstanceId(),
					InstanceStatus:  req.GetInstanceStatus(),
					DBEngineVersion: req.GetDBEngineVersion(),
					ZoneId:          req.GetZoneId(),
					TenantId:        fwctx.GetTenantID(ctx),
					PageNumber:      pageNumber,
					PageSize:        DefaultPageSize,
					UserId:          fwctx.GetUserID(ctx),
					Tags:            req.GetTags(),
					ProjectName:     req.GetProjectName(),
				}
				if req.LinkType == nil {
					query.LinkType = shared.Volc
				} else {
					query.LinkType = conv.ToSharedLinkType(req.GetLinkType())
				}
				if req.GetDSType() == model.DSType_MSSQL && req.GetInstanceId() != "" {
					query.InstanceId = "" //sqlserver不支持实例id模糊查询
				}
				if req.IsSetSubInstanceType() {
					query.SubInstanceType = req.GetSubInstanceType().String()
				}
				if req.IsSetQueryInstanceFilter() {
					if req.GetQueryInstanceFilter().IsSetOwned() {
						query.Owned = req.GetQueryInstanceFilter().GetOwned()
					}
					if req.GetQueryInstanceFilter().IsSetUserName() {
						query.UserId = req.GetQueryInstanceFilter().GetUserName()
					}
					if req.GetQueryInstanceFilter().IsSetFavor() {
						query.Favor = req.GetQueryInstanceFilter().GetFavor()
					}
				}
				resp, err := h.dsSvc.ListInstance(ctx, query)
				if err != nil {
					log.Warn(ctx, "failed to list instance, err=%v", err)
					return
				}
				for _, instance := range resp.InstanceList {
					if req.GetDSType() == model.DSType_Redis {
						instance.InstanceSpec.MemInGiB = mbToGiB(instance.InstanceSpec.MemInGiB)
					}
					instancesChannel <- instance
				}
			}(pageNumber)
		}
		go func() {
			wg.Wait()
			close(instancesChannel)
		}()

		for instance := range instancesChannel {
			instanceList = append(instanceList, instance)
		}
	} else {
		instanceList = append(instanceList, resp.InstanceList...)
	}
	return instanceList, resp.Total, nil
}

func (h *DescribeInstancesHandler) FillInstanceInspection(ctx context.Context, req *model.DescribeInstancesReq, instanceList []*model.InstanceInfo) ([]*model.InstanceInfo, error) {
	log.Info(ctx, "FillInstanceInspection in: %s", instanceList)
	var newInstanceList []*model.InstanceInfo
	var dsType string
	if instanceList == nil {
		return newInstanceList, nil
	}
	// 获取所有开启自动巡检实例列表
	if req.IsSetDSType() {
		dsType = req.GetDSType().String()
	}
	inspectionInsts, err := h.dbwInspectionConfigDal.ListAll(ctx, fwctx.GetTenantID(ctx), dsType)
	if err != nil {
		log.Warn(ctx, "get All inspection Config instances failed %+v", err)
	}
	for index, inst := range instanceList {
		if inst == nil {
			log.Warn(ctx, "instance index %d is null, continue", index) // 兜底
			continue
		}
		inst.InstanceInspectionInfo = h.fillInstanceInspection(ctx, inst.GetInstanceId(), inspectionInsts)
		// 巡检状态过滤
		if req.IsSetIsInspectionOpen() && req.GetIsInspectionOpen() != inst.InstanceInspectionInfo.IsInspectionOpen {
			continue
		}
		newInstanceList = append(newInstanceList, inst)
	}

	log.Info(ctx, "FillInstanceMetricData out: %s", newInstanceList)
	return newInstanceList, nil
}

func (h *DescribeInstancesHandler) FillInstanceMetricData(ctx context.Context, req *model.DescribeInstancesReq, instanceList []*model.InstanceInfo) ([]*model.InstanceInfo, error) {
	log.Info(ctx, "FillInstanceMetricData in: %s", instanceList)
	var newInstanceList []*model.InstanceInfo
	if instanceList == nil {
		return newInstanceList, nil
	}
	// 获取所有管控实例列表
	query := &datasource.ListInstanceReq{
		Type:        conv.ToSharedType(req.GetDSType()),
		ControlMode: int(model.ControlMode_Management),
	}
	controlInsts, err := h.dbwInstanceDal.ListAll(ctx, fwctx.GetTenantID(ctx), query, "", "")
	if err != nil {
		log.Warn(ctx, "get All Control Mode instances failed %+v", err)
	}
	log.Info(ctx, "get All Control Mode instances %+v", controlInsts)
	// 获取所有开启自动巡检实例列表
	inspectionInsts, err := h.dbwInspectionConfigDal.ListAll(ctx, fwctx.GetTenantID(ctx), req.DSType.String())
	if err != nil {
		log.Warn(ctx, "get All inspection Config instances failed %+v", err)
	}
	for index, inst := range instanceList {
		if inst == nil {
			log.Warn(ctx, "instance index %d is null, continue", index) // 兜底
			continue
		}
		mode, secId := h.fillControlModeAndSecurityID(ctx, inst.GetInstanceId(), controlInsts)
		inst.ControlMode = &mode
		inst.SecurityRuleId = &secId
		inst.InstanceInspectionInfo = h.fillInstanceInspection(ctx, inst.GetInstanceId(), inspectionInsts)
		// 安全管控过滤
		if req.IsSetSearchControlMode() && !modeInSlice(req.GetSearchControlMode(), inst.ControlMode.String()) {
			continue
		}
		//// 巡检状态过滤
		//if req.IsSetIsInspectionOpen() && req.GetIsInspectionOpen() != inst.InstanceInspectionInfo.IsInspectionOpen {
		//	continue
		//}
		// 模糊查询
		if req.GetQuery() != "" && !strings.Contains(inst.GetInstanceId(), req.GetQuery()) &&
			!strings.Contains(inst.GetInstanceName(), req.GetQuery()) {
			continue
		}
		// 安全规则过滤
		if req.IsSetSecurityRuleId() && req.GetSecurityRuleId() != *inst.SecurityRuleId {
			continue
		}
		// 支持sqlserver实例id模糊查询
		if req.GetDSType() == model.DSType_MSSQL && !strings.Contains(inst.GetInstanceId(), req.GetInstanceId()) {
			continue
		}
		newInstanceList = append(newInstanceList, inst)
	}

	log.Info(ctx, "FillInstanceMetricData out: %s", newInstanceList)
	return newInstanceList, nil
}

// 填充实例安全管控状态&安全规则ID
func (h *DescribeInstancesHandler) fillControlModeAndSecurityID(ctx context.Context, instanceId string, allControlInsts *dao.DbwInstances) (model.ControlMode, string) {
	var node *dao.DbwInstance
	if allControlInsts.Total < 1 {
		return model.ControlMode_None, ""
	}
	fp.StreamOf(allControlInsts.Items).Filter(func(d *dao.DbwInstance) bool {
		return d.InstanceId == instanceId
	}).First().To(&node)
	if node == nil {
		return model.ControlMode_None, ""
	} else {
		return model.ControlMode_Management, strconv.FormatInt(node.SecurityGroupId, 10)
	}
}

// 填充巡检状态
func (h *DescribeInstancesHandler) fillInstanceInspection(ctx context.Context, instanceId string, allInspectionInsts []*dao.InspectionConfig) *model.InstanceInspectionInfo {
	var (
		node             *dao.InspectionConfig
		isInspectionOpen bool
	)
	fp.StreamOf(allInspectionInsts).Filter(func(d *dao.InspectionConfig) bool {
		return d.InstanceId == instanceId
	}).First().To(&node)
	if node == nil {
		return &model.InstanceInspectionInfo{
			IsInspectionOpen:        false,
			InspectionExecStartTime: 0,
			InspectionExecEndTime:   0,
		}
	} else {
		if node.IsOpen == 0 {
			isInspectionOpen = false
		} else {
			isInspectionOpen = true
		}
		return &model.InstanceInspectionInfo{
			IsInspectionOpen:        isInspectionOpen,
			InspectionExecStartTime: int32(node.InspectionExecutableStartTime),
			InspectionExecEndTime:   int32(node.InspectionExecutableEndTime),
		}
	}
}

func GetInstancesLatestCpuUsage(
	ctx context.Context,
	dsType model.DSType,
	instanceList []*model.InstanceInfo,
	regionID string,
	tenantID string,
	cfg config.ConfigProvider,
	crossAuthSvc crossauth.CrossServiceAuthorizationService,
	component string,
	nodeId string,
	nodeIds []string,
) (map[string]float64, error) {
	if dsType != model.DSType_MySQL && dsType != model.DSType_VeDBMySQL && dsType != model.DSType_Postgres &&
		dsType != model.DSType_MySQLSharding && dsType != model.DSType_Mongo && dsType != model.DSType_ByteRDS && dsType != model.DSType_ByteDoc {
		return nil, nil
	}
	var (
		instances  []cloudmonitor.Instance
		metricInfo cloudmonitor.MetricSubName
		cli        cloudmonitor.ClientCloudMonitor
		input      *cloudmonitor.CloudMonitorInput
		credential crossauth.Credentials
		err        error
	)
	// 运维面账号跳过cpu获取
	if fwctx.GetTenantID(ctx) == "0" || fwctx.GetTenantID(ctx) == "1" {
		return nil, nil
	}
	intervalStr := "70s"
	interval, _ := time.ParseDuration(intervalStr)
	endTime := time.Now().Add(-5 * time.Second)
	startTime := endTime.Add(-interval)

	endpoint := cfg.Get(ctx).CloudMonitorEndpoint
	if dsType != model.DSType_ByteRDS && dsType != model.DSType_ByteDoc { // 字节云实例跳过assumeRole
		credential, err = crossAuthSvc.AssumeRole(ctx, tenantID)
		if err != nil {
			log.Warn(ctx, "failed to get user ak/sk, err=%v", err)
			return nil, consts.ErrorOf(model.ErrorCode_GetTempCredentialFailed)
		}
	}
	appName := os.Getenv("PSM")
	appSecret := os.Getenv("METRICS_SECRET")
	if component == model.Component_DBEngine.String() {
		// db engine 监控
		switch dsType {
		case model.DSType_MySQL:
			// 适配请求传入多个实例id
			if nodeId == "" {
				fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
					return cloudmonitor.Instance{
						Dimensions: []cloudmonitor.Dimension{
							{
								Name:  "ResourceID",
								Value: instInfo.GetInstanceId(),
							},
						},
					}
				}).ToSlice(&instances)
			} else {
				fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
					return cloudmonitor.Instance{
						Dimensions: []cloudmonitor.Dimension{
							{
								Name:  "ResourceID",
								Value: instInfo.GetInstanceId(),
							},
							{
								Name:  "Node",
								Value: nodeId,
							},
						},
					}
				}).ToSlice(&instances)
			}
			metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_RDS_MySQL][1] // 实例监控
			input = &cloudmonitor.CloudMonitorInput{
				Input: &cloudmonitor.Input{
					StartTime:    startTime.Unix(),
					EndTime:      endTime.Unix(),
					MetricName:   metricInfo.MetricName,
					SubNamespace: metricInfo.SubNamespace,
					Region:       regionID,
					Period:       "30s",
					Namespace:    string(metrics.VCM_RDS_MySQL),
					Ak:           credential.GetAK(),
					Sk:           credential.GetSK(),
					Token:        credential.GetToken(),
					Endpoint:     endpoint,
					Instances:    instances,
				},
			}
		case model.DSType_VeDBMySQL:
			if nodeId == "" {
				fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
					return cloudmonitor.Instance{
						Dimensions: []cloudmonitor.Dimension{
							{
								Name:  "ResourceID",
								Value: instInfo.GetInstanceId(),
							},
						},
					}
				}).ToSlice(&instances)
			} else {
				fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
					return cloudmonitor.Instance{
						Dimensions: []cloudmonitor.Dimension{
							{
								Name:  "ResourceID",
								Value: instInfo.GetInstanceId(),
							},
							{
								Name:  "Pod",
								Value: nodeId,
							},
						},
					}
				}).ToSlice(&instances)
			}
			metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_veDB_MySQL][1]
			input = &cloudmonitor.CloudMonitorInput{
				Input: &cloudmonitor.Input{
					StartTime:    startTime.Unix(),
					EndTime:      endTime.Unix(),
					MetricName:   metricInfo.MetricName,
					SubNamespace: metricInfo.SubNamespace,
					Region:       regionID,
					Period:       "30s",
					Namespace:    string(metrics.VCM_veDB_MySQL),
					Ak:           credential.GetAK(),
					Sk:           credential.GetSK(),
					Token:        credential.GetToken(),
					Endpoint:     endpoint,
					Instances:    instances,
				},
			}
		case model.DSType_Postgres:
			if nodeId == "" {
				fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
					return cloudmonitor.Instance{
						Dimensions: []cloudmonitor.Dimension{
							{
								Name:  "ResourceID",
								Value: instInfo.GetInstanceId(),
							},
							{
								Name:  "Node",
								Value: instInfo.GetInstanceId(),
							},
						},
					}
				}).ToSlice(&instances)
			} else {
				fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
					return cloudmonitor.Instance{
						Dimensions: []cloudmonitor.Dimension{
							{
								Name:  "ResourceID",
								Value: instInfo.GetInstanceId(),
							},
							{
								Name:  "Node",
								Value: nodeId,
							},
						},
					}
				}).ToSlice(&instances)
			}
			metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_RDS_PostgreSQL][0] // 实例监控
			input = &cloudmonitor.CloudMonitorInput{
				Input: &cloudmonitor.Input{
					StartTime:    startTime.Unix(),
					EndTime:      endTime.Unix(),
					MetricName:   metricInfo.MetricName,
					SubNamespace: metricInfo.SubNamespace,
					Region:       regionID,
					Period:       "30s",
					Namespace:    string(metrics.VCM_RDS_PostgreSQL),
					Ak:           credential.GetAK(),
					Sk:           credential.GetSK(),
					Token:        credential.GetToken(),
					Endpoint:     endpoint,
					Instances:    instances,
				},
			}
		case model.DSType_MySQLSharding:
			fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
				return cloudmonitor.Instance{
					Dimensions: []cloudmonitor.Dimension{
						{
							Name:  "ResourceID",
							Value: instInfo.GetInstanceId(),
						},
						{
							Name:  "Pod",
							Value: nodeId,
						},
					},
				}
			}).ToSlice(&instances)
			if strings.HasPrefix(nodeId, "vedbm") {
				// vedb
				metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MySQL_Sharding][2]
			} else {
				// mysql
				metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MySQL_Sharding][1]
			}
			input = &cloudmonitor.CloudMonitorInput{
				Input: &cloudmonitor.Input{
					StartTime:    startTime.Unix(),
					EndTime:      endTime.Unix(),
					MetricName:   metricInfo.MetricName,
					SubNamespace: metricInfo.SubNamespace,
					Region:       regionID,
					Period:       "30s",
					Namespace:    string(metrics.VCM_MySQL_Sharding),
					Ak:           credential.GetAK(),
					Sk:           credential.GetSK(),
					Token:        credential.GetToken(),
					Endpoint:     endpoint,
					Instances:    instances,
				},
			}
		case model.DSType_Mongo:
			var (
				isShard  bool
				metricNs string
			)
			if strings.Contains(instanceList[0].GetInstanceId(), "shard") {
				isShard = true
				metricNs = string(metrics.VCM_MongoDB_Sharded_Cluster)
			} else {
				isShard = false
				metricNs = string(metrics.VCM_MongoDB_Replica)
			}
			if nodeId == "" {
				//需要判断是分片实例还是副本集实例
				if isShard {
					metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MongoDB_Sharded_Cluster][1]
				} else {
					metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MongoDB_Replica][0]
				}
				fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
					return cloudmonitor.Instance{
						Dimensions: []cloudmonitor.Dimension{
							{
								Name:  "ResourceID",
								Value: instInfo.GetInstanceId(),
							},
						},
					}
				}).ToSlice(&instances)
			} else {
				//需要判断是分片实例还是副本集实例
				if isShard {
					//需要区分节点类型(config,mongos,shard)
					if strings.Contains(nodeId, "config") {
						metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MongoDB_Sharded_Cluster][0]
					} else if len(strings.Split(nodeId, "-")) == 4 {
						metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MongoDB_Sharded_Cluster][2]
					} else {
						metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MongoDB_Sharded_Cluster][3]
					}
				} else {
					metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MongoDB_Replica][1]
				}
				fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
					return cloudmonitor.Instance{
						Dimensions: []cloudmonitor.Dimension{
							{
								Name:  "ResourceID",
								Value: instInfo.GetInstanceId(),
							},
							{
								Name:  "Node",
								Value: nodeId,
							},
						},
					}
				}).ToSlice(&instances)
			}
			input = &cloudmonitor.CloudMonitorInput{
				Input: &cloudmonitor.Input{
					StartTime:    startTime.Unix(),
					EndTime:      endTime.Unix(),
					MetricName:   metricInfo.MetricName,
					SubNamespace: metricInfo.SubNamespace,
					Region:       regionID,
					Period:       "30s",
					Namespace:    metricNs,
					Ak:           credential.GetAK(),
					Sk:           credential.GetSK(),
					Token:        credential.GetToken(),
					Endpoint:     endpoint,
					Instances:    instances,
				},
			}

		case model.DSType_ByteRDS:
			instanceId := instanceList[0].GetInstanceId()
			return getLatestDbEngineCpuUsageWithByteMetric(ctx, instanceId, nodeIds, dsType, regionID, appName, appSecret), nil
		case model.DSType_ByteDoc:
			clusterId := instanceList[0].GetInstanceId()
			return getLatestDbEngineCpuUsageWithByteMetric(ctx, clusterId, nodeIds, dsType, regionID, appName, appSecret), nil
		}

	} else {
		// proxy 监控
		switch dsType {
		case model.DSType_MySQL:
			fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
				return cloudmonitor.Instance{
					Dimensions: []cloudmonitor.Dimension{
						{
							Name:  "ResourceID",
							Value: instInfo.GetInstanceId(),
						},
					},
				}

			}).ToSlice(&instances)
			metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_RDS_MySQL][0] // proxy 监控
			input = &cloudmonitor.CloudMonitorInput{
				Input: &cloudmonitor.Input{
					StartTime:    startTime.Unix(),
					EndTime:      endTime.Unix(),
					MetricName:   metricInfo.MetricName,
					SubNamespace: metricInfo.SubNamespace,
					Region:       regionID,
					Period:       "30s",
					Namespace:    string(metrics.VCM_RDS_MySQL),
					Ak:           credential.GetAK(),
					Sk:           credential.GetSK(),
					Token:        credential.GetToken(),
					Endpoint:     endpoint,
					Instances:    instances,
				},
			}
		case model.DSType_VeDBMySQL:
			// 实例监控
			fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
				return cloudmonitor.Instance{
					Dimensions: []cloudmonitor.Dimension{
						{
							Name:  "ResourceID",
							Value: instInfo.GetInstanceId(),
						},
					},
				}

			}).ToSlice(&instances)
			metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_veDB_MySQL][0] // 实例监控
			input = &cloudmonitor.CloudMonitorInput{
				Input: &cloudmonitor.Input{
					StartTime:    startTime.Unix(),
					EndTime:      endTime.Unix(),
					MetricName:   metricInfo.MetricName,
					SubNamespace: metricInfo.SubNamespace,
					Region:       regionID,
					Period:       "30s",
					Namespace:    string(metrics.VCM_veDB_MySQL),
					Ak:           credential.GetAK(),
					Sk:           credential.GetSK(),
					Token:        credential.GetToken(),
					Endpoint:     endpoint,
					Instances:    instances,
				},
			}
		case model.DSType_MySQLSharding:
			fp.StreamOf(instanceList).Map(func(instInfo *model.InstanceInfo) cloudmonitor.Instance {
				return cloudmonitor.Instance{
					Dimensions: []cloudmonitor.Dimension{
						{
							Name:  "ResourceID",
							Value: instInfo.GetInstanceId(),
						},
					},
				}

			}).ToSlice(&instances)
			metricInfo = cloudmonitor.Cpu[cloudmonitor.VCM_MySQL_Sharding][0] // proxy 监控
			input = &cloudmonitor.CloudMonitorInput{
				Input: &cloudmonitor.Input{
					StartTime:    startTime.Unix(),
					EndTime:      endTime.Unix(),
					MetricName:   metricInfo.MetricName,
					SubNamespace: metricInfo.SubNamespace,
					Region:       regionID,
					Period:       "30s",
					Namespace:    string(metrics.VCM_MySQL_Sharding),
					Ak:           credential.GetAK(),
					Sk:           credential.GetSK(),
					Token:        credential.GetToken(),
					Endpoint:     endpoint,
					Instances:    instances,
				},
			}
		case model.DSType_ByteRDS:
			instanceId := instanceList[0].GetInstanceId()
			return getLatestProxyCpuUsageWithByteMetric(ctx, instanceId, dsType, regionID, appName, appSecret), nil
		case model.DSType_ByteDoc:
			clusterId := instanceList[0].GetInstanceId()
			return getLatestProxyCpuUsageWithByteMetric(ctx, clusterId, dsType, regionID, appName, appSecret), nil
		}
	}
	log.Info(ctx, "cloudMonitorInput is %s", utils.Show(input))
	cli, err = cloudmonitor.NewClient(input)
	if err != nil {
		log.Warn(ctx, "failed to get cloud monitor client, err=%v", err)
		return nil, consts.ErrorOf(model.ErrorCode_GetMonitorDataFailed)
	}

	var resp *volcobserveopen.GetMetricDataOutput
	resp, err = cli.GetMetricDataDbw(ctx, input)
	if err != nil {
		log.Warn(ctx, "failed to get metric data, err=%v", err)
		return nil, err
	}

	var instanceCpuUsage map[string]float64
	fp.StreamOf(resp.MetricDataResults).ToSetBy(func(rest *interface{}) (string, float64) {
		var instanceId string
		var cpuUsage float64
		// sharding proxy cpu 未返回Dimensions,需手动补齐instanceId
		if res, ok := (*rest).(map[string]interface{}); ok {
			if dims, ok := res["Dimensions"].([]interface{}); ok {
				for _, item := range dims {
					if dim, ok := item.(map[string]interface{}); ok {
						if instId, ok := dim["Value"].(string); ok {
							if dim["Name"] == "ResourceID" {
								instanceId = instId
								break
							}
						}
					}
				}
			}

			dataPoints := res["DataPoints"]
			log.Info(ctx, "dataPoints is %s", utils.Show(dataPoints))
			if datas, ok := dataPoints.([]interface{}); ok {
				if len(datas) > 0 {
					data := datas[len(datas)-1]
					if latestData, ok := data.(map[string]interface{}); ok {
						//if _, ok := latestData["Timestamp"].(string); ok {
						//}
						if cpu, ok := latestData["Value"].(float64); ok {
							cpuUsage = cpu
						} else {
							cpu, err = latestData["Value"].(json.Number).Float64()
							cpuUsage = cpu
						}
					}
				}

			}
		}
		if nodeId != "" {
			return nodeId, cpuUsage
		} else {
			// 手动补齐sharding proxy cpu 的ResourceId
			if instanceId == "" && dsType == model.DSType_MySQLSharding && component == model.Component_Proxy.String() {
				instanceId = instanceList[0].GetInstanceId()
			}
			return instanceId, cpuUsage
		}
	}).To(&instanceCpuUsage)
	log.Info(ctx, "InstanceCpu is %s", utils.Show(instanceCpuUsage))
	return instanceCpuUsage, nil
}

func (h *DescribeInstancesHandler) GetControlMode(ctx context.Context, dsType model.DSType, inst *model.InstanceInfo) (model.ControlMode, string) {
	controlMode := model.ControlMode_Management
	//根据instanceId、instanceType、source查表
	instanceInfo, err := h.dbwInstanceDal.Get(ctx, *inst.InstanceId, dsType.String(), shared.Volc.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		controlMode = model.ControlMode_None
		return controlMode, ""
	}
	return controlMode, strconv.FormatInt(instanceInfo.SecurityGroupId, 10)
}

type DescribeInstancesHandler struct {
	cli                            cli.ActorClient
	dsSvc                          datasource.DataSourceService
	loc                            location.Location
	cfg                            config.ConfigProvider
	crossAuthSvc                   crossauth.CrossServiceAuthorizationService
	dbwInstanceDal                 dal.DbwInstanceDAL
	dbwInspectionConfigDal         dal.InspectionConfigDAL
	describeDataSourceTypesHandler DescribeDataSourceTypesHandler
	dbwInstanceSvc                 dbwInsSvc.DbwInstanceInterface
}

func NewDescribeInstancesHandler(
	cli cli.ActorClient,
	dsSvc datasource.DataSourceService,
	loc location.Location,
	cfg config.ConfigProvider,
	crossAuthSvc crossauth.CrossServiceAuthorizationService,
	dbwInstanceDal dal.DbwInstanceDAL,
	dbwInspectionConfigDal dal.InspectionConfigDAL,
	dbwInstanceSvc dbwInsSvc.DbwInstanceInterface,
) HandlerImplementationEnvolope {
	hder := &DescribeInstancesHandler{
		cli:                    cli,
		dsSvc:                  dsSvc,
		loc:                    loc,
		cfg:                    cfg,
		crossAuthSvc:           crossAuthSvc,
		dbwInstanceDal:         dbwInstanceDal,
		dbwInspectionConfigDal: dbwInspectionConfigDal,
		dbwInstanceSvc:         dbwInstanceSvc,
		describeDataSourceTypesHandler: DescribeDataSourceTypesHandler{
			Cp: cfg,
		},
	}
	return NewHandler(hder.DescribeInstances)
}

// mbToGiB 将兆字节转换为吉比字节
func mbToGiB(mb float64) float64 {
	return mb / 1024
}

func getLatestDbEngineCpuUsageWithByteMetric(ctx context.Context, InstanceId string, nodeIds []string, dsType model.DSType, regionId string, appName string, appSecret string) map[string]float64 {

	client, err := mq.NewClient(appName, appSecret, //【必填】线上环境需填写对应控制面的app_name和app_secret，BOE环境为空即可。
		mq.WithPeriodicTokenUpdate,                       //【可选】Token默认有效期2h，此方法可自动更新token
		mq.WithCluster(convertRegionToCluster(regionId)), //【可选】控制面，与Region对应即可
	)
	if err != nil {
		log.Warn(ctx, "init byteMetric client failed %s", err)
		return map[string]float64{
			InstanceId: 0,
		}
	}
	defer client.Close()
	var query *mq.Query
	switch dsType {
	case model.DSType_ByteRDS:
		query = mq.NewQuery(mq.AggregatorAvg,
			"inf.mysql.server.cpu").
			SetTenant("storage.mysql").
			SetDownsample(60*time.Second, mq.DownsampleAvg).
			SetTag("dbname", InstanceId).
			SetMultivalue("usage_idle").SetTopK(1, mq.TopKMax)
	case model.DSType_ByteDoc:
		log.Info(ctx, "get cluster %s node ids %v", InstanceId, nodeIds)
		query = mq.NewQuery(mq.AggregatorAvg,
			"inf.bytedoc.server.mongodb_common_cpu_idle_amount").
			SetTenant("storage.bytedoc").
			SetDownsample(5*time.Minute, mq.DownsampleAvg).
			SetTag("cluster_id", InstanceId).
			SetMultivalue("gauge").SetTopK(1, mq.TopKMax)
		if len(nodeIds) > 0 {
			query.SetTag("server", strings.Join(nodeIds, "|"))
		}
	default:
		return map[string]float64{
			InstanceId: 0.0,
		}
	}
	// 第三步，执行查询。
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second) // 设置客户端超时时间。
	defer cancel()
	result := client.NewRequest(convertRegionToVRegion(regionId)).
		SetIntervalAgo(5 * time.Minute).
		AddQuery(query).
		DoWithContext(ctx)
	if err := result.Err(); err != nil {
		log.Warn(ctx, "get cpu usage failed %s", err)
		return map[string]float64{
			InstanceId: 0,
		}
	}
	resp := result.First()
	var cpuUsage float64
	// 仅返回最近一个点
	log.Info(ctx, "latest is %s", utils.Show(resp))
	for _, curve := range resp.SortedCurves() {
		cpuUsage = curve.Values[0]
		log.Info(ctx, "current cpu value is %s", utils.Show(cpuUsage))
	}
	return map[string]float64{
		InstanceId: 100 - cpuUsage,
	}
}

func getLatestProxyCpuUsageWithByteMetric(ctx context.Context, InstanceId string, dsType model.DSType, regionId string, appName string, appSecret string) map[string]float64 {
	client, err := mq.NewClient(appName, appSecret, //【必填】线上环境需填写对应控制面的app_name和app_secret，BOE环境为空即可。
		mq.WithPeriodicTokenUpdate,                       //【可选】Token默认有效期2h，此方法可自动更新token
		mq.WithCluster(convertRegionToCluster(regionId)), //【可选】控制面，与Region对应即可
	)
	if err != nil {
		log.Warn(ctx, "init byteMetric client failed %s", err)
		return map[string]float64{
			InstanceId: 0,
		}
	}
	defer client.Close()
	var query *mq.Query
	switch dsType {
	case model.DSType_ByteRDS:
		query = mq.NewQuery(mq.AggregatorAvg,
			"toutiao.ttds.dbatman.cpu_use_rate").
			SetTenant("default").
			SetDownsample(60*time.Second, mq.DownsampleAvg).
			SetTag("db_list", InstanceId+":").SetTag("port", "3306|3307")
	case model.DSType_ByteDoc:
		query = mq.NewQuery(mq.AggregatorAvg,
			"inf.bytedoc.server.mongodb_common_cpu_idle_amount").
			SetTenant("storage.bytedoc").
			SetDownsample(60*time.Second, mq.DownsampleAvg).
			SetTag("cluster_id", InstanceId).
			SetTag("replset_name", InstanceId+"-mongos").
			SetMultivalue("gauge").SetTopK(1, mq.TopKMax)
	default:
		return map[string]float64{
			InstanceId: 0.0,
		}
	}

	// 第三步，执行查询。
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second) // 设置客户端超时时间。
	defer cancel()
	result := client.NewRequest(convertRegionToVRegion(regionId)).
		SetIntervalAgo(1 * time.Minute).
		AddQuery(query).
		DoWithContext(ctx)
	if err := result.Err(); err != nil {
		log.Warn(ctx, "get proxy cpu usage failed %s", err)
		return map[string]float64{
			InstanceId: 0,
		}
	}
	resp := result.First()
	var cpuUsage float64
	// 仅返回最近一个点
	for _, curve := range resp.SortedCurves() {
		log.Info(ctx, "current proxy cpu value is %s", utils.Show(curve))
		cpuUsage = curve.Values[0]
	}
	return map[string]float64{
		InstanceId: cpuUsage,
	}
}

func convertRegionToCluster(region string) mq.ItemCluster {
	switch region {
	case "boe2":
		return mq.ClusterBOE2
	case "boe":
		return mq.ClusterBOE
	case "cn":
		return mq.ClusterCN
	default:
		return mq.ClusterCN
	}
}

func convertRegionToVRegion(region string) mq.ItemRegion {
	switch region {
	case "boe2":
		return mq.RegionChinaBOE2
	case "boe":
		return mq.RegionChinaBOE
	case "cn":
		return mq.RegionCN
	default:
		return mq.RegionCN
	}
}

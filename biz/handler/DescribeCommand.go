package handler

import (
	bizUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"context"
	"strconv"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"

	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/qjpcpu/fp"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

func (h *DescribeCommandHandler) DescribeCommand(ctx context.Context, req *model.DescribeCommandReq) (ret *model.DescribeCommandResp, err error) {
	if err = h.checkReq(ctx, req); err != nil {
		return
	}
	cmd, err := h.cmdRepo.GetCommand(ctx, req.GetCommandId())
	if err != nil {
		log.Info(ctx, "get command %s fail %v", req.GetCommandId(), err)
		return nil, consts.ErrorOf(model.ErrorCode_CommandNotFound)
	}
	if cmd == nil {
		return nil, consts.ErrorOf(model.ErrorCode_CommandNotFound)
	}
	// 增加租户鉴权
	tenantId := fwctx.GetTenantID(ctx)
	if tenantId != cmd.TenantID {
		return nil, consts.ErrorOf(model.ErrorCode_CommandNotFound)
	}
	var records []*entity.CommandResult
	var limitInfo []*model.CommandResultLimitInfo
	cnf := h.cnf.Get(ctx)
	// 白名单用户走不落库方案
	if bizUtils.IsTenantEnabledFromCtx(ctx, cnf.DBLessTenantIdList) {
		cs, err := h.cmdRepo.GetCommandSetByCmdID(ctx, req.GetCommandId())
		if err != nil {
			log.Warn(ctx, "get command set by cmd id %s fail %v", req.GetCommandId(), err)
			return nil, err
		}
		connectionID := cs.GetConnectionId()

		if cmd.State != entity.CommandTerminated {
			log.Warn(ctx, "command %s state is %s, not terminated, describe command will return nothing", req.GetCommandId(), cmd.State)
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}

		callOpts := h.actorClient.NewCallOpts().WithTimeout(25 * time.Second).WithRetry(1)
		resp, err := h.actorClient.KindOf(consts.QueryResultActorKind).Call(ctx, connectionID, &shared.ScanCursorResult{
			ConnectionId: connectionID,
			CommandId:    cmd.ID,
			// TimeOutMs:    cnf.CommandResultTimeout,
		}, callOpts)

		if err != nil {
			log.Warn(ctx, "failed to fetch result from QueryResultActor, Connection[%s], CommandId[%s], err: %v", connectionID, cmd.ID, err)
			return nil, consts.BuildDBErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}

		switch msg := resp.(type) {
		case *shared.CommandResult:
			now := time.Now().Unix() * 1000
			fp.StreamOf(msg.Payload).
				FlatMap(func(chunk *shared.CommandResultChunk) fp.Stream {
					return fp.StreamOf(chunk.Rows).
						Reject(func(r *shared.CommandResultChunk_Row) bool {
							return r == nil || len(r.Cells) == 0
						}).
						Map(func(r *shared.CommandResultChunk_Row) *entity.CommandResult {
							return &entity.CommandResult{
								SessionID:      cs.SessionID,
								SessionExpired: false,
								ConnectionID:   connectionID,
								CommandID:      cmd.ID,
								ChunkOffset:    chunk.Offset,
								Chunk:          r,
								CreateTimeMS:   now,
							}
						})
				}).
				ToSlice(&records)
			if msg.GetLimitInfo() != nil {
				for _, info := range msg.GetLimitInfo() {
					if info == nil {
						continue
					}
					limitInfo = append(limitInfo, &model.CommandResultLimitInfo{
						Type:       model.ScanLimitType(info.Type),
						LimitValue: info.LimitValue,
					})
				}
			}
		case *shared.ErrScanCursorResult:
			return nil, consts.ErrorWithParam(model.ErrorCode_ParamError, msg.ErrorMessage)
		default:
			log.Warn(ctx, "Unexpected response type from QueryResultActor, got %T", msg)
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}

		// 再查一遍，有的信息是拿到resp过后才维护落库的
		cmd, err = h.cmdRepo.GetCommand(ctx, req.GetCommandId())
		if err != nil {
			log.Info(ctx, "get command %s fail %v", req.GetCommandId(), err)
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
	} else {
		offset, limit := (req.GetPageNumber()-1)*req.GetPageSize(), req.GetPageSize()
		records, err = h.crRepo.List(ctx, req.GetCommandId(), int64(offset), int64(limit))
	}
	if err != nil {
		log.Info(ctx, "get command %s result fail %v", req.GetCommandId(), err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	ret = &model.DescribeCommandResp{
		Command:    clearSensitiveInfoInCommandItem(conv.CommandEntityToModel(cmd)),
		ResultType: conv.ResultTypeEntityToModel(cmd.ResultType),
		Header:     cmd.Header,
		Total: utils.Int32Ref(int32(func() int64 {
			if bizUtils.IsTenantEnabledFromCtx(ctx, cnf.DBLessTenantIdList) {
				return int64(len(records))
			}
			return h.crRepo.Count(ctx, req.GetCommandId())
		}())),
		LimitConfig: &model.LimitConfig{
			Config: map[model.ScanLimitType]int64{
				model.ScanLimitType_RowLimit:  cnf.MaxCommandResultCount,
				model.ScanLimitType_ByteLimit: cnf.MaxCommandResultBytes,
			},
		},
		LimitInfo: limitInfo,
	}

	fp.StreamOf(records).
		Map(func(r *entity.CommandResult) *model.CommandRow {
			return &model.CommandRow{
				Cells: r.Chunk.Cells,
			}
		}).
		ToSlice(&ret.Rows)
	return
}

func (h *DescribeCommandHandler) checkReq(ctx context.Context, req *model.DescribeCommandReq) error {
	if req == nil || req.GetCommandId() == "" {
		log.Info(ctx, "DescribeCommand request param is wrong")
		err := consts.ErrorOf(model.ErrorCode_ParamError)
		return err
	}

	if _, err := strconv.ParseUint(req.GetCommandId(), 10, 64); err != nil {
		log.Warn(ctx, "DescribeCommand CommandId is not a valid number: %s", req.GetCommandId())
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}

	if req.GetPageNumber() < 1 {
		req.SetPageNumber(utils.Int32Ref(consts.DefaultPageNumber))
	}
	if req.GetPageSize() < 1 {
		req.SetPageSize(utils.Int32Ref(consts.DefaultPageSize))
	}
	return nil
}

type DescribeCommandHandler struct {
	cmdRepo     repository.CommandRepo
	crRepo      repository.CommandResultRepo
	actorClient cli.ActorClient
	cnf         config.ConfigProvider
}

type DescribeCommandHandlerOut struct {
	dig.Out
	HandlerImplementationEnvolopeBody HandlerImplementationEnvolope
	DescribeCommandHandlerPtr         *DescribeCommandHandler
}

func NewDescribeCommandHandler(
	cmdRepo repository.CommandRepo,
	crRepo repository.CommandResultRepo,
	actorClient cli.ActorClient,
	cnf config.ConfigProvider,
) DescribeCommandHandlerOut {
	hder := &DescribeCommandHandler{
		cmdRepo:     cmdRepo,
		crRepo:      crRepo,
		actorClient: actorClient,
		cnf:         cnf,
	}
	return DescribeCommandHandlerOut{HandlerImplementationEnvolopeBody: NewHandler(hder.DescribeCommand), DescribeCommandHandlerPtr: hder}
}

package openapi

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler/openapi/sqltask"
	bizUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
)

func (h *DataGetCommandSetResultHandler) DataGetCommandSetResult(ctx context.Context, req *model.DataGetCommandSetResultReq) (ret *model.DataGetCommandSetResultResp, err error) {
	ret = &model.DataGetCommandSetResultResp{}

	resp, err := h.describeCommandSetHandler.DescribeCommandSet(ctx, &model.DescribeCommandSetReq{
		CommandSetId: utils.StringRef(req.GetCommandSetId()),
	})
	if err != nil {
		log.Error(ctx, "DataGetCommandSetResult fail because err: %v", err)
		return
	}
	log.Info(ctx, "describe command set resp is %s", utils.Show(resp))
	var Results []*model.ResultObject
	if bizUtils.IsTenantEnabledFromCtx(ctx, h.cnf.Get(ctx).DBLessTenantIdList) {
		Results = collectResultFromStorage(ctx, h.actorClient, req.GetCommandSetId())
	} else {
		Results = collectResult(ctx, h.cnf, h.describeCommandHandler, h.describeSQLTaskHandler, resp.GetCommands())
	}

	ret.FilterType = req.FilterType
	if req.IsSetFilterType() {
		switch req.GetFilterType() {
		case model.FilterType_Success:
			ret.Results, err = commandFileter(Results, model.FilterType_Success)
		case model.FilterType_Failed:
			ret.Results, err = commandFileter(Results, model.FilterType_Failed)
		case model.FilterType_Cancel:
			ret.Results, err = commandFileter(Results, model.FilterType_Cancel)
		case model.FilterType_All:
			ret.Results = Results
		}
	} else {
		ret.Results = Results
	}
	if err != nil {
		log.Error(ctx, "CommandsResult Filter fail because err: %v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return ret, nil
}

type DataGetCommandSetResultHandler struct {
	describeCommandSetHandler *handler.DescribeCommandSetHandler
	describeCommandHandler    *handler.DescribeCommandHandler // get result
	describeSQLTaskHandler    *sqltask.DescribeSqlTaskHandler
	actorClient               cli.ActorClient
	cnf                       config.ConfigProvider
}

func NewDataGetCommandSetResultHandler(
	describeCommandSetHandler *handler.DescribeCommandSetHandler,
	describeCommandHandler *handler.DescribeCommandHandler, // get result
	describeSQLTaskHandler *sqltask.DescribeSqlTaskHandler,
	actorClient cli.ActorClient,
	cnf config.ConfigProvider,
) handler.HandlerImplementationEnvolope {
	hder := &DataGetCommandSetResultHandler{
		describeCommandSetHandler: describeCommandSetHandler,
		describeCommandHandler:    describeCommandHandler,
		describeSQLTaskHandler:    describeSQLTaskHandler,
		cnf:                       cnf,
		actorClient:               actorClient,
	}
	return handler.NewHandler(hder.DataGetCommandSetResult)
}

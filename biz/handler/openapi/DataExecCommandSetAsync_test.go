package openapi

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"k8s.io/utils/pointer"

	config_biz "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/persistence"
)

// we create the test template for the runnable function
// please fill the testcase and mock function

type DataExecCommandSetAsyncTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

type DataExecCommandSetAsyncMock struct {
	cli    *mocks.MockActorClient
	ctx    context.Context
	ret    *model.DataExecCommandSetAsyncResp
	codec  CodecService
	peeker *mocks.MockActorStoragePeeker
	sp     *mocks.MockCommandParser
	h      func(context.Context, *model.DataExecCommandSetAsyncReq) (*model.DataExecCommandSetAsyncResp, error)
}

func (g *DataExecCommandSetAsyncMock) newMock(s *DataExecCommandSetAsyncTestSuite) {
	g.cli = mocks.NewMockActorClient(s.ctrl)
	g.ctx = context.Background()
	g.codec = NewIDCodec()
	g.peeker = mocks.NewMockActorStoragePeeker(s.ctrl)
	g.sp = mocks.NewMockCommandParser(s.ctrl)
	dataExecCommandsHandler := &DataExecCommandsHandler{}
	cnf := mocks.NewMockConfigProvider(s.ctrl)
	cnf.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{DBLessTenantIdList: []string{}}).AnyTimes()
	executeCommandHandler := &handler.ExecuteCommandSetHandler{}
	changeDBHandler := &handler.ChangeDBHandler{}
	g.h = NewDataExecCommandSetAsyncHandler(dataExecCommandsHandler, executeCommandHandler, changeDBHandler, g.codec, g.cli, g.peeker, g.sp, cnf).Impl.Impl.(func(context.Context, *model.DataExecCommandSetAsyncReq) (*model.DataExecCommandSetAsyncResp, error))
}

func (s *DataExecCommandSetAsyncTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
}

func (s *DataExecCommandSetAsyncTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func TestDataExecCommandSetAsyncTestSuite(t *testing.T) {
	suite.Run(t, new(DataExecCommandSetAsyncTestSuite))
}

func (s *DataExecCommandSetAsyncTestSuite) TestDataExecCommandSetAsync_Success() {
	req := &model.DataExecCommandSetAsyncReq{
		SessionId:         "MTU5MDk4ODQzMTE2NzU5MDQwMC8xNTkwOTg4NDMxMTg0MzcxNzEy",
		CommandSetContent: "show tables;select version();select 1;",
		DatabaseName:      utils.StringRef("yctest"),
		AutoOnlineDDL:     utils.BoolRef(true),
		Force:             pointer.Bool(true),
	}
	source := &shared.DataSource{
		Type: shared.MySQL,
	}
	mock := mockey.Mock(handler.GetSourceBySessionId).Return(source, nil).Build()
	defer mock.UnPatch()

	var g DataExecCommandSetAsyncMock
	g.newMock(s)
	var resp = &model.ExecuteCommandSetResp{
		CommandSetId: utils.StringRef("1590989482411180035"),
		Commands: []*model.CommandItem{
			{CommandStr: utils.StringRef("show databases;"),
				CommandId: utils.StringRef("1590989482411180032"),
				State:     model.CommandStatePtr(model.CommandState_Pending),
			},
			{CommandStr: utils.StringRef("show tables;"),
				CommandId: utils.StringRef("1590989482411180033"),
				State:     model.CommandStatePtr(model.CommandState_Pending),
			},
			{CommandStr: utils.StringRef("select vesion();"),
				CommandId: utils.StringRef("1590989482411180034"),
				State:     model.CommandStatePtr(model.CommandState_Pending),
			},
		},
	}
	commandSetStructs := []*model.CommandSetStruct{
		{
			Command:             "SELECT * FROM table1",
			CommandLanguageType: model.SqlLanguageType_Normal,
			CommandExecuteType:  model.SqlExecuteType_Connection,
		},
		{
			Command:             "UPDATE table2 SET column1 = value1",
			CommandLanguageType: model.SqlLanguageType_OnlineDDL,
			CommandExecuteType:  model.SqlExecuteType_Task,
		},
	}
	mockey.PatchConvey("Success", s.T(), func() {
		//mockey.Mock(handler.ValidateSession).Return(error(nil)).Build()
		g.sp.EXPECT().PreCheck(g.ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(commandSetStructs, nil).Times(1)
		g.peeker.EXPECT().Get(g.ctx, gomock.Any(), gomock.Any()).Return(&persistence.Actor{State: persistence.ActorState_Active}).Times(1)
		mockey.Mock(currentDB).Return("sys", error(nil)).Build()
		mockey.Mock(changeDBIfNeed).Return(error(nil)).Build()
		mockey.Mock((*handler.ExecuteCommandSetHandler).ExecuteCommandSet).Return(resp, error(nil)).Build()
		gotRet, err := g.h(g.ctx, req)
		expectRet := &model.DataExecCommandSetAsyncResp{CommandSetId: utils.StringRef("1590989482411180035")}
		s.NotEmpty(gotRet)
		s.Equal(expectRet, gotRet)
		s.Empty(err)
	})
}

func (s *DataExecCommandSetAsyncTestSuite) TestDataExecCommandSetAsync_FailedForSession() {
	req := &model.DataExecCommandSetAsyncReq{
		SessionId:         "MTU5MDk4ODQzMTE2NzU5MDQ",
		CommandSetContent: "show tables;select version();select 1;",
		DatabaseName:      utils.StringRef("yctest"),
	}
	mock := mockey.Mock(handler.GetSourceBySessionId).Return(nil, nil).Build()
	defer mock.UnPatch()

	var g DataExecCommandSetAsyncMock
	g.newMock(s)
	mockey.PatchConvey("FailedForSession", s.T(), func() {
		gotRet, err := g.h(g.ctx, req)
		expectError := consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid SessionID "+req.GetSessionId())
		s.Empty(gotRet)
		s.Equal(expectError, err)
	})
}

func (s *DataExecCommandSetAsyncTestSuite) TestDataExecCommandSetAsync_FailedForValidateSession() {
	req := &model.DataExecCommandSetAsyncReq{
		SessionId:         "MTU5MDk4ODQzMTE2NzU5MDQwMC8xNTkwOTg4NDMxMTg0MzcxNzEy",
		CommandSetContent: "show tables;select version();select 1;",
		DatabaseName:      utils.StringRef(""),
	}
	mock := mockey.Mock(handler.GetSourceBySessionId).Return(nil, nil).Build()
	defer mock.UnPatch()
	var g DataExecCommandSetAsyncMock
	g.newMock(s)
	mockey.PatchConvey("FailedForValidateSession", s.T(), func() {
		g.peeker.EXPECT().Get(g.ctx, gomock.Any(), gomock.Any()).Return(&persistence.Actor{State: persistence.ActorState_Dead}).Times(1)
		gotRet, err := g.h(g.ctx, req)
		expectError := consts.ErrorOf(model.ErrorCode_SessionNotExistError)
		s.Empty(gotRet)
		s.Equal(expectError, err)
	})
}

func (s *DataExecCommandSetAsyncTestSuite) TestDataExecCommandSetAsync_FailedForCurrentDB() {
	req := &model.DataExecCommandSetAsyncReq{
		SessionId:         "MTU5MDk4ODQzMTE2NzU5MDQwMC8xNTkwOTg4NDMxMTg0MzcxNzEy",
		CommandSetContent: "show tables;select version();select 1;",
		DatabaseName:      utils.StringRef("yctest"),
	}
	mock := mockey.Mock(handler.GetSourceBySessionId).Return(nil, nil).Build()
	defer mock.UnPatch()
	var g DataExecCommandSetAsyncMock
	g.newMock(s)
	mockey.PatchConvey("FailedForCurrentDB", s.T(), func() {
		returnError := errors.New(fmt.Sprintf("could not find db for connection id %s", "1590988431184371712"))
		g.peeker.EXPECT().Get(g.ctx, gomock.Any(), gomock.Any()).Return(&persistence.Actor{State: persistence.ActorState_Active}).Times(1)
		mockey.Mock(currentDB).Return("", returnError).Build()
		gotRet, err := g.h(g.ctx, req)
		expectError := consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid SessionID "+req.GetSessionId())
		s.Empty(gotRet)
		s.Equal(expectError, err)
	})
}

func (s *DataExecCommandSetAsyncTestSuite) TestDataExecCommandSetAsync_FailedForChangeDB() {
	req := &model.DataExecCommandSetAsyncReq{
		SessionId:         "MTU5MDk4ODQzMTE2NzU5MDQwMC8xNTkwOTg4NDMxMTg0MzcxNzEy",
		CommandSetContent: "show tables;select version();select 1;",
		DatabaseName:      utils.StringRef("yctestjjjj"),
	}
	mock := mockey.Mock(handler.GetSourceBySessionId).Return(nil, nil).Build()
	defer mock.UnPatch()
	var g DataExecCommandSetAsyncMock
	g.newMock(s)
	mockey.PatchConvey("FailedForCurrentDB", s.T(), func() {
		returnError := consts.ErrorOf(model.ErrorCode_DataSourceOpFail)
		g.peeker.EXPECT().Get(g.ctx, gomock.Any(), gomock.Any()).Return(&persistence.Actor{State: persistence.ActorState_Active}).Times(1)
		mockey.Mock(currentDB).Return("sys", error(nil)).Build()
		mockey.Mock(changeDBIfNeed).Return(returnError).Build()
		gotRet, err := g.h(g.ctx, req)
		expectError := consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid Database "+req.GetDatabaseName())
		s.Empty(gotRet)
		s.Equal(expectError, err)
	})
}

//func (s *DataExecCommandSetAsyncTestSuite) TestDataExecCommandSetAsync_FailedForExecuteCommandSet() {
//	req := &model.DataExecCommandSetAsyncReq{
//		SessionId:         "MTU5MDk4ODQzMTE2NzU5MDQwMC8xNTkwOTg4NDMxMTg0MzcxNzEy",
//		CommandSetContent: "show tables;select version();select 1;",
//		DatabaseName:      utils.StringRef("yctestjjjj"),
//		AutoOnlineDDL:     utils.BoolRef(true),
//	}
//	commandSetStructs := []*model.CommandSetStruct{
//		{
//			Command:             "SELECT * FROM table1",
//			CommandLanguageType: model.SqlLanguageType_Normal,
//			CommandExecuteType:  model.SqlExecuteType_Connection,
//		},
//		{
//			Command:             "UPDATE table2 SET column1 = value1",
//			CommandLanguageType: model.SqlLanguageType_OnlineDDL,
//			CommandExecuteType:  model.SqlExecuteType_Task,
//		},
//	}
//	source := &shared.DataSource{
//		Type: shared.MySQL,
//	}
//	mock := mockey.Mock(handler.GetSourceBySessionId).Return(source, nil).Build()
//	defer mock.UnPatch()
//	var g DataExecCommandSetAsyncMock
//	g.newMock(s)
//	mockey.PatchConvey("FailedForExecuteCommandSet", s.T(), func() {
//		g.sp.EXPECT().PreCheck(g.ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(commandSetStructs, nil).Times(1)
//		returnError := consts.ErrorOf(model.ErrorCode_ConnectionBusy)
//		g.peeker.EXPECT().Get(g.ctx, gomock.Any(), gomock.Any()).Return(&persistence.Actor{State: persistence.ActorState_Active}).Times(1)
//		mockey.Mock(currentDB).Return("sys", error(nil)).Build()
//		mockey.Mock(changeDBIfNeed).Return(error(nil)).Build()
//		mockey.Mock((*handler.ExecuteCommandSetHandler).ExecuteCommandSet).Return(nil, returnError).Build()
//		gotRet, err := g.h(g.ctx, req)
//		expectError := consts.ErrorOf(model.ErrorCode_InternalError)
//		s.Empty(gotRet)
//		s.Equal(expectError, err)
//	})
//}

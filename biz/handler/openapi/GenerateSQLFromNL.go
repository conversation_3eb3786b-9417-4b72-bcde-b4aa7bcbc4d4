package openapi

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/handler/dbgpt"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"go.uber.org/dig"
)

type GenerateSQLFromNLHandler struct {
	actor                cli.ActorClient
	createSessionHandler *handler.CreateSessionHandler
	sqlAssistantHandler  *dbgpt.SqlAssistantHandler
}

type GenerateSQLFromNLHandlerIn struct {
	dig.In
	Actor                cli.ActorClient
	CreateSessionHandler *handler.CreateSessionHandler
	SqlAssistantHandler  *dbgpt.SqlAssistantHandler
}

func NewGenerateSQLFromNLHandler(in GenerateSQLFromNLHandlerIn) handler.HTTPHandlerImplementationEnvolope {
	h := &GenerateSQLFromNLHandler{
		actor:                in.Actor,
		createSessionHandler: in.CreateSessionHandler,
		sqlAssistantHandler:  in.SqlAssistantHandler,
	}

	return handler.NewHTTPHandler(h.GenerateSQLFromNL)
}

func (h *GenerateSQLFromNLHandler) GenerateSQLFromNL(ctx context.Context, req *model.GenerateSQLFromNLReq) (*model.GenerateSQLFromNLResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}

	// 查询或新建SessionID
	sessionID, err := h.getOrCreateSessionID(ctx, req.InstanceID, req.InstanceType)
	if err != nil {
		log.Error(ctx, "failed to get or create sessionID, instanceID: %s, err: %v", req.InstanceID, err)
		return nil, err
	}

	sqlAssistantReq := &model.SqlAssistantReq{
		SessionId: sessionID,
		Database:  req.Database,
		Query:     req.Query,
		Action:    model.QueryAction_Generate,
		Tables:    req.Tables,
		IsStream:  req.IsStream,
	}

	// 请求SqlAssistant接口
	resp, err := h.sqlAssistantHandler.SqlAssistant(ctx, sqlAssistantReq)
	if err != nil {
		log.Error(ctx, "failed to request SqlAssistant, err: %v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	// 处理服务端SSE流式返回的情况：SqlAssistant接口向透传的*gin.Context直接写入SSE Events，此处直接返回
	if req.GetIsStream() {
		return nil, nil
	}

	// 处理服务端同步返回的情况：解析SqlAssistantResp并转换成GenerateSQLFromNLResp
	if resp == nil || resp.Reply == nil {
		log.Error(ctx, "failed to decode the SqlAssistant response, err: %v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	return &model.GenerateSQLFromNLResp{
		SQL: resp.Reply.Text,
	}, nil
}

func (h *GenerateSQLFromNLHandler) checkReq(ctx context.Context, req *model.GenerateSQLFromNLReq) error {
	if req == nil {
		log.Error(ctx, "request is empty")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "request is empty")
	}

	if len(req.InstanceID) == 0 {
		log.Error(ctx, "instanceID is empty")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "instanceID is empty")
	}

	if len(req.Database) == 0 {
		log.Error(ctx, "database is empty")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "database is empty")
	}

	if len(req.Query) == 0 {
		log.Error(ctx, "query is empty")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "query is empty")
	}

	return nil
}

func (h *GenerateSQLFromNLHandler) getOrCreateSessionID(ctx context.Context, instanceID string, instanceType string) (string, error) {
	sessionCacheKey := &shared.SessionCacheKey{
		TenantID:   fwctx.GetTenantID(ctx),
		UserID:     fwctx.GetUserID(ctx),
		InstanceID: instanceID,
	}

	// 查询NL2SQLSessionActor缓存
	resp, err := h.actor.KindOf(consts.NL2SQLSessionActorKind).Call(ctx, consts.NL2SQLSessionActorKind, &shared.DescribeNL2SQLSessionReq{
		NL2SQLSessionCacheKey: sessionCacheKey,
	})
	if err != nil {
		log.Error(ctx, "failed to describe the NL2SQL session, err: %v", err)
		return "", consts.ErrorOf(model.ErrorCode_InternalError)
	}

	describeNL2SQLSessionResp, ok := resp.(*shared.DescribeNL2SQLSessionResp)
	if !ok {
		log.Error(ctx, "failed to convert the NL2SQL session actor response to the DescribeNL2SQLSessionResp type")
		return "", consts.ErrorOf(model.ErrorCode_InternalError)
	}

	// 若NL2SQLSessionActor缓存中存在当前租户和数据库实例的会话信息，则直接返回
	if describeNL2SQLSessionResp.SessionID != "" {
		return describeNL2SQLSessionResp.SessionID, nil
	}

	// 若NL2SQLSessionActor缓存中不存在当前租户和数据库实例的会话信息，则需要调用CreateSession接口创建会话
	dsType, err := model.DSTypeFromString(instanceType)
	if err != nil {
		log.Error(ctx, "failed to convert the instance type to the DSType, instance type: %s, err: %v", err)
		return "", consts.ErrorWithParam(model.ErrorCode_InputParamError, err.Error())
	}

	session, err := h.createSessionHandler.CreateSession(ctx, &model.CreateSessionReq{
		DataSource: &model.DataSource{
			Type:       dsType,
			LinkType:   model.LinkType_Volc,
			InstanceId: &instanceID,
		},
		TimeoutSeconds: &consts.NL2SQLSessionTimeoutSeconds,
	})
	if err != nil || session.SessionId == nil {
		log.Error(ctx, "failed to create session, instanceID: %s, err: %v", instanceID, err)
		return "", consts.ErrorOf(model.ErrorCode_CreateSessionError)
	}

	// 向NL2SQLSessionActor发送当前租户和数据库实例的连接会话信息
	_, err = h.actor.KindOf(consts.NL2SQLSessionActorKind).Call(ctx, consts.NL2SQLSessionActorKind, &shared.CreateNL2SQLSessionReq{
		NL2SQLSessionCacheKey: sessionCacheKey,
		SessionID:             *session.SessionId,
	})
	if err != nil {
		log.Error(ctx, "failed to create the NL2SQL session, instanceID: %s, err: %v", instanceID, err)
		return "", consts.ErrorOf(model.ErrorCode_InternalError)
	}

	return session.GetSessionId(), nil
}

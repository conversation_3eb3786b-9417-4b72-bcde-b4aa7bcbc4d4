package openapi

import (
	context "context"
	fmt "fmt"
	strings "strings"
	syscall "syscall"
	testing "testing"

	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/golang/mock/gomock"

	atgconv "code.byted.org/gopkg/lang/conv"
	config_biz "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	handler "code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	model "code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/bytedance/mockey"
	convey "github.com/smartystreets/goconvey/convey"
)

func TestDataGetCommandSetResult_Success(t *testing.T) {
	type Fields struct {
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		Cnf                       *config.MockConfigProvider
	}
	type Args struct {
		Ctx context.Context
		Req *model.DataGetCommandSetResultReq
	}
	type test struct {
		Name    string
		Fields  Fields
		Args    Args
		WantRet *model.DataGetCommandSetResultResp
		WantErr bool
		Mocks   func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{{
		Name: "Success",
		Args: Args{
			Ctx: ctx,
			Req: &model.DataGetCommandSetResultReq{
				CommandSetId: atgconv.StringPtr("1592071154460282883"),
			},
		},
		WantRet: &model.DataGetCommandSetResultResp{
			Results: []*model.ResultObject{
				{
					CommandStr:  "SHOW VARIABLES LIKE \"general_log%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(2),
					Rows: []*model.CommandRow{
						{Cells: []string{"general_log", "OFF"}},
						{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
					},
				},
				{
					CommandStr:  "show variables like \"log_bin%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(5),
					Rows: []*model.CommandRow{
						{Cells: []string{"log_bin", "ON"}},
						{Cells: []string{"log_bin_basename", "/var/log/mysql/bin/binlog"}},
						{Cells: []string{"log_bin_index", "/var/log/mysql/bin/binlog.index"}},
						{Cells: []string{"log_bin_trust_function_creators", "ON"}},
						{Cells: []string{"log_bin_use_v1_row_events", "OFF"}},
					},
				},
				{
					CommandStr:  "select version();",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"version()"},
					RowCount:    int32(1),
					Rows: []*model.CommandRow{
						{Cells: []string{"5.7.32-log"}},
					},
				},
			},
		},
		WantErr: false,
		Mocks: func() {
			mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
				StartTime: atgconv.Int64Ptr(int64(1668414317000)),
				EndTime:   atgconv.Int64Ptr(int64(1668414317000)),
				Progress:  atgconv.Int32Ptr(int32(100)),
				Content:   atgconv.StringPtr("SHOW VARIABLES LIKE \\\"general_log%\\\";show variables like \\\"log_bin%\\\";select version();"),
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("show variables like \"log_bin%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282881"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("select version();"),
						CommandId:  atgconv.StringPtr("1592071154460282882"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
				},
			}, error(nil)).Build()
		},
	}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000}).AnyTimes()
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "show variables like \"log_bin%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin", "ON"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_basename", "/var/log/mysql/bin/binlog"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(2),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_index", "/var/log/mysql/bin/binlog.index"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(3),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_trust_function_creators", "ON"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(4),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_use_v1_row_events", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(5))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "select version();",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt3),
				Header:       []string{"version()"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282882",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"5.7.32-log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(1))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			tt.Mocks()
			mockey.Mock(syscall.Connect).Return(fmt.Errorf("SU stops the Connection"))
			h := &DataGetCommandSetResultHandler{
				describeCommandSetHandler: tt.Fields.DescribeCommandSetHandler,
				describeCommandHandler:    tt.Fields.DescribeCommandHandler,
				cnf:                       tt.Fields.Cnf,
			}
			gotRet, err := h.DataGetCommandSetResult(tt.Args.Ctx, tt.Args.Req)
			if (err != nil) != tt.WantErr {
				convey.So((err != nil), convey.ShouldResemble, tt.WantErr)
			}
			if !strings.EqualFold(fmt.Sprintf("%v", gotRet), fmt.Sprintf("%v", tt.WantRet)) {
				convey.So(gotRet, convey.ShouldNotEqual, tt.WantRet)
			}
		})
	}
}

func TestDataGetCommandSetResult_FailedForDescribeCommandSet(t *testing.T) {
	type Fields struct {
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		Cnf                       *config.MockConfigProvider
	}
	type Args struct {
		Ctx context.Context
		Req *model.DataGetCommandSetResultReq
	}
	type test struct {
		Name    string
		Fields  Fields
		Args    Args
		WantRet *model.DataGetCommandSetResultResp
		WantErr bool
		Mocks   func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{{
		Name: "FailedForDescribeCommandSet",
		Args: Args{
			Ctx: ctx,
			Req: &model.DataGetCommandSetResultReq{
				CommandSetId: atgconv.StringPtr("1592071154460282883"),
			},
		},
		WantRet: &model.DataGetCommandSetResultResp{},
		WantErr: true,
		Mocks: func() {
			mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(nil, consts.ErrorOf(model.ErrorCode_CommandNotFound)).Build()
		},
	}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			tt.Mocks()
			mockey.Mock(syscall.Connect).Return(fmt.Errorf("SU stops the Connection"))
			h := &DataGetCommandSetResultHandler{
				describeCommandSetHandler: tt.Fields.DescribeCommandSetHandler,
				describeCommandHandler:    tt.Fields.DescribeCommandHandler,
				cnf:                       tt.Fields.Cnf,
			}
			gotRet, err := h.DataGetCommandSetResult(tt.Args.Ctx, tt.Args.Req)
			if (err != nil) != tt.WantErr {
				convey.So(err, convey.ShouldEqual, tt.WantErr)
			}
			if !strings.EqualFold(fmt.Sprintf("%v", gotRet), fmt.Sprintf("%v", tt.WantRet)) {
				convey.So(gotRet, convey.ShouldEqual, tt.WantRet)
			}
		})
	}
}

func TestDataGetCommandSetResult_Success_FilterForSuccess(t *testing.T) {
	type Fields struct {
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		Cnf                       *config.MockConfigProvider
	}
	type Args struct {
		Ctx context.Context
		Req *model.DataGetCommandSetResultReq
	}
	type test struct {
		Name    string
		Fields  Fields
		Args    Args
		WantRet *model.DataGetCommandSetResultResp
		WantErr bool
		Mocks   func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{{
		Name: "Success_FilterForSuccess",
		Args: Args{
			Ctx: ctx,
			Req: &model.DataGetCommandSetResultReq{
				CommandSetId: atgconv.StringPtr("1592071154460282883"),
				FilterType:   model.FilterTypePtr(model.FilterType_Success),
			},
		},
		WantRet: &model.DataGetCommandSetResultResp{
			Results: []*model.ResultObject{
				{
					CommandStr:  "SHOW VARIABLES LIKE \"general_log%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(2),
					Rows: []*model.CommandRow{
						{Cells: []string{"general_log", "OFF"}},
						{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
					},
				},
				{
					CommandStr:  "show variables like \"log_bin%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(5),
					Rows: []*model.CommandRow{
						{Cells: []string{"log_bin", "ON"}},
						{Cells: []string{"log_bin_basename", "/var/log/mysql/bin/binlog"}},
						{Cells: []string{"log_bin_index", "/var/log/mysql/bin/binlog.index"}},
						{Cells: []string{"log_bin_trust_function_creators", "ON"}},
						{Cells: []string{"log_bin_use_v1_row_events", "OFF"}},
					},
				},
				{
					CommandStr:  "select version();",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"version()"},
					RowCount:    int32(1),
					Rows: []*model.CommandRow{
						{Cells: []string{"5.7.32-log"}},
					},
				},
			},
			FilterType: model.FilterTypePtr(model.FilterType_Success),
		},
		WantErr: false,
		Mocks: func() {
			mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
				StartTime: atgconv.Int64Ptr(int64(1668414317000)),
				EndTime:   atgconv.Int64Ptr(int64(1668414317000)),
				Progress:  atgconv.Int32Ptr(int32(100)),
				Content:   atgconv.StringPtr("SHOW VARIABLES LIKE \\\"general_log%\\\";show variables like \\\"log_bin%\\\";select version();"),
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("show variables like \"log_bin%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282881"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("select version();"),
						CommandId:  atgconv.StringPtr("1592071154460282882"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
				},
			}, error(nil)).Build()
		},
	}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000}).AnyTimes()
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "show variables like \"log_bin%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin", "ON"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_basename", "/var/log/mysql/bin/binlog"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(2),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_index", "/var/log/mysql/bin/binlog.index"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(3),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_trust_function_creators", "ON"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(4),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_use_v1_row_events", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(5))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "select version();",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt3),
				Header:       []string{"version()"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282882",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"5.7.32-log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(1))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			tt.Mocks()
			mockey.Mock(syscall.Connect).Return(fmt.Errorf("SU stops the Connection"))
			h := &DataGetCommandSetResultHandler{
				describeCommandSetHandler: tt.Fields.DescribeCommandSetHandler,
				describeCommandHandler:    tt.Fields.DescribeCommandHandler,
				cnf:                       tt.Fields.Cnf,
			}
			gotRet, err := h.DataGetCommandSetResult(tt.Args.Ctx, tt.Args.Req)
			if (err != nil) != tt.WantErr {
				convey.So((err != nil), convey.ShouldResemble, tt.WantErr)
			}
			if !strings.EqualFold(fmt.Sprintf("%v", gotRet), fmt.Sprintf("%v", tt.WantRet)) {
				convey.So(gotRet, convey.ShouldNotEqual, tt.WantRet)
			}
		})
	}
}

func TestDataGetCommandSetResult_Success_FilterForAll(t *testing.T) {
	type Fields struct {
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		Cnf                       *config.MockConfigProvider
	}
	type Args struct {
		Ctx context.Context
		Req *model.DataGetCommandSetResultReq
	}
	type test struct {
		Name    string
		Fields  Fields
		Args    Args
		WantRet *model.DataGetCommandSetResultResp
		WantErr bool
		Mocks   func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{{
		Name: "Success_FilterForFailed",
		Args: Args{
			Ctx: ctx,
			Req: &model.DataGetCommandSetResultReq{
				CommandSetId: atgconv.StringPtr("1592071154460282883"),
				FilterType:   model.FilterTypePtr(model.FilterType_All),
			},
		},
		WantRet: &model.DataGetCommandSetResultResp{
			Results: []*model.ResultObject{
				{
					CommandStr:  "SHOW VARIABLES LIKE \"general_log%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(2),
					Rows: []*model.CommandRow{
						{Cells: []string{"general_log", "OFF"}},
						{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
					},
				},
				{
					CommandStr:   "select *from jq.ndb;",
					State:        model.CommandResultState_Failed,
					RunTime:      int64(1668414317000),
					RowCount:     int32(0),
					ReasonDetail: "Error 1146: Table 'jq.ndb' doesn't exist",
				},
				{
					CommandStr:   "select version();",
					State:        model.CommandResultState_Cancel,
					RowCount:     int32(0),
					ReasonDetail: "取消执行",
				},
			},
			FilterType: model.FilterTypePtr(model.FilterType_All),
		},
		WantErr: false,
		Mocks: func() {
			mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
				StartTime: atgconv.Int64Ptr(int64(1668414317000)),
				EndTime:   atgconv.Int64Ptr(int64(1668414317000)),
				Progress:  atgconv.Int32Ptr(int32(100)),
				Content:   atgconv.StringPtr("SHOW VARIABLES LIKE \\\"general_log%\\\";show variables like \\\"log_bin%\\\";select version();"),
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr:   atgconv.StringPtr("select *from jq.ndb;"),
						CommandId:    atgconv.StringPtr("1592071154460282881"),
						State:        model.CommandStatePtr(model.CommandState_Terminated),
						Reason:       model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Failed),
						ReasonDetail: atgconv.StringPtr("Error 1146: Table 'jq.ndb' doesn't exist"),
						StartTime:    atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:      atgconv.Int64Ptr(int64(1668414317000)),
						Extra:        map[string]string{"CanEdit": "true", "Schema": "jq", "Table": "ndb"},
					},
					{
						CommandStr:   atgconv.StringPtr("select version();"),
						CommandId:    atgconv.StringPtr("1592071154460282882"),
						State:        model.CommandStatePtr(model.CommandState_Terminated),
						Reason:       model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Cancel),
						ReasonDetail: atgconv.StringPtr("取消执行"),
						StartTime:    atgconv.Int64Ptr(int64(0)),
						EndTime:      atgconv.Int64Ptr(int64(0)),
						Extra:        map[string]string{"CanEdit": "false"},
					},
				},
			}, error(nil)).Build()
		},
	}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000}).AnyTimes()
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Failed),
				ReasonDetail: "Error 1146: Table 'jq.ndb' doesn't exist",
				Content:      "select *from jq.ndb;",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(0))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Cancel),
				ReasonDetail: "取消执行",
				Content:      "select version();",
				StartTimeMS:  int64(0),
				EndTimeMS:    int64(0),
				ResultType:   &(rt3),
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(0))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			tt.Mocks()
			mockey.Mock(syscall.Connect).Return(fmt.Errorf("SU stops the Connection"))
			h := &DataGetCommandSetResultHandler{
				describeCommandSetHandler: tt.Fields.DescribeCommandSetHandler,
				describeCommandHandler:    tt.Fields.DescribeCommandHandler,
				cnf:                       tt.Fields.Cnf,
			}
			gotRet, err := h.DataGetCommandSetResult(tt.Args.Ctx, tt.Args.Req)
			if (err != nil) != tt.WantErr {
				convey.So((err != nil), convey.ShouldResemble, tt.WantErr)
			}
			if !strings.EqualFold(fmt.Sprintf("%v", gotRet), fmt.Sprintf("%v", tt.WantRet)) {
				convey.So(gotRet, convey.ShouldNotEqual, tt.WantRet)
			}
		})
	}
}

func TestDataGetCommandSetResult_Success_FilterForFailed(t *testing.T) {
	type Fields struct {
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		Cnf                       *config.MockConfigProvider
	}
	type Args struct {
		Ctx context.Context
		Req *model.DataGetCommandSetResultReq
	}
	type test struct {
		Name    string
		Fields  Fields
		Args    Args
		WantRet *model.DataGetCommandSetResultResp
		WantErr bool
		Mocks   func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{{
		Name: "Success_FilterForFailed",
		Args: Args{
			Ctx: ctx,
			Req: &model.DataGetCommandSetResultReq{
				CommandSetId: atgconv.StringPtr("1592071154460282883"),
				FilterType:   model.FilterTypePtr(model.FilterType_Failed),
			},
		},
		WantRet: &model.DataGetCommandSetResultResp{
			Results: []*model.ResultObject{
				{
					CommandStr:   "select *from jq.ndb;",
					State:        model.CommandResultState_Failed,
					RunTime:      int64(1668414317000),
					RowCount:     int32(0),
					ReasonDetail: "Error 1146: Table 'jq.ndb' doesn't exist",
				},
			},
			FilterType: model.FilterTypePtr(model.FilterType_Failed),
		},
		WantErr: false,
		Mocks: func() {
			mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
				StartTime: atgconv.Int64Ptr(int64(1668414317000)),
				EndTime:   atgconv.Int64Ptr(int64(1668414317000)),
				Progress:  atgconv.Int32Ptr(int32(100)),
				Content:   atgconv.StringPtr("SHOW VARIABLES LIKE \\\"general_log%\\\";show variables like \\\"log_bin%\\\";select version();"),
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr:   atgconv.StringPtr("select *from jq.ndb;"),
						CommandId:    atgconv.StringPtr("1592071154460282881"),
						State:        model.CommandStatePtr(model.CommandState_Terminated),
						Reason:       model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Failed),
						ReasonDetail: atgconv.StringPtr("Error 1146: Table 'jq.ndb' doesn't exist"),
						StartTime:    atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:      atgconv.Int64Ptr(int64(1668414317000)),
						Extra:        map[string]string{"CanEdit": "true", "Schema": "jq", "Table": "ndb"},
					},
					{
						CommandStr:   atgconv.StringPtr("select version();"),
						CommandId:    atgconv.StringPtr("1592071154460282882"),
						State:        model.CommandStatePtr(model.CommandState_Terminated),
						Reason:       model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Cancel),
						ReasonDetail: atgconv.StringPtr("取消执行"),
						StartTime:    atgconv.Int64Ptr(int64(0)),
						EndTime:      atgconv.Int64Ptr(int64(0)),
						Extra:        map[string]string{"CanEdit": "false"},
					},
				},
			}, error(nil)).Build()
		},
	}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000}).AnyTimes()
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Failed),
				ReasonDetail: "Error 1146: Table 'jq.ndb' doesn't exist",
				Content:      "select *from jq.ndb;",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(0))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Cancel),
				ReasonDetail: "取消执行",
				Content:      "select version();",
				StartTimeMS:  int64(0),
				EndTimeMS:    int64(0),
				ResultType:   &(rt3),
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(0))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			tt.Mocks()
			mockey.Mock(syscall.Connect).Return(fmt.Errorf("SU stops the Connection"))
			h := &DataGetCommandSetResultHandler{
				describeCommandSetHandler: tt.Fields.DescribeCommandSetHandler,
				describeCommandHandler:    tt.Fields.DescribeCommandHandler,
				cnf:                       tt.Fields.Cnf,
			}
			gotRet, err := h.DataGetCommandSetResult(tt.Args.Ctx, tt.Args.Req)
			if (err != nil) != tt.WantErr {
				convey.So((err != nil), convey.ShouldResemble, tt.WantErr)
			}
			if !strings.EqualFold(fmt.Sprintf("%v", gotRet), fmt.Sprintf("%v", tt.WantRet)) {
				convey.So(gotRet, convey.ShouldNotEqual, tt.WantRet)
			}
		})
	}
}

func TestDataGetCommandSetResult_Success_FilterForSuccess2(t *testing.T) {
	type Fields struct {
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		Cnf                       *config.MockConfigProvider
	}
	type Args struct {
		Ctx context.Context
		Req *model.DataGetCommandSetResultReq
	}
	type test struct {
		Name    string
		Fields  Fields
		Args    Args
		WantRet *model.DataGetCommandSetResultResp
		WantErr bool
		Mocks   func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{{
		Name: "Success_FilterForFailed",
		Args: Args{
			Ctx: ctx,
			Req: &model.DataGetCommandSetResultReq{
				CommandSetId: atgconv.StringPtr("1592071154460282883"),
				FilterType:   model.FilterTypePtr(model.FilterType_Success),
			},
		},
		WantRet: &model.DataGetCommandSetResultResp{
			Results: []*model.ResultObject{
				{
					CommandStr:  "SHOW VARIABLES LIKE \"general_log%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(2),
					Rows: []*model.CommandRow{
						{Cells: []string{"general_log", "OFF"}},
						{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
					},
				},
			},
			FilterType: model.FilterTypePtr(model.FilterType_Success),
		},
		WantErr: false,
		Mocks: func() {
			mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
				StartTime: atgconv.Int64Ptr(int64(1668414317000)),
				EndTime:   atgconv.Int64Ptr(int64(1668414317000)),
				Progress:  atgconv.Int32Ptr(int32(100)),
				Content:   atgconv.StringPtr("SHOW VARIABLES LIKE \\\"general_log%\\\";show variables like \\\"log_bin%\\\";select version();"),
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr:   atgconv.StringPtr("select *from jq.ndb;"),
						CommandId:    atgconv.StringPtr("1592071154460282881"),
						State:        model.CommandStatePtr(model.CommandState_Terminated),
						Reason:       model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Failed),
						ReasonDetail: atgconv.StringPtr("Error 1146: Table 'jq.ndb' doesn't exist"),
						StartTime:    atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:      atgconv.Int64Ptr(int64(1668414317000)),
						Extra:        map[string]string{"CanEdit": "true", "Schema": "jq", "Table": "ndb"},
					},
					{
						CommandStr:   atgconv.StringPtr("select version();"),
						CommandId:    atgconv.StringPtr("1592071154460282882"),
						State:        model.CommandStatePtr(model.CommandState_Terminated),
						Reason:       model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Cancel),
						ReasonDetail: atgconv.StringPtr("取消执行"),
						StartTime:    atgconv.Int64Ptr(int64(0)),
						EndTime:      atgconv.Int64Ptr(int64(0)),
						Extra:        map[string]string{"CanEdit": "false"},
					},
				},
			}, error(nil)).Build()
		},
	}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000}).AnyTimes()
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Failed),
				ReasonDetail: "Error 1146: Table 'jq.ndb' doesn't exist",
				Content:      "select *from jq.ndb;",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(0))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Cancel),
				ReasonDetail: "取消执行",
				Content:      "select version();",
				StartTimeMS:  int64(0),
				EndTimeMS:    int64(0),
				ResultType:   &(rt3),
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(0))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			tt.Mocks()
			mockey.Mock(syscall.Connect).Return(fmt.Errorf("SU stops the Connection"))
			h := &DataGetCommandSetResultHandler{
				describeCommandSetHandler: tt.Fields.DescribeCommandSetHandler,
				describeCommandHandler:    tt.Fields.DescribeCommandHandler,
				cnf:                       tt.Fields.Cnf,
			}
			gotRet, err := h.DataGetCommandSetResult(tt.Args.Ctx, tt.Args.Req)
			if (err != nil) != tt.WantErr {
				convey.So((err != nil), convey.ShouldResemble, tt.WantErr)
			}
			if !strings.EqualFold(fmt.Sprintf("%v", gotRet), fmt.Sprintf("%v", tt.WantRet)) {
				convey.So(gotRet, convey.ShouldNotEqual, tt.WantRet)
			}
		})
	}
}

func TestDataGetCommandSetResult_Success_FilterForCancel(t *testing.T) {
	type Fields struct {
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		Cnf                       *config.MockConfigProvider
	}
	type Args struct {
		Ctx context.Context
		Req *model.DataGetCommandSetResultReq
	}
	type test struct {
		Name    string
		Fields  Fields
		Args    Args
		WantRet *model.DataGetCommandSetResultResp
		WantErr bool
		Mocks   func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{{
		Name: "Success_FilterForFailed",
		Args: Args{
			Ctx: ctx,
			Req: &model.DataGetCommandSetResultReq{
				CommandSetId: atgconv.StringPtr("1592071154460282883"),
				FilterType:   model.FilterTypePtr(model.FilterType_Cancel),
			},
		},
		WantRet: &model.DataGetCommandSetResultResp{
			Results: []*model.ResultObject{
				{
					CommandStr:   "select version();",
					State:        model.CommandResultState_Cancel,
					RowCount:     int32(0),
					ReasonDetail: "取消执行",
				},
			},
			FilterType: model.FilterTypePtr(model.FilterType_Cancel),
		},
		WantErr: false,
		Mocks: func() {
			mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
				StartTime: atgconv.Int64Ptr(int64(1668414317000)),
				EndTime:   atgconv.Int64Ptr(int64(1668414317000)),
				Progress:  atgconv.Int32Ptr(int32(100)),
				Content:   atgconv.StringPtr("SHOW VARIABLES LIKE \\\"general_log%\\\";show variables like \\\"log_bin%\\\";select version();"),
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr:   atgconv.StringPtr("select *from jq.ndb;"),
						CommandId:    atgconv.StringPtr("1592071154460282881"),
						State:        model.CommandStatePtr(model.CommandState_Terminated),
						Reason:       model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Failed),
						ReasonDetail: atgconv.StringPtr("Error 1146: Table 'jq.ndb' doesn't exist"),
						StartTime:    atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:      atgconv.Int64Ptr(int64(1668414317000)),
						Extra:        map[string]string{"CanEdit": "true", "Schema": "jq", "Table": "ndb"},
					},
					{
						CommandStr:   atgconv.StringPtr("select version();"),
						CommandId:    atgconv.StringPtr("1592071154460282882"),
						State:        model.CommandStatePtr(model.CommandState_Terminated),
						Reason:       model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Cancel),
						ReasonDetail: atgconv.StringPtr("取消执行"),
						StartTime:    atgconv.Int64Ptr(int64(0)),
						EndTime:      atgconv.Int64Ptr(int64(0)),
						Extra:        map[string]string{"CanEdit": "false"},
					},
				},
			}, error(nil)).Build()
		},
	}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000}).AnyTimes()
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Failed),
				ReasonDetail: "Error 1146: Table 'jq.ndb' doesn't exist",
				Content:      "select *from jq.ndb;",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(0))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Cancel),
				ReasonDetail: "取消执行",
				Content:      "select version();",
				StartTimeMS:  int64(0),
				EndTimeMS:    int64(0),
				ResultType:   &(rt3),
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(0))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			tt.Mocks()
			mockey.Mock(syscall.Connect).Return(fmt.Errorf("SU stops the Connection"))
			h := &DataGetCommandSetResultHandler{
				describeCommandSetHandler: tt.Fields.DescribeCommandSetHandler,
				describeCommandHandler:    tt.Fields.DescribeCommandHandler,
				cnf:                       tt.Fields.Cnf,
			}
			gotRet, err := h.DataGetCommandSetResult(tt.Args.Ctx, tt.Args.Req)
			if (err != nil) != tt.WantErr {
				convey.So((err != nil), convey.ShouldResemble, tt.WantErr)
			}
			if !strings.EqualFold(fmt.Sprintf("%v", gotRet), fmt.Sprintf("%v", tt.WantRet)) {
				convey.So(gotRet, convey.ShouldNotEqual, tt.WantRet)
			}
		})
	}
}

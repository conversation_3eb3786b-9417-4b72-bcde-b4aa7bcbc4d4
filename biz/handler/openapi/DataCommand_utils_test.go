package openapi

import (
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"errors"
	"fmt"
	"reflect"
	"syscall"
	"testing"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/handler/openapi/sqltask"
	config2 "code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"

	atgconv "code.byted.org/gopkg/lang/conv"
	config_biz "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	config "code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"github.com/bytedance/mockey"
)

// we create the test template for the runnable function
// please fill the testcase and mock function
func Test_changeDBIfNeed(t *testing.T) {
	type Args struct {
		Ctx             context.Context
		OldDB           string
		NewDB           string
		Ids             []string
		ChangeDBHandler *handler.ChangeDBHandler
	}
	type test struct {
		Name    string
		Args    Args
		WantErr bool
	}
	tests := []test{
		{
			Name: "Success",
			Args: Args{
				Ctx:   context.Background(),
				OldDB: "sys",
				NewDB: "yctest",
				Ids:   []string{"1590248381408612352", "1592419713638494208"},
			},
			WantErr: false,
		},
		{
			Name: "NoChange",
			Args: Args{
				Ctx:   context.Background(),
				OldDB: "sys",
				NewDB: "sys",
				Ids:   []string{"1590248381408612352", "1592419713638494208"},
			},
			WantErr: false,
		},
	}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			mockey.Mock((*handler.ChangeDBHandler).ChangeDB).Return(&model.ChangeDBResp{}, error(nil)).Build()
			if err := changeDBIfNeed(tt.Args.Ctx, tt.Args.OldDB, tt.Args.NewDB, tt.Args.Ids, tt.Args.ChangeDBHandler); (err != nil) != tt.WantErr {
				t.Errorf("%q. changeDBIfNeed() error = %v, wantErr %v", tt.Name, err, tt.WantErr)
			}
		})
	}
}

// we create the test template for the runnable function
// please fill the testcase and mock function
func Test_commandFileter(t *testing.T) {
	type Args struct {
		CommandsResult []*model.ResultObject
		FilterType     model.FilterType
	}
	type test struct {
		Name              string
		Args              Args
		WantFilterResults []*model.ResultObject
		WantErr           bool
	}
	tests := []test{
		{
			Name: "Sueccess",
			Args: Args{
				CommandsResult: []*model.ResultObject{
					{
						CommandStr: "show databases;",
						State:      model.CommandResultState_Success,
						RunTime:    int64(1668397550000),
					},
					{
						CommandStr: "show tables;",
						State:      model.CommandResultState_Success,
						RunTime:    int64(1668397550000),
					},
					{
						CommandStr: "select version();",
						State:      model.CommandResultState_Success,
						RunTime:    int64(1668397550000),
					},
				},
				FilterType: model.FilterType_Success,
			},
			WantFilterResults: []*model.ResultObject{
				{
					CommandStr: "show databases;",
					State:      model.CommandResultState_Success,
					RunTime:    int64(1668397550000),
				},
				{
					CommandStr: "show tables;",
					State:      model.CommandResultState_Success,
					RunTime:    int64(1668397550000),
				},
				{
					CommandStr: "select version();",
					State:      model.CommandResultState_Success,
					RunTime:    int64(1668397550000),
				},
			},
			WantErr: false,
		},
		{
			Name: "filter Cancel",
			Args: Args{
				CommandsResult: []*model.ResultObject{
					{
						CommandStr: "show databases;",
						State:      model.CommandResultState_Success,
						RunTime:    int64(1668397550000),
					},
					{
						CommandStr: "show tabl;",
						State:      model.CommandResultState_Failed,
						RunTime:    int64(1668397550000),
					},
					{
						CommandStr: "select version();",
						State:      model.CommandResultState_Cancel,
						RunTime:    int64(1668397550000),
					},
				},
				FilterType: model.FilterType_Cancel,
			},
			WantFilterResults: []*model.ResultObject{

				{
					CommandStr: "select version();",
					State:      model.CommandResultState_Cancel,
					RunTime:    int64(1668397550000),
				},
			},
			WantErr: false,
		},
		{
			Name: "filter Failed",
			Args: Args{
				CommandsResult: []*model.ResultObject{
					{
						CommandStr: "show databases;",
						State:      model.CommandResultState_Success,
						RunTime:    int64(1668397550000),
					},
					{
						CommandStr: "show tabl;",
						State:      model.CommandResultState_Failed,
						RunTime:    int64(1668397550000),
					},
					{
						CommandStr: "select version();",
						State:      model.CommandResultState_Cancel,
						RunTime:    int64(1668397550000),
					},
				},
				FilterType: model.FilterType_Failed,
			},
			WantFilterResults: []*model.ResultObject{
				{
					CommandStr: "show tabl;",
					State:      model.CommandResultState_Failed,
					RunTime:    int64(1668397550000),
				},
			},
			WantErr: false,
		},
		{
			Name: "filter Success",
			Args: Args{
				CommandsResult: []*model.ResultObject{
					{
						CommandStr: "show databases;",
						State:      model.CommandResultState_Success,
						RunTime:    int64(1668397550000),
					},
					{
						CommandStr: "show tabl;",
						State:      model.CommandResultState_Failed,
						RunTime:    int64(1668397550000),
					},
					{
						CommandStr: "select version();",
						State:      model.CommandResultState_Cancel,
						RunTime:    int64(1668397550000),
					},
				},
				FilterType: model.FilterType_Success,
			},
			WantFilterResults: []*model.ResultObject{
				{
					CommandStr: "show databases;",
					State:      model.CommandResultState_Success,
					RunTime:    int64(1668397550000),
				},
			},
			WantErr: false,
		},
	}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			// TODO: add the return of mock functions
			gotFilterResults, err := commandFileter(tt.Args.CommandsResult, tt.Args.FilterType)
			if (err != nil) != tt.WantErr {
				t.Errorf("%q. commandFileter() error = %v, wantErr %v", tt.Name, err, tt.WantErr)
			}
			if !reflect.DeepEqual(gotFilterResults, tt.WantFilterResults) {
				t.Errorf("%q. commandFileter() = %v, want %v", tt.Name, gotFilterResults, tt.WantFilterResults)
			}
		})
	}
}

// we create the test template for the runnable function
// please fill the testcase and mock function
func Test_collectResult_Success(t *testing.T) {
	type Fields struct {
		CnfProvider            config.ConfigProvider
		DescribeCommandHandler *handler.DescribeCommandHandler
		DescribeSQLTaskHandler *sqltask.DescribeSqlTaskHandler
		Cnf                    *config2.MockConfigProvider
	}

	type Args struct {
		Ctx      context.Context
		Commands []*model.CommandItem
	}
	type test struct {
		Name        string
		Args        Args
		Fields      Fields
		WantResults []*model.ResultObject
		WantErr     bool
		Mocks       func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{
		{
			Name: "Success",
			Args: Args{
				Ctx: ctx,
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("show variables like \"log_bin%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282881"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("select version();"),
						CommandId:  atgconv.StringPtr("1592071154460282882"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
				},
			},
			WantErr: false,
			WantResults: []*model.ResultObject{
				{
					CommandStr:  "SHOW VARIABLES LIKE \"general_log%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(2),
					Rows: []*model.CommandRow{
						{Cells: []string{"general_log", "OFF"}},
						{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
					},
				},
				{
					CommandStr:  "show variables like \"log_bin%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(5),
					Rows: []*model.CommandRow{
						{Cells: []string{"log_bin", "ON"}},
						{Cells: []string{"log_bin_basename", "/var/log/mysql/bin/binlog"}},
						{Cells: []string{"log_bin_index", "/var/log/mysql/bin/binlog.index"}},
						{Cells: []string{"log_bin_trust_function_creators", "ON"}},
						{Cells: []string{"log_bin_use_v1_row_events", "OFF"}},
					},
				},
				{
					CommandStr:  "select version();",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"version()"},
					RowCount:    int32(1),
					Rows: []*model.CommandRow{
						{Cells: []string{"5.7.32-log"}},
					},
				},
			},
		}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config2.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000})
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "show variables like \"log_bin%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin", "ON"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_basename", "/var/log/mysql/bin/binlog"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(2),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_index", "/var/log/mysql/bin/binlog.index"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(3),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_trust_function_creators", "ON"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(4),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_use_v1_row_events", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(5))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "select version();",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt3),
				Header:       []string{"version()"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282882",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"5.7.32-log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(1))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			if gotResults := collectResult(tt.Args.Ctx, tt.Fields.Cnf, tt.Fields.DescribeCommandHandler, tt.Fields.DescribeSQLTaskHandler, tt.Args.Commands); !reflect.DeepEqual(gotResults, tt.WantResults) {
				convey.So(gotResults, convey.ShouldNotEqual, tt.WantResults)
			}
		})
	}
}

func Test_collectResult_Cancel(t *testing.T) {
	type Fields struct {
		CnfProvider            config.ConfigProvider
		DescribeCommandHandler *handler.DescribeCommandHandler
		DescribeSQLTaskHandler *sqltask.DescribeSqlTaskHandler
		Cnf                    *config2.MockConfigProvider
	}

	type Args struct {
		Ctx      context.Context
		Commands []*model.CommandItem
	}
	type test struct {
		Name        string
		Args        Args
		Fields      Fields
		WantResults []*model.ResultObject
		WantErr     bool
		Mocks       func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{
		{
			Name: "Success",
			Args: Args{
				Ctx: ctx,
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("show variab like \"log_bin%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282881"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Failed),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
					},
					{
						CommandStr: atgconv.StringPtr("select version();"),
						CommandId:  atgconv.StringPtr("1592071154460282882"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Cancel),
						StartTime:  atgconv.Int64Ptr(int64(0)),
						EndTime:    atgconv.Int64Ptr(int64(0)),
					},
				},
			},
			WantErr: false,
			WantResults: []*model.ResultObject{
				{
					CommandStr:  "SHOW VARIABLES LIKE \"general_log%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(2),
					Rows: []*model.CommandRow{
						{Cells: []string{"general_log", "OFF"}},
						{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
					},
				},
				{
					CommandStr: "show variab like \"log_bin%\";",
					State:      model.CommandResultState_Failed,
					RunTime:    int64(1668414317000),
					RowCount:   int32(0),
				},
				{
					CommandStr: "select version();",
					State:      model.CommandResultState_Cancel,
					RunTime:    int64(0),
					RowCount:   int32(0),
				},
			},
		}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config2.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000})
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Failed),
				Content:      "show variab like \"log_bin%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(0))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Cancel),
				Content:      "select version();",
				StartTimeMS:  int64(0),
				EndTimeMS:    int64(0),
				ResultType:   &(rt3),
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(0))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			if gotResults := collectResult(tt.Args.Ctx, tt.Fields.Cnf, tt.Fields.DescribeCommandHandler, tt.Fields.DescribeSQLTaskHandler, tt.Args.Commands); !reflect.DeepEqual(gotResults, tt.WantResults) {
				convey.So(gotResults, convey.ShouldNotEqual, tt.WantResults)
			}
		})
	}
}

func Test_collectResult_Sleep(t *testing.T) {
	type Fields struct {
		CnfProvider            config.ConfigProvider
		DescribeCommandHandler *handler.DescribeCommandHandler
		DescribeSQLTaskHandler *sqltask.DescribeSqlTaskHandler
		Cnf                    *config2.MockConfigProvider
	}

	type Args struct {
		Ctx      context.Context
		Commands []*model.CommandItem
	}
	type test struct {
		Name        string
		Args        Args
		Fields      Fields
		WantResults []*model.ResultObject
		WantErr     bool
		Mocks       func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{
		{
			Name: "Success",
			Args: Args{
				Ctx: ctx,
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("select sleep(30);"),
						CommandId:  atgconv.StringPtr("1592071154460282881"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
					},
					{
						CommandStr: atgconv.StringPtr("select version();"),
						CommandId:  atgconv.StringPtr("1592071154460282882"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(0)),
						EndTime:    atgconv.Int64Ptr(int64(0)),
					},
				},
			},
			WantErr: false,
			WantResults: []*model.ResultObject{
				{
					CommandStr:  "SHOW VARIABLES LIKE \"general_log%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(2),
					Rows: []*model.CommandRow{
						{Cells: []string{"general_log", "OFF"}},
						{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
					},
				},
				{
					CommandStr: "select sleep(30);",
					State:      model.CommandResultState_Executing,
					RunTime:    int64(1668414317000),
					RowCount:   int32(0),
				},
				{
					CommandStr: "select version();",
					State:      model.CommandResultState_Pending,
					RunTime:    int64(0),
					RowCount:   int32(0),
				},
			},
		}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config2.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000})
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandExecuting,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "select sleep(30);",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(0),
				ResultType:   &(rt2),
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(0))
			rt3 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{
				ID:           "1592071154460282882",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandPending,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "select version();",
				StartTimeMS:  int64(0),
				EndTimeMS:    int64(0),
				ResultType:   &(rt3),
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282882", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282882").Return(int64(0))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			if gotResults := collectResult(tt.Args.Ctx, tt.Fields.Cnf, tt.Fields.DescribeCommandHandler, tt.Fields.DescribeSQLTaskHandler, tt.Args.Commands); !reflect.DeepEqual(gotResults, tt.WantResults) {
				convey.So(gotResults, convey.ShouldNotEqual, tt.WantResults)
			}
		})
	}
}

func Test_collectResult_CommandFailed(t *testing.T) {
	type Fields struct {
		CnfProvider            config.ConfigProvider
		DescribeCommandHandler *handler.DescribeCommandHandler
		DescribeSQLTaskHandler *sqltask.DescribeSqlTaskHandler
		Cnf                    *config2.MockConfigProvider
	}

	type Args struct {
		Ctx      context.Context
		Commands []*model.CommandItem
	}
	type test struct {
		Name        string
		Args        Args
		Fields      Fields
		WantResults []*model.ResultObject
		WantErr     bool
		Mocks       func()
	}
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{
		{
			Name: "Success",
			Args: Args{
				Ctx: ctx,
				Commands: []*model.CommandItem{
					{
						CommandStr: atgconv.StringPtr("SHOW VARIABLES LIKE \"general_log%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282880"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("show variables like \"log_bin%\";"),
						CommandId:  atgconv.StringPtr("1592071154460282881"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
					{
						CommandStr: atgconv.StringPtr("select version();"),
						CommandId:  atgconv.StringPtr("1592071154460282882"),
						State:      model.CommandStatePtr(model.CommandState_Terminated),
						Reason:     model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						StartTime:  atgconv.Int64Ptr(int64(1668414317000)),
						EndTime:    atgconv.Int64Ptr(int64(1668414317000)),
						Extra:      map[string]string{"CanEdit": "false"},
					},
				},
			},
			WantErr: false,
			WantResults: []*model.ResultObject{
				{
					CommandStr:  "SHOW VARIABLES LIKE \"general_log%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(2),
					Rows: []*model.CommandRow{
						{Cells: []string{"general_log", "OFF"}},
						{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
					},
				},
				{
					CommandStr:  "show variables like \"log_bin%\";",
					State:       model.CommandResultState_Success,
					RunTime:     int64(1668414317000),
					ColumnNames: []string{"Variable_name", "Value"},
					RowCount:    int32(5),
					Rows: []*model.CommandRow{
						{Cells: []string{"log_bin", "ON"}},
						{Cells: []string{"log_bin_basename", "/var/log/mysql/bin/binlog"}},
						{Cells: []string{"log_bin_index", "/var/log/mysql/bin/binlog.index"}},
						{Cells: []string{"log_bin_trust_function_creators", "ON"}},
						{Cells: []string{"log_bin_use_v1_row_events", "OFF"}},
					},
				},
				{
					CommandStr:   "select version();",
					State:        model.CommandResultState_Failed,
					ReasonDetail: consts.ErrorOf(model.ErrorCode_InternalError).Error(),
					RunTime:      int64(0),
					ColumnNames:  make([]string, 0),
					RowCount:     int32(0),
					Rows:         make([]*model.CommandRow, 0),
				},
			},
		}}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			CommandRepo := mocks.NewMockCommandRepo(ctrl)
			CommandResultRepo := mocks.NewMockCommandResultRepo(ctrl)
			actorClientMock := mocks.NewMockActorClient(ctrl)
			cnfMock := mocks.NewMockConfigProvider(ctrl)
			cnfMock.EXPECT().Get(gomock.Any()).Return(&config_biz.Config{}).AnyTimes()
			tt.Fields.Cnf = config2.NewMockConfigProvider(ctrl)
			tt.Fields.Cnf.EXPECT().Get(tt.Args.Ctx).Return(&config_biz.Config{MaxCommandResultCount: 3000})
			rt1 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282880").Return(&entity.Command{
				ID:           "1592071154460282880",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "SHOW VARIABLES LIKE \"general_log%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt1),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282880", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282880",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"general_log_file", "/var/lib/mysql/mysql-e88bf565ed5b-0.log"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282880").Return(int64(2))
			rt2 := entity.TableResult
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282881").Return(&entity.Command{
				ID:           "1592071154460282881",
				CommandSetID: "1592071154460282883",
				State:        entity.CommandTerminated,
				Reason:       entity.CommandTerminatedReasonPtr(entity.CommandTerminated_Success),
				Content:      "show variables like \"log_bin%\";",
				StartTimeMS:  int64(1668414317000),
				EndTimeMS:    int64(1668414317000),
				ResultType:   &(rt2),
				Header:       []string{"Variable_name", "Value"},
				Extra:        map[string]interface{}{"CanEdit": false},
				TenantID:     "1111",
			}, error(nil))
			CommandResultRepo.EXPECT().List(tt.Args.Ctx, "1592071154460282881", int64(0), int64(3000)).Return(
				[]*entity.CommandResult{
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(0),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin", "ON"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(1),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_basename", "/var/log/mysql/bin/binlog"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(2),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_index", "/var/log/mysql/bin/binlog.index"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(3),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_trust_function_creators", "ON"}},
						CreateTimeMS:   int64(1668414317000),
					},
					{
						SessionID:      "1592070828483162112",
						SessionExpired: false,
						ConnectionID:   "1592070828495749120",
						CommandID:      "1592071154460282881",
						ChunkOffset:    int64(4),
						Chunk:          &shared.CommandResultChunk_Row{Cells: []string{"log_bin_use_v1_row_events", "OFF"}},
						CreateTimeMS:   int64(1668414317000),
					},
				},
				error(nil),
			)
			CommandResultRepo.EXPECT().Count(tt.Args.Ctx, "1592071154460282881").Return(int64(5))
			CommandRepo.EXPECT().GetCommand(tt.Args.Ctx, "1592071154460282882").Return(&entity.Command{}, consts.ErrorOf(model.ErrorCode_InternalError))
			tt.Fields.DescribeCommandHandler = handler.NewDescribeCommandHandler(CommandRepo, CommandResultRepo, actorClientMock, cnfMock).DescribeCommandHandlerPtr
			if gotResults := collectResult(tt.Args.Ctx, tt.Fields.Cnf, tt.Fields.DescribeCommandHandler, tt.Fields.DescribeSQLTaskHandler, tt.Args.Commands); !reflect.DeepEqual(gotResults, tt.WantResults) {
				convey.So(gotResults, convey.ShouldNotEqual, tt.WantResults)
			}
		})
	}
}

func Test_currentDB(t *testing.T) {
	type Args struct {
		Ctx         context.Context
		ActorClient cli.ActorClient
		SessionId   string
		ConnId      string
	}
	type test struct {
		Name        string
		Args        Args
		Want        string
		WantErr     bool
		Mocks       func()
		expectError error
	}
	tests := []test{
		{

			Name: "failed",
			Args: Args{
				Ctx:       context.Background(),
				SessionId: "1590248381408612352",
				ConnId:    "1592419713638494208",
			},
			WantErr: true,
			Mocks: func() {
				mockey.Mock(handler.DescribeSessionHelper).Return(&model.DescribeSessionResp{
					Connections: []*model.ConnectionInfo{},
				}, consts.ErrorOf(model.ErrorCode_SessionNotExistError)).Build()
				mockey.Mock((*model.ConnectionInfo).GetCurrentDB).Return("").Build()
			},
			Want:        "",
			expectError: consts.ErrorOf(model.ErrorCode_SessionNotExistError),
		},
		{

			Name: "Success",
			Args: Args{
				Ctx:       context.Background(),
				SessionId: "1590248381408612352",
				ConnId:    "1592419713638494208",
			},
			WantErr: false,
			Mocks: func() {
				mockey.Mock(handler.DescribeSessionHelper).Return(&model.DescribeSessionResp{
					Connections: []*model.ConnectionInfo{
						{
							Id:        atgconv.StringPtr("1592419713638494208"),
							Name:      atgconv.StringPtr("default"),
							CurrentDB: atgconv.StringPtr("sys"),
						}},
				}, error(nil)).Build()
				mockey.Mock((*model.ConnectionInfo).GetCurrentDB).Return("sys").Build()
			},
			Want: "sys",
		},
	}
	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			tt.Mocks()
			mockey.Mock(syscall.Connect).Return(fmt.Errorf("SU stops the Connection"))
			got, err := currentDB(tt.Args.Ctx, tt.Args.ActorClient, tt.Args.SessionId, tt.Args.ConnId)
			if (err != nil) != tt.WantErr {
				convey.So(err, convey.ShouldEqual, tt.expectError)
			}
			if got != tt.Want {
				convey.So(got, convey.ShouldEqual, tt.Want)
			}
		})
	}
}

func Test_CommandType(t *testing.T) {
	ctx := context.Background()
	_, err := GetSqlType(ctx, "show VARIABLES;")
	if err != nil {
		t.Fatal("Test_CommandType failed!", err)
	}
}

func Test_collectResultFromStorage(t *testing.T) {
	type Args struct {
		Ctx          context.Context
		ActorClient  cli.ActorClient
		CommandSetID string
	}
	type test struct {
		Name        string
		Args        Args
		WantResults []*model.ResultObject
		WantErr     bool
	}

	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"
	tests := []test{
		{
			Name: "Success",
			Args: Args{
				Ctx:          ctx,
				CommandSetID: "1592071154460282883",
			},
			WantResults: []*model.ResultObject{
				{
					CommandStr:   "show databases;",
					State:        model.CommandResultState_Success,
					RunTime:      int64(1668397550000),
					ColumnNames:  []string{"Database"},
					RowCount:     int32(1),
					Rows:         []*model.CommandRow{{Cells: []string{"test_db"}}},
					RunningInfo:  &model.RunningInfo{},
					ReasonDetail: "",
				},
			},
			WantErr: false,
		},
		{
			Name: "Error_NoBuffer",
			Args: Args{
				Ctx:          ctx,
				CommandSetID: "1592071154460282883",
			},
			WantResults: []*model.ResultObject{
				{
					ReasonDetail: "no buffer found",
					CommandStr:   "",
					State:        model.CommandResultState_Success,
					RunTime:      0,
					ColumnNames:  []string{},
					RowCount:     0,
					Rows:         []*model.CommandRow{},
					RunningInfo:  &model.RunningInfo{},
				},
			},
			WantErr: false,
		},
	}

	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			actorClientMock := mocks.NewMockActorClient(ctrl)
			kindClientMock := mocks.NewMockKindClient(ctrl)

			if tt.Name == "Success" {
				actorClientMock.EXPECT().KindOf(consts.CommandSetResultBufferActorKind).Return(kindClientMock)
				kindClientMock.EXPECT().Call(tt.Args.Ctx, tt.Args.CommandSetID, &shared.FetchBufferedResults{}).Return([]*shared.ResultObject{
					{
						CommandStr:   "show databases;",
						State:        shared.CommandResultState_Success,
						RunTime:      int64(1668397550000),
						ColumnNames:  []string{"Database"},
						RowCount:     int32(1),
						Rows:         []*shared.CommandRow{{Cells: []string{"test_db"}}},
						RunningInfo:  &shared.RunningInfo{},
						ReasonDetail: "",
					},
				}, error(nil))
			} else {
				actorClientMock.EXPECT().KindOf(consts.CommandSetResultBufferActorKind).Return(kindClientMock)
				kindClientMock.EXPECT().Call(tt.Args.Ctx, tt.Args.CommandSetID, &shared.FetchBufferedResults{}).Return(&shared.ErrNoBuffer{
					ErrorMessage: "no buffer found",
				}, error(nil))
			}

			gotResults := collectResultFromStorage(tt.Args.Ctx, actorClientMock, tt.Args.CommandSetID)
			if !reflect.DeepEqual(gotResults, tt.WantResults) {
				convey.So(gotResults, convey.ShouldNotEqual, tt.WantResults)
			}
		})
	}
}

func Test_collectResultOnce(t *testing.T) {
	type Args struct {
		Ctx                       context.Context
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		CancelCommandSetHandler   *handler.CancelCommandSetHandler
		ActorClient               cli.ActorClient
		SqlTaskHandler            *sqltask.DescribeSqlTaskHandler
		Resp                      *model.ExecuteCommandSetResp
		ExecTimeout               int64
		Async                     bool
	}
	type test struct {
		Name        string
		Args        Args
		WantResults []*model.ResultObject
		Mocks       func()
	}

	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"

	tests := []test{
		{
			Name: "Sync_Success",
			Args: Args{
				Ctx: ctx,
				Resp: &model.ExecuteCommandSetResp{
					CommandSetId: utils.StringRef("1592071154460282883"),
					Commands: []*model.CommandItem{
						{
							CommandId:  utils.StringRef("cmd1"),
							CommandStr: utils.StringRef("show databases;"),
						},
					},
				},
				ExecTimeout: 30000,
				Async:       false,
			},
			WantResults: []*model.ResultObject{
				{
					CommandStr:   "show databases;",
					State:        model.CommandResultState_Success,
					RunTime:      int64(1668397550000),
					ColumnNames:  []string{"Database"},
					RowCount:     int32(1),
					Rows:         []*model.CommandRow{{Cells: []string{"test_db"}}},
					RunningInfo:  &model.RunningInfo{},
					ReasonDetail: "",
				},
			},
			Mocks: func() {
				mockey.Mock(collectSingleCommandResult).Return(&model.ResultObject{
					CommandStr:   "show databases;",
					State:        model.CommandResultState_Success,
					RunTime:      int64(1668397550000),
					ColumnNames:  []string{"Database"},
					RowCount:     int32(1),
					Rows:         []*model.CommandRow{{Cells: []string{"test_db"}}},
					RunningInfo:  &model.RunningInfo{},
					ReasonDetail: "",
				}, nil).Build()
			},
		},
		{
			Name: "Async_Success",
			Args: Args{
				Ctx: ctx,
				Resp: &model.ExecuteCommandSetResp{
					CommandSetId: utils.StringRef("1592071154460282883"),
				},
				ExecTimeout: 30000,
				Async:       true,
			},
			WantResults: []*model.ResultObject{
				{
					CommandStr:   "show databases;",
					State:        model.CommandResultState_Success,
					RunTime:      int64(1668397550000),
					ColumnNames:  []string{"Database"},
					RowCount:     int32(1),
					Rows:         []*model.CommandRow{{Cells: []string{"test_db"}}},
					RunningInfo:  &model.RunningInfo{},
					ReasonDetail: "",
				},
			},
			Mocks: func() {
				// Async模式不等待完成，直接返回
			},
		},
		{
			Name: "Sync_Timeout",
			Args: Args{
				Ctx: ctx,
				Resp: &model.ExecuteCommandSetResp{
					CommandSetId: utils.StringRef("1592071154460282883"),
				},
				ExecTimeout: 1000, // 短超时时间
				Async:       false,
			},
			WantResults: []*model.ResultObject{},
			Mocks: func() {
				mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
					StartTime: atgconv.Int64Ptr(int64(1668397550000)),
					Progress:  atgconv.Int32Ptr(int32(50)),
					Commands:  []*model.CommandItem{},
				}, error(nil)).Build()
			},
		},
	}

	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			actorClientMock := mocks.NewMockActorClient(ctrl)
			kindClientMock := mocks.NewMockKindClient(ctrl)

			tt.Args.ActorClient = actorClientMock
			tt.Args.DescribeCommandSetHandler = &handler.DescribeCommandSetHandler{}
			tt.Args.DescribeCommandHandler = &handler.DescribeCommandHandler{}
			tt.Args.CancelCommandSetHandler = &handler.CancelCommandSetHandler{}
			tt.Args.SqlTaskHandler = &sqltask.DescribeSqlTaskHandler{}

			actorClientMock.EXPECT().KindOf(consts.CommandSetResultBufferActorKind).Return(kindClientMock).AnyTimes()

			if tt.Args.Async {
				kindClientMock.EXPECT().Send(gomock.Any(), tt.Args.Resp.GetCommandSetId(), gomock.Any()).Return(nil).AnyTimes()
			}

			kindClientMock.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*shared.ResultObject{
				{
					CommandStr:   "show databases;",
					State:        shared.CommandResultState_Success,
					RunTime:      int64(1668397550000),
					ColumnNames:  []string{"Database"},
					RowCount:     int32(1),
					Rows:         []*shared.CommandRow{{Cells: []string{"test_db"}}},
					RunningInfo:  &shared.RunningInfo{},
					ReasonDetail: "",
				},
			}, error(nil)).AnyTimes()

			tt.Mocks()

			gotResults := collectResultOnce(
				tt.Args.Ctx,
				tt.Args.DescribeCommandSetHandler,
				tt.Args.DescribeCommandHandler,
				tt.Args.CancelCommandSetHandler,
				tt.Args.ActorClient,
				tt.Args.SqlTaskHandler,
				tt.Args.Resp,
				tt.Args.ExecTimeout,
				tt.Args.Async,
			)

			if tt.Name == "Async_Success" {
				convey.So(len(gotResults), convey.ShouldEqual, 0)
			} else if tt.Name == "Sync_Timeout" {
				convey.So(len(gotResults), convey.ShouldEqual, 0)
			} else {
				convey.So(len(gotResults), convey.ShouldBeGreaterThan, 0)
				if len(gotResults) > 0 && len(tt.WantResults) > 0 {
					convey.So(gotResults[0].CommandStr, convey.ShouldEqual, tt.WantResults[0].CommandStr)
					convey.So(gotResults[0].State, convey.ShouldEqual, tt.WantResults[0].State)
				}
			}
		})
	}
}

func Test_ConvertResultObjectToProto(t *testing.T) {
	type Args struct {
		M *model.ResultObject
	}
	type test struct {
		Name string
		Args Args
		Want *shared.ResultObject
	}

	tests := []test{
		{
			Name: "Nil_Input",
			Args: Args{M: nil},
			Want: nil,
		},
		{
			Name: "Success",
			Args: Args{
				M: &model.ResultObject{
					CommandStr:   "show databases;",
					State:        model.CommandResultState_Success,
					ReasonDetail: "",
					RunTime:      int64(1668397550000),
					ColumnNames:  []string{"Database"},
					RowCount:     int32(1),
					Rows: []*model.CommandRow{
						{Cells: []string{"test_db"}},
						nil, // Test nil row handling
					},
					RunningInfo: &model.RunningInfo{
						IsOnlineDDL: true,
						Progress:    "50",
						Info:        "running",
						TaskID:      "task123",
					},
				},
			},
			Want: &shared.ResultObject{
				CommandStr:   "show databases;",
				State:        shared.CommandResultState_Success,
				ReasonDetail: "",
				RunTime:      int64(1668397550000),
				ColumnNames:  []string{"Database"},
				RowCount:     int32(1),
				Rows: []*shared.CommandRow{
					{Cells: []string{"test_db"}},
					nil,
				},
				RunningInfo: &shared.RunningInfo{
					IsOnlineDDL: true,
					Progress:    "50",
					Info:        "running",
					TaskID:      "task123",
				},
			},
		},
	}

	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			got := ConvertResultObjectToProto(tt.Args.M)
			if !reflect.DeepEqual(got, tt.Want) {
				convey.So(got, convey.ShouldResemble, tt.Want)
			}
		})
	}
}

func Test_collectSingleCommandResult(t *testing.T) {
	type Args struct {
		Ctx          context.Context
		CommandSetId string
		Cmd          *model.CommandItem
	}
	type test struct {
		Name       string
		Args       Args
		WantResult *model.ResultObject
		WantErr    bool
		Mocks      func()
	}

	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"

	tests := []test{
		{
			Name: "UntilCommandFinished_Error",
			Args: Args{
				Ctx:          ctx,
				CommandSetId: "cs1",
				Cmd: &model.CommandItem{
					CommandId:  utils.StringRef("cmd1"),
					CommandStr: utils.StringRef("show databases;"),
				},
			},
			WantResult: &model.ResultObject{
				CommandStr:   "show databases;",
				State:        model.CommandResultState_Failed,
				ReasonDetail: "mock",
				RunTime:      0,
				ColumnNames:  []string{},
				RowCount:     0,
				Rows:         []*model.CommandRow{},
				RunningInfo: &model.RunningInfo{
					IsOnlineDDL: false,
					Progress:    "",
					Info:        "",
					TaskID:      "",
				},
			},
			WantErr: false,
			Mocks: func() {
				mockey.Mock(handler.UntilCommandFinished).Return(errors.New("command timeout")).Build()
				mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
					Commands: []*model.CommandItem{
						{
							CommandId: utils.StringRef("cmd1"),
							State:     model.CommandStatePtr(model.CommandState_Terminated),
							Reason:    model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						},
					},
				}, nil).Build()
				mockey.Mock((*handler.DescribeCommandHandler).DescribeCommand).Return(nil, errors.New("mock")).Build()
				mockey.Mock((*sqltask.DescribeSqlTaskHandler).DescribeSqlTask).Return(nil, errors.New("mock")).Build()
			},
		},
		{
			Name: "DescribeCommand_Error",
			Args: Args{
				Ctx:          ctx,
				CommandSetId: "cs1",
				Cmd: &model.CommandItem{
					CommandId:  utils.StringRef("cmd1"),
					CommandStr: utils.StringRef("show databases;"),
				},
			},
			WantResult: &model.ResultObject{
				CommandStr:   "show databases;",
				State:        model.CommandResultState_Failed,
				ReasonDetail: "describe command failed",
				RunTime:      0,
				ColumnNames:  []string{},
				RowCount:     0,
				Rows:         []*model.CommandRow{},
				RunningInfo: &model.RunningInfo{
					IsOnlineDDL: false,
					Progress:    "",
					Info:        "",
					TaskID:      "",
				},
			},
			WantErr: false,
			Mocks: func() {
				mockey.Mock(handler.UntilCommandFinished).Return(nil).Build()
				mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
					Commands: []*model.CommandItem{
						{
							CommandId: utils.StringRef("cmd1"),
							State:     model.CommandStatePtr(model.CommandState_Terminated),
							Reason:    model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						},
					},
				}, nil).Build()
				mockey.Mock((*handler.DescribeCommandHandler).DescribeCommand).Return(nil, errors.New("describe command failed")).Build()
				mockey.Mock((*sqltask.DescribeSqlTaskHandler).DescribeSqlTask).Return(nil, errors.New("mock")).Build()
			},
		},
		{
			Name: "Success_Pending",
			Args: Args{
				Ctx:          ctx,
				CommandSetId: "cs1",
				Cmd: &model.CommandItem{
					CommandId:  utils.StringRef("cmd1"),
					CommandStr: utils.StringRef("show databases;"),
				},
			},
			WantResult: &model.ResultObject{
				CommandStr:   "show databases;",
				State:        model.CommandResultState_Pending,
				ReasonDetail: "",
				RunTime:      int64(1668397550000),
				ColumnNames:  []string{"Database"},
				RowCount:     1,
				Rows:         []*model.CommandRow{{Cells: []string{"test_db"}}},
				RunningInfo: &model.RunningInfo{
					IsOnlineDDL: false,
					Progress:    "0",
					Info:        "",
					TaskID:      "",
				},
			},
			WantErr: false,
			Mocks: func() {
				mockey.Mock(handler.UntilCommandFinished).Return(nil).Build()
				mockey.Mock((*handler.DescribeCommandSetHandler).DescribeCommandSet).Return(&model.DescribeCommandSetResp{
					Commands: []*model.CommandItem{
						{
							CommandId: utils.StringRef("cmd1"),
							State:     model.CommandStatePtr(model.CommandState_Terminated),
							Reason:    model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success),
						},
					},
				}, nil).Build()
				mockey.Mock((*handler.DescribeCommandHandler).DescribeCommand).Return(&model.DescribeCommandResp{
					Command: &model.CommandItem{
						CommandStr:   utils.StringRef("show databases;"),
						State:        model.CommandStatePtr(model.CommandState_Pending),
						StartTime:    utils.Int64Ref(int64(1668397550000)),
						ReasonDetail: utils.StringRef(""),
					},
					Header: []string{"Database"},
					Total:  utils.Int32Ref(1),
					Rows:   []*model.CommandRow{{Cells: []string{"test_db"}}},
				}, nil).Build()
				mockey.Mock((*sqltask.DescribeSqlTaskHandler).DescribeSqlTask).Return(&model.DescribeSqlTaskResp{}, nil).Build()
			},
		},
	}

	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			tt.Mocks()
			gotResult, err := collectSingleCommandResult(
				tt.Args.Ctx,
				&handler.DescribeCommandSetHandler{},
				&handler.DescribeCommandHandler{},
				&sqltask.DescribeSqlTaskHandler{},
				tt.Args.CommandSetId,
				tt.Args.Cmd,
			)
			if (err != nil) != tt.WantErr {
				convey.So(err != nil, convey.ShouldEqual, tt.WantErr)
			}
			if !reflect.DeepEqual(gotResult, tt.WantResult) {
				convey.So(gotResult, convey.ShouldResemble, tt.WantResult)
			}
		})
	}
}

func Test_collectResultOnce_Timeout(t *testing.T) {
	type Args struct {
		Ctx                       context.Context
		DescribeCommandSetHandler *handler.DescribeCommandSetHandler
		DescribeCommandHandler    *handler.DescribeCommandHandler
		CancelCommandSetHandler   *handler.CancelCommandSetHandler
		ActorClient               cli.ActorClient
		SqlTaskHandler            *sqltask.DescribeSqlTaskHandler
		Resp                      *model.ExecuteCommandSetResp
		ExecTimeout               int64
		Async                     bool
	}
	type test struct {
		Name        string
		Args        Args
		WantResults []*model.ResultObject
		Mocks       func()
	}

	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "1111"

	tests := []test{
		{
			Name: "Sync_Timeout_CancelSuccess",
			Args: Args{
				Ctx: ctx,
				Resp: &model.ExecuteCommandSetResp{
					CommandSetId: utils.StringRef("1592071154460282883"),
					Commands: []*model.CommandItem{
						{
							CommandId:  utils.StringRef("cmd1"),
							CommandStr: utils.StringRef("show databases;"),
							StartTime:  utils.Int64Ref(int64(1668397550000)),
						},
					},
				},
				ExecTimeout: 1, // 1秒超时
				Async:       false,
			},
			WantResults: []*model.ResultObject{
				{
					CommandStr:   "show databases;",
					State:        model.CommandResultState_Failed,
					ReasonDetail: "user stop task, command timeout",
					RunTime:      int64(1668397550000),
					ColumnNames:  []string{},
					RowCount:     0,
					Rows:         []*model.CommandRow{},
					RunningInfo:  &model.RunningInfo{},
				},
			},
			Mocks: func() {
				mockey.Mock(collectSingleCommandResult).To(func(ctx context.Context, describeCommandSetHandler *handler.DescribeCommandSetHandler, describeCommandHandler *handler.DescribeCommandHandler, sqlTaskhder *sqltask.DescribeSqlTaskHandler, commandSetId string, cmd *model.CommandItem) (*model.ResultObject, error) {
					time.Sleep(2 * time.Second) // 模拟长时间执行
					return nil, nil
				}).Build()
				mockey.Mock((*handler.CancelCommandSetHandler).CancelCommandSet).Return(&model.CancelCommandSetResp{}, nil).Build()
			},
		},
		{
			Name: "Async_Timeout_SendError",
			Args: Args{
				Ctx: ctx,
				Resp: &model.ExecuteCommandSetResp{
					CommandSetId: utils.StringRef("1592071154460282883"),
					Commands: []*model.CommandItem{
						{
							CommandId:  utils.StringRef("cmd1"),
							CommandStr: utils.StringRef("show databases;"),
							StartTime:  utils.Int64Ref(int64(1668397550000)),
						},
					},
				},
				ExecTimeout: 1,
				Async:       true,
			},
			WantResults: []*model.ResultObject{
				{
					CommandStr:   "show databases;",
					State:        model.CommandResultState_Failed,
					ReasonDetail: "user stop task, command timeout",
					RunTime:      int64(1668397550000),
					ColumnNames:  []string{},
					RowCount:     0,
					Rows:         []*model.CommandRow{},
					RunningInfo:  &model.RunningInfo{},
				},
			},
			Mocks: func() {
				mockey.Mock(collectSingleCommandResult).To(func(ctx context.Context, describeCommandSetHandler *handler.DescribeCommandSetHandler, describeCommandHandler *handler.DescribeCommandHandler, sqlTaskhder *sqltask.DescribeSqlTaskHandler, commandSetId string, cmd *model.CommandItem) (*model.ResultObject, error) {
					time.Sleep(2 * time.Second)
					return nil, nil
				}).Build()
				mockey.Mock((*handler.CancelCommandSetHandler).CancelCommandSet).Return(&model.CancelCommandSetResp{}, nil).Build()
				mockey.Mock(ConvertResultObjectListToProto).Return([]*shared.ResultObject{}).Build()
			},
		},
	}

	for _, tt := range tests {
		mockey.PatchConvey(tt.Name, t, func() {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			actorClientMock := mocks.NewMockActorClient(ctrl)
			kindClientMock := mocks.NewMockKindClient(ctrl)

			tt.Args.ActorClient = actorClientMock
			tt.Args.DescribeCommandSetHandler = &handler.DescribeCommandSetHandler{}
			tt.Args.DescribeCommandHandler = &handler.DescribeCommandHandler{}
			tt.Args.CancelCommandSetHandler = &handler.CancelCommandSetHandler{}
			tt.Args.SqlTaskHandler = &sqltask.DescribeSqlTaskHandler{}

			if tt.Args.Async {
				actorClientMock.EXPECT().KindOf(consts.CommandSetResultBufferActorKind).Return(kindClientMock)
				kindClientMock.EXPECT().Send(gomock.Any(), tt.Args.Resp.GetCommandSetId(), gomock.Any()).Return(errors.New("send failed"))
			}

			tt.Mocks()

			gotResults := collectResultOnce(
				tt.Args.Ctx,
				tt.Args.DescribeCommandSetHandler,
				tt.Args.DescribeCommandHandler,
				tt.Args.CancelCommandSetHandler,
				tt.Args.ActorClient,
				tt.Args.SqlTaskHandler,
				tt.Args.Resp,
				tt.Args.ExecTimeout,
				tt.Args.Async,
			)

			convey.So(len(gotResults), convey.ShouldEqual, len(tt.WantResults))
			if len(gotResults) > 0 && len(tt.WantResults) > 0 {
				convey.So(gotResults[0].State, convey.ShouldEqual, tt.WantResults[0].State)
				convey.So(gotResults[0].ReasonDetail, convey.ShouldEqual, tt.WantResults[0].ReasonDetail)
			}
		})
	}
}

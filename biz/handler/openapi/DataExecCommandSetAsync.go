package openapi

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	dbw_utils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/persistence"
	"context"
)

func (h *DataExecCommandSetAsyncHandler) DataExecCommandSetAsync(ctx context.Context, req *model.DataExecCommandSetAsyncReq) (ret *model.DataExecCommandSetAsyncResp, err error) {
	ret = &model.DataExecCommandSetAsyncResp{}

	ids, err := h.codec.DecodeTo(req.GetSessionId())
	if err != nil || len(ids) != 2 {
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid SessionID "+req.GetSessionId())
	}
	if err = handler.ValidateSession(ctx, ids[0], h.peeker); err != nil {
		log.Info(ctx, "validate session %s %v", req.SessionId, err)
		return
	}

	db, e := currentDB(ctx, h.actorClient, ids[0], ids[1])
	if e != nil {
		log.Info(ctx, "fetch currentDB failed connectionId:%s", ids[1])
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid SessionID "+req.GetSessionId())
	}

	database := req.GetDatabaseName()
	if database == "" {
		database = db
	}

	if err = changeDBIfNeed(ctx, db, database, ids, h.changeDBHandler); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid Database "+database)
	}

	cmds := req.GetCommandSetContent()

	var source *shared.DataSource
	if source, err = handler.GetSourceBySessionId(ctx, ids[0], h.actorClient, h.peeker); err != nil {
		return
	}
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid SessionID "+req.GetSessionId())
	}
	rreq := &model.ExecuteCommandSetReq{
		SessionId:    ids[0],
		ConnectionId: ids[1],
	}
	switch source.Type {
	case shared.MySQL, shared.VeDBMySQL, shared.MySQLSharding:
		if req.GetAutoOnlineDDL() {
			commandSetStruct, _ := h.sp.PreCheck(ctx, source, cmds, model.SqlExecuteType_Task, req.CommandInfo)
			//当Force为false，如果语句中有onlineddl且rds不支持，则拒绝并提示用户sql中存在Online DDL不支持的DDL语句
			if !req.GetForce() {
				for _, cmd := range commandSetStruct {
					sqlType, _ := GetSqlType(ctx, cmd.Command)
					if cmd.CommandLanguageType != model.SqlLanguageType_OnlineDDL && sqlType == dbw_utils.DDL {
						if !IsDryRunReasonExist(cmd) {
							return nil, consts.ErrorOf(model.ErrorCode_NotSupportedByOnlineDDL)
						}
						return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, *cmd.SqlTaskConfig.ShardingConfig.DryRunResult_.Reason)
					}
				}
			}
			rreq.CommandSetStruct = commandSetStruct
		} else {
			rreq.CommandSetContent = &cmds
		}
	default:
		rreq.CommandSetContent = &cmds
	}

	log.Info(ctx, "DataExecCommandSetAsync req %v", utils.Show(rreq))

	resp, err := h.executeCommandHandler.ExecuteCommandSet(ctx, rreq)
	if err != nil {
		return nil, err
	}
	ret.CommandSetId = utils.StringRef(resp.GetCommandSetId())

	if dbw_utils.IsTenantEnabledFromCtx(ctx, h.cnf.Get(ctx).DBLessTenantIdList) {
		// 不落库时异步持续收集并且写一份数据给storageActor
		go collectResultOnce(ctx,
			h.dataExecCommandsHandler.describeCommandSetHandler,
			h.dataExecCommandsHandler.describeCommandHandler,
			h.dataExecCommandsHandler.cancelCommandSetHandler,
			h.actorClient,
			h.dataExecCommandsHandler.describeSqlTaskHandler,
			resp,
			60*60,
			true)
	}
	return ret, nil
}

type DataExecCommandSetAsyncHandler struct {
	dataExecCommandsHandler *DataExecCommandsHandler
	executeCommandHandler   *handler.ExecuteCommandSetHandler
	changeDBHandler         *handler.ChangeDBHandler
	codec                   CodecService
	actorClient             cli.ActorClient
	peeker                  persistence.ActorStoragePeeker
	sp                      parser.CommandParser
	cnf                     config.ConfigProvider
}

func NewDataExecCommandSetAsyncHandler(
	dataExecCommandsHandler *DataExecCommandsHandler,
	executeCommandHandler *handler.ExecuteCommandSetHandler,
	changeDBHandler *handler.ChangeDBHandler,
	codec CodecService,
	actorClient cli.ActorClient,
	peeker persistence.ActorStoragePeeker,
	sp parser.CommandParser,
	cnf config.ConfigProvider,
) handler.HandlerImplementationEnvolope {
	hder := &DataExecCommandSetAsyncHandler{
		dataExecCommandsHandler: dataExecCommandsHandler,
		executeCommandHandler:   executeCommandHandler,
		changeDBHandler:         changeDBHandler,
		codec:                   codec,
		actorClient:             actorClient,
		peeker:                  peeker,
		sp:                      sp,
		cnf:                     cnf,
	}
	return handler.NewHandler(hder.DataExecCommandSetAsync)
}

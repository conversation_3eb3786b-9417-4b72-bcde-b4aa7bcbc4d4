package openapi

import (
	bizUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"context"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/handler/openapi/sqltask"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/persistence"
)

func (h *DataExecCommandsHandler) DataExecCommands(ctx context.Context, req *model.DataExecCommandsReq) (ret *model.DataExecCommandsResp, err error) {
	ret = &model.DataExecCommandsResp{}
	ids, err := h.codec.DecodeTo(req.GetSessionId())
	if err != nil || len(ids) != 2 {
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid SessionID "+req.GetSessionId())
	}
	if err = handler.ValidateSession(ctx, ids[0], h.peeker); err != nil {
		log.Info(ctx, "validate session %s %v", req.SessionId, err)
		return
	}

	db, e := currentDB(ctx, h.actorClient, ids[0], ids[1])
	if e != nil {
		log.Info(ctx, "fetch currentDB failed connectionId:%s", ids[1])
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid SessionID "+req.GetSessionId())
	}

	database := req.GetDatabaseName()
	if database == "" {
		database = db
	}

	// ignore err
	defer changeDBIfNeed(ctx, database, db, ids, h.changeDBHandler)

	if err = changeDBIfNeed(ctx, db, database, ids, h.changeDBHandler); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, "Invalid Database "+database)
	}

	cmds := req.GetCommands()
	rreq := &model.ExecuteCommandSetReq{
		SessionId:         ids[0],
		ConnectionId:      ids[1],
		CommandSetContent: &cmds,
	}
	resp, err := h.executeCommandHandler.ExecuteCommandSet(ctx, rreq)
	if err != nil {
		return nil, err
	}

	// 5 minute
	var execTimeout int64 = 5 * 60
	if req.TimeOutSeconds != nil {
		execTimeout = *req.TimeOutSeconds
	}
	// 白名单用户走不落库方案
	if bizUtils.IsTenantEnabledFromCtx(ctx, h.cnf.Get(ctx).DBLessTenantIdList) {
		ret.Results = collectResultOnce(ctx,
			h.describeCommandSetHandler,
			h.describeCommandHandler,
			h.cancelCommandSetHandler,
			h.actorClient,
			h.describeSqlTaskHandler,
			resp,
			execTimeout,
			false)
		return ret, nil
	}

	e = handler.UntilCommandSetFinished(ctx, resp.GetCommandSetId(), h.describeCommandSetHandler, h.cancelCommandSetHandler, time.Duration(execTimeout)*time.Second)
	log.Info(ctx, "executing command finished err: %v", e)

	ret.Results = collectResult(ctx, h.cnf, h.describeCommandHandler, h.describeSqlTaskHandler, resp.GetCommands())

	return ret, nil
}

type DataExecCommandsHandler struct {
	executeCommandHandler     *handler.ExecuteCommandSetHandler
	describeCommandSetHandler *handler.DescribeCommandSetHandler
	describeCommandHandler    *handler.DescribeCommandHandler // get result
	cancelCommandSetHandler   *handler.CancelCommandSetHandler
	changeDBHandler           *handler.ChangeDBHandler
	describeSqlTaskHandler    *sqltask.DescribeSqlTaskHandler
	codec                     CodecService
	cnf                       config.ConfigProvider
	actorClient               cli.ActorClient
	peeker                    persistence.ActorStoragePeeker
}

type DataExecCommandsHandlerIn struct {
	dig.In
	ExecuteCommandHandler     *handler.ExecuteCommandSetHandler
	DescribeCommandSetHandler *handler.DescribeCommandSetHandler
	DescribeCommandHandler    *handler.DescribeCommandHandler
	CancelCommandSetHandler   *handler.CancelCommandSetHandler
	ChangeDBHandler           *handler.ChangeDBHandler
	DescribeSqlTaskHandler    *sqltask.DescribeSqlTaskHandler
	Codec                     CodecService
	Cnf                       config.ConfigProvider
	ActorClient               cli.ActorClient
	Peeker                    persistence.ActorStoragePeeker
}

type DataExecCommandsHandlerOut struct {
	dig.Out
	HandlerImplementationEnvolopeBody handler.HandlerImplementationEnvolope
	DataExecCommandsHandler           *DataExecCommandsHandler
}

func NewDataExecCommandsHandler(d DataExecCommandsHandlerIn) DataExecCommandsHandlerOut {
	hder := &DataExecCommandsHandler{
		executeCommandHandler:     d.ExecuteCommandHandler,
		describeCommandSetHandler: d.DescribeCommandSetHandler,
		describeCommandHandler:    d.DescribeCommandHandler,
		cancelCommandSetHandler:   d.CancelCommandSetHandler,
		changeDBHandler:           d.ChangeDBHandler,
		describeSqlTaskHandler:    d.DescribeSqlTaskHandler,
		codec:                     d.Codec,
		cnf:                       d.Cnf,
		actorClient:               d.ActorClient,
		peeker:                    d.Peeker,
	}
	return DataExecCommandsHandlerOut{HandlerImplementationEnvolopeBody: handler.NewHandler(hder.DataExecCommands), DataExecCommandsHandler: hder}
}

package openapi

//import (
//	"code.byted.org/infcs/dbw-mgr/biz/handler/dialog"
//	"code.byted.org/infcs/dbw-mgr/biz/location"
//	"context"
//	"github.com/qjpcpu/fp"
//	"go.uber.org/dig"
//
//	"code.byted.org/infcs/dbw-mgr/biz/consts"
//	"code.byted.org/infcs/dbw-mgr/biz/handler"
//	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
//)
//
//type NewDescribeCurrentDialogsHandlerIn struct {
//	dig.In
//	Location                   location.Location
//	DescribeDialogInfosHandler *dialog.DescribeDialogInfosHandler
//}
//
//func (h *DescribeCurrentDialogsApiHandler) DescribeCurrentDialogs(ctx context.Context, req *model.DescribeCurrentDialogsReq) (ret *model.DescribeCurrentDialogsResp, err error) {
//	ret = &model.DescribeCurrentDialogsResp{}
//	if err = h.checkReq(ctx, req); err != nil {
//		return
//	}
//	resp, err := h.handler.DescribeDialogInfos(ctx, &model.DescribeDialogInfosReq{
//		InstanceType: req.InstanceType,
//		InstanceId:   req.InstanceId,
//		QueryFilter:  req.GetQueryFilter(),
//		RegionId:     req.RegionId,
//	})
//	if err != nil {
//		return nil, err
//	}
//	ret.Total = resp.Details.Total
//	if err := fp.StreamOf(resp.Details.DialogDetails).Map(func(detail *model.DialogDetail) *model.CurrentDialogInfo {
//		item := &model.CurrentDialogInfo{
//			ProcessID:   &detail.ProcessID,
//			User:        &detail.User,
//			Host:        &detail.Host,
//			DB:          &detail.DB,
//			Command:     &detail.Command,
//			ExecuteTime: &detail.Time,
//			State:       &detail.State,
//			SqlText:     &detail.Info,
//			NodeId:      detail.NodeId,
//		}
//		if req.GetInstanceType() == model.DSType_ByteRDS {
//			item.PSM = detail.PSM
//		}
//		if req.GetInstanceType() == model.DSType_Mongo || req.GetInstanceType() == model.DSType_ByteDoc {
//			item.PlanSummary = detail.PlanSummary
//			item.Namespace = detail.Namespace
//			item.Desc = detail.Desc
//		}
//		return item
//	}).ToSlice(&ret.DialogDetails); err != nil {
//		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
//	}
//	return ret, nil
//}
//
//type DescribeCurrentDialogsApiHandler struct {
//	loc     location.Location
//	handler *dialog.DescribeDialogInfosHandler
//}
//
//func NewDescribeCurrentDialogsHandler(in NewDescribeCurrentDialogsHandlerIn) handler.HandlerImplementationEnvolope {
//	h := &DescribeCurrentDialogsApiHandler{
//		loc:     in.Location,
//		handler: in.DescribeDialogInfosHandler,
//	}
//	return handler.NewHandler(h.DescribeCurrentDialogs)
//}
//
//func (h *DescribeCurrentDialogsApiHandler) checkReq(ctx context.Context, req *model.DescribeCurrentDialogsReq) (err error) {
//	// check instance type
//	if req.GetInstanceType() != model.DSType_MySQL && req.GetInstanceType() != model.DSType_VeDBMySQL &&
//		req.GetInstanceType() != model.DSType_ByteRDS && req.GetInstanceType() != model.DSType_MySQLSharding &&
//		req.GetInstanceType() != model.DSType_Postgres && req.GetInstanceType() != model.DSType_ByteDoc &&
//		req.GetInstanceType() != model.DSType_Mongo && req.GetInstanceType() != model.DSType_MetaMySQL {
//		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
//	}
//	return nil
//}

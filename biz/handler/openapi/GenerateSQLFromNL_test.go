package openapi

import (
	"context"
	"errors"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/handler/dbgpt"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

var mockErr = errors.New("somehow err")

type GenerateSQLFromNLHandlerTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller

	actor                cli.ActorClient
	createSessionHandler *handler.CreateSessionHandler
	sqlAssistantHandler  *dbgpt.SqlAssistantHandler

	generateSQLFromNLHandler *GenerateSQLFromNLHandler
}

func (suite *GenerateSQLFromNLHandlerTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())

	suite.actor = mocks.NewMockActorClient(suite.ctrl)
	suite.createSessionHandler = &handler.CreateSessionHandler{}
	suite.sqlAssistantHandler = &dbgpt.SqlAssistantHandler{}

	suite.generateSQLFromNLHandler = &GenerateSQLFromNLHandler{
		actor:                suite.actor,
		createSessionHandler: suite.createSessionHandler,
		sqlAssistantHandler:  suite.sqlAssistantHandler,
	}
}

func (suite *GenerateSQLFromNLHandlerTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestGenerateSQLFromNLHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(GenerateSQLFromNLHandlerTestSuite))
}

func (suite *GenerateSQLFromNLHandlerTestSuite) TestNewGenerateSQLFromNLHandler() {
	_ = NewGenerateSQLFromNLHandler(GenerateSQLFromNLHandlerIn{
		Actor:                suite.actor,
		CreateSessionHandler: suite.createSessionHandler,
		SqlAssistantHandler:  suite.sqlAssistantHandler,
	})
}

func (suite *GenerateSQLFromNLHandlerTestSuite) TestGenerateSQLFromNL() {
	ctx := context.TODO()
	isStream := false
	req := &model.GenerateSQLFromNLReq{
		InstanceID:   "mysql-abc",
		InstanceType: "MySQL",
		Database:     "info",
		Query:        "NL2SQL",
		Tables:       nil,
		IsStream:     &isStream,
	}

	mockey.PatchConvey("failed to check the req", suite.T(), func() {
		resp, err := suite.generateSQLFromNLHandler.GenerateSQLFromNL(ctx, nil)
		suite.Nil(resp)
		suite.Error(err)
	})

	mockey.PatchConvey("failed to get or create the sessionID", suite.T(), func() {
		mockey.Mock((*GenerateSQLFromNLHandler).getOrCreateSessionID).Return("", mockErr).Build()
		resp, err := suite.generateSQLFromNLHandler.GenerateSQLFromNL(ctx, req)
		suite.Nil(resp)
		suite.Error(err)
	})

	mockey.PatchConvey("failed to request SqlAssistant", suite.T(), func() {
		mockey.Mock((*GenerateSQLFromNLHandler).getOrCreateSessionID).Return("123", nil).Build()
		mockey.Mock((*dbgpt.SqlAssistantHandler).SqlAssistant).Return(nil, mockErr).Build()
		resp, err := suite.generateSQLFromNLHandler.GenerateSQLFromNL(ctx, req)
		suite.Nil(resp)
		suite.Error(err)
	})

	mockey.PatchConvey("failed to request SqlAssistant", suite.T(), func() {
		mockey.Mock((*GenerateSQLFromNLHandler).getOrCreateSessionID).Return("123", nil).Build()
		mockey.Mock((*dbgpt.SqlAssistantHandler).SqlAssistant).Return(nil, nil).Build()
		resp, err := suite.generateSQLFromNLHandler.GenerateSQLFromNL(ctx, req)
		suite.Nil(resp)
		suite.Error(err)
	})

	mockey.PatchConvey("request SqlAssistant", suite.T(), func() {
		mockey.Mock((*GenerateSQLFromNLHandler).getOrCreateSessionID).Return("123", nil).Build()
		mockey.Mock((*dbgpt.SqlAssistantHandler).SqlAssistant).Return(&model.SqlAssistantResp{
			ChatId: "123",
			Query:  &model.Message{},
			Reply:  &model.Message{},
		}, nil).Build()
		resp, err := suite.generateSQLFromNLHandler.GenerateSQLFromNL(ctx, req)
		suite.NotNil(resp)
		suite.NoError(err)
	})

	mockey.PatchConvey("request SqlAssistant", suite.T(), func() {
		mockey.Mock((*GenerateSQLFromNLHandler).getOrCreateSessionID).Return("123", nil).Build()
		mockey.Mock((*dbgpt.SqlAssistantHandler).SqlAssistant).Return(&model.SqlAssistantResp{
			ChatId: "123",
			Query:  &model.Message{},
			Reply:  &model.Message{},
		}, nil).Build()
		isStream = true
		resp, err := suite.generateSQLFromNLHandler.GenerateSQLFromNL(ctx, req)
		suite.Nil(resp)
		suite.NoError(err)
	})
}

func (suite *GenerateSQLFromNLHandlerTestSuite) TestCheckReq() {
	ctx := context.TODO()
	req := &model.GenerateSQLFromNLReq{}

	mockey.PatchConvey("", suite.T(), func() {
		err := suite.generateSQLFromNLHandler.checkReq(ctx, nil)
		suite.Error(err)

		err = suite.generateSQLFromNLHandler.checkReq(ctx, req)
		suite.Error(err)

		req.InstanceID = "mysql-abc"
		err = suite.generateSQLFromNLHandler.checkReq(ctx, req)
		suite.Error(err)

		req.Database = "info"
		err = suite.generateSQLFromNLHandler.checkReq(ctx, req)
		suite.Error(err)

		req.Query = "NL2SQL"
		err = suite.generateSQLFromNLHandler.checkReq(ctx, req)
		suite.NoError(err)
	})
}

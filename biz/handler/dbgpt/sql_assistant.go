package dbgpt

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository/chat"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbgpt"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"github.com/gin-gonic/gin"
	"go.uber.org/dig"
)

func NewSqlAssistantHandler(in CreateSqlAssistantHandlerIn) CreateSqlAssistantHandlerOut {
	hder := &SqlAssistantHandler{
		chatRepo:    in.ChatRepo,
		messageRepo: in.MessageRepo,
		actor:       in.Actor,
		nl2sqlSvc:   in.NL2SqlSvc,
		IDSvc:       in.IDSvc,
	}

	return CreateSqlAssistantHandlerOut{
		HTTPHandlerImplementationEnvolope: handler.NewHTTPHandler(hder.SqlAssistant),
		Handler:                           hder,
	}
}

type CreateSqlAssistantHandlerIn struct {
	dig.In
	ChatRepo    chat.ChatRepo
	MessageRepo chat.MessageRepo
	Actor       cli.ActorClient
	NL2SqlSvc   dbgpt.NL2SQLService
	IDSvc       idgen.Service
}

type CreateSqlAssistantHandlerOut struct {
	dig.Out
	HTTPHandlerImplementationEnvolope handler.HTTPHandlerImplementationEnvolope
	Handler                           *SqlAssistantHandler
}

type SqlAssistantHandler struct {
	chatRepo    chat.ChatRepo
	messageRepo chat.MessageRepo
	actor       cli.ActorClient
	nl2sqlSvc   dbgpt.NL2SQLService
	IDSvc       idgen.Service
}

func (s *SqlAssistantHandler) SqlAssistant(ctx context.Context, req *model.SqlAssistantReq) (*model.SqlAssistantResp, error) {
	//if err := s.checkReq(ctx, req); err != nil {
	//	return nil, err
	//}

	chatId, _ := strconv.ParseInt(req.SessionId, 10, 64)
	if req.GetIsStream() {
		sqlReq := &shared.DescribeDBTablesReq{
			Database: req.Database,
			Tables:   req.Tables,
		}
		log.Info(ctx, "start request actor got tables")
		resp, err := s.actor.KindOf(consts.SqlAssistantActorKind).Call(ctx, req.SessionId, sqlReq)
		if err != nil {
			log.Warn(ctx, "request actor error", err)
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		tables, _ := resp.(*shared.DescribeDBTablesResp)
		dbMetadata := &dbgpt.DBMetadata{
			DBID: req.Database,
		}
		for _, table := range tables.Tables {
			t := &dbgpt.Table{
				Name:        table.Name,
				Description: table.Description,
			}
			t.Columns = make([]dbgpt.Column, 0)
			for _, column := range table.Columns {
				t.Columns = append(t.Columns, dbgpt.Column{
					Name:        column.Name,
					Type:        column.Type,
					Description: column.Description,
					IsPrimary:   column.IsPrimary,
				})
			}
			dbMetadata.Tables = append(dbMetadata.Tables, t)
		}
		err = s.handleStream(ctx, req, chatId, dbMetadata)
		if err != nil {
			log.Warn(ctx, "handle stream error: %s", err)
			return nil, err
		}
		return nil, err
	}

	sqlReq := &shared.SqlAssistantReq{
		Query:           req.Query,
		Database:        req.Database,
		Tables:          req.Tables,
		Action:          conv.ToShardAction(req.Action),
		WithExplanation: req.GetWithExplanation(),
	}
	resp, err := s.actor.KindOf(consts.SqlAssistantActorKind).Call(ctx, req.SessionId, sqlReq)
	if err != nil {
		log.Warn(ctx, "request actor error", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	answer, ok := resp.(*shared.SqlAssistantResp)
	if ok {
		if len(answer.Answer) == 0 {
			log.Warn(ctx, "request actor got empty answer")
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		chatId, err := strconv.ParseInt(req.SessionId, 10, 64)
		queryMessage, answerMessage, err := s.messageRepo.Create(ctx, chatId, req.Query, answer.Answer)
		if err != nil {
			log.Info(ctx, "[dbgpt] chat error: ", err)
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		return &model.SqlAssistantResp{
			ChatId: req.SessionId,
			Query:  conv.MessageEntityToModel(queryMessage),
			Reply:  conv.MessageEntityToModel(answerMessage),
		}, nil
	}

	return nil, consts.ErrorOf(model.ErrorCode_InternalError)
}

func (s *SqlAssistantHandler) checkReq(ctx context.Context, req *model.SqlAssistantReq) error {
	// empty message
	checkSessionReq := &shared.CheckSession{}
	resp, err := s.actor.KindOf(consts.SessionActorKind).Call(ctx, req.SessionId, checkSessionReq)
	if err != nil {
		log.Info(ctx, "check session error: %s", err)
		return err
	}
	switch rsp := resp.(type) {
	case *shared.SessionAccessDenied:
		return consts.ErrorOf(model.ErrorCode_ChatNotFoundError)
	case *shared.CheckSessionSuccess:
		if (rsp.Source.Type != shared.MySQL) && (rsp.Source.Type != shared.VeDBMySQL) {
			return consts.ErrorOf(model.ErrorCode_InstanceTypeNotSupport)
		}
	}
	return nil
}

func (s *SqlAssistantHandler) handleStream(ctx context.Context, req *model.SqlAssistantReq, chatId int64, dbMeta *dbgpt.DBMetadata) error {
	c := ctx.(*gin.Context)
	c.Header("content-type", "Text/event-stream")
	dataCh := make(chan dbgpt.StreamData, 10)

	sqlReq := dbgpt.NewSQLChatRequest(chatId, 1, req.Query, dbMeta, true, req.GetWithExplanation())
	err := s.nl2sqlSvc.ChatStream(ctx, sqlReq, dataCh)
	if err != nil {
		return err
	}

	answerMessageId, err := s.IDSvc.NextID(ctx)
	if err != nil {
		return nil
	}

	c.SSEvent("start", "start")
	var index int32
	var answer string
	//var preIsPrefix bool
	for data := range dataCh {
		log.Debug(ctx, "nl2sql chat got data: %s", data)
		if data.Err != nil {
			log.Warn(ctx, "nl2sql chat error: %s", data.Err)
			c.SSEvent("error", data.Err)
			c.Writer.Flush()
			return nil
		}
		//if !req.GetWithExplanation() {
		//	if data.Data == "```" && index == 0 {
		//		preIsPrefix = true
		//		continue
		//	}
		//	if preIsPrefix && data.Data == "sql" {
		//		preIsPrefix = false
		//		continue
		//	}
		//	if data.Data == "```" && !preIsPrefix {
		//		continue
		//	}
		//}
		answer += data.Data
		jsonData, _ := json.Marshal(model.SqlAssistantStreamResp{
			ChatId:    req.SessionId,
			MessageId: strconv.FormatInt(answerMessageId, 10),
			Content:   data.Data,
			Index:     index,
		})
		//c.Writer.(http.Flusher).Flush()
		c.SSEvent("message", string(jsonData))
		c.Writer.Flush()
		index++
		time.Sleep(time.Millisecond * 10)
	}

	c.SSEvent("end", "end")
	if req.GetWithExplanation() {
		_, _, err := s.messageRepo.CreateMessage(ctx, chatId, answerMessageId, req.Query, answer)
		if err != nil {
			log.Info(ctx, "create chat record error: %s", err)
		}
	}

	return nil
}

package sql_ccl

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"go.uber.org/dig"
)

type DescribeSqlFingerPrintHandler struct {
	dsSvc datasource.DataSourceService
}

type DescribeSqlFingerPrintHandlerIn struct {
	dig.In
	DsSvc datasource.DataSourceService
}

func NewDescribeSqlFingerPrintHandler(in DescribeSqlFingerPrintHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeSqlFingerPrintHandler{
		dsSvc: in.DsSvc,
	}
	return handler.NewHandler(h.DescribeSqlFingerPrint)
}

func (h *DescribeSqlFingerPrintHandler) DescribeSqlFingerPrint(ctx context.Context, req *model.DescribeSqlFingerPrintReq) (*model.DescribeSqlFingerPrintResp, error) {
	// check req params
	if err := req.IsValid(); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// sql不允许为空
	if req.GetSql() == "" {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// check instance type
	if req.GetInstanceType() != model.DSType_MySQL && req.GetInstanceType() != model.DSType_VeDBMySQL &&
		req.GetInstanceType() != model.DSType_ByteRDS && req.GetInstanceType() != model.DSType_MySQLSharding {
		return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	resp, err := h.dsSvc.DescribeSqlFingerPrintOrKeywords(ctx, &datasource.DescribeSqlFingerPrintOrKeywordsReq{
		Type:       conv.ToSharedType(req.GetInstanceType()),
		SqlText:    req.GetSql(),
		ObjectType: "Fingerprint",
	})
	if err != nil {
		return nil, err
	}
	ret := &model.DescribeSqlFingerPrintResp{
		SqlFingerPrint: resp.FingerPrint,
	}
	return ret, nil
}

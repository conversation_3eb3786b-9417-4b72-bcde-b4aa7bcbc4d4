package sql_ccl

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"go.uber.org/dig"
	"strconv"
	"time"
)

type ModifySqlConcurrencyControlRuleHandler struct {
	dsSvc                datasource.DataSourceService
	cclEventDal          dal.SqlCCLEventDAL
	ccLRuleDal           dal.SqlCCLRulesDAL
	idSvc                idgen.Service
	operateRecordService operate_record.OperateRecordService
}

type ModifySqlConcurrencyControlRuleHandlerIn struct {
	dig.In
	DsSvc                datasource.DataSourceService
	CCLEventDal          dal.SqlCCLEventDAL
	CCLRuleDal           dal.SqlCCLRulesDAL
	IdSvc                idgen.Service
	OperateRecordService operate_record.OperateRecordService
}

func NewModifySqlConcurrencyControlRuleHandler(in ModifySqlConcurrencyControlRuleHandlerIn) handler.HandlerImplementationEnvolope {
	h := &ModifySqlConcurrencyControlRuleHandler{
		dsSvc:                in.DsSvc,
		cclEventDal:          in.CCLEventDal,
		ccLRuleDal:           in.CCLRuleDal,
		idSvc:                in.IdSvc,
		operateRecordService: in.OperateRecordService,
	}
	return handler.NewHandler(h.ModifySqlConcurrencyControlRule)
}

func (h *ModifySqlConcurrencyControlRuleHandler) ModifySqlConcurrencyControlRule(ctx context.Context, req *model.ModifySqlConcurrencyControlRuleReq) (ret *model.ModifySqlConcurrencyControlRuleResp, err error) {
	// DAS操作审计
	var ruleDetail *model.RuleDetail
	defer func() {
		rule, _ := json.Marshal(ruleDetail)
		operationRecord := &entity.OperationRecord{
			OperationType: model.DasOperationCategory_CCL.String(),
			InstanceId:    req.GetInstanceId(),
			InstanceType:  req.GetInstanceType().String(),
			TriggerType:   model.TriggerType_Manual.String(),
			Action:        model.DasAction_ModifyThrottleRule.String(),
			Extra:         string(rule),
			TaskId:        req.GetTaskId(),
			TenantId:      fwctx.GetTenantID(ctx),
			UserId:        fwctx.GetUserID(ctx),
		}
		if err != nil {
			operationRecord.Status = model.OpsTaskState_RunningFailed.String()
			log.Warn(ctx, "ModifySqlConcurrencyControlRule error:%+v", err)
		} else {
			operationRecord.Status = model.OpsTaskState_Done.String()
		}
		err := h.operateRecordService.CreateDasRecord(ctx, operationRecord)
		if err != nil {
			log.Warn(ctx, "create das record failed %+v", err)
		}
	}()
	ruleDetail = &model.RuleDetail{
		ThrottleMode:      utils.StringRef(req.GetThrottleMode().String()),
		ThrottleThreshold: utils.Int32Ref(req.GetThreshold()),
		TaskId:            req.GetTaskId(),
	}
	// check req params
	if err := req.IsValid(); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if !h.dsSvc.IsMyOwnInstance(ctx, req.GetInstanceId(), conv.ToSharedType(req.GetInstanceType())) {
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.dsSvc.CheckInstanceState(ctx, req.GetInstanceId(), conv.ToSharedType(req.GetInstanceType()), true); err != nil {
		return nil, err
	}
	// threshold
	if req.GetThreshold() < 1 || req.GetThreshold() > 99999 {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// check instance type
	if req.GetInstanceType() != model.DSType_MySQL && req.GetInstanceType() != model.DSType_VeDBMySQL && req.GetInstanceType() != model.DSType_MySQLSharding {
		return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	// 查元数据库获取
	taskId, err := strconv.ParseInt(req.GetTaskId(), 10, 64)
	if err != nil {
		log.Warn(ctx, "req.TaskId format is invalid %s", req.GetTaskId())
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	ruleInfo, err := h.ccLRuleDal.Get(ctx, taskId)
	if err != nil {
		log.Warn(ctx, "Get CCLRule from metadata failed:%s", utils.Show(err))
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	timestampMS := time.Now().UnixMilli()
	// 记录事件
	eventId, err := h.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "Generate CCLEventId failed %+v", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	dalEvent := &dao.SqlCCLEvent{
		ID:         eventId,
		CreatedAt:  timestampMS,
		TenantID:   fwctx.GetTenantID(ctx),
		InstanceID: req.InstanceId,
		UpdatedAt:  timestampMS,
		Operator:   fwctx.GetUserID(ctx),
		EventType:  int8(model.CCLEventType_Modify),
	}
	if err := h.cclEventDal.Create(ctx, dalEvent); err != nil {
		log.Warn(ctx, "Create SqlCCLEvent record on metaDB failed%+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	switch ruleInfo.ThrottleMode {
	case model.ThrottleMode_DBThrottle.String():
		// 暂时不支持DB侧的限流阈值修改
		log.Warn(ctx, "Only support Proxy Throttle Mode")
		return nil, consts.ErrorOf(model.ErrorCode_NotSupportThrottleMode)
	case model.ThrottleMode_ProxyThrottle.String():
		// 仅运行中的限流规则可热修改
		if ruleInfo.State == int8(model.RuleState_ACTIVE) {
			_, err = h.dsSvc.ModifyProxyThrottleRule(ctx, &datasource.ModifyProxyThrottleRuleReq{
				Type:       conv.ToSharedType(req.GetInstanceType()),
				InstanceId: req.InstanceId,
				Action:     model.CCLEventType_Modify.String(),
				ProxyThrottleRule: &datasource.ProxyThrottleRuleInfo{
					ThrottleType:        ruleInfo.ThrottleType,
					ThrottleThreshold:   req.Threshold,
					ThrottleSqlText:     ruleInfo.ThrottleSqlText,
					ThrottleDB:          ruleInfo.ThrottleDB,
					ThrottleHost:        ruleInfo.ThrottleHost,
					ThrottleFingerPrint: ruleInfo.ThrottleFingerPrint,
					Keywords:            ruleInfo.Keywords,
					EndpointID:          ruleInfo.EndpointID,
					EndpointType:        ruleInfo.EndpointType,
					ThrottleTarget:      ruleInfo.ThrottleTarget,
					GroupIds:            ruleInfo.ThrottleGroupIds,
				},
			})
			if err != nil {
				return nil, err
			}
		}
		if err := h.ccLRuleDal.UpdateThresholdByID(ctx, ruleInfo.ID, req.Threshold); err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
		}
	default:
		return nil, consts.ErrorOf(model.ErrorCode_NotSupportThrottleMode)
	}
	return &model.ModifySqlConcurrencyControlRuleResp{}, nil
}

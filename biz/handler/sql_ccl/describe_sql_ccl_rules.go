package sql_ccl

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	bizConv "code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

type DescribeSqlConcurrencyControlRulesHandler struct {
	ccLRuleDal dal.SqlCCLRulesDAL
	dsSvc      datasource.DataSourceService
}

type DescribeSqlConcurrencyControlRulesHandlerIn struct {
	dig.In
	CCLRuleDal dal.SqlCCLRulesDAL
	DsSvc      datasource.DataSourceService
}

func NewDescribeSqlConcurrencyControlRulesHandler(in DescribeSqlConcurrencyControlRulesHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeSqlConcurrencyControlRulesHandler{
		ccLRuleDal: in.CCLRuleDal,
		dsSvc:      in.DsSvc,
	}
	return handler.NewHandler(h.DescribeSqlConcurrencyControlRules)
}

func (h *DescribeSqlConcurrencyControlRulesHandler) DescribeSqlConcurrencyControlRules(ctx context.Context, req *model.DescribeSqlConcurrencyControlRulesReq) (*model.DescribeSqlConcurrencyControlRulesResp, error) {
	// check req params
	if err := req.IsValid(); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if !h.dsSvc.IsMyOwnInstance(ctx, req.GetInstanceId(), conv.ToSharedType(req.GetInstanceType())) {
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// check instance type
	if req.GetInstanceType() != model.DSType_MySQL && req.GetInstanceType() != model.DSType_VeDBMySQL && req.GetInstanceType() != model.DSType_ByteRDS &&
		req.GetInstanceType() != model.DSType_MySQLSharding && req.GetInstanceType() != model.DSType_MetaMySQL {
		return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	switch req.GetInstanceType() {
	case model.DSType_MySQL:
		checkPassed, err := rdsVersionChecker(ctx, req.GetInstanceId(), req.GetInstanceType(), h.dsSvc)
		if err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceVersion)
		}
		if !checkPassed {
			return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceVersion)
		}
	case model.DSType_MetaMySQL:
		checkPassed, err := rdsVersionChecker(ctx, req.GetInstanceId(), req.GetInstanceType(), h.dsSvc)
		if err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceVersion)
		}
		if !checkPassed {
			return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceVersion)
		}
	default:
		//skip
	}
	var (
		pageNumber int32
		pageSize   int32
		linkType   string
		sortBy     string
		orderBy    string
		shardList  []*datasource.ShardInfo
	)
	if !req.IsSetLinkType() {
		linkType = model.LinkType_Volc.String() // 默认是火山
	} else {
		linkType = req.GetLinkType().String()
	}
	//PageNumber默认为1
	if !req.IsSetPageNumber() {
		pageNumber = 1
	} else {
		pageNumber = req.GetPageNumber()
	}
	//PageSize默认为10
	if !req.IsSetPageSize() {
		pageSize = 10
	} else {
		pageSize = req.GetPageSize()
	}
	//默认为升序
	if !req.IsSetSortBy() {
		sortBy = "ASC"
	} else {
		sortBy = handler.SortByModelToString(req.GetSortBy())
	}
	//默认按创建时间排序
	if !req.IsSetOrderBy() {
		orderBy = "created_at"
	} else {
		orderBy = handler.OrderByForDBRuleModelToString(req.GetOrderBy())
	}
	limit := pageSize
	offset := (pageNumber - 1) * pageSize
	queryFilter := h.getFilterCondition(ctx, req)
	throttleRuleInfos, err := h.ccLRuleDal.List(ctx, req.GetInstanceId(), queryFilter, sortBy, orderBy, limit, offset, false)
	if err != nil {
		log.Warn(ctx, "Get SqlCCLEvent lists failed:%+v", err)
		e := consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
		return nil, e
	}
	if req.GetInstanceType() == model.DSType_MySQLSharding {
		shardInfoReq := &datasource.DescribeDBInstanceShardInfosReq{
			InstanceId: req.GetInstanceId(),
			Type:       bizConv.ToSharedType(req.GetInstanceType()),
		}
		shardInfos, err := h.dsSvc.DescribeDBInstanceShardInfos(ctx, shardInfoReq)
		if err != nil {
			log.Warn(ctx, "Get instance %s Shards failed  %+v", req.GetInstanceId(), err)
			return nil, err
		}
		shardList = shardInfos.Shards
	}
	// 查询限流开关状态
	resp, err := h.dsSvc.DescribeSQLCCLConfig(ctx, &datasource.DescribeSQLCCLConfigReq{
		InstanceId: req.GetInstanceId(),
		Type:       bizConv.ToSharedType(req.GetInstanceType()),
	})
	if err != nil {
		log.Warn(ctx, "Get sql Concurrency status failed %v", err)
		return nil, err
	}
	ret := &model.DescribeSqlConcurrencyControlRulesResp{}
	// 仅限流开关打开时可调用（火山rds、vedb支持）后续proxy支持限流拒绝数时再调整
	if resp.SQLConcurrencyControlStatus && linkType == model.LinkType_Volc.String() {
		// 运行中时调rds接口获取实时拒绝数
		cclShowReq := &datasource.ListSQLCCLRulesReq{
			InstanceId: req.GetInstanceId(),
			Type:       bizConv.ToSharedType(req.GetInstanceType()),
		}
		cclRules, err := h.dsSvc.ListSQLCCLRules(ctx, cclShowReq)
		if err != nil {
			log.Warn(ctx, "Get instance %s CCLRules failed  %+v", req.GetInstanceId(), err)
			return nil, err
		}
		ret.RuleDetails = SqlCclInfosEntityToModels(throttleRuleInfos.SqlCCLRules, cclRules.CCLRules, shardList)
	} else {
		ret.RuleDetails = SqlCclInfosEntityToModels(throttleRuleInfos.SqlCCLRules, []*datasource.CCLRuleInfo{}, shardList)
	}
	ret.Total = throttleRuleInfos.Total
	return ret, nil
}

func (h *DescribeSqlConcurrencyControlRulesHandler) getFilterCondition(ctx context.Context, req *model.DescribeSqlConcurrencyControlRulesReq) *dal.SqlCCLQueryFilter {
	queryFilter := &dal.SqlCCLQueryFilter{}
	if req.IsSetSearchParam() {
		if req.SearchParam.IsSetSqlType() {
			queryFilter.SqlType = req.GetSearchParam().GetSqlType()
		}
		if req.SearchParam.IsSetTaskId() {
			queryFilter.TaskIds = []string{req.SearchParam.GetTaskId()}
		}
		if req.SearchParam.IsSetRuleState() {
			queryFilter.RuleState = req.SearchParam.GetRuleState()
		}
		if req.SearchParam.IsSetThrottleType() {
			queryFilter.ThrottleType = req.SearchParam.GetThrottleType().String()
		}
		if req.SearchParam.IsSetThrottleTarget() {
			queryFilter.ThrottleTarget = req.SearchParam.GetThrottleTarget().String()
		}
		if req.SearchParam.IsSetEndpointType() {
			queryFilter.EndpointType = req.SearchParam.GetEndpointType().String()
		}
		return queryFilter
	} else {
		return nil
	}
}

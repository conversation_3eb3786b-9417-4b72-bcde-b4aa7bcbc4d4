package sql_ccl

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	bizConv "code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/dig"
	"strconv"
	"time"
)

type RestartSqlConcurrencyControlRuleHandler struct {
	actorClient          cli.ActorClient
	cclEventDal          dal.SqlCCLEventDAL
	ccLRuleDal           dal.SqlCCLRulesDAL
	idSvc                idgen.Service
	dsSvc                datasource.DataSourceService
	operateRecordService operate_record.OperateRecordService
}

type RestartSqlConcurrencyControlRuleHandlerIn struct {
	dig.In
	ActorClient          cli.ActorClient
	CCLEventDal          dal.SqlCCLEventDAL
	CCLRuleDal           dal.SqlCCLRulesDAL
	IDSvc                idgen.Service
	DsSvc                datasource.DataSourceService
	OperateRecordService operate_record.OperateRecordService
}

func NewRestartSqlConcurrencyControlRuleHandler(in RestartSqlConcurrencyControlRuleHandlerIn) handler.HandlerImplementationEnvolope {
	h := &RestartSqlConcurrencyControlRuleHandler{
		actorClient:          in.ActorClient,
		cclEventDal:          in.CCLEventDal,
		ccLRuleDal:           in.CCLRuleDal,
		idSvc:                in.IDSvc,
		dsSvc:                in.DsSvc,
		operateRecordService: in.OperateRecordService,
	}
	return handler.NewHandler(h.RestartSqlConcurrencyControlRule)
}

func (h *RestartSqlConcurrencyControlRuleHandler) RestartSqlConcurrencyControlRule(ctx context.Context, req *model.RestartSqlConcurrencyControlRuleReq) (ret *model.RestartSqlConcurrencyControlRuleResp, err error) {
	var (
		ruleDetail *model.RuleDetail
		ruleId     int64
	)
	defer func() {
		rule, _ := json.Marshal(ruleDetail)
		operationRecord := &entity.OperationRecord{
			OperationType: model.DasOperationCategory_CCL.String(),
			InstanceId:    req.GetInstanceId(),
			InstanceType:  req.GetInstanceType().String(),
			TriggerType:   model.TriggerType_Manual.String(),
			Action:        model.DasAction_RestartThrottleRule.String(),
			TaskId:        fmt.Sprintf("%d", ruleId),
			Extra:         string(rule),
			TenantId:      fwctx.GetTenantID(ctx),
			UserId:        fwctx.GetUserID(ctx),
		}
		if err != nil {
			operationRecord.Status = model.OpsTaskState_RunningFailed.String()
			log.Warn(ctx, "RestartSqlConcurrencyControlRule error:%+v", err)
		} else {
			operationRecord.Status = model.OpsTaskState_Done.String()
		}
		err := h.operateRecordService.CreateDasRecord(ctx, operationRecord)
		if err != nil {
			log.Warn(ctx, "create das record failed %+v", err)
		}
	}()
	// 查询终止的规则详情
	taskId, err := strconv.ParseInt(req.GetTaskId(), 10, 64)
	if err != nil {
		log.Warn(ctx, "req.TaskId format is invalid %s", req.GetTaskId())
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	rule, err := h.ccLRuleDal.Get(ctx, taskId)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_MetaDBQueryError)
	}
	var terminationType *string
	if rule.Duration >= 105120000 {
		terminationType = utils.StringRef(model.TerminationType_Manual.String())
	} else {
		terminationType = utils.StringRef(model.TerminationType_Auto.String())
	}
	ruleDetail = &model.RuleDetail{
		KeyWord:             rule.Keywords,
		SqlType:             model.SqlType(rule.SqlType),
		ConcurrencyCount:    rule.ConcurrencyCount,
		EffectiveTime:       int32(rule.Duration),
		CreateTime:          time.UnixMilli(rule.CreatedAt).UTC().Format(time.RFC3339),
		ThrottleMode:        utils.StringRef(rule.ThrottleMode),
		ThrottleType:        utils.StringRef(rule.ThrottleType),
		TaskId:              fmt.Sprintf("%d", rule.ID),
		ThrottleSqlText:     utils.StringRef(rule.ThrottleSqlText),
		ThrottleTarget:      utils.StringRef(rule.ThrottleTarget),
		ThrottleFingerPrint: utils.StringRef(rule.ThrottleFingerPrint),
		TerminationType:     terminationType,
		Desc:                utils.StringRef(rule.Description),
		ThrottleThreshold:   utils.Int32Ref(rule.ThrottleThreshold),
	}
	// check req params
	if err := req.IsValid(); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if !h.dsSvc.IsMyOwnInstance(ctx, req.GetInstanceId(), bizConv.ToSharedType(req.GetInstanceType())) {
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.dsSvc.CheckInstanceState(ctx, req.GetInstanceId(), bizConv.ToSharedType(req.GetInstanceType()), true); err != nil {
		return nil, err
	}
	// check instance type
	if req.GetInstanceType() != model.DSType_MySQL && req.GetInstanceType() != model.DSType_VeDBMySQL && req.GetInstanceType() != model.DSType_ByteRDS &&
		req.GetInstanceType() != model.DSType_MySQLSharding && req.GetInstanceType() != model.DSType_MetaMySQL {
		return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	tenantId := fwctx.GetTenantID(ctx)
	// 查元数据库获取
	taskId, err = strconv.ParseInt(req.GetTaskId(), 10, 64)
	if err != nil {
		log.Warn(ctx, "req.TaskId format is invalid %s", req.GetTaskId())
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// 检查CCL任务状态(仅支持状态为已完成和已终止的任务)
	allowedState := []model.RuleState{model.RuleState_DONE, model.RuleState_STOPPED}
	ruleInfo, err := h.ccLRuleDal.Get(ctx, taskId)
	if err != nil {
		log.Warn(ctx, "Get CCLRule from metadata failed:%+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	stateIsValid := false
	for _, state := range allowedState {
		if ruleInfo.State == int8(state) {
			stateIsValid = true
			break
		}
	}
	if !stateIsValid {
		log.Info(ctx, "Current CCL Task State %d is not supported to restart", ruleInfo.State)
		return nil, consts.ErrorOf(model.ErrorCode_NotSupportAction)
	}
	//generate ruleId
	ruleId, err = h.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "Generate CCLRuleId failed %+v", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	//generate eventId
	eventId, err := h.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "Generate CCLEventId failed %+v", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())

	}
	timestampMS := time.Now().UnixMilli()
	dalRule := &dao.SqlCCLRule{
		ID:                  ruleId,
		TenantID:            tenantId,
		UserID:              ruleInfo.UserID,
		InstanceID:          ruleInfo.InstanceID,
		InstanceType:        ruleInfo.InstanceType,
		Duration:            ruleInfo.Duration,
		Keywords:            ruleInfo.Keywords,
		ConcurrencyCount:    ruleInfo.ConcurrencyCount,
		SqlType:             ruleInfo.SqlType,
		State:               int8(model.RuleState_NONE),
		CreatedAt:           timestampMS,
		UpdatedAt:           timestampMS,
		ThrottleHost:        ruleInfo.ThrottleHost,
		ThrottleDB:          ruleInfo.ThrottleDB,
		ThrottleSqlText:     ruleInfo.ThrottleSqlText,
		ThrottleFingerPrint: ruleInfo.ThrottleFingerPrint,
		ThrottleThreshold:   ruleInfo.ThrottleThreshold,
		ThrottleType:        ruleInfo.ThrottleType,
		EndpointID:          ruleInfo.EndpointID,
		EndpointType:        ruleInfo.EndpointType,
		RejectedCount:       0,
		LinkType:            ruleInfo.LinkType,
		ThrottleMode:        ruleInfo.ThrottleMode,
		ThrottleTarget:      ruleInfo.ThrottleTarget,
		RegionId:            ruleInfo.RegionId,
		Description:         ruleInfo.Description,
		ThrottleGroupIds:    ruleInfo.ThrottleGroupIds,
		ThrottleObjId:       ruleInfo.ThrottleObjId,
	}
	//create new Rule
	if err := h.ccLRuleDal.Create(ctx, dalRule); err != nil {
		log.Warn(ctx, "Create SqlCCLRule records on metaDB failed%+v", err)
		e := consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
		return nil, e
	}
	dalEvent := &dao.SqlCCLEvent{
		ID:         eventId,
		CreatedAt:  timestampMS,
		UpdatedAt:  timestampMS,
		TenantID:   fwctx.GetTenantID(ctx),
		InstanceID: ruleInfo.InstanceID,
		Operator:   ruleInfo.UserID,
		EventType:  int8(model.CCLEventType_Add),
	}
	//create new Event
	if err := h.cclEventDal.Create(ctx, dalEvent); err != nil {
		log.Warn(ctx, "Create SqlCCLEvent records on metaDB failed%+v", err)
		e := consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
		return nil, e
	}
	//delete old cclRule
	task, err := strconv.ParseInt(req.GetTaskId(), 10, 64)
	if err != nil {
		log.Warn(ctx, "req.TaskId format is invalid %s", req.GetTaskId())
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if err := h.ccLRuleDal.Delete(ctx, task); err != nil {
		log.Warn(ctx, "Delete SQLCCLRule on metaDB failed%+v", err)
		e := consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
		return nil, e
	}
	msg := &shared.CreateSqlConcurrencyControlRuleReq{
		TaskId:   ruleId,
		TenantId: tenantId,
	}
	log.Info(ctx, "RestartSqlConcurrencyControlRule msg is %s", msg)
	// 调用CCLRuleActor执行具体的业务逻辑
	actorResp, err := h.actorClient.KindOf(consts.CCLRuleActorKind).Call(ctx, conv.Int64ToStr(ruleId), msg)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	switch msg := actorResp.(type) {
	case *shared.CCLRuleActorFailResp:
		log.Warn(ctx, "RestartSqlConcurrencyControlRule error:%s", msg)
		return nil, consts.TranslateSharedToStandardError(msg.StandardError)
	case *shared.ActionSuccess:
		ret = &model.RestartSqlConcurrencyControlRuleResp{}
		return ret, nil
	default:
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
}

package sql_ccl

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"testing"
)

type ModifySqlConcurrencyControlRuleTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *ModifySqlConcurrencyControlRuleTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *ModifySqlConcurrencyControlRuleTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestModifySqlConcurrencyControlRuleTestSuite(t *testing.T) {
	suite.Run(t, new(ModifySqlConcurrencyControlRuleTestSuite))
}

func (suite *ModifySqlConcurrencyControlRuleTestSuite) TestCorrect() {
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	cclEventDal := mocks.NewMockSqlCCLEventDAL(suite.ctrl)
	idSvc := mocks.NewMockService(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	operationRecordSvc := mocks.NewMockOperateRecordService(suite.ctrl)
	operationRecordSvc.EXPECT().CreateDasRecord(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	dsSvc.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true)
	dsSvc.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	ModifySqlConcurrencyControlRuleHandler := NewModifySqlConcurrencyControlRuleHandler(ModifySqlConcurrencyControlRuleHandlerIn{IdSvc: idSvc, DsSvc: dsSvc, CCLRuleDal: cclRuleDal, CCLEventDal: cclEventDal, OperateRecordService: operationRecordSvc})
	fn := ModifySqlConcurrencyControlRuleHandler.Impl.Impl.(func(ctx context.Context, req *model.ModifySqlConcurrencyControlRuleReq) (*model.ModifySqlConcurrencyControlRuleResp, error))
	req := &model.ModifySqlConcurrencyControlRuleReq{
		InstanceId:   "mysql-111",
		TaskId:       "111",
		InstanceType: model.DSTypePtr(model.DSType_MySQL),
		Threshold:    10,
		ThrottleMode: model.ThrottleModePtr(model.ThrottleMode_ProxyThrottle),
	}
	ruleInfo := &dao.SqlCCLRule{
		InstanceType: model.DSType_MySQL.String(),
		State:        0,
		ThrottleMode: model.ThrottleMode_ProxyThrottle.String(),
	}
	idSvc.EXPECT().NextID(gomock.Any()).AnyTimes()
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	cclEventDal.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	cclRuleDal.EXPECT().UpdateThresholdByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	dsSvc.EXPECT().ModifyProxyThrottleRule(gomock.Any(), gomock.Any()).Return(nil, nil)
	want := &model.ModifySqlConcurrencyControlRuleResp{}
	got, err := fn(ctx, req)
	suite.Empty(err)
	suite.Equal(want, got)
}

func (suite *ModifySqlConcurrencyControlRuleTestSuite) TestInternalError() {
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	cclEventDal := mocks.NewMockSqlCCLEventDAL(suite.ctrl)
	idSvc := mocks.NewMockService(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	operationRecordSvc := mocks.NewMockOperateRecordService(suite.ctrl)
	operationRecordSvc.EXPECT().CreateDasRecord(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	dsSvc.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true)
	dsSvc.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	ModifySqlConcurrencyControlRuleHandler := NewModifySqlConcurrencyControlRuleHandler(ModifySqlConcurrencyControlRuleHandlerIn{IdSvc: idSvc, DsSvc: dsSvc, CCLRuleDal: cclRuleDal, CCLEventDal: cclEventDal, OperateRecordService: operationRecordSvc})
	fn := ModifySqlConcurrencyControlRuleHandler.Impl.Impl.(func(ctx context.Context, req *model.ModifySqlConcurrencyControlRuleReq) (*model.ModifySqlConcurrencyControlRuleResp, error))
	req := &model.ModifySqlConcurrencyControlRuleReq{
		InstanceId:   "mysql-111",
		TaskId:       "111",
		InstanceType: model.DSTypePtr(model.DSType_MySQL),
		Threshold:    10,
		ThrottleMode: model.ThrottleModePtr(model.ThrottleMode_DBThrottle),
	}
	ruleInfo := &dao.SqlCCLRule{
		InstanceType: model.DSType_MySQL.String(),
		State:        4,
		ThrottleMode: model.ThrottleMode_DBThrottle.String(),
	}
	idSvc.EXPECT().NextID(gomock.Any()).AnyTimes()
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	cclEventDal.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	_, err := fn(ctx, req)
	suite.NotEmpty(err)
}

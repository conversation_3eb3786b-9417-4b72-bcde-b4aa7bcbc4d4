package sql_ccl

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	bizConv "code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/dig"
)

type CreateSqlConcurrencyControlRuleHandler struct {
	actorClient          cli.ActorClient
	ccLRuleDal           dal.SqlCCLRulesDAL
	cclEventDal          dal.SqlCCLEventDAL
	idSvc                idgen.Service
	dsSvc                datasource.DataSourceService
	operateRecordService operate_record.OperateRecordService
}

type CreateSqlConcurrencyControlRuleHandlerIn struct {
	dig.In
	ActorClient          cli.ActorClient
	CCLRuleDal           dal.SqlCCLRulesDAL
	CCLEventDal          dal.SqlCCLEventDAL
	IDSvc                idgen.Service
	DsSvc                datasource.DataSourceService
	OperateRecordService operate_record.OperateRecordService
}

func NewCreateSqlConcurrencyControlRuleHandler(in CreateSqlConcurrencyControlRuleHandlerIn) handler.HandlerImplementationEnvolope {
	h := &CreateSqlConcurrencyControlRuleHandler{
		actorClient:          in.ActorClient,
		ccLRuleDal:           in.CCLRuleDal,
		cclEventDal:          in.CCLEventDal,
		idSvc:                in.IDSvc,
		dsSvc:                in.DsSvc,
		operateRecordService: in.OperateRecordService,
	}
	return handler.NewHandler(h.CreateSqlConcurrencyControlRule)
}

func (h *CreateSqlConcurrencyControlRuleHandler) CreateSqlConcurrencyControlRule(ctx context.Context, req *model.CreateSqlConcurrencyControlRuleReq) (ret *model.CreateSqlConcurrencyControlRuleResp, err error) {
	// DAS操作审计
	var (
		ruleDetail *model.RuleDetail
		ruleId     int64
	)
	defer func() {
		ruleDetail = &model.RuleDetail{
			KeyWord:             req.GetKeyWords(),
			SqlType:             req.GetSqlType(),
			ConcurrencyCount:    req.GetConcurrencyCount(),
			EffectiveTime:       req.GetEffectiveTime(),
			CreateTime:          time.UnixMilli(time.Now().UnixMilli()).UTC().Format(time.RFC3339),
			ThrottleMode:        utils.StringRef(req.GetThrottleMode().String()),
			ThrottleType:        utils.StringRef(req.GetThrottleType().String()),
			TaskId:              fmt.Sprintf("%d", ruleId),
			ThrottleTarget:      utils.StringRef(req.GetThrottleTarget().String()),
			ThrottleFingerPrint: utils.StringRef(req.GetThrottleFingerPrint()),
			ThrottleSqlText:     utils.StringRef(req.GetThrottleSqlText()),
			TerminationType:     utils.StringRef(req.GetTerminationType().String()),
			ThrottleThreshold:   utils.Int32Ref(req.GetThrottleThreshold()),
			Desc:                utils.StringRef(req.GetDesc()),
		}
		if req.GetTerminationType() == model.TerminationType_Manual {
			ruleDetail.EffectiveTime = 105120000
		}
		rule, _ := json.Marshal(ruleDetail)
		operationRecord := &entity.OperationRecord{
			OperationType: model.DasOperationCategory_CCL.String(),
			InstanceId:    req.GetInstanceId(),
			InstanceType:  req.GetInstanceType().String(),
			TriggerType:   model.TriggerType_Manual.String(),
			Action:        model.DasAction_CreateThrottleRule.String(),
			Extra:         string(rule),
			TenantId:      fwctx.GetTenantID(ctx),
			UserId:        fwctx.GetUserID(ctx),
			TaskId:        fmt.Sprintf("%d", ruleId),
		}
		if err != nil {
			operationRecord.Status = model.OpsTaskState_RunningFailed.String()
			log.Warn(ctx, "CreateSqlConcurrencyControlRule error:%+v", err)
		} else {
			operationRecord.Status = model.OpsTaskState_Done.String()
		}
		err := h.operateRecordService.CreateDasRecord(ctx, operationRecord)
		if err != nil {
			log.Warn(ctx, "create das record failed %+v", err)
		}
	}()
	if !req.IsSetLinkType() {
		// 默认为火山
		req.LinkType = model.LinkTypePtr(model.LinkType_Volc)
	}
	if !req.IsSetThrottleMode() {
		req.ThrottleMode = model.ThrottleModePtr(model.ThrottleMode_DBThrottle)
	}
	if req.LinkType.String() == model.LinkType_Volc.String() {
		req.ThrottleMode = model.ThrottleModePtr(model.ThrottleMode_DBThrottle) // 火山默认为内核限流
	}
	if req.LinkType.String() == model.LinkType_ByteInner.String() {
		req.ThrottleMode = model.ThrottleModePtr(model.ThrottleMode_ProxyThrottle) // 字节云默认为代理限流
	}

	if !req.IsSetThrottleType() {
		req.ThrottleType = model.ThrottleTypePtr(model.ThrottleType_ConcurrencyCount)
	}
	if !req.IsSetThrottleTarget() {
		req.ThrottleTarget = model.ThrottleTargetPtr(model.ThrottleTarget_MaxConcurrencyCount)
	}
	if !req.IsSetTerminationType() {
		req.TerminationType = model.TerminationTypePtr(model.TerminationType_Auto)
	}
	switch req.GetLinkType() {
	case model.LinkType_Volc:
		if err := h.preCheck(ctx, req); err != nil {
			return nil, err
		}
	case model.LinkType_ByteInner:
		if err := h.preCheckForByteInner(ctx, req); err != nil {
			return nil, err
		}
	}
	ruleId, err = h.CreateSqlThrottleRecords(ctx, req)
	if err != nil {
		return nil, err
	}
	msg := &shared.CreateSqlConcurrencyControlRuleReq{
		TaskId:   ruleId,
		TenantId: fwctx.GetTenantID(ctx),
	}
	log.Info(ctx, "CreateSqlConcurrencyControlRule msg is %s", utils.Show(msg))
	actorResp, err := h.actorClient.KindOf(consts.CCLRuleActorKind).Call(ctx, conv.Int64ToStr(ruleId), msg)
	if err != nil {
		log.Warn(ctx, "CreateSqlConcurrencyControlRule error:%+v", err)
		return nil, err
	}
	switch msg := actorResp.(type) {
	case *shared.CCLRuleActorFailResp:
		log.Warn(ctx, "CreateSqlConcurrencyControlRule error:%+v", msg)
		return nil, consts.TranslateSharedToStandardError(msg.StandardError)
	case *shared.ActionSuccess:
		ret = &model.CreateSqlConcurrencyControlRuleResp{
			RuleId: ruleId,
		}
		return ret, nil
	default:
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
}

func (h *CreateSqlConcurrencyControlRuleHandler) CreateSqlThrottleRecords(ctx context.Context, req *model.CreateSqlConcurrencyControlRuleReq) (int64, error) {
	tenantId := fwctx.GetTenantID(ctx)
	//generate ruleId
	ruleId, err := h.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "Generate CCLRuleId failed %+v", err)
		return 0, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	//generate eventId
	eventId, err := h.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "Generate CCLEventId failed %+v", err)
		return 0, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	timestampMS := time.Now().UnixMilli()
	dalRule := &dao.SqlCCLRule{
		ID:                  ruleId,
		TenantID:            tenantId,
		UserID:              fwctx.GetUserID(ctx),
		InstanceID:          req.GetInstanceId(),
		InstanceType:        req.GetInstanceType().String(),
		LinkType:            req.GetLinkType().String(),
		SqlType:             int8(req.GetSqlType()),
		State:               int8(model.RuleState_NONE),
		CreatedAt:           timestampMS,
		UpdatedAt:           timestampMS,
		RejectedCount:       0,
		ThrottleHost:        req.GetThrottleHost(),
		ThrottleDB:          req.GetThrottleDB(),
		ThrottleSqlText:     req.GetThrottleSqlText(),
		ThrottleFingerPrint: req.GetThrottleFingerPrint(),
		ThrottleThreshold:   req.GetThrottleThreshold(),
		ThrottleType:        req.GetThrottleType().String(),
		EndpointID:          req.GetEndpointId(),
		EndpointType:        req.GetEndpointType().String(),
		ConcurrencyCount:    req.GetConcurrencyCount(),
		Keywords:            req.GetKeyWords(),
		ThrottleTarget:      req.ThrottleTarget.String(),
		ThrottleMode:        req.GetThrottleMode().String(),
		ThrottleGroupIds:    strings.Join(int64ToStrSlice(req.GetGroupIds()), ","),
		RegionId:            req.GetRegionId(),
		Description:         req.GetDesc(),
	}
	if req.GetInstanceType() == model.DSType_ByteRDS {
		objId := primitive.NewObjectID().Hex()
		// 内场限流规则ID生成
		dalRule.ThrottleObjId = objId
	}
	if req.GetTerminationType() == model.TerminationType_Manual {
		dalRule.Duration = 105120000 // 200 years
	} else {
		dalRule.Duration = int64(req.EffectiveTime)
	}
	if err := h.ccLRuleDal.Create(ctx, dalRule); err != nil {
		log.Warn(ctx, "Create SqlCCLRule records on metaDB failed%+v", err)
		return 0, consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	dalEvent := &dao.SqlCCLEvent{
		ID:         eventId,
		CreatedAt:  timestampMS,
		UpdatedAt:  timestampMS,
		Operator:   fwctx.GetUserID(ctx),
		TenantID:   tenantId,
		InstanceID: req.GetInstanceId(),
		EventType:  int8(model.CCLEventType_Add),
	}
	if err := h.cclEventDal.Create(ctx, dalEvent); err != nil {
		log.Warn(ctx, "Create SqlCCLEvent records on metaDB failed%+v", err)
		return 0, consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return ruleId, nil
}

func (h *CreateSqlConcurrencyControlRuleHandler) preCheck(ctx context.Context, req *model.CreateSqlConcurrencyControlRuleReq) error {
	// check req params valid
	if err := req.IsValid(); err != nil {
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if !h.dsSvc.IsMyOwnInstance(ctx, req.GetInstanceId(), bizConv.ToSharedType(req.GetInstanceType())) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.dsSvc.CheckInstanceState(ctx, req.GetInstanceId(), bizConv.ToSharedType(req.GetInstanceType()), true); err != nil {
		return err
	}
	// check instance type
	if req.GetInstanceType() != model.DSType_MySQL && req.GetInstanceType() != model.DSType_VeDBMySQL &&
		req.GetInstanceType() != model.DSType_MySQLSharding && req.GetInstanceType() != model.DSType_MetaMySQL {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	// check CCLTasks nums(>=128)
	CCLRuleInfos, err := h.ccLRuleDal.List(ctx, req.GetInstanceId(), &dal.SqlCCLQueryFilter{
		ThrottleMode: req.GetThrottleMode().String(),
	}, "ASC", "created_at", 10, 0, true)
	if err != nil {
		log.Warn(ctx, "Get SqlCCLEvent lists failed:%+v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDBQueryError)
	}
	if CCLRuleInfos.Total >= 128 {
		log.Warn(ctx, "The maximum number of CCL rules per instance does not exceed 128")
		return consts.ErrorOf(model.ErrorCode_SqlRuleMaxNumsErr)
	}
	//check duration <= int32
	if req.EffectiveTime > 99999 && req.GetTerminationType() == model.TerminationType_Auto {
		log.Warn(ctx, "The maximum sql ccl duration exceed 99999 minutes")
		return consts.ErrorOf(model.ErrorCode_SqlDurationMaxNumsErr)
	}
	// check sql text/fingerprint lengths
	if len(req.GetThrottleSqlText()) > 65535 || len(req.GetThrottleFingerPrint()) > 65535 {
		log.Warn(ctx, "The lengths of sqlText per does not exceed 65535")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// check keywords lengths
	// vedb支持调整关键字长度到65535，其他暂不支持
	if req.GetInstanceType() == model.DSType_VeDBMySQL {
		if len(req.GetKeyWords()) > 65535 {
			log.Warn(ctx, "The lengths of keywords per does not exceed 65535")
			return consts.ErrorOf(model.ErrorCode_KeywordLengthErr)
		}
	} else {
		if len(req.GetKeyWords()) > 512 {
			log.Warn(ctx, "The lengths of keywords per does not exceed 512")
			return consts.ErrorOf(model.ErrorCode_KeywordLengthErr)
		}
	}
	// check threshold Maximum
	if req.GetConcurrencyCount() > 99999 {
		log.Warn(ctx, "The concurrency count does not exceed 99999")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// check whether keyword legally
	blackListSymbol := []string{`,`, `'`, ";"}
	for _, char := range blackListSymbol {
		if strings.Contains(req.GetKeyWords(), char) {
			log.Warn(ctx, "keyWords Can't include %s", char)
			return consts.ErrorOf(model.ErrorCode_NotSupportSymbol)
		}
	}
	// 检查关键字中是否仅包含SQL类型，会导致限流范围过大
	keywordBlackList := []string{
		model.SqlType_SELECT.String(),
		model.SqlType_SELECT.String() + "~",
		model.SqlType_UPDATE.String(),
		model.SqlType_UPDATE.String() + "~",
		model.SqlType_DELETE.String(),
		model.SqlType_DELETE.String() + "~",
		model.SqlType_INSERT.String(),
		model.SqlType_INSERT.String() + "~",
		model.SqlType_REPLACE.String(),
		model.SqlType_REPLACE.String() + "~",
	}
	for _, typ := range keywordBlackList {
		if strings.ToUpper(req.GetKeyWords()) == typ {
			log.Warn(ctx, "Keywords can't be exactly the same as the sql type")
			return consts.ErrorOf(model.ErrorCode_NotSupportSqlLevelKeyWord)
		}
	}

	// 限流规则唯一性判定
	switch req.GetThrottleTarget() {
	case model.ThrottleTarget_FingerCCL:
		// 仅开放veDB
		if req.GetInstanceType() != model.DSType_VeDBMySQL {
			return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
		}
		// 需要将sql->sql模版->sql关键字
		sqlTemplate, _ := datasource.GetSqlTemplate(ctx, req.GetThrottleSqlText())
		targetKeyword := GenerateSqlKeywordsForVolc(sqlTemplate)
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.ThrottleFingerPrint == req.GetThrottleFingerPrint() &&
				rule.SqlType == int8(req.GetSqlType()) &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate fingerprint for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
			// 临时方案额外需要考虑finger不能和keywords重复，vedb 630后需要去掉
			if rule.Keywords == targetKeyword &&
				rule.SqlType == int8(req.GetSqlType()) &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate fingerprint for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	case model.ThrottleTarget_MaxConcurrencyCount:
		// check whether keywords duplicated
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.Keywords == req.GetKeyWords() && rule.SqlType == int8(req.GetSqlType()) &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate keywords for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	case model.ThrottleTarget_SqlCCL:
		// 仅开放veDB
		if req.GetInstanceType() != model.DSType_VeDBMySQL {
			return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
		}
		targetKeyword := GenerateSqlKeywordsForVolc(req.GetThrottleSqlText())
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.ThrottleSqlText == req.GetThrottleSqlText() &&
				rule.SqlType == int8(req.GetSqlType()) &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate sql text for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
			// 临时方案额外需要考虑sqlText不能和keywords重复，vedb 582后需要去掉
			if rule.Keywords == targetKeyword &&
				rule.SqlType == int8(req.GetSqlType()) &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate sql text for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	default:
		log.Warn(ctx, "Not support ThrottleTarget %s", req.GetThrottleTarget().String())
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	return nil
}
func (h *CreateSqlConcurrencyControlRuleHandler) preCheckForByteInner(ctx context.Context, req *model.CreateSqlConcurrencyControlRuleReq) error {

	// check req params valid
	if err := req.IsValid(); err != nil {
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// check instance type
	if req.GetInstanceType() != model.DSType_MySQL && req.GetInstanceType() != model.DSType_VeDBMySQL && req.GetInstanceType() != model.DSType_ByteRDS && req.GetInstanceType() != model.DSType_MySQLSharding {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	// 终端类型、限流类型、限流对象、限流阈值为必填项
	if !req.IsSetEndpointType() || !req.IsSetThrottleType() || !req.IsSetThrottleTarget() ||
		!req.IsSetThrottleThreshold() {
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	//check duration <= 99999
	if req.EffectiveTime > 99999 && req.GetTerminationType() == model.TerminationType_Auto {
		log.Warn(ctx, "The maximum sql ccl duration exceed 99999 minutes")
		return consts.ErrorOf(model.ErrorCode_SqlDurationMaxNumsErr)
	}
	// check keywords lengths
	if len(req.GetKeyWords()) > 65535 {
		log.Warn(ctx, "The lengths of keywords per does not exceed 65535")
		return consts.ErrorOf(model.ErrorCode_KeywordLengthErr)
	}
	// check sql text/fingerprint lengths
	if len(req.GetThrottleSqlText()) > 4096 || len(req.GetThrottleFingerPrint()) > 4096 {
		log.Warn(ctx, "The lengths of sqlText per does not exceed 4096")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// check threshold Maximum
	if req.GetThrottleThreshold() > 99999 {
		log.Warn(ctx, "The throttle threshold does not exceed 99999")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// check byteRDS regionId(required)
	if req.GetInstanceType() == model.DSType_ByteRDS && req.GetRegionId() == "" {
		log.Warn(ctx, "regionId is Null with ByteRDS")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// 获取Endpoints
	endpointList, err := h.dsSvc.DescribeDBInstanceEndpoints(ctx, &datasource.DescribeDBInstanceEndpointsReq{
		Type:       bizConv.ToSharedType(req.GetInstanceType()),
		InstanceId: req.GetInstanceId(),
	})
	if err != nil {
		log.Warn(ctx, "Get instance %s endpoint failed %s", req.GetInstanceId(), utils.Show(err))
		return err
	}
	// 获取EndpointID
	if len(endpointList.Endpoints) < 1 {
		log.Warn(ctx, "get instance %s endpoints null", req.GetInstanceId())
		return consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No mysqld pod found")
	} else {
		switch req.GetInstanceType() {
		case model.DSType_MySQL:
			for _, ed := range endpointList.Endpoints {
				if ed.ReadWriteMode == req.GetEndpointType().String() && ed.EndpointType == "Custom" {
					req.EndpointId = utils.StringRef(ed.EndpointID)
					break
				}
			}
		case model.DSType_VeDBMySQL:
			for _, ed := range endpointList.Endpoints {
				if ed.ReadWriteMode == req.GetEndpointType().String() && ed.EndpointType != "Cluster" {
					req.EndpointId = utils.StringRef(ed.EndpointID)
					break
				}
			}
		case model.DSType_MySQLSharding:
			for _, ed := range endpointList.Endpoints {
				if ed.ReadWriteMode == req.GetEndpointType().String() && ed.EndpointType != "Cluster" {
					req.EndpointId = utils.StringRef(ed.EndpointID)
					break
				}
			}
		case model.DSType_ByteRDS:
			for _, ed := range endpointList.Endpoints {
				if ed.ReadWriteMode == req.GetEndpointType().String() {
					req.EndpointId = utils.StringRef(ed.EndpointID)
					break
				}
			}
		}
		if req.GetEndpointId() == "" {
			return consts.ErrorOf(model.ErrorCode_ParamError)
		}
	}
	CCLRuleInfos, err := h.ccLRuleDal.List(ctx, req.GetInstanceId(), &dal.SqlCCLQueryFilter{
		ThrottleMode: req.GetThrottleMode().String(),
	}, "ASC", "created_at", 10, 0, true)
	if err != nil {
		log.Warn(ctx, "Get SqlCCLEvent lists failed:%+v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDBQueryError)
	}

	// 检查关键字中是否包含不支持的符号
	blackListSymbol := []string{`,`, `'`, `;`, `.`, `=`, `>`, `<`, `+`, `/`, `(`, `)`, `"`, `!`, `:`, `{`, `}`, `[`, `]`, `\n`, `\\`, `\`}
	for _, char := range blackListSymbol {
		if strings.Contains(req.GetKeyWords(), char) {
			log.Warn(ctx, "keyWords Can't include %s", char)
			return consts.ErrorOf(model.ErrorCode_NotSupportSymbol)
		}
	}
	// 限流规则唯一性判定
	switch req.GetThrottleTarget() {
	case model.ThrottleTarget_FingerQPS:
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.ThrottleFingerPrint == req.GetThrottleFingerPrint() &&
				rule.EndpointID == req.GetEndpointId() &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate fingerprint for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	case model.ThrottleTarget_KeywordQPS:
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.Keywords == req.GetKeyWords() &&
				rule.EndpointID == req.GetEndpointId() &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate keywords for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	case model.ThrottleTarget_SqlQPS:
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.ThrottleSqlText == req.GetThrottleSqlText() &&
				rule.EndpointID == req.GetEndpointId() &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate sql text for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	case model.ThrottleTarget_PsmDbQPS:
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.ThrottleHost == req.GetThrottleHost() &&
				rule.ThrottleDB == req.GetThrottleDB() &&
				rule.EndpointID == req.GetEndpointId() &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support duplicate PsmDb for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	case model.ThrottleTarget_FrontQPS:
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.EndpointID == req.GetEndpointId() &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support multi frontQPS for the same endpoint for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	case model.ThrottleTarget_MaxFrontConn:
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.EndpointID == req.GetEndpointId() &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				log.Warn(ctx, "Not support multi MaxFrontConn for the same endpoint for SQL throttling rules")
				return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
			}
		}
	case model.ThrottleTarget_GroupQPS:
		for _, rule := range CCLRuleInfos.SqlCCLRules {
			if rule.EndpointID == req.GetEndpointId() &&
				rule.ThrottleTarget == req.GetThrottleTarget().String() {
				groupIdStr := int64ToStrSlice(req.GroupIds)
				if len(req.GroupIds) == 0 || rule.ThrottleGroupIds == "" ||
					strings.Contains(rule.ThrottleGroupIds, strings.Join(groupIdStr, ",")) ||
					strings.Contains(strings.Join(groupIdStr, ","), rule.ThrottleGroupIds) {
					log.Warn(ctx, "Not support multi groupQPS for the same endpoint for SQL throttling rules")
					return consts.ErrorOf(model.ErrorCode_ThrottleRuleRepeatedErr)
				}
			}
		}
	default:
		log.Warn(ctx, "Not support ThrottleTarget %s", req.GetThrottleTarget().String())
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	return nil
}

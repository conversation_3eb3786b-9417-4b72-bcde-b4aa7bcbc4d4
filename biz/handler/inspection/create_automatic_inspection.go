package inspection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/inspection"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2_new "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	dslibutils "code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"go.uber.org/dig"
	"sync"
)

func NewCreateAutomaticInspectionHandler(in NewCreateAutomaticInspectionIn) handler.HandlerImplementationEnvolope {
	h := &CreateAutomaticInspectionHandler{
		inspectionService: in.InspectionService,
		ds:                in.Ds,
	}
	return handler.NewHandler(h.CreateAutomaticInspection)
}

type CreateAutomaticInspectionHandler struct {
	inspectionService inspection.InspectionService
	ds                datasource.DataSourceService
}

type NewCreateAutomaticInspectionIn struct {
	dig.In
	InspectionService inspection.InspectionService
	Ds                datasource.DataSourceService
}

func (h *CreateAutomaticInspectionHandler) CreateAutomaticInspection(ctx context.Context, req *model.CreateAutomaticInspectionReq) (*model.CreateAutomaticInspectionResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	// 检查租户和实例是否匹配
	err := h.checkTenantAndInstance(ctx, req)
	if err != nil {
		log.Warn(ctx, "inspection: check tenant err")
		return nil, err
	}
	automaticInspection, err := h.inspectionService.CreateAutoMaticInspection(ctx, req)
	if err != nil {
		return nil, err
	}
	return automaticInspection, nil
}

func (h *CreateAutomaticInspectionHandler) checkReq(ctx context.Context, req *model.CreateAutomaticInspectionReq) error {
	// 检查实例
	log.Info(ctx, "inspection: CreateAutomaticInspection req is: ", dslibutils.Show(req))
	if req == nil || len(req.GetInstanceInfo()) == 0 || req.GetRegionId() == "" ||
		req.GetInstanceType().String() == "" {
		log.Warn(ctx, "inspection: param error,%d,%s,%s", len(req.GetInstanceInfo()), req.GetRegionId(), req.GetInstanceType().String())
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	log.Info(ctx, "inspection: req.GetInstanceType().String() is:%s", req.GetInstanceType().String())
	if req.GetInstanceType().String() != model.InstanceType_MySQL.String() &&
		req.GetInstanceType().String() != model.InstanceType_VeDBMySQL.String() &&
		req.GetInstanceType().String() != model.InstanceType_Redis.String() &&
		req.GetInstanceType().String() != model.InstanceType_Postgres.String() {
		log.Warn(ctx, "inspection: param error")
		return consts.ErrorOf(model.ErrorCode_InstanceTypeNotSupport)
	}
	// 如果是关闭巡检,则需要将巡检时间置为0
	if req.EnableAutoInspection == false {
		req.ExecStartTime, req.ExecEndTime = dslibutils.Int32Ref(0), dslibutils.Int32Ref(0)
	}
	return nil
}

func (h *CreateAutomaticInspectionHandler) checkTenantAndInstance(ctx context.Context, req *model.CreateAutomaticInspectionReq) error {
	wg := sync.WaitGroup{}
	errCh := make(chan error)
	waitCh := make(chan struct{})
	go func() {
		for _, val := range req.InstanceInfo {
			wg.Add(1)
			instanceInfo := val
			go func() {
				defer wg.Done()
				resp, err := h.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
					InstanceId: instanceInfo.InstanceId,
					Type:       shared.DataSourceType(req.InstanceType),
				})
				if err != nil {
					log.Warn(ctx, "inspection: describe instances error,%s", err.Error())
					errCh <- err
					return
				}
				if !h.isInstanceRunning(ctx, instanceInfo.InstanceId, resp) {
					log.Warn(ctx, "inspection: describe instances error,instance does not match tenant")
					errCh <- consts.ErrorOf(model.ErrorCode_TenantPermissionDeny)
					return
				}
			}()
		}
		wg.Wait()
		close(waitCh)
	}()
	select {
	case err := <-errCh:
		log.Info(ctx, "get DescribeDBInstances error: %s", err.Error())
		return err
	case <-waitCh:
		return nil
	}
}

func (h *CreateAutomaticInspectionHandler) isInstanceRunning(ctx context.Context, instanceID string, resp *datasource.DescribeDBInstanceDetailResp) bool {
	if resp == nil {
		log.Warn(ctx, "instance %s info is empty")
		return false
	}
	// 对于不同类型的实例,都是使用Running字符串来判断是否运行,所以这里直接采用RDS枚举来代替，也可以直接写成"Running"
	if resp.InstanceId == instanceID && resp.InstanceStatus == rdsModel_v2_new.InstanceStatus_Running.String() {
		log.Info(ctx, "inspection: Instance %s is Running", instanceID)
		return true
	}
	log.Warn(ctx, "inspection: Instance %s is not Running,please check", instanceID)
	return false
}

package handler

import (
	"context"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
)

type CommandChecker func(context.Context, string, *DescribeCommandSetHandler) (bool, error)
type SingleCommandChecker func(context.Context, string, string, *DescribeCommandSetHandler) (bool, error)

// CreateTable
func checkExcuteCommandSet(
	ctx context.Context,
	commandSetId string,
	describeCommandSetHandler *DescribeCommandSetHandler,
	cancelCommandSetHandler *CancelCommandSetHandler,
) (err error) {
	return UntilCommandSetFinishedHelper(ctx, commandSetId, describeCommandSetHandler, cancelCommandSetHandler, checkCommandSetFinished, 3*time.Second)
}

// UntilCommandSetFinished TODO execTimeout in optional params
func UntilCommandSetFinished(
	ctx context.Context,
	commandSetId string,
	describeCommandSetHandler *DescribeCommandSetHandler,
	cancelCommandSetHandler *CancelCommandSetHandler,
	execTimeout time.Duration,
) (err error) {
	return UntilCommandSetFinishedHelper(ctx, commandSetId, describeCommandSetHandler, cancelCommandSetHandler, checkCommandSetFinishedOpenAPI, execTimeout)
}

func UntilCommandSetFinishedHelper(
	ctx context.Context,
	commandSetId string,
	describeCommandSetHandler *DescribeCommandSetHandler,
	cancelCommandSetHandler *CancelCommandSetHandler,
	checkHelper CommandChecker,
	execTimeout time.Duration,
) (err error) {
	var ticker = time.NewTicker(1000 * time.Microsecond)
	var timer = time.NewTimer(execTimeout)
	defer ticker.Stop()
	defer timer.Stop()

	for {
		select {
		case <-ticker.C:
			if ok, err := checkHelper(ctx, commandSetId, describeCommandSetHandler); ok {
				if err != nil {
					return err
				} else {
					return nil
				}
			}
		case <-timer.C:
			log.Warn(ctx, "Command Set %s Timeout", commandSetId)
			_, err = cancelCommandSetHandler.CancelCommandSet(ctx, &model.CancelCommandSetReq{
				CommandSetId: utils.StringRef(commandSetId),
			})
			if err != nil {
				log.Warn(ctx, "Cancel Command Set %s Failed", commandSetId)
				return err
			}
			err = consts.ErrorOf(model.ErrorCode_ConnectionFailed)
			return err
		}
	}

}

func UntilCommandFinished(
	ctx context.Context,
	commandSetId string,
	commandId string,
	describeCommandSetHandler *DescribeCommandSetHandler,
) (err error) {
	return UntilCommandFinishedHelper(ctx, commandSetId, commandId, describeCommandSetHandler, checkCommandFinishedOpenAPI)
}

func UntilCommandFinishedHelper(
	ctx context.Context,
	commandSetId string,
	commandId string,
	describeCommandSetHandler *DescribeCommandSetHandler,
	checkHelper SingleCommandChecker,
) (err error) {
	var ticker = time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if ok, err := checkHelper(ctx, commandSetId, commandId, describeCommandSetHandler); ok {
				return err
			}
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func checkCommandSetFinished(
	ctx context.Context,
	csID string,
	describeCommandSetHandler *DescribeCommandSetHandler,
) (ok bool, err error) {

	resp, err := describeCommandSetHandler.DescribeCommandSet(ctx, &model.DescribeCommandSetReq{
		CommandSetId: utils.StringRef(csID),
	})
	if err != nil {
		log.Warn(ctx, "DescribeCommandSet set %s error", csID)
		return false, err
	}
	// Check the execution progress of commandSet.
	if resp.GetEndTime() == 0 {
		return false, nil
	}
	// All command in CommandSet is terminated.
	if len(resp.GetCommands()) <= 0 {
		log.Warn(ctx, "command set %s has no command", csID)
		return true, consts.ErrorOf(model.ErrorCode_CommandSetEmpty)
	}
	if reason := resp.GetCommands()[0].GetReason(); reason != model.CommandTerminatedReason_Success {
		log.Warn(ctx, "command set %s failed by %s", csID, resp.GetCommands()[0].GetReasonDetail())
		return true, consts.ErrorWithParam(model.ErrorCode_SystemError, resp.GetCommands()[0].GetReasonDetail())
	}
	return true, nil
}

func checkCommandSetFinishedOpenAPI(
	ctx context.Context,
	csID string,
	describeCommandSetHandler *DescribeCommandSetHandler,
) (ok bool, err error) {
	ok, _ = checkCommandSetFinished(ctx, csID, describeCommandSetHandler)
	return
}

func checkCommandFinishedOpenAPI(
	ctx context.Context,
	csID string,
	cmdID string,
	describeCommandSetHandler *DescribeCommandSetHandler,
) (ok bool, err error) {
	ok, err = checkCommandFinished(ctx, csID, cmdID, describeCommandSetHandler)
	return
}

func checkCommandFinished(
	ctx context.Context,
	csID string,
	cmdID string,
	describeCommandSetHandler *DescribeCommandSetHandler,
) (ok bool, err error) {
	// if ok, this command is terminated (whatever reason) else waiting
	resp, err := describeCommandSetHandler.DescribeCommandSet(ctx, &model.DescribeCommandSetReq{
		CommandSetId: utils.StringRef(csID),
	})
	if err != nil {
		log.Warn(ctx, "DescribeCommandSet set %s error", csID)
		return false, err
	}

	if len(resp.GetCommands()) <= 0 {
		log.Warn(ctx, "command set %s has no command", csID)
		return true, consts.ErrorOf(model.ErrorCode_CommandSetEmpty)
	}

	var targetCommand *model.CommandItem
	for _, cmd := range resp.GetCommands() {
		if cmd.GetCommandId() == cmdID {
			targetCommand = cmd
			break
		}
	}

	if targetCommand == nil {
		log.Warn(ctx, "command %s not found in command set %s", cmdID, csID)
		return true, consts.ErrorOf(model.ErrorCode_SystemError)
	}

	if targetCommand.GetState() != model.CommandState_Terminated {
		log.Info(ctx, "command %s is not terminated, waiting...")
		return false, nil
	}

	if reason := targetCommand.GetReason(); reason != model.CommandTerminatedReason_Success {
		log.Warn(ctx, "command %s in set %s failed by %s", cmdID, csID, targetCommand.GetReasonDetail())
		return true, consts.ErrorWithParam(model.ErrorCode_SystemError, targetCommand.GetReasonDetail())
	}

	return true, nil
}

func getOnePage(ctx context.Context, offset, limit, total int, items []string) []string {
	var itemsLimit []string
	var end = offset + limit
	if end > total {
		end = total
	}
	if offset > total {
		itemsLimit = make([]string, 0)
	} else {
		itemsLimit = make([]string, end-offset)
		copy(itemsLimit, items[offset:end])
	}
	return itemsLimit
}

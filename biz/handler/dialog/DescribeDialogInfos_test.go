package dialog

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"context"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"

	bizConfig "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
)

type DescribeDialogInfosSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *DescribeDialogInfosSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *DescribeDialogInfosSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestDescribeDialogInfosSuite(t *testing.T) {
	suite.Run(t, new(DescribeDialogInfosSuite))
}

func (suite *DescribeDialogInfosSuite) TestOnStarted() {
}

//	func (suite *DescribeDialogInfosSuite) TestDescribeDialogInfosHandler_DescribeDialogInfos_Succeed() {
//		PatchConvey("test describe succeed", suite.T(), func() {
//			cfg := config.NewMockConfigProvider(suite.ctrl)
//			c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
//			actorCli := mocks.NewMockActorClient(suite.ctrl)
//			c3Conf.EXPECT().GetNamespace(context.Background(), consts.C3ApplicationNamespace).Return(
//				&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_internal_account"}})
//			cfg.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{
//				DBInternalUsers: "{\"VeDBMySQL\":\"root,repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,mysql.infoschema,mysql.session,mysql.sys\",\"MySQL\":\"repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,system user,root\",\"Postgres\":\"postgres,ccp_monitoring,byte_rds_repl,byte_rds_proxy,primaryuser\"}"}).AnyTimes()
//			Mock(fwctx.GetTenantID).Return("**********")
//			credential := mocks.NewMockCredentials(suite.ctrl)
//			credential.EXPECT().GetAK().Return("test_ak").AnyTimes()
//			credential.EXPECT().GetSK().Return("test_sk").AnyTimes()
//			credential.EXPECT().GetToken().Return("test_token").AnyTimes()
//			cas := mocks.NewMockCrossServiceAuthorizationService(suite.ctrl)
//			cas.EXPECT().AssumeRole(gomock.Any(), gomock.Any()).Return(credential, nil).AnyTimes()
//			ds := mocks.NewMockDataSourceService(suite.ctrl)
//			ds.EXPECT().EnsureAccount(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//			ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
//			ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			ds.EXPECT().DescribeDialogInfos(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDialogInfosResp{
//				DialogDetails: &shared.DialogDetails{}}, nil).AnyTimes()
//			h := NewDescribeDialogInfosHandler(cfg, cas, ds, c3Conf, actorCli).
//				Impl.Impl.(func(ctx context.Context, req *model.DescribeDialogInfosReq) (ret *model.DescribeDialogInfosResp, err error))
//			_, err := h(context.Background(), &model.DescribeDialogInfosReq{
//				DSType:     model.DSType_MySQL,
//				InstanceId: "mysql-12345",
//				PageNumber: utils.Int32Ref(1),
//				PageSize:   utils.Int32Ref(20),
//				QueryFilter: &model.QueryFilter{
//					ShowSleepConnection: utils.BoolRef(true),
//					ProcessID:           utils.StringRef("1241"),
//					User:                utils.StringRef("user1"),
//					DB:                  utils.StringRef("db1"),
//					Host:                utils.StringRef("localhost"),
//					Command:             utils.StringRef("select"),
//					LowerExecTimeLimit:  utils.StringRef("3"),
//					State:               utils.StringRef("state"),
//					Info:                utils.StringRef("info"),
//				},
//			})
//			So(err, ShouldResemble, nil)
//		})
//	}
func (suite *DescribeDialogInfosSuite) TestDescribeDialogInfosHandler_DescribeDialogInfos_Succeed2() {
	PatchConvey("test describe succeed", suite.T(), func() {
		c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
		cfg := config.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{
			DBInternalUsers: "{\"VeDBMySQL\":\"root,repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,mysql.infoschema,mysql.session,mysql.sys\",\"MySQL\":\"repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,system user,root\",\"Postgres\":\"postgres,ccp_monitoring,byte_rds_repl,byte_rds_proxy,primaryuser\"}"}).AnyTimes()
		Mock(fwctx.GetTenantID).Return("**********")
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
		ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		ds.EXPECT().EnsureAccount(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		ds.EXPECT().DescribeDialogInfos(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDialogInfosResp{
			DialogDetails: &shared.DialogDetails{}}, nil).AnyTimes()
		c3Conf.EXPECT().GetNamespace(context.Background(), consts.C3ApplicationNamespace).Return(
			&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_internal_account"}})
		h := NewDescribeDialogInfosHandler(cfg, ds, c3Conf).
			Impl.Impl.(func(ctx context.Context, req *model.DescribeDialogInfosReq) (ret *model.DescribeDialogInfosResp, err error))
		_, err := h(context.Background(), &model.DescribeDialogInfosReq{
			DSType:     model.DSType_Postgres,
			InstanceId: "postgres-12345",
			PageNumber: utils.Int32Ref(1),
			PageSize:   utils.Int32Ref(20),
			QueryFilter: &model.QueryFilter{
				ShowSleepConnection: utils.BoolRef(true),
				ProcessID:           utils.StringRef("1241"),
				User:                utils.StringRef("user1"),
				DB:                  utils.StringRef("db1"),
				Host:                utils.StringRef("localhost"),
				Command:             utils.StringRef("select"),
				LowerExecTimeLimit:  utils.StringRef("3"),
				State:               utils.StringRef("state"),
				Info:                utils.StringRef("info"),
				ShardId:             utils.StringRef("1"),
				NodeIds:             []string{"1"},
			},
		})
		So(err, ShouldResemble, nil)
	})
}

func (suite *DescribeDialogInfosSuite) TestCheckParam() {
	c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	c3Conf.EXPECT().GetNamespace(context.Background(), consts.C3ApplicationNamespace).Return(
		&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_internal_account"}}).AnyTimes()
	ds := mocks.NewMockDataSourceService(suite.ctrl)
	cfg := config.NewMockConfigProvider(suite.ctrl)
	ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
	cfg.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{
		DBInternalUsers: "{\"VeDBMySQL\":\"root,repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,mysql.infoschema,mysql.session,mysql.sys\",\"MySQL\":\"repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,system user,root\",\"Postgres\":\"postgres,ccp_monitoring,byte_rds_repl,byte_rds_proxy,primaryuser\"}"}).AnyTimes()
	ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
	ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	h := NewDescribeDialogInfosHandler(cfg, ds, c3Conf).
		Impl.Impl.(func(ctx context.Context, req *model.DescribeDialogInfosReq) (ret *model.DescribeDialogInfosResp, err error))
	ctx := context.Background()

	testCases := []struct {
		name string
		req  *model.DescribeDialogInfosReq
		want error
	}{
		{
			name: "VeDB param error",
			req: &model.DescribeDialogInfosReq{DSType: model.DSType_VeDBMySQL, Component: model.ComponentPtr(model.Component_DBEngine),
				InstanceId: "vedbm-111",
				PageNumber: utils.Int32Ref(1),
				PageSize:   utils.Int32Ref(10)},
			want: consts.ErrorOf(model.ErrorCode_ParamError),
		},
		{
			name: "VeDB param error1",
			req: &model.DescribeDialogInfosReq{DSType: model.DSType_VeDBMySQL, Component: model.ComponentPtr(model.Component_DBEngine),
				QueryFilter: &model.QueryFilter{User: utils.StringRef("111")},
				InstanceId:  "vedbm-111",
				PageNumber:  utils.Int32Ref(1),
				PageSize:    utils.Int32Ref(10)},
			want: consts.ErrorOf(model.ErrorCode_ParamError),
		},
		{
			name: "sharding",
			req: &model.DescribeDialogInfosReq{DSType: model.DSType_MySQLSharding, Component: model.ComponentPtr(model.Component_DBEngine),
				InstanceId: "myshard-222",
				PageNumber: utils.Int32Ref(1),
				PageSize:   utils.Int32Ref(10)},
			want: consts.ErrorOf(model.ErrorCode_ParamError),
		},
		{
			name: "empty InstanceId",
			req:  &model.DescribeDialogInfosReq{DSType: model.DSType_MySQL},
			want: consts.ErrorOf(model.ErrorCode_InstanceIdParamError),
		},
	}

	for _, testCase := range testCases {
		suite.Suite.Run(testCase.name, func() {
			ret, got := h(ctx, testCase.req)
			suite.Empty(ret)
			suite.Equal(testCase.want, got)
		})
	}
}

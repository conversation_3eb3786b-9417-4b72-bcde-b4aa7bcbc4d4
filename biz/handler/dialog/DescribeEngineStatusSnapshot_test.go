package dialog

import (
	"context"
	"testing"

	bizConfig "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	tlsCli "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/tls/tls_client"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/bytedance/mockey"

	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	. "code.byted.org/luoshiqi/mockito"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
)

type DescribeEngineStatusSnapShotSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *DescribeEngineStatusSnapShotSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *DescribeEngineStatusSnapShotSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestDescribeEngineStatusSnapShotSuite(t *testing.T) {
	suite.Run(t, new(DescribeEngineStatusSnapShotSuite))
}

func (suite *DescribeEngineStatusSnapShotSuite) TestOnStarted() {
}

func (suite *DescribeEngineStatusSnapShotSuite) TestDescribeEngineStatusSnapShotHandler_DescribeEngineStatusSnapShot_Failed_2() {
	PatchConvey("para error", suite.T(), func() {
		cfg := config.NewMockConfigProvider(suite.ctrl)
		c3Cfg := mocks.NewMockC3ConfigProvider(suite.ctrl)
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
		h := NewDescribeEngineStatusSnapShotHandler(cfg, c3Cfg, ds).
			Impl.Impl.(func(ctx context.Context, req *model.DescribeEngineStatusSnapShotReq) (ret *model.DescribeEngineStatusSnapShotResp, err error))
		gotRet, err := h(context.Background(), &model.DescribeEngineStatusSnapShotReq{
			DSType:       model.DSType_MySQL,
			InstanceId:   "",
			SnapshotTime: 1234,
		})
		So(err, ShouldResemble, consts.ErrorOf(model.ErrorCode_InstanceIdParamError))
		So(gotRet, ShouldBeNil)
	})
}

func (suite *DescribeEngineStatusSnapShotSuite) TestCorrect() {
	cfg := config.NewMockConfigProvider(suite.ctrl)
	c3Cfg := mocks.NewMockC3ConfigProvider(suite.ctrl)
	ds := mocks.NewMockDataSourceService(suite.ctrl)
	ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
	ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	mock1 := mockey.Mock(tls.NewTLSClient).Return(&tlsCli.MockClient{}).Build()
	defer mock1.UnPatch()
	cfg.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{
		EnableByteRDS:          false,
		TlsZone:                "cn-guilin-boe",
		TlsServiceEndpoint:     "https://tls-cn-guilin-boe-inner.ivolces.com",
		DBInternalUsers:        "{\"VeDBMySQL\":\"root,repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,mysql.infoschema,mysql.session,mysql.sys\",\"MySQL\":\"repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,system user,root\",\"Postgres\":\"postgres,ccp_monitoring,byte_rds_repl,byte_rds_proxy,primaryuser\"}",
		TLSEngineStatusTopicV2: "{\"VeDBMySQL\":\"b289ae60-e8ca-461c-a4d9-7c373422f9b7\",\"MySQL\":\"fbfd743d-9591-40c6-be35-cfae1a0550b1\",\"Postgres\":\"9d8f9414-d606-4e3f-b24f-c0fbd8568940\"}"}).AnyTimes()
	c3Cfg.EXPECT().GetNamespace(gomock.Any(), gomock.Any()).Return(&bizConfig.C3Config{
		Application: bizConfig.Application{
			TLSServiceAccessKey: "AKLTMmMwNzM5ZWUxYjliNGUzY2FjNWZkMTg1YmZkZWQ0MTU",
			TLSServiceSecretKey: "TldWaVlUTXpNemszWlRJeE5ETTFNamhpTXpKaE56SmxPVE15TWpGbE5HSQ==",
		},
	}).AnyTimes()
	mock2 := mockey.Mock((*tlsCli.MockClient).DescribeEngineStatusSnapshot).Return(&tls.DescribeEngineStatusSnapshotResp{Status: &tls.EngineStatusLog{}},
		nil).Build()
	defer mock2.UnPatch()
	h := NewDescribeEngineStatusSnapShotHandler(cfg, c3Cfg, ds).
		Impl.Impl.(func(ctx context.Context, req *model.DescribeEngineStatusSnapShotReq) (ret *model.DescribeEngineStatusSnapShotResp, err error))
	_, err := h(context.Background(), &model.DescribeEngineStatusSnapShotReq{
		DSType:     model.DSType_MySQL,
		InstanceId: "mysql-xxx",
		NodeId:     utils.StringRef("mysql-xxx-r11"),
	})
	suite.Empty(err)
}

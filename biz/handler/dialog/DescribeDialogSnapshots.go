package dialog

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/qjpcpu/fp"
)

func (h *DescribeDialogSnapshotsHandler) DescribeDialogSnapshots(
	ctx context.Context,
	req *model.DescribeDialogSnapshotsReq,
) (ret *model.DescribeDialogSnapshotsResp, err error) {
	var (
		component                     model.Component
		tlsEngineStatusTopic          map[string]string
		nodeId                        string
		ak, sk, regionId, tlsEndpoint string
	)
	if err = h.checkParam(ctx, req); err != nil {
		return nil, err
	}
	upperLimit := time.Now()
	lowerLimit := upperLimit.AddDate(0, 0, -2)
	startTime := fp.MinInt64(fp.MaxInt64(int64(req.GetStartTime()), lowerLimit.Unix()), upperLimit.Unix())
	endTime := fp.MinInt64(fp.MaxInt64(int64(req.GetEndTime()), lowerLimit.Unix()), upperLimit.Unix())
	if h.cnf.Get(ctx).EnableByteRDS {
		c3Cfg := h.cnf.Get(ctx).C3Config
		ak = c3Cfg.TLSServiceAccessKey
		sk = c3Cfg.TLSServiceSecretKey
		regionId = c3Cfg.ByteInnerTLSServiceRegion
		tlsEndpoint = c3Cfg.ByteInnerTLSServiceEndpoint
		err = json.Unmarshal([]byte(c3Cfg.TLSEngineStatusTopicV2), &tlsEngineStatusTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSEngineStatusTopic str failed %+v", err)
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	} else {
		cnf := h.cnf.Get(ctx)
		regionId = cnf.TlsZone
		c3 := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		ak = c3.TLSServiceAccessKey
		sk = c3.TLSServiceSecretKey
		tlsEndpoint = cnf.TlsServiceEndpoint

		// 获取tls相关topic
		err = json.Unmarshal([]byte(h.cnf.Get(ctx).TLSEngineStatusTopicV2), &tlsEngineStatusTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSEngineStatusTopic str failed %+v", err)
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	}
	cli := tls.NewTLSClient(&tls.ConnectionInfo{
		Endpoint:        tlsEndpoint,
		AccessKeyID:     ak,
		AccessKeySecret: sk,
		Region:          regionId,
		TopicId:         tlsEngineStatusTopic[req.GetDSType().String()],
	}, h.cnf)
	if req.IsSetComponent() {
		component = req.GetComponent()
	} else {
		component = model.Component_DBEngine
	}
	if req.IsSetNodeId() {
		nodeId = req.GetNodeId()
	} else {
		nodeId = ""
	}
	var resp *tls.DescribeDialogSnapshotsResp
	if resp, err = cli.DescribeDialogSnapshots(ctx, &tls.DescribeDialogSnapshotsReq{
		TenantId:     fwctx.GetTenantID(ctx),
		InstanceId:   req.InstanceId,
		StartTime:    startTime,
		EndTime:      endTime,
		Component:    component.String(),
		NodeId:       nodeId,
		InstanceType: req.GetDSType(),
	}); err != nil {
		return
	}

	ret = &model.DescribeDialogSnapshotsResp{
		Total: int32(resp.Total),
	}
	fp.StreamOf(resp.Snapshots).Map(func(snapshot *tls.Snapshot) *model.DialogSnapshot {
		return &model.DialogSnapshot{
			SnapshotTime: int32(snapshot.CollectTime),
		}
	}).ToSlice(&ret.DialogSnapshots)

	// pagination
	realCount := int32(len(resp.Snapshots))
	if req.GetPageNumber() > 0 || req.GetPageSize() > 0 {
		begIdx := fp.MinInt32((req.GetPageNumber()-1)*req.GetPageSize(), realCount)
		endIdx := fp.MinInt32(begIdx+req.GetPageSize(), realCount)
		ret.DialogSnapshots = ret.DialogSnapshots[begIdx:endIdx]
	}
	return
}

func (h *DescribeDialogSnapshotsHandler) checkParam(ctx context.Context, req *model.DescribeDialogSnapshotsReq) error {
	if req.InstanceId == "" {
		return consts.ErrorOf(model.ErrorCode_InstanceIdParamError)
	}
	if req.GetPageNumber() <= 0 || req.GetPageSize() <= 0 {
		return consts.ErrorOf(model.ErrorCode_PaginationParamError)
	}
	if req.GetStartTime() >= req.GetEndTime() {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "The start time cannot be greater than the end time.")
	}
	if !h.ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedType(req.DSType)) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.ds.CheckInstanceState(ctx, req.InstanceId, conv.ToSharedType(req.DSType), true); err != nil {
		return err
	}
	return nil
}

type DescribeDialogSnapshotsHandler struct {
	cnf    config.ConfigProvider
	c3Conf c3.ConfigProvider
	ds     datasource.DataSourceService
}

func NewDescribeDialogSnapshotsHandler(
	cnf config.ConfigProvider,
	C3Config c3.ConfigProvider,
	ds datasource.DataSourceService,
) handler.HandlerImplementationEnvolope {
	hder := &DescribeDialogSnapshotsHandler{
		cnf:    cnf,
		c3Conf: C3Config,
		ds:     ds,
	}
	return handler.NewHandler(hder.DescribeDialogSnapshots)
}

package dialog

import (
	"context"
	"encoding/json"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls"
	"code.byted.org/infcs/ds-lib/common/log"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

func NewDescribeDialogHotspotsHandler(
	cnf config.ConfigProvider,
	C3Config c3.ConfigProvider,
	ds datasource.DataSourceService,
) handler.HandlerImplementationEnvolope {
	hder := &DescribeDialogHotspotsHandler{
		Cnf:    cnf,
		C3Conf: C3Config,
		Ds:     ds,
	}
	return handler.NewHandler(hder.DescribeDialogHotspots)
}

// DescribeDialogHotspots 查询热点SQL，该方法由于没有Service层，因此Copilot拷贝了一份代码，若修改逻辑，需要同步修改Copilot逻辑
func (h *DescribeDialogHotspotsHandler) DescribeDialogHotspots(
	ctx context.Context,
	req *model.DescribeDialogHotspotsReq,
) (ret *model.DescribeDialogHotspotsResp, err error) {
	var (
		component                     model.Component
		tlsDialogDetailTopic          map[string]string
		dbInternalUsers               map[string]string
		ak, sk, regionId, tlsEndpoint string
		nodeIds, internalUsers        []string
	)
	if err = h.checkParam(ctx, req); err != nil {
		return nil, err
	}

	if req.IsSetComponent() {
		component = req.GetComponent()
	} else {
		component = model.Component_DBEngine
	}
	if req.IsSetNodeIds() {
		nodeIds = req.GetNodeIds()
	}
	err = json.Unmarshal([]byte(h.Cnf.Get(ctx).DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		internalUsers = strings.Split(dbInternalUsers[req.GetInstanceType().String()], ",")
	}
	rreq := &tls.DescribeDialogHotspotsReq{
		InstanceId:    req.InstanceId,
		InstanceType:  req.GetInstanceType().String(),
		Component:     component.String(),
		SearchParam:   req.GetSearchParam(),
		InternalUsers: internalUsers,
		StartTime:     req.GetStartTime(),
		EndTime:       req.GetEndTime(),
		NodeIds:       nodeIds,
	}
	if h.Cnf.Get(ctx).EnableByteRDS {
		c3Cfg := h.Cnf.Get(ctx).C3Config
		ak = c3Cfg.TLSServiceAccessKey
		sk = c3Cfg.TLSServiceSecretKey
		regionId = c3Cfg.ByteInnerTLSServiceRegion
		tlsEndpoint = c3Cfg.ByteInnerTLSServiceEndpoint
		err = json.Unmarshal([]byte(c3Cfg.TLSDialogDetailTopicV2), &tlsDialogDetailTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSDialogDetailTopic str failed %+v", err)
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	} else {
		cnf := h.Cnf.Get(ctx)
		regionId = cnf.TlsZone
		tlsEndpoint = cnf.TlsServiceEndpoint
		c3 := h.C3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		ak = c3.TLSServiceAccessKey
		sk = c3.TLSServiceSecretKey
		err = json.Unmarshal([]byte(h.Cnf.Get(ctx).TLSDialogDetailTopicV2), &tlsDialogDetailTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSDialogDetailTopic str failed %+v", err)
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	}

	tlsCli := tls.NewTLSClient(&tls.ConnectionInfo{
		Endpoint:        tlsEndpoint,
		AccessKeyID:     ak,
		AccessKeySecret: sk,
		Region:          regionId,
		TopicId:         tlsDialogDetailTopic[req.GetInstanceType().String()],
	}, h.Cnf)

	var resp *tls.DescribeDialogHotspotsResp
	if resp, err = tlsCli.DescribeDialogHotspots(ctx, rreq); err != nil {
		log.Warn(ctx, "Get DialogHotspots failed %s", err)
		return nil, err
	}
	ret = &model.DescribeDialogHotspotsResp{
		SqlHotspots: resp.SqlHotspots,
	}
	return ret, nil
}

func (h *DescribeDialogHotspotsHandler) checkParam(ctx context.Context, req *model.DescribeDialogHotspotsReq) error {
	if req.InstanceId == "" {
		return consts.ErrorOf(model.ErrorCode_InstanceIdParamError)
	}
	if req.GetRegionId() == "" {
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if req.GetStartTime() < 0 || req.GetEndTime() <= 0 {
		return consts.ErrorOf(model.ErrorCode_QueryTimeRangeError)
	}

	if req.GetStartTime() >= req.GetEndTime() {
		return consts.ErrorOf(model.ErrorCode_QueryTimeRangeError)
	}
	if !h.Ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedType(req.GetInstanceType())) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.Ds.CheckInstanceState(ctx, req.InstanceId, conv.ToSharedType(req.GetInstanceType()), false); err != nil {
		return err
	}
	return nil
}

type DescribeDialogHotspotsHandler struct {
	Cnf    config.ConfigProvider
	C3Conf c3.ConfigProvider
	Ds     datasource.DataSourceService
}

package dialog

import (
	"context"
	"testing"

	bizConfig "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"

	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
)

type KillProcessSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *KillProcessSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *KillProcessSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestKillProcessSuite(t *testing.T) {
	suite.Run(t, new(KillProcessSuite))
}

func (suite *KillProcessSuite) TestOnStarted() {
}

func (suite *KillProcessSuite) TestKillProcessHandler_KillProcess_Succeed() {
	PatchConvey("kill process succeed", suite.T(), func() {
		Mock(fwctx.GetTenantID).Return("**********").Build()
		Mock(handler.GetInstanceSession).Return("session-124124", nil).Build()
		Mock(handler.GiveBackInstanceSession).Return().Build()
		component := model.Component_DBEngine
		operationRecordSvc := mocks.NewMockOperateRecordService(suite.ctrl)
		operationRecordSvc.EXPECT().CreateDasRecord(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		cnf := mocks.NewMockConfigProvider(suite.ctrl)
		c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
		listInstancePodsResp := &datasource.ListInstancePodsResp{Data: []*shared.KubePod{{NodeId: "Node1", Role: model.NodeRole_ReadOnly.String(), KubeCluster: "cluster1", PodIP: "111"}, {NodeId: "Node2", Role: model.NodeType_Primary.String(), KubeCluster: "cluster2", PodIP: "111"}}, Total: 2}
		ds.EXPECT().ListInstancePods(gomock.Any(), gomock.Any()).Return(listInstancePodsResp, nil).AnyTimes()
		c3Conf.EXPECT().GetNamespace(gomock.Any(), gomock.Any()).Return(&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_account_password_gen_key", DBWAccountName: "dbw_account_name"}}).AnyTimes()
		cnf.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{}).AnyTimes()
		ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
		ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		ds.EXPECT().EnsureAccount(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		ds.EXPECT().KillProcess(gomock.Any(), gomock.Any()).Return(&datasource.KillProcessResp{FailInfoList: []*shared.KillFailInfo{}}, nil).AnyTimes()
		//kindClient.EXPECT().GetPod(gomock.Any(), gomock.Any(), gomock.Any()).Return(&corev1.Pod{}, nil).AnyTimes()
		h := NewKillProcessHandler(ds, cnf, c3Conf, operationRecordSvc).
			Impl.Impl.(func(ctx context.Context, req *model.KillProcessReq) (ret *model.KillProcessResp, err error))
		gotRet, err := h(context.Background(), &model.KillProcessReq{
			DSType:     model.DSType_MySQL,
			InstanceId: "mysql-12345",
			ProcessInfo: []*model.ProcessInfo{
				{
					ProcessIDs: []string{"ID1", "ID2"},
					NodeId:     "Node1",
				},
				{
					ProcessIDs: []string{"ID3", "ID4"},
					NodeId:     "Node2",
				},
			},
			Component: &component,
		})
		So(err, ShouldResemble, nil)
		So(gotRet, ShouldResemble, &model.KillProcessResp{
			FailInfoList: nil,
		})
	})
}

func (suite *KillProcessSuite) TestKillProcessHandler_KillProcess_Succeed1() {
	PatchConvey("kill process succeed", suite.T(), func() {
		Mock(fwctx.GetTenantID).Return("**********").Build()
		Mock(handler.GetInstanceSession).Return("session-124124", nil).Build()
		Mock(handler.GiveBackInstanceSession).Return().Build()
		component := model.Component_DBEngine

		//kindClient := mocks.NewMockKindClient(suite.ctrl)
		operationRecordSvc := mocks.NewMockOperateRecordService(suite.ctrl)
		operationRecordSvc.EXPECT().CreateDasRecord(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		cnf := mocks.NewMockConfigProvider(suite.ctrl)
		c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
		listInstancePodsResp := &datasource.ListInstancePodsResp{Data: []*shared.KubePod{{NodeId: "Node1", Role: model.NodeRole_ReadOnly.String(), KubeCluster: "cluster1", PodIP: "111"}, {NodeId: "Node2", Role: model.NodeType_Primary.String(), KubeCluster: "cluster2", PodIP: "111"}}, Total: 2}
		ds.EXPECT().ListInstancePods(gomock.Any(), gomock.Any()).Return(listInstancePodsResp, nil).AnyTimes()
		ds.EXPECT().EnsureAccount(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		ds.EXPECT().KillProcess(gomock.Any(), gomock.Any()).Return(&datasource.KillProcessResp{FailInfoList: []*shared.KillFailInfo{}}, nil).AnyTimes()
		c3Conf.EXPECT().GetNamespace(gomock.Any(), gomock.Any()).Return(&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_account_password_gen_key", DBWAccountName: "dbw_account_name"}}).AnyTimes()
		cnf.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{}).AnyTimes()
		ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
		ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		h := NewKillProcessHandler(ds, cnf, c3Conf, operationRecordSvc).
			Impl.Impl.(func(ctx context.Context, req *model.KillProcessReq) (ret *model.KillProcessResp, err error))
		gotRet, err := h(context.Background(), &model.KillProcessReq{
			DSType:     model.DSType_VeDBMySQL,
			InstanceId: "vedbm-12345",
			ProcessInfo: []*model.ProcessInfo{
				{
					ProcessIDs: []string{"ID1", "ID2"},
					NodeId:     "Node1",
				},
				{
					ProcessIDs: []string{"ID3", "ID4"},
					NodeId:     "Node2",
				},
			},
			Component: &component,
		})
		So(err, ShouldResemble, nil)
		So(gotRet, ShouldResemble, &model.KillProcessResp{
			FailInfoList: nil,
		})
	})
}

func (suite *KillProcessSuite) TestKillProcessHandler_KillProcess_Succeed2() {
	PatchConvey("kill process succeed", suite.T(), func() {
		Mock(fwctx.GetTenantID).Return("**********").Build()
		Mock(handler.GetInstanceSession).Return("session-124124", nil).Build()
		Mock(handler.GiveBackInstanceSession).Return().Build()
		component := model.Component_DBEngine
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		cnf := mocks.NewMockConfigProvider(suite.ctrl)
		c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
		operationRecordSvc := mocks.NewMockOperateRecordService(suite.ctrl)
		operationRecordSvc.EXPECT().CreateDasRecord(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		listInstancePodsResp := &datasource.ListInstancePodsResp{Data: []*shared.KubePod{{NodeId: "Node1", Role: model.NodeRole_ReadOnly.String(), KubeCluster: "cluster1", PodIP: "111"}, {NodeId: "Node2", Role: model.NodeType_Primary.String(), KubeCluster: "cluster2", PodIP: "111"}}, Total: 2}
		ds.EXPECT().ListInstancePods(gomock.Any(), gomock.Any()).Return(listInstancePodsResp, nil).AnyTimes()
		ds.EXPECT().EnsureAccount(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		ds.EXPECT().KillProcess(gomock.Any(), gomock.Any()).Return(&datasource.KillProcessResp{FailInfoList: []*shared.KillFailInfo{}}, nil).AnyTimes()
		c3Conf.EXPECT().GetNamespace(gomock.Any(), gomock.Any()).Return(&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_account_password_gen_key", DBWAccountName: "dbw_account_name"}}).AnyTimes()
		cnf.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{}).AnyTimes()
		ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
		ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		h := NewKillProcessHandler(ds, cnf, c3Conf, operationRecordSvc).
			Impl.Impl.(func(ctx context.Context, req *model.KillProcessReq) (ret *model.KillProcessResp, err error))
		gotRet, err := h(context.Background(), &model.KillProcessReq{
			DSType:     model.DSType_Postgres,
			InstanceId: "postgres-9d56065ba5b2",
			ProcessInfo: []*model.ProcessInfo{
				{
					ProcessIDs: []string{"ID1", "ID2"},
					NodeId:     "Node1",
				},
				{
					ProcessIDs: []string{"ID3", "ID4"},
					NodeId:     "Node2",
				},
			},
			Component: &component,
		})
		So(err, ShouldResemble, nil)
		So(gotRet, ShouldResemble, &model.KillProcessResp{
			FailInfoList: nil,
		})
	})
}

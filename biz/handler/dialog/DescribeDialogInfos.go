package dialog

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"encoding/json"
	"github.com/qjpcpu/fp"
	"k8s.io/utils/pointer"
	"strings"
)

func (h *DescribeDialogInfosHandler) DescribeDialogInfos(
	ctx context.Context,
	req *model.DescribeDialogInfosReq,
) (*model.DescribeDialogInfosResp, error) {
	if err := h.checkParam(ctx, req); err != nil {
		return nil, err
	}
	ret := &model.DescribeDialogInfosResp{
		Details: &model.DialogDetails{},
	}
	var (
		component       model.Component
		dbInternalUsers map[string]string
		dbwAdmin        string
		dbwAdminPwd     string
		internalUsers   []string
		nodeId          string
	)
	var queryFilter *shared.DialogQueryFilter
	cnf := h.cnf.Get(ctx)
	if req.GetQueryFilter() != nil {
		queryFilter = &shared.DialogQueryFilter{
			ProcessID:          req.GetQueryFilter().GetProcessID(),
			User:               req.GetQueryFilter().GetUser(),
			Host:               req.GetQueryFilter().GetHost(),
			DB:                 req.GetQueryFilter().GetDB(),
			Command:            req.GetQueryFilter().GetCommand(),
			LowerExecTimeLimit: req.GetQueryFilter().GetLowerExecTimeLimit(),
			State:              req.GetQueryFilter().GetState(),
			Info:               req.GetQueryFilter().GetInfo(),
			NodeId:             req.GetQueryFilter().GetNodeId(),
			EndpointName:       req.GetQueryFilter().GetEndpointName(),
			PSM:                req.GetQueryFilter().GetPSM(),
			ShardId:            req.GetQueryFilter().GetShardId(),
			NodeIds:            req.GetQueryFilter().GetNodeIds(),
			PlanSummary:        req.GetQueryFilter().GetPlanSummary(),
			Desc:               req.GetQueryFilter().GetDesc(),
			Namespace:          req.GetQueryFilter().GetNamespace(),
		}
		nodeId = req.GetQueryFilter().GetNodeId()
		if req.GetQueryFilter().IsSetShowSleepConnection() {
			if req.GetQueryFilter().GetShowSleepConnection() {
				queryFilter.ShowSleepConnection = "true"
			} else {
				queryFilter.ShowSleepConnection = "false"
			}
		}
		if req.GetQueryFilter().IsSetNodeType() {
			queryFilter.NodeType = NodeRoleToString(req.GetQueryFilter().GetNodeType())
		}
		if req.GetQueryFilter().IsSetIsFuzzy() {
			queryFilter.IsFuzzy = req.GetQueryFilter().GetIsFuzzy()
		} else {
			queryFilter.IsFuzzy = true // 默认是模糊查询
		}
		if req.GetQueryFilter().IsSetSQLTemplate() {
			queryFilter.SqlTemplate = req.GetQueryFilter().GetSQLTemplate()
		}
		if req.GetQueryFilter().IsSetSqlType() {
			queryFilter.SqlType = req.GetQueryFilter().GetSqlType()
		}
	}
	if req.IsSetComponent() {
		component = req.GetComponent()
	} else {
		component = model.Component_DBEngine
	}
	internalUsers = make([]string, 0)
	err := json.Unmarshal([]byte(cnf.DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		internalUsers = strings.Split(dbInternalUsers[req.GetDSType().String()], ",")
	}
	ret = &model.DescribeDialogInfosResp{
		Details: &model.DialogDetails{},
	}
	if req.GetDSType() == model.DSType_ByteRDS {
		dbwAdmin = cnf.C3Config.DBWAccountName
		dbwAdminPwd = cnf.C3Config.DbwPwd
	} else if req.GetDSType() == model.DSType_ByteDoc {
		dbwAdmin = cnf.C3Config.DBWAccountName
		// 需要基于clusterName动态生成
	} else {
		c3Cfg := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		dbwAdmin = c3Cfg.DBWAccountName
		dbwAdminPwd = handler.GetAccountPassword(c3Cfg.DbwAccountPasswordGenKey, req.GetInstanceId())
	}
	ds := &shared.DataSource{
		Type:       shared.DataSourceType(req.GetDSType()),
		LinkType:   shared.Volc,
		User:       dbwAdmin,
		Password:   dbwAdminPwd,
		InstanceId: req.GetInstanceId(),
		NodeId:     nodeId,
		Region:     req.GetRegionId(),
	}
	// 验证账号是否存在
	if err := h.ds.EnsureAccount(ctx, &datasource.EnsureAccountReq{Source: ds}); err != nil {
		log.Warn(ctx, "Check dbw_admin account failed %s", err)
		return nil, err
	}
	dialogInfosReq := &datasource.DescribeDialogInfosReq{
		Offset:        0,
		Limit:         10000, // 默认返回10000条
		Source:        ds,
		QueryFilter:   queryFilter,
		InternalUsers: internalUsers,
		Component:     component.String(),
	}
	dialogInfosResp, err := h.ds.DescribeDialogInfos(ctx, dialogInfosReq)
	if err != nil {
		log.Warn(ctx, "err is %s", err)
		return nil, err
	}
	//nolint:byted_use_uninitialized_object
	// nolint:byted_nilcheck:dialogInfosResp.DialogDetails
	if err := fp.StreamOf(dialogInfosResp.DialogDetails.Details).Map(func(detail *shared.DialogDetail) *model.DialogDetail {
		return &model.DialogDetail{
			ProcessID:     detail.ProcessID,
			User:          detail.User,
			Host:          detail.Host,
			DB:            detail.DB,
			Command:       detail.Command,
			Time:          detail.Time,
			State:         detail.State,
			Info:          detail.Info,
			BlockingPid:   detail.BlockingPid,
			NodeId:        pointer.String(detail.NodeId),
			NodeType:      pointer.String(detail.NodeType),
			PSM:           &detail.PSM,
			EndpointName:  &detail.EndpointName,
			EndpointId:    &detail.EndpointId,
			PlanSummary:   &detail.PlanSummary,
			Namespace:     &detail.Namespace,
			Desc:          &detail.Desc,
			SQLTemplate:   &detail.SqlTemplate,
			SQLTemplateID: &detail.SqlTemplateID,
			SqlType:       &detail.SqlType,
		}
	}).ToSlice(&ret.Details.DialogDetails); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	// 默认按Time倒序
	//SortDialog(ret.Details.DialogDetails, shared.DESC, "Time")
	ret.Details.Total = dialogInfosResp.DialogDetails.Total
	return ret, nil
}

func (h *DescribeDialogInfosHandler) checkParam(ctx context.Context, req *model.DescribeDialogInfosReq) error {
	if req.InstanceId == "" {
		return consts.ErrorOf(model.ErrorCode_InstanceIdParamError)
	}
	// 校验实例owner
	if !h.ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedType(req.DSType)) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.ds.CheckInstanceState(ctx, req.InstanceId, conv.ToSharedType(req.DSType), true); err != nil {
		return err
	}
	// 实例类型为veDB时,nodeID和NodeType为必传
	if req.DSType == model.DSType_VeDBMySQL && req.GetComponent() == model.Component_DBEngine {
		if req.GetQueryFilter() == nil {
			return consts.ErrorOf(model.ErrorCode_ParamError)
		} else {
			if !req.GetQueryFilter().IsSetNodeId() {
				return consts.ErrorOf(model.ErrorCode_ParamError)
			}
		}
	}
	// 实例类型为sharding时,shardId,NodeIds为必传
	if req.DSType == model.DSType_MySQLSharding && req.GetComponent() == model.Component_DBEngine {
		if req.GetQueryFilter() == nil {
			return consts.ErrorOf(model.ErrorCode_ParamError)
		} else {
			if !req.GetQueryFilter().IsSetShardId() || !req.GetQueryFilter().IsSetNodeIds() {
				return consts.ErrorOf(model.ErrorCode_ParamError)
			}
		}
	}
	return nil
}

type DescribeDialogInfosHandler struct {
	cnf          config.ConfigProvider
	crossAuthSvc crossauth.CrossServiceAuthorizationService
	ds           datasource.DataSourceService
	c3Conf       c3.ConfigProvider
}

func NewDescribeDialogInfosHandler(
	cnf config.ConfigProvider,
	ds datasource.DataSourceService,
	c3 c3.ConfigProvider,
) handler.HandlerImplementationEnvolope {
	hder := &DescribeDialogInfosHandler{
		cnf:    cnf,
		ds:     ds,
		c3Conf: c3,
	}
	return handler.NewHandler(hder.DescribeDialogInfos)
}

func NodeRoleToString(p model.NodeRole) string {
	switch p {
	case model.NodeRole_Primary:
		return "Primary"
	case model.NodeRole_Secondary:
		return "Secondary"
	case model.NodeRole_Hidden:
		return "Hidden"
	case model.NodeRole_ReadOnly:
		return "ReadOnly"
	default:
		return "Primary"
	}

}

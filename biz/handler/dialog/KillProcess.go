package dialog

import (
	"context"
	"encoding/json"

	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/qjpcpu/fp"
)

type killInfo struct {
	NodeId     string
	PodRole    string
	PodIp      string
	ShardId    string
	ProcessIDs []string
}

func (h *KillProcessHandler) checkParam(ctx context.Context, req *model.KillProcessReq) error {
	if req.InstanceId == "" {
		return consts.ErrorOf(model.ErrorCode_InstanceIdParamError)
	}
	// 兼容前端传空值
	if !req.IsSetInstanceType() && req.GetDSType().String() != "<UNSET>" {
		req.InstanceType = model.DSTypePtr(req.DSType)
	}
	// check instance type
	if req.GetInstanceType() != model.DSType_MySQL && req.GetInstanceType() != model.DSType_VeDBMySQL &&
		req.GetInstanceType() != model.DSType_ByteRDS && req.GetInstanceType() != model.DSType_MySQLSharding &&
		req.GetInstanceType() != model.DSType_Postgres && req.GetInstanceType() != model.DSType_ByteDoc &&
		req.GetInstanceType() != model.DSType_Mongo && req.GetInstanceType() != model.DSType_MetaMySQL {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	// 校验实例owner
	if !h.ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedType(req.DSType)) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.ds.CheckInstanceState(ctx, req.InstanceId, conv.ToSharedType(req.DSType), true); err != nil {
		return err
	}
	return nil
}

func (h *KillProcessHandler) KillProcess(
	ctx context.Context,
	req *model.KillProcessReq,
) (ret *model.KillProcessResp, err error) {
	var failedProcessList []*model.FailInfo
	defer func() {
		rule, _ := json.Marshal(req.ProcessInfo)
		operationRecord := &entity.OperationRecord{
			OperationType: model.DasOperationCategory_Kill.String(),
			InstanceId:    req.GetInstanceId(),
			InstanceType:  req.GetDSType().String(),
			TriggerType:   model.TriggerType_Manual.String(),
			Action:        model.DasAction_ManualKill.String(),
			Extra:         string(rule),
			TenantId:      fwctx.GetTenantID(ctx),
			UserId:        fwctx.GetUserID(ctx),
		}
		if err != nil || len(failedProcessList) > 0 {
			operationRecord.Status = model.OpsTaskState_RunningFailed.String()
			log.Warn(ctx, "Manual Kill error:%+v", err)
		} else {
			operationRecord.Status = model.OpsTaskState_Done.String()
		}
		err := h.operateRecordService.CreateDasRecord(ctx, operationRecord)
		if err != nil {
			log.Warn(ctx, "create das record failed %+v", err)
		}
	}()
	// 参数检查
	if err = h.checkParam(ctx, req); err != nil {
		return nil, err
	}
	ret, err = h.killGeneralProcess(ctx, req)
	if ret != nil {
		failedProcessList = ret.FailInfoList
	}
	return ret, err
}

func (h *KillProcessHandler) killGeneralProcess(ctx context.Context, req *model.KillProcessReq) (*model.KillProcessResp, error) {
	ret := &model.KillProcessResp{}
	for _, node := range req.ProcessInfo {
		obj := &killInfo{
			ProcessIDs: node.ProcessIDs,
			NodeId:     node.NodeId,
			ShardId:    node.GetShardId(), // 适配sharding
		}
		res, err := h.killProcessByNodeAddr(ctx, req, obj)
		if err != nil {
			return nil, err
		}
		ret.FailInfoList = append(ret.FailInfoList, res.FailInfoList...)
	}
	return ret, nil
}

func (h *KillProcessHandler) killProcessByNodeAddr(ctx context.Context, req *model.KillProcessReq, pod *killInfo) (*model.KillProcessResp, error) {
	var (
		dbwAdmin    string
		dbwAdminPwd string
	)
	ret := &model.KillProcessResp{}
	cnf := h.cnf.Get(ctx)
	if req.GetDSType() == model.DSType_ByteRDS {
		dbwAdmin = cnf.C3Config.DBWAccountName
		dbwAdminPwd = cnf.C3Config.DbwPwd
	} else if req.GetDSType() == model.DSType_ByteDoc {
		dbwAdmin = cnf.C3Config.DBWAccountName
		// 需要基于clusterName动态生成
	} else {
		c3Cfg := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		dbwAdmin = c3Cfg.DBWAccountName
		dbwAdminPwd = handler.GetAccountPassword(c3Cfg.DbwAccountPasswordGenKey, req.GetInstanceId())
	}
	ds := &shared.DataSource{
		Type:       shared.DataSourceType(req.GetDSType()),
		LinkType:   shared.Volc,
		User:       dbwAdmin,
		Password:   dbwAdminPwd,
		InstanceId: req.GetInstanceId(),
		NodeId:     pod.NodeId,
		Region:     req.GetRegionId(),
	}
	// 验证账号是否存在
	if err := h.ds.EnsureAccount(ctx, &datasource.EnsureAccountReq{Source: ds}); err != nil {
		log.Warn(ctx, "Check dbw_admin account failed %s", err)
		return nil, err
	}
	killProcessReq := &datasource.KillProcessReq{
		Source:     ds,
		ProcessIDs: pod.ProcessIDs,
		NodeId:     pod.NodeId,
		ShardId:    pod.ShardId,
		Region:     req.GetRegionId(),
	}
	killResp, err := h.ds.KillProcess(ctx, killProcessReq)
	if err != nil {
		log.Warn(ctx, "err is %s", err)
		return nil, err
	}
	if err := fp.StreamOf(killResp.FailInfoList).Map(func(info *shared.KillFailInfo) *model.FailInfo {
		return &model.FailInfo{
			ProcessId:    utils.StringRef(info.ProcessId),
			ErrorMessage: utils.StringRef(info.ErrorMessage),
		}
	}).ToSlice(&ret.FailInfoList); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	return ret, nil
}

type KillProcessHandler struct {
	ds                   datasource.DataSourceService
	cnf                  config.ConfigProvider
	c3Conf               c3.ConfigProvider
	operateRecordService operate_record.OperateRecordService
}

func NewKillProcessHandler(
	ds datasource.DataSourceService,
	cnf config.ConfigProvider,
	c3Conf c3.ConfigProvider,
	OperateRecordService operate_record.OperateRecordService,
) handler.HandlerImplementationEnvolope {
	hder := &KillProcessHandler{
		ds:                   ds,
		cnf:                  cnf,
		c3Conf:               c3Conf,
		operateRecordService: OperateRecordService,
	}
	return handler.NewHandler(hder.KillProcess)
}

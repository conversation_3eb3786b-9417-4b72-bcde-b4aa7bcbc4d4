package dialog

import (
	"context"
	"encoding/json"
	"strings"

	"code.byted.org/infcs/ds-lib/common/utils"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/qjpcpu/fp"
)

func (h *DescribeDialogDetailSnapshotHandler) DescribeDialogDetailSnapshot(
	ctx context.Context,
	req *model.DescribeDialogDetailSnapshotReq,
) (ret *model.DescribeDialogDetailSnapshotResp, err error) {
	var (
		component                     model.Component
		tlsDialogDetailTopic          map[string]string
		nodeId                        string
		dbInternalUsers               map[string]string
		ak, sk, regionId, tlsEndpoint string
		queryFilter                   *shared.DialogQueryFilter
	)
	if err = h.checkParam(ctx, req); err != nil {
		return nil, err
	}
	internalUsers := make([]string, 0)
	cnf := h.cnf.Get(ctx)
	err = json.Unmarshal([]byte(cnf.DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		internalUsers = strings.Split(dbInternalUsers[req.GetDSType().String()], ",")
	}
	if h.cnf.Get(ctx).EnableByteRDS {
		c3Cfg := h.cnf.Get(ctx).C3Config
		ak = c3Cfg.TLSServiceAccessKey
		sk = c3Cfg.TLSServiceSecretKey
		regionId = c3Cfg.ByteInnerTLSServiceRegion
		tlsEndpoint = c3Cfg.ByteInnerTLSServiceEndpoint
		err = json.Unmarshal([]byte(c3Cfg.TLSDialogDetailTopicV2), &tlsDialogDetailTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSDialogDetailTopic str failed %+v", err)
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	} else {
		regionId = cnf.TlsZone
		tlsEndpoint = cnf.TlsServiceEndpoint
		c3 := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		ak = c3.TLSServiceAccessKey
		sk = c3.TLSServiceSecretKey
		// 获取tls相关topic
		err = json.Unmarshal([]byte(h.cnf.Get(ctx).TLSDialogDetailTopicV2), &tlsDialogDetailTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSDialogDetailTopic str failed %+v", err)
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	}
	tlsCli := tls.NewTLSClient(&tls.ConnectionInfo{
		Endpoint:        tlsEndpoint,
		AccessKeyID:     ak,
		AccessKeySecret: sk,
		Region:          regionId,
		TopicId:         tlsDialogDetailTopic[req.GetDSType().String()],
	}, h.cnf)
	if req.IsSetComponent() {
		component = req.GetComponent()
	} else {
		component = model.Component_DBEngine
	}
	if req.IsSetNodeId() {
		nodeId = req.GetNodeId()
	}
	if req.GetQueryFilter() != nil {
		queryFilter = &shared.DialogQueryFilter{
			ProcessID:          req.GetQueryFilter().GetProcessID(),
			User:               req.GetQueryFilter().GetUser(),
			Host:               req.GetQueryFilter().GetHost(),
			DB:                 req.GetQueryFilter().GetDB(),
			Command:            req.GetQueryFilter().GetCommand(),
			LowerExecTimeLimit: req.GetQueryFilter().GetLowerExecTimeLimit(),
			State:              req.GetQueryFilter().GetState(),
			Info:               req.GetQueryFilter().GetInfo(),
			NodeId:             req.GetQueryFilter().GetNodeId(),
			EndpointName:       req.GetQueryFilter().GetEndpointName(),
			NodeIds:            req.GetQueryFilter().GetNodeIds(),
			PSM:                req.GetQueryFilter().GetPSM(),
			Desc:               req.GetQueryFilter().GetDesc(),
			PlanSummary:        req.GetQueryFilter().GetPlanSummary(),
			Namespace:          req.GetQueryFilter().GetNamespace(),
		}
		if req.GetQueryFilter().IsSetShowSleepConnection() {
			if req.GetQueryFilter().GetShowSleepConnection() {
				queryFilter.ShowSleepConnection = "true"
			} else {
				queryFilter.ShowSleepConnection = "false"
			}
		}
		if req.GetQueryFilter().IsSetNodeType() {
			queryFilter.NodeType = NodeRoleToString(req.GetQueryFilter().GetNodeType())
		}
	}
	var resp *tls.DescribeDialogDetailSnapshotResp
	if resp, err = tlsCli.DescribeDialogDetailSnapshot(ctx, &tls.DescribeDialogDetailSnapshotReq{
		TenantId:      fwctx.GetTenantID(ctx),
		InstanceId:    req.InstanceId,
		CollectTime:   int64(req.SnapshotTime),
		Component:     component.String(),
		NodeId:        nodeId,
		InternalUsers: internalUsers,
		StartTime:     req.GetStartTime(),
		EndTime:       req.GetEndTime(),
		SQLTemplateID: req.GetSQLTemplateID(),
		QueryFilter:   queryFilter,
		InstanceType:  req.GetDSType(),
	}); err != nil {
		return nil, err
	}

	ret = &model.DescribeDialogDetailSnapshotResp{
		Total: int32(resp.Total),
	}
	begIdx := fp.MinInt32((req.GetPageNumber()-1)*req.GetPageSize(), int32(resp.Total))
	endIdx := fp.MinInt32(begIdx+req.GetPageSize(), int32(resp.Total))
	details := resp.Details[begIdx:endIdx]

	fp.StreamOf(details).Map(func(detail *tls.DialogLog) *model.DialogDetail {
		return &model.DialogDetail{
			ProcessID:    detail.ProcessID,
			User:         detail.User,
			Host:         detail.Host,
			DB:           detail.DB,
			Command:      detail.Command,
			Time:         detail.Time,
			State:        detail.State,
			Info:         detail.Info,
			NodeId:       &detail.NodeId,
			PSM:          &detail.PSM,
			SnapshotTime: utils.Int32Ref(int32(detail.CollectTime)),
			PlanSummary:  &detail.PlanSummary,
			Desc:         &detail.Desc,
			Namespace:    &detail.Namespace,
		}
	}).ToSlice(&ret.DialogDetails)
	return
}

func (h *DescribeDialogDetailSnapshotHandler) checkParam(ctx context.Context, req *model.DescribeDialogDetailSnapshotReq) error {
	if req.InstanceId == "" {
		return consts.ErrorOf(model.ErrorCode_InstanceIdParamError)
	}
	if req.GetPageNumber() <= 0 || req.GetPageSize() <= 0 {
		return consts.ErrorOf(model.ErrorCode_PaginationParamError)
	}
	if req.IsSetStartTime() || req.IsSetEndTime() {
		if req.GetStartTime() >= req.GetEndTime() {
			return consts.ErrorOf(model.ErrorCode_QueryTimeRangeError)
		}
	}
	if !h.ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedType(req.DSType)) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.ds.CheckInstanceState(ctx, req.InstanceId, conv.ToSharedType(req.DSType), false); err != nil {
		return err
	}
	return nil
}

type DescribeDialogDetailSnapshotHandler struct {
	cnf    config.ConfigProvider
	c3Conf c3.ConfigProvider
	ds     datasource.DataSourceService
}

func NewDescribeDialogDetailSnapshotHandler(
	cnf config.ConfigProvider,
	C3Config c3.ConfigProvider,
	ds datasource.DataSourceService,
) handler.HandlerImplementationEnvolope {
	hder := &DescribeDialogDetailSnapshotHandler{
		cnf:    cnf,
		c3Conf: C3Config,
		ds:     ds,
	}
	return handler.NewHandler(hder.DescribeDialogDetailSnapshot)
}

package dialog

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"github.com/qjpcpu/fp"
	"strings"
)

func (h *DescribeDialogStatisticsHandler) DescribeDialogStatistics(
	ctx context.Context,
	req *model.DescribeDialogStatisticsReq,
) (ret *model.DescribeDialogStatisticsResp, err error) {
	if err = h.checkParam(ctx, req); err != nil {
		return nil, err
	}
	instanceId := req.GetInstanceId()
	var (
		component       model.Component
		dbInternalUsers map[string]string
		dbwAdmin        string
		dbwAdminPwd     string
		nodeId          string
		topN            int32
		nodeIds         []string
	)
	queryFilter := &shared.DialogQueryFilter{}
	if req.GetQueryFilter() != nil {
		if req.DSType == model.DSType_Mongo {
			nodeId = req.GetQueryFilter().GetNodeIds()[0]
		} else {
			nodeId = req.GetQueryFilter().GetNodeId()
		}
		nodeIds = req.GetQueryFilter().GetNodeIds()
		queryFilter = &shared.DialogQueryFilter{
			NodeId:  req.GetQueryFilter().GetNodeId(),
			ShardId: req.GetQueryFilter().GetShardId(),
			NodeIds: nodeIds,
		}
	}
	if req.IsSetComponent() {
		component = req.GetComponent()
	} else {
		component = model.Component_DBEngine
	}
	if req.IsSetTopN() {
		if req.GetTopN() > 50 || req.GetTopN() < 1 {
			topN = 5
		} else {
			topN = req.GetTopN()
		}
	} else {
		topN = 5 // 默认为Top5
	}
	cfg := h.cnf.Get(ctx)
	internalUsers := make([]string, 0)
	err = json.Unmarshal([]byte(cfg.DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		internalUsers = strings.Split(dbInternalUsers[req.GetDSType().String()], ",")
	}
	ret = &model.DescribeDialogStatisticsResp{}
	if req.GetDSType() == model.DSType_ByteRDS {
		dbwAdmin = cfg.C3Config.DBWAccountName
		dbwAdminPwd = cfg.C3Config.DbwPwd
	} else if req.GetDSType() == model.DSType_ByteDoc {
		dbwAdmin = cfg.C3Config.DBWAccountName
		// 需要基于clusterName动态生成
	} else {
		c3Cfg := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		dbwAdmin = c3Cfg.DBWAccountName
		dbwAdminPwd = handler.GetAccountPassword(c3Cfg.DbwAccountPasswordGenKey, req.GetInstanceId())
	}
	ds := &shared.DataSource{
		Type:       shared.DataSourceType(req.GetDSType()),
		LinkType:   shared.Volc,
		User:       dbwAdmin,
		Password:   dbwAdminPwd,
		InstanceId: req.GetInstanceId(),
		NodeId:     nodeId,
		Region:     req.GetRegionId(),
	}
	// 验证账号是否存在
	if err := h.ds.EnsureAccount(ctx, &datasource.EnsureAccountReq{Source: ds}); err != nil {
		log.Warn(ctx, "Check dbw_admin account failed %s", err)
		return nil, err
	}
	dialogStatisticsReq := &datasource.DescribeDialogStatisticsReq{
		Source:        ds,
		QueryFilter:   queryFilter,
		InternalUsers: internalUsers,
		Component:     component.String(),
		TopN:          topN,
	}

	dialogStatisticsResp, err := h.ds.DescribeDialogStatistics(ctx, dialogStatisticsReq)
	if err != nil {
		log.Warn(ctx, "err is %s", err)
		return nil, err
	}
	// nolint:byted_use_uninitialized_object
	// nolint:byted_nilcheck:dialogStatisticsResp.DialogStatistics
	rsp := dialogStatisticsResp.DialogStatistics
	ret = &model.DescribeDialogStatisticsResp{}
	ret.DialogOverview = &model.DialogOverview{
		TotalConnections:       rsp.DialogOver.TotalConnections,
		ActiveConnections:      rsp.DialogOver.ActiveConnections,
		QueryCountWithoutIndex: utils.Int32Ref(rsp.DialogOver.QueryCountWithoutIndex),
		LongTransactionCount:   utils.Int32Ref(rsp.DialogOver.LongTransactionCount),
		LongestSecsRunning:     utils.Int32Ref(rsp.DialogOver.LongestSecsRunning),
	}
	fp.StreamOf(rsp.UserAggregatedInfo).Map(func(user *shared.UserAggregatedInfo) *model.UserAggregatedInfo {
		return &model.UserAggregatedInfo{
			UserName:          user.User,
			TotalConnections:  user.TotalConnections,
			ActiveConnections: user.ActiveConnections,
		}
	}).ToSlice(&ret.UserAggregatedInfos)
	fp.StreamOf(rsp.IPAggregatedInfo).Map(func(ip *shared.IPAggregatedInfo) *model.IPAggregatedInfo {
		return &model.IPAggregatedInfo{
			IP:                ip.IP,
			TotalConnections:  ip.TotalConnections,
			ActiveConnections: ip.ActiveConnections,
		}
	}).ToSlice(&ret.IPAggregatedInfos)
	fp.StreamOf(rsp.DBAggregatedInfo).Map(func(db *shared.DBAggregatedInfo) *model.DBAggregatedInfo {
		return &model.DBAggregatedInfo{
			DB:                db.DB,
			TotalConnections:  db.TotalConnections,
			ActiveConnections: db.ActiveConnections,
		}
	}).ToSlice(&ret.DBAggregatedInfos)
	fp.StreamOf(rsp.PSMAggregatedInfo).Map(func(db *shared.PSMAggregatedInfo) *model.PSMAggregatedInfo {
		return &model.PSMAggregatedInfo{
			PSM:               db.PSM,
			TotalConnections:  db.TotalConnections,
			ActiveConnections: db.ActiveConnections,
		}
	}).ToSlice(&ret.PSMAggregatedInfos)
	fp.StreamOf(rsp.NsAggregatedInfo).Map(func(ns *shared.NsAggregatedInfo) *model.NsAggregatedInfo {
		return &model.NsAggregatedInfo{
			Namespace:         ns.Namespace,
			TotalConnections:  ns.TotalConnections,
			ActiveConnections: ns.ActiveConnections,
		}
	}).ToSlice(&ret.NsAggregatedInfos)
	var (
		instanceList []*model.InstanceInfo
	)
	if req.GetDSType() == model.DSType_ByteDoc {
		var clusterId string
		// 获取bytedoc cpu使用率需要获取实例的clusterId
		resp, err := h.ds.DescribeDBInstanceShardInfos(ctx, &datasource.DescribeDBInstanceShardInfosReq{
			InstanceId: req.GetInstanceId(),
			Type:       shared.ByteDoc,
			DBName:     instanceId,
			RegionId:   req.GetRegionId(),
		})
		if err != nil {
			// 获取失败cpu返回为0
			log.Warn(ctx, "get instance %s shards failed %v", req.GetInstanceId(), err)
			ret.DialogOverview.CpuUsage = 0.0
			return ret, nil
		}
		for _, shardItem := range resp.Shards {
			if shardItem.ComponentType == model.ComponentType_ConfigServers {
				clusterId = shardItem.ShardId
				break
			}
		}
		log.Info(ctx, " bytedoc clusterId is %s", clusterId)
		if clusterId == "" {
			ret.DialogOverview.CpuUsage = 0.0
			return ret, nil
		}
		instanceId = clusterId
	}
	instanceList = []*model.InstanceInfo{
		{
			InstanceId: utils.StringRef(instanceId),
		},
	}
	var (
		cpuUsageMap map[string]float64
		cpuUtil     float64
	)
	if req.DSType == model.DSType_MySQLSharding {
		// sharding支持选择多个节点，cpu利用率为选定节点的平均cpu值
		if component == model.Component_DBEngine {
			var cpuShardingNode []float64
			for _, nodeID := range req.GetQueryFilter().GetNodeIds() {
				cpuMap, err := handler.GetInstancesLatestCpuUsage(ctx, req.DSType, instanceList, h.loc.RegionID(),
					fwctx.GetTenantID(ctx), h.cnf, h.crossAuthSvc, component.String(), nodeID, nodeIds)
				if err != nil {
					log.Warn(ctx, "failed to get instance cpu usage, err=%v", err)
					continue
				} else {
					cpuShardingNode = append(cpuShardingNode, cpuMap[nodeID])
				}
			}
			cpuUtil = averageFloat64(cpuShardingNode)
		} else {
			cpuUsageMap, err = handler.GetInstancesLatestCpuUsage(ctx, req.DSType, instanceList, h.loc.RegionID(),
				fwctx.GetTenantID(ctx), h.cnf, h.crossAuthSvc, component.String(), nodeId, nodeIds)
			if err != nil {
				log.Warn(ctx, "failed to get instance cpu usage, err=%v", err)
				cpuUtil = 0
			} else {
				cpuUtil = cpuUsageMap[req.GetInstanceId()]
			}
		}
	} else {
		cpuUsageMap, err = handler.GetInstancesLatestCpuUsage(ctx, req.DSType, instanceList, h.loc.RegionID(),
			fwctx.GetTenantID(ctx), h.cnf, h.crossAuthSvc, component.String(), nodeId, nodeIds)
		if err != nil {
			log.Warn(ctx, "failed to get instance cpu usage, err=%v", err)
			cpuUtil = 0
		} else {
			if nodeId != "" {
				cpuUtil = cpuUsageMap[nodeId]
			} else {
				cpuUtil = cpuUsageMap[req.GetInstanceId()]
			}
		}
	}
	ret.DialogOverview.CpuUsage = cpuUtil
	return ret, nil
}

type DescribeDialogStatisticsHandler struct {
	cnf          config.ConfigProvider
	crossAuthSvc crossauth.CrossServiceAuthorizationService
	c3Conf       c3.ConfigProvider
	ds           datasource.DataSourceService
	c3           c3.ConfigProvider
	loc          location.Location
}

func NewDescribeDialogStatisticsHandler(
	cnf config.ConfigProvider,
	crossAuthSvc crossauth.CrossServiceAuthorizationService,
	ds datasource.DataSourceService,
	c3 c3.ConfigProvider,
	loc location.Location,
) handler.HandlerImplementationEnvolope {
	hder := &DescribeDialogStatisticsHandler{
		cnf:          cnf,
		crossAuthSvc: crossAuthSvc,
		c3Conf:       c3,
		ds:           ds,
		c3:           c3,
		loc:          loc,
	}
	return handler.NewHandler(hder.DescribeDialogStatistics)
}

func (h *DescribeDialogStatisticsHandler) checkParam(ctx context.Context, req *model.DescribeDialogStatisticsReq) error {
	if req.InstanceId == "" {
		return consts.ErrorOf(model.ErrorCode_InstanceIdParamError)
	}
	// 实例类型为veDB时,nodeID必传
	if req.DSType == model.DSType_VeDBMySQL && req.GetComponent() == model.Component_DBEngine {
		if req.GetQueryFilter() == nil {
			return consts.ErrorOf(model.ErrorCode_ParamError)
		} else {
			if !req.GetQueryFilter().IsSetNodeId() {
				return consts.ErrorOf(model.ErrorCode_ParamError)
			}
		}
	}
	// 实例类型为sharding时,shardId,NodeIds为必传
	if req.DSType == model.DSType_MySQLSharding && req.GetComponent() == model.Component_DBEngine {
		if req.GetQueryFilter() == nil {
			return consts.ErrorOf(model.ErrorCode_ParamError)
		} else {
			if !req.GetQueryFilter().IsSetShardId() || !req.GetQueryFilter().IsSetNodeIds() {
				return consts.ErrorOf(model.ErrorCode_ParamError)
			}
		}
	}
	if !h.ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedType(req.DSType)) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.ds.CheckInstanceState(ctx, req.InstanceId, conv.ToSharedType(req.DSType), true); err != nil {
		return err
	}
	return nil
}

func averageFloat64(nums []float64) float64 {
	if len(nums) == 0 {
		return 0
	}

	var sum float64
	for _, num := range nums {
		sum += num
	}

	return sum / float64(len(nums))
}

package dialog

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"testing"

	bizConfig "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
)

type DescribeDialogStatisticsSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *DescribeDialogStatisticsSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *DescribeDialogStatisticsSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestDescribeDialogStatisticsSuite(t *testing.T) {
	suite.Run(t, new(DescribeDialogStatisticsSuite))
}

func (suite *DescribeDialogStatisticsSuite) TestOnStarted() {
}

func (suite *DescribeDialogStatisticsSuite) TestDescribeDialogStatisticsHandler_DescribeDialogStatistics_Succeed() {
	PatchConvey("test describe succeed", suite.T(), func() {
		c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
		c3Conf.EXPECT().GetNamespace(context.Background(), consts.C3ApplicationNamespace).Return(
			&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_internal_account"}}).AnyTimes()
		cfg := config.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{
			DBInternalUsers: "{\"VeDBMySQL\":\"root,repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,mysql.infoschema,mysql.session,mysql.sys\",\"MySQL\":\"repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,system user,root\",\"Postgres\":\"postgres,ccp_monitoring,byte_rds_repl,byte_rds_proxy,primaryuser\"}"}).AnyTimes()
		Mock(fwctx.GetTenantID).Return("**********")
		Mock(handler.GetInstancesLatestCpuUsage).Return(map[string]float64{"mysql-12345": 3.14}, nil).Build()
		credential := mocks.NewMockCredentials(suite.ctrl)
		credential.EXPECT().GetAK().Return("test_ak").AnyTimes()
		credential.EXPECT().GetSK().Return("test_sk").AnyTimes()
		credential.EXPECT().GetToken().Return("test_token").AnyTimes()
		cas := mocks.NewMockCrossServiceAuthorizationService(suite.ctrl)
		cas.EXPECT().AssumeRole(gomock.Any(), gomock.Any()).Return(credential, nil).AnyTimes()
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		ds.EXPECT().EnsureAccount(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
		ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		resp := &datasource.DescribeDialogStatisticsResp{
			DialogStatistics: &shared.DialogStatistics{
				DialogOver: &shared.DialogOverview{
					TotalConnections:       3,
					ActiveConnections:      1,
					LongestSecsRunning:     0,
					LongTransactionCount:   0,
					QueryCountWithoutIndex: 0,
				},
				UserAggregatedInfo: []*shared.UserAggregatedInfo{
					{
						User:              "user1",
						TotalConnections:  3,
						ActiveConnections: 1,
					},
				},
				IPAggregatedInfo: []*shared.IPAggregatedInfo{
					{
						IP:                "localhost",
						TotalConnections:  3,
						ActiveConnections: 1,
					},
				},
				DBAggregatedInfo: []*shared.DBAggregatedInfo{
					{
						DB:                "information_schama",
						TotalConnections:  3,
						ActiveConnections: 1,
					},
				},
				PSMAggregatedInfo: []*shared.PSMAggregatedInfo{
					{
						PSM:               "toutiao.db1.t1",
						TotalConnections:  3,
						ActiveConnections: 1,
					},
				},
			},
		}
		loc := mocks.NewMockLocation(suite.ctrl)
		loc.EXPECT().RegionID().Return("cn-nanjing-bbit").AnyTimes()
		ds.EXPECT().DescribeDialogStatistics(gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes()
		h := NewDescribeDialogStatisticsHandler(cfg, cas, ds, c3Conf, loc).
			Impl.Impl.(func(ctx context.Context, req *model.DescribeDialogStatisticsReq) (ret *model.DescribeDialogStatisticsResp, err error))
		gotRet, err := h(context.Background(), &model.DescribeDialogStatisticsReq{
			DSType:     model.DSType_MySQL,
			InstanceId: "mysql-12345",
			TopN:       utils.Int32Ref(10),
		})
		So(err, ShouldResemble, nil)
		So(gotRet, ShouldResemble, &model.DescribeDialogStatisticsResp{
			DialogOverview: &model.DialogOverview{
				TotalConnections:       3,
				ActiveConnections:      1,
				CpuUsage:               3.14,
				LongestSecsRunning:     utils.Int32Ref(0),
				LongTransactionCount:   utils.Int32Ref(0),
				QueryCountWithoutIndex: utils.Int32Ref(0),
			},
			UserAggregatedInfos: []*model.UserAggregatedInfo{
				{
					UserName:          "user1",
					ActiveConnections: 1,
					TotalConnections:  3,
				},
			},
			IPAggregatedInfos: []*model.IPAggregatedInfo{
				{
					IP:                "localhost",
					ActiveConnections: 1,
					TotalConnections:  3,
				},
			},
			DBAggregatedInfos: []*model.DBAggregatedInfo{
				{
					DB:                "information_schama",
					ActiveConnections: 1,
					TotalConnections:  3,
				},
			},
			PSMAggregatedInfos: []*model.PSMAggregatedInfo{
				{
					PSM:               "toutiao.db1.t1",
					ActiveConnections: 1,
					TotalConnections:  3,
				},
			},
		})
		gotRet1, err := h(context.Background(), &model.DescribeDialogStatisticsReq{
			DSType:     model.DSType_VeDBMySQL,
			InstanceId: "ndb-12345",
			QueryFilter: &model.QueryFilter{
				NodeIds: []string{"node0", "node1"},
			},
			TopN: utils.Int32Ref(10),
		})
		So(err, ShouldResemble, nil)
		So(gotRet1, ShouldResemble, &model.DescribeDialogStatisticsResp{
			DialogOverview: &model.DialogOverview{
				TotalConnections:       3,
				ActiveConnections:      1,
				CpuUsage:               0,
				LongestSecsRunning:     utils.Int32Ref(0),
				LongTransactionCount:   utils.Int32Ref(0),
				QueryCountWithoutIndex: utils.Int32Ref(0),
			},
			UserAggregatedInfos: []*model.UserAggregatedInfo{
				{
					UserName:          "user1",
					ActiveConnections: 1,
					TotalConnections:  3,
				},
			},
			IPAggregatedInfos: []*model.IPAggregatedInfo{
				{
					IP:                "localhost",
					ActiveConnections: 1,
					TotalConnections:  3,
				},
			},
			DBAggregatedInfos: []*model.DBAggregatedInfo{
				{
					DB:                "information_schama",
					ActiveConnections: 1,
					TotalConnections:  3,
				},
			},
			PSMAggregatedInfos: []*model.PSMAggregatedInfo{
				{
					PSM:               "toutiao.db1.t1",
					ActiveConnections: 1,
					TotalConnections:  3,
				},
			},
		})
	})
}

func (suite *DescribeDialogStatisticsSuite) TestDescribeDialogStatisticsHandler_DescribeDialogStatistics_Failed_1() {
	PatchConvey("get instance failed", suite.T(), func() {
		c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
		c3Conf.EXPECT().GetNamespace(context.Background(), consts.C3ApplicationNamespace).Return(
			&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_internal_account"}}).AnyTimes()
		cfg := config.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{
			DBInternalUsers: "{\"VeDBMySQL\":\"root,repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,mysql.infoschema,mysql.session,mysql.sys\",\"MySQL\":\"repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,system user,root\",\"Postgres\":\"postgres,ccp_monitoring,byte_rds_repl,byte_rds_proxy,primaryuser\"}"}).AnyTimes()
		Mock(fwctx.GetTenantID).Return("**********")
		credential := mocks.NewMockCredentials(suite.ctrl)
		credential.EXPECT().GetAK().Return("test_ak").AnyTimes()
		credential.EXPECT().GetSK().Return("test_sk").AnyTimes()
		credential.EXPECT().GetToken().Return("test_token").AnyTimes()
		cas := mocks.NewMockCrossServiceAuthorizationService(suite.ctrl)
		cas.EXPECT().AssumeRole(gomock.Any(), gomock.Any()).Return(credential, nil).AnyTimes()
		loc := mocks.NewMockLocation(suite.ctrl)
		loc.EXPECT().RegionID().Return("cn-nanjing-bbit").AnyTimes()
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		ds.EXPECT().EnsureAccount(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
		ds.EXPECT().CheckInstanceState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		ds.EXPECT().DescribeDialogStatistics(gomock.Any(), gomock.Any()).Return(nil, consts.ErrorOf(model.ErrorCode_InternalError)).AnyTimes()
		h := NewDescribeDialogStatisticsHandler(cfg, cas, ds, c3Conf, loc).
			Impl.Impl.(func(ctx context.Context, req *model.DescribeDialogStatisticsReq) (ret *model.DescribeDialogStatisticsResp, err error))
		gotRet, err := h(context.Background(), &model.DescribeDialogStatisticsReq{
			DSType:     model.DSType_MySQL,
			InstanceId: "mysql-12345",
			QueryFilter: &model.QueryFilter{
				NodeId: utils.StringRef("node1"),
			},
		})
		So(err, ShouldResemble, consts.ErrorOf(model.ErrorCode_InternalError))
		So(gotRet, ShouldBeNil)
	})
}
func (suite *DescribeDialogStatisticsSuite) TestCheckParam() {
	c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	c3Conf.EXPECT().GetNamespace(context.Background(), consts.C3ApplicationNamespace).Return(
		&bizConfig.C3Config{Application: bizConfig.Application{DbwAccountPasswordGenKey: "dbw_internal_account"}}).AnyTimes()
	cas := mocks.NewMockCrossServiceAuthorizationService(suite.ctrl)
	ds := mocks.NewMockDataSourceService(suite.ctrl)
	cfg := config.NewMockConfigProvider(suite.ctrl)
	loc := mocks.NewMockLocation(suite.ctrl)
	loc.EXPECT().RegionID().Return("cn-nanjing-bbit").AnyTimes()
	ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
	cfg.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{
		DBInternalUsers: "{\"VeDBMySQL\":\"root,repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,mysql.infoschema,mysql.session,mysql.sys\",\"MySQL\":\"repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,internal_admin_user,system user,root\",\"Postgres\":\"postgres,ccp_monitoring,byte_rds_repl,byte_rds_proxy,primaryuser\"}"}).AnyTimes()
	ds.EXPECT().IsMyOwnInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()

	h := NewDescribeDialogStatisticsHandler(cfg, cas, ds, c3Conf, loc).
		Impl.Impl.(func(ctx context.Context, req *model.DescribeDialogStatisticsReq) (ret *model.DescribeDialogStatisticsResp, err error))
	ctx := context.Background()

	testCases := []struct {
		name string
		req  *model.DescribeDialogStatisticsReq
		want error
	}{
		{
			name: "VeDB param error",
			req: &model.DescribeDialogStatisticsReq{DSType: model.DSType_VeDBMySQL, Component: model.ComponentPtr(model.Component_DBEngine),
				InstanceId: "vedbm-111"},
			want: consts.ErrorOf(model.ErrorCode_ParamError),
		},
		{
			name: "VeDB param error1",
			req: &model.DescribeDialogStatisticsReq{DSType: model.DSType_VeDBMySQL, Component: model.ComponentPtr(model.Component_DBEngine),
				QueryFilter: &model.QueryFilter{User: utils.StringRef("111")},
				InstanceId:  "vedbm-111"},
			want: consts.ErrorOf(model.ErrorCode_ParamError),
		},
		{
			name: "sharding",
			req: &model.DescribeDialogStatisticsReq{DSType: model.DSType_MySQLSharding, Component: model.ComponentPtr(model.Component_DBEngine),
				InstanceId: "myshard-222"},
			want: consts.ErrorOf(model.ErrorCode_ParamError),
		},
		{
			name: "empty InstanceId",
			req:  &model.DescribeDialogStatisticsReq{DSType: model.DSType_MySQL},
			want: consts.ErrorOf(model.ErrorCode_InstanceIdParamError),
		},
	}

	for _, testCase := range testCases {
		suite.Suite.Run(testCase.name, func() {
			ret, got := h(ctx, testCase.req)
			suite.Empty(ret)
			suite.Equal(testCase.want, got)
		})
	}
}

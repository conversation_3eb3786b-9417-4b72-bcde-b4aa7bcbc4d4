package dialog

import (
	"context"
	"encoding/json"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

func (h *DescribeEngineStatusSnapShotHandler) DescribeEngineStatusSnapShot(
	ctx context.Context,
	req *model.DescribeEngineStatusSnapShotReq,
) (ret *model.DescribeEngineStatusSnapShotResp, err error) {
	var (
		tlsEngineStatusTopic              map[string]string
		nodeId                            string
		ak, sk, regionId, tlsEndpointName string
	)
	if err := h.checkParam(ctx, req); err != nil {
		return nil, err
	}
	if h.cnf.Get(ctx).EnableByteRDS {
		c3Cfg := h.cnf.Get(ctx).C3Config
		ak = c3Cfg.TLSServiceAccessKey
		sk = c3Cfg.TLSServiceSecretKey
		regionId = c3Cfg.ByteInnerTLSServiceRegion
		tlsEndpointName = c3Cfg.ByteInnerTLSServiceEndpoint
		err = json.Unmarshal([]byte(c3Cfg.TLSEngineStatusTopicV2), &tlsEngineStatusTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSEngineStatusTopic str failed %+v", err)
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	} else {
		cnf := h.cnf.Get(ctx)
		regionId = cnf.TlsZone
		c3 := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		ak = c3.TLSServiceAccessKey
		sk = c3.TLSServiceSecretKey
		tlsEndpointName = cnf.TlsServiceEndpoint
		// 获取tls相关topic
		err = json.Unmarshal([]byte(h.cnf.Get(ctx).TLSEngineStatusTopicV2), &tlsEngineStatusTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSEngineStatusTopic str failed %+v", err)
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	}

	cli := tls.NewTLSClient(&tls.ConnectionInfo{
		Endpoint:        tlsEndpointName,
		AccessKeyID:     ak,
		AccessKeySecret: sk,
		Region:          regionId,
		//TopicId: h.cnf.Get(ctx).TLSEngineStatusTopic,
		TopicId: tlsEngineStatusTopic[req.GetDSType().String()],
	}, h.cnf)
	if req.IsSetNodeId() {
		nodeId = req.GetNodeId()
	} else {
		nodeId = ""
	}
	var resp *tls.DescribeEngineStatusSnapshotResp
	if resp, err = cli.DescribeEngineStatusSnapshot(ctx, &tls.DescribeEngineStatusSnapshotReq{
		TenantId:    fwctx.GetTenantID(ctx),
		InstanceId:  req.InstanceId,
		CollectTime: int64(req.SnapshotTime),
		NodeId:      nodeId,
	}); err != nil {
		return
	}

	ret = &model.DescribeEngineStatusSnapShotResp{
		Type:   resp.Status.Type,
		Name:   resp.Status.Name,
		Status: resp.Status.Status,
		NodeId: &(resp.Status.NodeId),
	}
	return
}

type DescribeEngineStatusSnapShotHandler struct {
	cnf    config.ConfigProvider
	c3Conf c3.ConfigProvider
	ds     datasource.DataSourceService
}

func NewDescribeEngineStatusSnapShotHandler(
	cnf config.ConfigProvider,
	C3Config c3.ConfigProvider,
	ds datasource.DataSourceService,
) handler.HandlerImplementationEnvolope {
	hder := &DescribeEngineStatusSnapShotHandler{
		cnf:    cnf,
		c3Conf: C3Config,
		ds:     ds,
	}
	return handler.NewHandler(hder.DescribeEngineStatusSnapShot)
}
func (h *DescribeEngineStatusSnapShotHandler) checkParam(ctx context.Context, req *model.DescribeEngineStatusSnapShotReq) error {
	if req.InstanceId == "" {
		return consts.ErrorOf(model.ErrorCode_InstanceIdParamError)
	}
	if !h.ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedType(req.DSType)) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.ds.CheckInstanceState(ctx, req.InstanceId, conv.ToSharedType(req.DSType), false); err != nil {
		return err
	}
	// check instance type
	if req.GetDSType() != model.DSType_MySQL && req.GetDSType() != model.DSType_VeDBMySQL &&
		req.GetDSType() != model.DSType_MySQLSharding && req.GetDSType() != model.DSType_MetaMySQL &&
		req.GetDSType() != model.DSType_ByteRDS {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}

	return nil

}

package dialog

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"github.com/qjpcpu/fp"
	//"github.com/qjpcpu/fp"
)

func (h *DescribeCurrentConnInfosHandler) DescribeCurrentConnInfos(
	ctx context.Context,
	req *model.DescribeCurrentConnInfosReq,
) (*model.DescribeCurrentConnInfosResp, error) {
	if err := h.checkParam(ctx, req); err != nil {
		return nil, err
	}
	var (
		component   model.Component
		dbwAdmin    string
		dbwAdminPwd string
	)
	var queryFilter *shared.DialogQueryFilter
	cnf := h.cnf.Get(ctx)
	if req.GetQueryFilter() != nil {
		queryFilter = &shared.DialogQueryFilter{
			ConnId:  req.GetQueryFilter().GetConnId(),
			Host:    req.GetQueryFilter().GetHost(),
			State:   req.GetQueryFilter().GetState(),
			Desc:    req.GetQueryFilter().GetDesc(),
			NodeIds: req.GetQueryFilter().GetNodeIds(),
		}
	}
	ret := &model.DescribeCurrentConnInfosResp{
		Details:    &model.ConnDetails{},
		Statistics: &model.ConnStatistics{},
	}
	if req.GetInstanceType() == model.DSType_ByteDoc {
		dbwAdmin = cnf.C3Config.DBWAccountName
		// 需要基于clusterName动态生成
	} else {
		// mongoDB
		c3Cfg := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		dbwAdmin = c3Cfg.DBWAccountName
		dbwAdminPwd = handler.GetAccountPassword(c3Cfg.DbwAccountPasswordGenKey, req.GetInstanceId())
	}
	ds := &shared.DataSource{
		Type:             shared.DataSourceType(req.GetInstanceType()),
		LinkType:         shared.Volc,
		User:             dbwAdmin,
		Password:         dbwAdminPwd,
		ConnectTimeoutMs: cnf.ConnectionConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.ConnectionReadTimeout * 1000,
		WriteTimeoutMs:   cnf.ConnectionWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.ConnectionIdleTimeout * 1000,
		InstanceId:       req.GetInstanceId(),
		Region:           req.GetRegionId(),
	}
	// 验证账号是否存在
	if err := h.ds.EnsureAccount(ctx, &datasource.EnsureAccountReq{Source: ds}); err != nil {
		log.Warn(ctx, "Check dbw_admin account failed %s", err)
		return nil, err
	}
	connInfosReq := &datasource.DescribeCurrentConnsReq{
		Source:      ds,
		QueryFilter: queryFilter,
		Component:   component.String(),
	}
	connInfosResp, err := h.ds.DescribeCurrentConn(ctx, connInfosReq)
	if err != nil {
		log.Warn(ctx, "err is %s", err)
		return nil, err
	}
	log.Info(ctx, "rsp is %v", connInfosResp)
	if err := fp.StreamOf(connInfosResp.ConnDetails.Details).Map(func(detail *shared.ConnDetail) *model.ConnDetail {
		return &model.ConnDetail{
			State:        detail.State,
			ConnectionID: detail.ConnID,
			Desc:         detail.Desc,
			Client:       detail.Client,
		}
	}).ToSlice(&ret.Details.ConnDetails); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	ret.Details.Total = connInfosResp.ConnDetails.Total
	ret.Statistics = &model.ConnStatistics{
		ConnOverview: &model.ConnOverview{
			TotalConnections:  connInfosResp.ConnStatistics.ConnOverview.TotalConnections,
			ActiveConnections: connInfosResp.ConnStatistics.ConnOverview.ActiveConnections,
		},
	}
	fp.StreamOf(connInfosResp.ConnStatistics.IPAggregatedInfo).Map(func(ip *shared.IPAggregatedInfo) *model.IPAggregatedInfo {
		return &model.IPAggregatedInfo{
			IP:                ip.IP,
			TotalConnections:  ip.TotalConnections,
			ActiveConnections: ip.ActiveConnections,
		}
	}).ToSlice(&ret.Statistics.IPAggregatedInfos)
	return ret, nil
}

func (h *DescribeCurrentConnInfosHandler) checkParam(ctx context.Context, req *model.DescribeCurrentConnInfosReq) error {
	if req.InstanceId == "" {
		return consts.ErrorOf(model.ErrorCode_InstanceIdParamError)
	}
	// 实例类型仅支持Mongo
	if req.GetInstanceType() != model.DSType_Mongo && req.GetInstanceType() != model.DSType_ByteDoc {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	if !h.ds.IsMyOwnInstance(ctx, req.InstanceId, conv.ToSharedType(req.GetInstanceType())) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.ds.CheckInstanceState(ctx, req.InstanceId, conv.ToSharedType(req.GetInstanceType()), true); err != nil {
		return err
	}
	return nil
}

type DescribeCurrentConnInfosHandler struct {
	cnf    config.ConfigProvider
	ds     datasource.DataSourceService
	c3Conf c3.ConfigProvider
}

func NewDescribeCurrentConnInfosHandler(
	cnf config.ConfigProvider,
	ds datasource.DataSourceService,
	c3 c3.ConfigProvider,
) handler.HandlerImplementationEnvolope {
	hder := &DescribeCurrentConnInfosHandler{
		cnf:    cnf,
		ds:     ds,
		c3Conf: c3,
	}
	return handler.NewHandler(hder.DescribeCurrentConnInfos)
}

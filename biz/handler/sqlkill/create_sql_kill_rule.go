package sqlkill

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	bizConv "code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"go.uber.org/dig"
	"strings"
	"time"
)

type CreateSqlKillRuleHandler struct {
	actorClient          cli.ActorClient
	killRuleDal          dal.SqlKillRulesDAL
	killEventDal         dal.SqlKillEventDAL
	idSvc                idgen.Service
	dsSvc                datasource.DataSourceService
	cnf                  config.ConfigProvider
	operateRecordService operate_record.OperateRecordService
}

type CreateSqlKillRuleHandlerIn struct {
	dig.In
	ActorClient          cli.ActorClient
	KillRuleDal          dal.SqlKillRulesDAL
	KillEventDal         dal.SqlKillEventDAL
	IDSvc                idgen.Service
	DsSvc                datasource.DataSourceService
	Cnf                  config.ConfigProvider
	OperateRecordService operate_record.OperateRecordService
}

func NewCreateSqlKillRuleHandler(in CreateSqlKillRuleHandlerIn) handler.HandlerImplementationEnvolope {
	h := &CreateSqlKillRuleHandler{
		actorClient:          in.ActorClient,
		killRuleDal:          in.KillRuleDal,
		killEventDal:         in.KillEventDal,
		idSvc:                in.IDSvc,
		dsSvc:                in.DsSvc,
		cnf:                  in.Cnf,
		operateRecordService: in.OperateRecordService,
	}
	return handler.NewHandler(h.CreateSqlKillRule)
}

type UniqueRuleTuple struct {
	NodeType    []string
	SQLType     []string
	Host        string
	FingerPrint string
	keywords    string
}

func (h *CreateSqlKillRuleHandler) CreateSqlKillRule(ctx context.Context, req *model.CreateSqlKillRuleReq) (ret *model.CreateSqlKillRuleResp, err error) {
	// DAS操作审计
	var (
		ruleDetail          *model.SQLKillRule
		dbInternalUsers     map[string]string
		protectedUsers      string
		tempUsers, prtUsers []string
		sqlType             []string
		ruleId              int64
	)
	defer func() {
		ruleDetail = &model.SQLKillRule{
			SqlType:       utils.StringRef(strings.Join(sqlType, ",")),
			NodeType:      utils.StringRef(strings.Join(req.GetNodeType(), ",")),
			MaxExecTime:   req.GetMaxExecTime(),
			EffectiveTime: req.GetEffectiveTime(),
			CreateTime:    time.UnixMilli(time.Now().UnixMilli()).UTC().Format(time.RFC3339),
			TaskId:        fmt.Sprintf("%d", ruleId),
			Host:          req.Host,
			KeyWords:      req.KeyWords,
			FingerPrint:   req.FingerPrint,
		}
		rule, _ := json.Marshal(ruleDetail)
		operationRecord := &entity.OperationRecord{
			OperationType: model.DasOperationCategory_Kill.String(),
			InstanceId:    req.GetInstanceId(),
			InstanceType:  req.GetInstanceType().String(),
			TriggerType:   model.TriggerType_Manual.String(),
			Action:        model.DasAction_CreateAutoKillRule.String(),
			Extra:         string(rule),
			TenantId:      fwctx.GetTenantID(ctx),
			UserId:        fwctx.GetUserID(ctx),
			TaskId:        fmt.Sprintf("%d", ruleId),
		}
		if err != nil {
			operationRecord.Status = model.OpsTaskState_RunningFailed.String()
			log.Warn(ctx, "CreateSqlKillRule error:%+v", err)
		} else {
			operationRecord.Status = model.OpsTaskState_Done.String()
		}
		err := h.operateRecordService.CreateDasRecord(ctx, operationRecord)
		if err != nil {
			log.Warn(ctx, "create das record failed %+v", err)
		}
	}()
	if !req.IsSetTerminationType() {
		req.TerminationType = model.TerminationTypePtr(model.TerminationType_Auto)
	}
	if req.GetTerminationType() == model.TerminationType_Manual {
		req.EffectiveTime = int32(3600 * 24 * 365 * 50) // 50 years
	}
	if err := h.preCheck(ctx, req); err != nil {
		return nil, err
	}
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if userId == "" {
		userId = "RootAccount"
	}
	//generate ruleId
	ruleId, err = h.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "Generate sql kill rule failed %+v", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	//generate eventId
	eventId, err := h.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "Generate sql kill event failed %+v", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	cfg := h.cnf.Get(ctx)
	timestampMS := time.Now().UnixMilli()
	err = json.Unmarshal([]byte(cfg.DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	protectedUsers = dbInternalUsers[req.GetInstanceType().String()]
	switch req.GetInstanceType() {
	case model.InstanceType_MySQL:
		// 过滤掉system user mysql.xxx，接口不支持带空格的user
		if strings.Contains(protectedUsers, "system user") || strings.Contains(protectedUsers, "mysql.") {
			tempUsers = strings.Split(protectedUsers, ",")
			for _, user := range tempUsers {
				if user != "system user" && !strings.HasPrefix(user, "mysql.") {
					prtUsers = append(prtUsers, user)
				}
			}
			protectedUsers = strings.Join(prtUsers, ",")
		}
	case model.InstanceType_VeDBMySQL:
		prtUsers = append(prtUsers, "dbw_admin")
		protectedUsers = strings.Join(prtUsers, ",")
	case model.InstanceType_MySQLSharding:
		// 过滤掉system user mysql.xxx，接口不支持带空格的user
		if strings.Contains(protectedUsers, "system user") || strings.Contains(protectedUsers, "mysql.") {
			tempUsers = strings.Split(protectedUsers, ",")
			for _, user := range tempUsers {
				if user != "system user" && !strings.HasPrefix(user, "mysql.") {
					prtUsers = append(prtUsers, user)
				}
			}
			protectedUsers = strings.Join(prtUsers, ",")
		}
	default:

	}
	if req.IsSetProtectedUsers() && len(req.ProtectedUsers) > 0 {
		specifiedProtectedUser := strings.Join(req.GetProtectedUsers(), ",")
		protectedUsers = fmt.Sprintf("%s,%s", protectedUsers, specifiedProtectedUser)
	}
	if len(req.GetSqlType()) > 0 {
		for _, item := range req.GetSqlType() {
			sqlType = append(sqlType, item.String())
		}
	}
	dalRule := &dao.SqlKillRule{
		ID:             ruleId,
		TenantID:       tenantId,
		UserID:         userId,
		InstanceID:     req.GetInstanceId(),
		InstanceType:   req.GetInstanceType().String(),
		Duration:       int64(req.EffectiveTime),
		State:          model.KillRuleState_NONE.String(),
		MaxExecTime:    req.GetMaxExecTime(),
		ProtectedUsers: protectedUsers,
		SqlType:        strings.Join(sqlType, ","),
		NodeType:       strings.Join(req.GetNodeType(), ","),
		CreatedAt:      timestampMS,
		UpdatedAt:      timestampMS,
		Host:           req.GetHost(),
		Keywords:       req.GetKeyWords(),
		FingerPrint:    req.GetFingerPrint(),
	}
	if err := h.killRuleDal.Create(ctx, dalRule); err != nil {
		log.Warn(ctx, "Create sql kill rule records on metaDB failed%+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	dalEvent := &dao.SqlKillEvent{
		ID:            eventId,
		CreatedAt:     timestampMS,
		SqlKillRuleID: ruleId,
		InstanceType:  req.GetInstanceType().String(),
		TenantID:      tenantId,
		InstanceID:    req.GetInstanceId(),
		Operator:      fwctx.GetUserID(ctx),
		EventType:     model.KillSqlEventType_Add.String(),
	}
	if err := h.killEventDal.Create(ctx, dalEvent); err != nil {
		log.Warn(ctx, "Create kill sql event record on metaDB failed%+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	msg := &shared.CreateSqlKillRuleReq{
		TaskId:   ruleId,
		TenantId: tenantId,
	}
	log.Info(ctx, "CreateSqlKillRule msg is %s", utils.Show(msg))
	actorResp, err := h.actorClient.KindOf(consts.CCLRuleActorKind).Call(ctx, conv.Int64ToStr(ruleId), msg)
	if err != nil {
		log.Warn(ctx, "CreateSqlKillRule error:%+v", err)
		return nil, err
	}
	switch msg := actorResp.(type) {
	case *shared.SQLKillRuleActorFailResp:
		log.Warn(ctx, "CreateSqlKillRule error:%+v", msg)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, msg.Error)
	case *shared.ActionSuccess:
		ret := &model.CreateSqlKillRuleResp{
			TaskId: ruleId,
		}
		return ret, nil
	default:
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
}

func (h *CreateSqlKillRuleHandler) preCheck(ctx context.Context, req *model.CreateSqlKillRuleReq) error {
	// check req params valid
	if err := req.IsValid(); err != nil {
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if !h.dsSvc.IsMyOwnInstance(ctx, req.GetInstanceId(), bizConv.ToSharedTypeV2(req.GetInstanceType())) {
		return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 校验实例状态
	if err := h.dsSvc.CheckInstanceState(ctx, req.GetInstanceId(), bizConv.ToSharedTypeV2(req.GetInstanceType()), true); err != nil {
		return err
	}
	// check instance type
	if req.GetInstanceType() != model.InstanceType_MySQL && req.GetInstanceType() != model.InstanceType_VeDBMySQL &&
		req.GetInstanceType() != model.InstanceType_MySQLSharding {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	// 检查mysql agent版本
	if req.GetInstanceType() == model.InstanceType_MySQL {
		checkPassed, err := h.rdsVersionChecker(ctx, req)
		if err != nil {
			return consts.ErrorOf(model.ErrorCode_NotSupportInstanceVersion)
		}
		if !checkPassed {
			return consts.ErrorOf(model.ErrorCode_NotSupportInstanceVersion)
		}
	}
	// check KillTasks nums(>5)
	ruleList, err := h.killRuleDal.List(ctx, req.GetInstanceId(), &dal.SqlKillQueryFilter{RuleState: model.KillRuleState_ACTIVE.String()}, "ASC", "created_at", 10, 0, true)
	if err != nil {
		log.Warn(ctx, "Get sql kill rule lists failed:%+v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	if ruleList.Total >= 5 {
		log.Warn(ctx, "The maximum number of sql kill rules per instance does not exceed 5")
		return consts.ErrorOf(model.ErrorCode_SqlRuleMaxNumsErr)
	}
	// check duration <= int32
	if req.EffectiveTime > 99999999 && req.GetTerminationType() == model.TerminationType_Auto {
		log.Warn(ctx, "The maximum sql kill duration exceed 99999999 seconds")
		return consts.ErrorOf(model.ErrorCode_SqlDurationMaxNumsErr)
	}
	// check MaxExecTime
	if req.MaxExecTime > 9999 {
		return consts.ErrorOf(model.ErrorCode_SqlRuleMaximumErr)
	}
	// check keywords lengths
	if len(req.GetKeyWords()) > 65535 {
		log.Warn(ctx, "The lengths of keywords per does not exceed 65535")
		return consts.ErrorOf(model.ErrorCode_KeywordLengthErr)
	}
	// check sql fingerprint lengths
	if len(req.GetFingerPrint()) > 65535 {
		log.Warn(ctx, "The lengths of sql fingerprint per does not exceed 65535")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	// 规则重复校验veDB强依赖，规则重复标准<NodeType,SqlType>
	inputRule := &UniqueRuleTuple{
		NodeType:    req.NodeType,
		SQLType:     killSQLTypeToStrSlice(req.GetSqlType()),
		Host:        req.GetHost(),
		FingerPrint: req.GetFingerPrint(),
		keywords:    req.GetKeyWords(),
	}
	for _, rule := range ruleList.SqlKillRules {
		targetRule := &UniqueRuleTuple{
			NodeType:    strings.Split(rule.NodeType, ","),
			SQLType:     strings.Split(rule.SqlType, ","),
			Host:        rule.Host,
			FingerPrint: rule.FingerPrint,
			keywords:    rule.Keywords,
		}
		if hasDuplicateTuples(inputRule, targetRule) {
			return consts.ErrorOf(model.ErrorCode_SqlKillRuleRepeatedErr)
		}
	}
	return nil
}

func (h *CreateSqlKillRuleHandler) rdsVersionChecker(ctx context.Context, req *model.CreateSqlKillRuleReq) (bool, error) {
	// 检查agent版本大于等于3.8.2
	if !CheckInstanceVersion(ctx, req.GetInstanceType(), req.GetInstanceId(), h.dsSvc) {
		return false, consts.ErrorOf(model.ErrorCode_NotSupportInstanceVersion)
	}
	return true, nil
}

func hasDuplicateTuples(rule1, rule2 *UniqueRuleTuple) bool {
	tuples := make(map[string]struct{})
	// Add all combinations from rule1 to the map
	for _, node := range rule1.NodeType {
		for _, sql := range rule1.SQLType {
			hash := sha256.New()
			hash.Write([]byte("nodetype" + node))
			hash.Write([]byte("sqltype" + sql))
			hash.Write([]byte("psm" + rule1.Host))
			hash.Write([]byte("fingerprint" + rule1.FingerPrint))
			hash.Write([]byte("keyword" + rule1.keywords))
			tuples[string(hash.Sum(nil))] = struct{}{}
		}
	}

	// Check if any combination from rule2 is in the map
	for _, node := range rule2.NodeType {
		for _, sql := range rule2.SQLType {
			hash := sha256.New()
			hash.Write([]byte("nodetype" + node))
			hash.Write([]byte("sqltype" + sql))
			hash.Write([]byte("psm" + rule2.Host))
			hash.Write([]byte("fingerprint" + rule2.FingerPrint))
			hash.Write([]byte("keyword" + rule2.keywords))
			if _, exists := tuples[string(hash.Sum(nil))]; exists {
				return true
			}
		}
	}

	return false
}

package sql_advisor

import (
	"bytes"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	dbw_utils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
)

const SQLAdvisorDbTypeForInner = "RDS_MYSQL"
const SQLAdvisorDbType = "VOLC_MYSQL"

type createSQLAdvisorTaskResp struct {
	Success bool   `json:"success"`
	Message string `json:"msg"`
	TaskID  string `json:"taskId"`
}

type describeSQLAdvisorTaskResp struct {
	Success bool                `json:"success"`
	State   string              `json:"state"`
	Message string              `json:"msg"`
	Data    *SQLAdvisorTaskInfo `json:"data"`
}

type SQLAdvisorTaskInfo struct {
	TaskId          string            `json:"task_id"`
	IndexAdvices    []*IndexAdvice    `json:"index_advices"`
	OptimizeAdvice  []*OptimizeAdvice `json:"optimize_advices"`
	AdviceTimeStamp int64             `json:"advice_timestamp"`
	OptimizeStatus  string            `json:"optimize_status"`
}

type IndexAdvice struct {
	DbName              string               ` json:"db_name"`
	TableName           string               ` json:"table_name"`
	Columns             []*SQLAdvisorColumns ` json:"columns"`
	DDL                 string               ` json:"ddl"`
	RelatedFingerPrints []*FigerPrint        ` json:"related_fingerprints"`
}
type SQLAdvisorColumns struct {
	Name string ` json:"name"`
}
type OptimizeAdvice struct {
	AdviceCode          string        ` json:"advice_code"`
	AdviceType          string        ` json:"advice_type"`
	AdviceContent       string        ` json:"advice_content"`
	RelatedFingerPrints []*FigerPrint ` json:"related_fingerprints"`
	ExtraInfo           *ExtraInfo    ` json:"extra_info"`
	Template            string        ` json:"template"`
	TemplateEn          string        ` json:"template_en"`
}

type FigerPrint struct {
	FingerPrintMd5 string ` json:"fingerprint_md5"`
	Benefit        string ` json:"benefit"`
}

type ExtraInfo struct {
	Condition       string `json:"Condition"`
	AffectedColumns string `json:"AffectedColumns"`
	Collations      string `json:"Collations"`
	Clause          string `json:"Clause"`
	Table           string `json:"Table"`
	CurrentCharset  string `json:"CurrentCharset"`
	NormalKeyNum    string `json:"NormalKeyNum"`
	DuplicateIndex  string `json:"DuplicateIndex"`
}

func getAccountPassword(key string, instanceId string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(instanceId))
	expectedMac := hex.EncodeToString(mac.Sum(nil))
	return "Dbw_" + expectedMac[:26]
}

func primaryKeyInfoDataSourceTOModel(info *datasource.PrimaryKeyInfo) *model.PrimaryKeyInfo {
	var res = &model.PrimaryKeyInfo{}
	res.MinNum = primaryKeyValueDataSourceToModel(info.MinNum)
	res.MaxNum = primaryKeyValueDataSourceToModel(info.MaxNum)
	return res
}

func primaryKeyValueDataSourceToModel(info []*datasource.PrimaryKeyValue) []*model.PrimaryKeyValue {
	var res = make([]*model.PrimaryKeyValue, 0)
	for _, val := range info {
		res = append(res, &model.PrimaryKeyValue{
			ColumnName: val.ColumnName,
			Value:      val.Value,
		})
	}
	return res
}

func primaryKeyValueModelToDataSource(info []*model.PrimaryKeyValue) []*datasource.PrimaryKeyValue {
	var res = make([]*datasource.PrimaryKeyValue, 0)
	for _, val := range info {
		res = append(res, &datasource.PrimaryKeyValue{
			ColumnName: val.ColumnName,
			Value:      val.Value,
		})
	}
	return res
}

func tableMetaInfoDatasourceTOModel(info []*datasource.SQLAdvisorTableMetaData) []*model.SQLAdvisorTableMetaData {
	var res = make([]*model.SQLAdvisorTableMetaData, 0)
	for _, val := range info {
		var item = &model.SQLAdvisorTableMetaData{}
		item.Name = val.Name
		item.TableInfo = tableInfoDatasourceTOModel(val.TableInfo)
		item.ColumnInfo = columnInfoDatasourceTOModel(val.ColumnInfo)
		item.StatisticsInfo = statisticsInfoDatasourceTOModel(val.StatisticsInfo)
		item.InnodbTableStatsInfo = innodbTableStatsInfoDatasourceTOModel(val.InnodbTableStatsInfo)
		item.CreateTableInfo = createTableInfoDatasourceTOModel(val.CreateTableInfo)
		res = append(res, item)
	}
	return res
}

func tableInfoDatasourceTOModel(info *datasource.SQLAdvisorTableInfo) *model.SQLAdvisorTableInfo {
	if info != nil {
		return &model.SQLAdvisorTableInfo{
			TableCatalog:   info.TableCatalog,
			TableSchema:    info.TableSchema,
			TableName:      info.TableName,
			TableType:      info.TableType,
			Engine:         info.Engine,
			Version:        info.Version,
			RowFormat:      info.RowFormat,
			TableRows:      info.TableRows,
			AvgRowLength:   info.AvgRowLength,
			DataLength:     info.DataLength,
			MaxDataLength:  info.MaxDataLength,
			IndexLength:    info.IndexLength,
			DataFree:       info.DataFree,
			AutoIncrement:  info.AutoIncrement,
			CreateTime:     info.CreateTime,
			UpdateTime:     info.UpdateTime,
			CheckTime:      info.CheckTime,
			TableCollation: info.TableCollation,
			CheckSum:       info.CheckSum,
			CreateOptions:  info.CreateOptions,
			TableComment:   info.TableComment,
		}
	}
	return &model.SQLAdvisorTableInfo{}

}

func columnInfoDatasourceTOModel(info []*datasource.SQLAdvisorColumnInfo) []*model.SQLAdvisorColumnInfo {
	var res = make([]*model.SQLAdvisorColumnInfo, 0)
	for _, val := range info {
		res = append(res, &model.SQLAdvisorColumnInfo{
			TableCatalog:           val.TableCatalog,
			TableSchema:            val.TableSchema,
			TableName:              val.TableName,
			ColumnName:             val.ColumnName,
			OrdinalPosition:        val.OrdinalPosition,
			ColumnDefault:          val.ColumnDefault,
			IsNullable:             val.IsNullable,
			DataType:               val.DataType,
			CharacterMaximumLength: val.CharacterMaximumLength,
			CharacterOctetLength:   val.CharacterOctetLength,
			NumericPrecision:       val.NumericPrecision,
			CharacterSetName:       val.CharacterSetName,
			CollationName:          val.CollationName,
			ColumnType:             val.ColumnType,
			ColumnKey:              val.ColumnKey,
			Extra:                  val.Extra,
			Privileges:             val.Privileges,
			ColumnComment:          val.ColumnComment,
			GenerationExpression:   val.GenerationExpression,
			SrsID:                  val.SrsID,
		})
	}
	return res
}

func statisticsInfoDatasourceTOModel(info []*datasource.SQLAdvisorStatisticsInfo) []*model.SQLAdvisorStatisticsInfo {
	var res = make([]*model.SQLAdvisorStatisticsInfo, 0)
	for _, val := range info {
		res = append(res, &model.SQLAdvisorStatisticsInfo{
			TableCatalog: val.TableCatalog,
			TableSchema:  val.TableSchema,
			TableName:    val.TableName,
			NonUnique:    val.NonUnique,
			IndexSchema:  val.IndexSchema,
			IndexName:    val.IndexName,
			SeqInIndex:   val.SeqInIndex,
			ColumnName:   val.ColumnName,
			Collation:    val.Collation,
			Cardinality:  val.Cardinality,
			SubPart:      val.SubPart,
			Packed:       val.Packed,
			Nullable:     val.Nullable,
			IndexType:    val.IndexType,
			Comment:      val.Comment,
			IndexComment: val.IndexComment,
			IsVisible:    val.IsVisible,
			Expression:   val.Expression,
		})
	}
	return res

}
func innodbTableStatsInfoDatasourceTOModel(info *datasource.SQLAdvisorInnodbTableStatsInfo) *model.SQLAdvisorInnodbTableStatsInfo {
	if info != nil {
		return &model.SQLAdvisorInnodbTableStatsInfo{
			DatabaseName:         info.DatabaseName,
			TableName:            info.TableName,
			LastUpdate:           info.LastUpdate,
			NRows:                info.NRows,
			ClusteredIndexSize:   info.ClusteredIndexSize,
			SumOfOtherIndexSizes: info.SumOfOtherIndexSizes,
		}
	}
	return &model.SQLAdvisorInnodbTableStatsInfo{}

}

func createTableInfoDatasourceTOModel(info *datasource.SQLAdvisorCreateTableInfo) (res *model.SQLAdvisorCreateTableInfo) {
	if info != nil {
		return &model.SQLAdvisorCreateTableInfo{
			Table:       info.Table,
			CreateTable: info.CreateTable,
		}
	}
	return &model.SQLAdvisorCreateTableInfo{}
}

type createSQLAdvisorTaskReq struct {
	TenantId     string     `json:"tenantId"`
	Region       string     `json:"region"`
	InstanceType string     `json:"instanceType"`
	InstanceId   string     `json:"instanceId"`
	DbName       string     `json:"dbName"`
	DbType       string     `json:"dbType"`
	SQLList      []*sqlItem `json:"sqlList"`
}

type sqlItem struct {
	Query       string `json:"query"`
	TemplateMd5 string `json:"template_md5"`
}

func generateBody(req *model.CreateSQLAdvisorTaskReq, tenantId string) ([]byte, error) {

	region := ""
	if req.RegionId != nil {
		region = *req.RegionId
	} else {
		region = os.Getenv(`BDC_REGION_ID`)
	}

	DbType := SQLAdvisorDbType
	if dbw_utils.IsByteCloud() {
		DbType = SQLAdvisorDbTypeForInner
	}
	rreq := createSQLAdvisorTaskReq{
		TenantId:     tenantId,
		Region:       region, //os.Getenv(`BDC_REGION_ID`),
		InstanceType: req.InstanceType.String(),
		InstanceId:   req.InstanceID,
		DbName:       req.DbName,
		DbType:       DbType,
	}
	for _, val := range req.SQLList {
		rreq.SQLList = append(rreq.SQLList, &sqlItem{
			Query:       val,
			TemplateMd5: getMd5ForString(val),
		})
	}
	jsonData, err := json.Marshal(rreq)
	if err != nil {
		return []byte{}, err
	}
	return jsonData, nil
}

func generateSqlList(req *model.CreateSQLAdvisorTaskReq) string {
	var res []string
	for _, val := range req.SQLList {
		tmp := strings.ReplaceAll(val, "\"", "\\\"")
		res = append(res, fmt.Sprintf("{\"query\":\"%s\",\"template_md5\":\"%s\"}", tmp, getMd5ForString(val)))
		//res = append(res, fmt.Sprintf("{\"query\":\"%s\",\"template_md5\":\"%s\"}", val, getMd5ForString(val)))
	}
	return strings.Join(res, ",")
}

func getMd5ForString(str string) string {
	// 创建一个 MD5 哈希对象
	hash := md5.New()
	// 将字符串转换为字节数组并计算哈希值
	hash.Write([]byte(str))
	// 计算哈希值的字节切片
	hashBytes := hash.Sum(nil)
	// 将字节切片转换为十六进制字符串
	return hex.EncodeToString(hashBytes)
}

func GetBody(resp *http.Response) ([]byte, error) {
	buf := new(bytes.Buffer)
	if err := Save(resp, buf); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

func Save(resp *http.Response, w io.Writer) error {
	if resp == nil || resp.Body == nil {
		return nil
	}
	defer resp.Body.Close()
	_, err := io.Copy(w, resp.Body)
	return err
}

func DescribeSQLAdvisorTaskRespToModel(resp *describeSQLAdvisorTaskResp) *model.DescribeSQLAdvisorTaskResp {
	if resp == nil {
		return &model.DescribeSQLAdvisorTaskResp{
			Success: false,
			State:   model.SQLAdvisorTaskStatus_EXCEPTION,
			Message: "",
			Data:    nil,
		}
	}
	data := &model.SQLAdvisorTaskInfo{}
	// 执行中的话,不解析data
	taskStatus, err := model.SQLAdvisorTaskStatusFromString(resp.State)
	if err != nil {
		return &model.DescribeSQLAdvisorTaskResp{
			Success: false,
			State:   model.SQLAdvisorTaskStatus_EXCEPTION,
			Message: err.Error(),
			Data:    nil,
		}
	}
	if resp.State == model.SQLAdvisorTaskStatus_FETCHED.String() || resp.Data == nil {
		return &model.DescribeSQLAdvisorTaskResp{
			Success: resp.Success,
			Message: resp.Message,
			State:   taskStatus,
			Data:    nil,
		}
	}
	if resp.Data != nil {
		data.TaskId = resp.Data.TaskId
		data.OptimizeStatus = resp.Data.OptimizeStatus
		data.AdviceTimestamp = utils.Int64ToStr(resp.Data.AdviceTimeStamp)
	}
	for _, val := range resp.Data.IndexAdvices {
		data.IndexAdvices = append(data.IndexAdvices, IndexAdvicesToModel(val))
	}
	for _, val := range resp.Data.OptimizeAdvice {
		data.OptimizeAdvice = append(data.OptimizeAdvice, OptimizeAdvicesToModel(val))
	}
	return &model.DescribeSQLAdvisorTaskResp{
		Success: resp.Success,
		Message: resp.Message,
		State:   taskStatus,
		Data:    data,
	}
}

func OptimizeAdvicesToModel(adv *OptimizeAdvice) *model.OptimizeAdvice {
	if adv == nil {
		return nil
	}
	var res = &model.OptimizeAdvice{}
	res.AdviceCode = adv.AdviceCode
	res.AdviceContent = adv.AdviceContent
	res.AdviceType = adv.AdviceType
	if adv.ExtraInfo != nil {
		res.ExtraInfo = &model.ExtraInfo{}
	}
	if len(adv.RelatedFingerPrints) != 0 {
		for _, val := range adv.RelatedFingerPrints {
			res.RelatedFingerPrints = append(res.RelatedFingerPrints, &model.FigerPrint{
				FingerPrintMd5: val.FingerPrintMd5,
				Benefit:        val.Benefit,
			})
		}
	}
	res.Template = adv.Template
	res.TemplateEn = adv.TemplateEn
	return res
}

func IndexAdvicesToModel(adv *IndexAdvice) *model.IndexAdvice {
	if adv == nil {
		return nil
	}
	var res = &model.IndexAdvice{}
	if len(adv.Columns) != 0 {
		for _, val := range adv.Columns {
			res.Columns = append(res.Columns, &model.SQLAdvisorColumns{
				Name: val.Name,
			})
		}
	}
	if len(adv.RelatedFingerPrints) != 0 {
		for _, val := range adv.RelatedFingerPrints {
			res.RelatedFingerPrints = append(res.RelatedFingerPrints, &model.FigerPrint{
				FingerPrintMd5: val.FingerPrintMd5,
				Benefit:        val.Benefit,
			})
		}
	}
	res.DbName = adv.DbName
	res.DDL = adv.DDL
	res.TableName = adv.TableName
	return res
}

func GetDBType(instanceId string) model.InstanceType {
	res := strings.Split(instanceId, "-")
	var dbTypeStr string
	if len(res) > 0 {
		dbTypeStr = res[0]
	}
	switch dbTypeStr {
	case "mysql":
		return model.InstanceType_MySQL
	case "vedbm":
		return model.InstanceType_VeDBMySQL
	case "myshard":
		return model.InstanceType_MySQLSharding
	default:
		return model.InstanceType_MySQL
	}
}

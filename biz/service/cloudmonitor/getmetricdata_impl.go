package cloudmonitor

import (
	"context"
	"errors"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	dbwutils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	volcobserveopen "code.byted.org/iaasng/volcstack-go-inner-sdk/service_open/volcobserve"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/credentials"
	"github.com/volcengine/volcengine-go-sdk/volcengine/custom"
	"github.com/volcengine/volcengine-go-sdk/volcengine/response"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
	"github.com/volcengine/volcengine-go-sdk/volcengine/volcengineutil"
)

type Namespace string

const (
	VCM_HBase                   Namespace = "VCM_HBase"
	VCM_MongoDB_Replica         Namespace = "VCM_MongoDB_Replica"
	VCM_MongoDB_Sharded_Cluster Namespace = "VCM_MongoDB_Sharded_Cluster"
	VCM_MySQL_Sharding          Namespace = "VCM_MySQL_Sharding"
	VCM_RDS_MySQL               Namespace = "VCM_RDS_MySQL"
	VCM_RDS_PostgreSQL          Namespace = "VCM_RDS_PostgreSQL"
	VCM_Redis                   Namespace = "VCM_Redis"
	VCM_veDB_MySQL              Namespace = "VCM_veDB_MySQL"
	VCM_veGraph_db              Namespace = "VCM_veGraph_db"
	CloudMonitorService                   = "volc-observe"
)

type MetricSubName struct {
	MetricName   string
	SubNamespace string
}

var Cpu = map[Namespace][]MetricSubName{
	VCM_HBase:                   {{"ClusterCpuUtil", "Cluster"}, {"CpuUtil", "RegionServer"}},
	VCM_MongoDB_Replica:         {{"AggregatedCpuUtil", "instance"}, {"CpuUtil", "replica"}},
	VCM_MongoDB_Sharded_Cluster: {{"CpuUtil", "config"}, {"AggregatedCpuUtil", "instance"}, {"CpuUtil", "mongos"}, {"CpuUtil", "shard"}},
	VCM_MySQL_Sharding:          {{"ProxyCpuUtil", "proxy_monitor"}, {"CpuUtil", "resource_monitor_new"}, {"PodCpuUtil", "vedb_pod"}},
	VCM_RDS_MySQL:               {{"ProxyCpuUtil", "proxy_monitor"}, {"CpuUtil", "resource_monitor"}},
	VCM_RDS_PostgreSQL:          {{"CpuUtil", "resource_monitor"}},
	VCM_Redis:                   {{"AggregatedCpuUtil", "aggregated_proxy"}, {"AggregatedCpuUtil", "aggregated_server"}, {"CpuUtil", "proxy"}, {"CpuUtil", "server"}},
	VCM_veDB_MySQL:              {{"CpuUtil", "Instance"}, {"PodCpuUtil", "Pod"}},
	VCM_veGraph_db:              {{"CpuUtil", "Instance"}, {"CpuUtil", "Pod"}},
}

var Mem = map[Namespace][]MetricSubName{
	VCM_HBase:                   {{"ClusterMemUtil", "Cluster"}, {"MemUtil", "RegionServer"}},
	VCM_MongoDB_Replica:         {{"AggregatedMemUtil", "instance"}, {"MemUtil", "replica"}},
	VCM_MongoDB_Sharded_Cluster: {{"MemUtil", "config"}, {"AggregatedMemUtil", "instance"}, {"MemUtil", "mongos"}, {"MemUtil", "shard"}},
	VCM_MySQL_Sharding:          {{"ProxyMemUtil", "proxy_monitor"}, {"MemUtil", "resource_monitor"}},
	VCM_RDS_MySQL:               {{"ProxyMemUtil", "proxy_monitor"}, {"MemUtil", "resource_monitor"}},
	VCM_RDS_PostgreSQL:          {{"MemUtil", "resource_monitor"}},
	VCM_Redis:                   {{"AggregatedMemUtil", "aggregated_server"}, {"MemUtil", "server"}},
	VCM_veDB_MySQL:              {{"MemUtil", "Instance"}, {"PodMemUtil", "Pod"}},
	VCM_veGraph_db:              {{"MemUtil", "Instance"}, {"MemUtil", "Pod"}},
}

type Instance struct {
	Dimensions []Dimension
}

type Dimension struct {
	Name  string
	Value string
}

type Input struct {
	StartTime, EndTime         int64
	MetricName, Region, Period string
	Namespace, SubNamespace    string
	Ak, Sk, Token              string
	Endpoint                   string //boe环境要配endpoint，线上环境不配
	Instances                  []Instance
	GroupBy                    []string
}

type CloudMonitorInput struct {
	Input *Input
}

type ClientCloudMonitor struct {
	client *volcobserveopen.VOLCOBSERVE
}

func NewClient(in *CloudMonitorInput) (ClientCloudMonitor, error) {
	if in.Input == nil {
		return ClientCloudMonitor{}, errors.New("input can't be empty")
	}

	config := volcengine.NewConfig().
		WithRegion(in.Input.Region).
		WithCustomerUnmarshalData(func(ctx context.Context, info custom.RequestInfo, resp response.VolcengineResponse) interface{} {
			if info.ClientInfo.ServiceName == "volc_observe" {
				d, _ := volcengineutil.ObtainSdkValue("Data", resp.Result)
				return d
			}
			return resp.Result
		}).
		WithCredentials(credentials.NewStaticCredentials(in.Input.Ak, in.Input.Sk, in.Input.Token)).
		WithDisableSSL(true).WithEndpoint(dbwutils.GetServiceEndpoint(context.Background(), CloudMonitorService, ""))

	sess, err := session.NewSession(config)
	if err != nil {
		log.Warn(context.Background(), "creat client fail")
		return ClientCloudMonitor{}, err
	}
	return ClientCloudMonitor{
		client: volcobserveopen.New(sess),
	}, nil
}

func (c *ClientCloudMonitor) GetMetricDataDbw(ctx context.Context, in *CloudMonitorInput) (resp *volcobserveopen.GetMetricDataOutput, err error) {
	var insts []*volcobserveopen.InstanceForGetMetricDataInput
	for _, i := range in.Input.Instances {
		var dimen []*volcobserveopen.DimensionForGetMetricDataInput
		for _, d := range i.Dimensions {
			dimen = append(dimen, &volcobserveopen.DimensionForGetMetricDataInput{
				Name:  utils.StringRef(d.Name),
				Value: utils.StringRef(d.Value),
			})
		}
		insts = append(insts, &volcobserveopen.InstanceForGetMetricDataInput{
			Dimensions: dimen,
		})
	}

	input := &volcobserveopen.GetMetricDataInput{
		StartTime:    volcengine.Int64(in.Input.StartTime),
		EndTime:      volcengine.Int64(in.Input.EndTime),
		Namespace:    volcengine.String(in.Input.Namespace),
		MetricName:   volcengine.String(in.Input.MetricName),
		SubNamespace: volcengine.String(in.Input.SubNamespace),
		Period:       volcengine.String(in.Input.Period),
		GroupBy:      volcengine.StringSlice(in.Input.GroupBy),
		Instances:    insts,
	}

	resp, err = c.client.GetMetricData(input)
	if err != nil {
		log.Warn(ctx, "GetMetricData fail")
		return nil, consts.ErrorWithParam(model.ErrorCode_GetMetricDataFailed, err.Error())
	}

	return resp, nil
}

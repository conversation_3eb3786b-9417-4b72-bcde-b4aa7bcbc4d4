package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"

	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"

	"code.byted.org/infcs/dbw-mgr/biz/conv"
	rdsModelV1 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2018-01-01/kitex_gen/model"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"gorm.io/gorm"

	"io/ioutil"
	"net"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity/instance"
	"code.byted.org/infcs/dbw-mgr/biz/instance/control"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	metaMysqlModel "code.byted.org/infcs/dbw-mgr/gen/meta-mysql-mgr/2022-01-01/kitex_gen/model/v2"
	metaMysqlModelv1 "code.byted.org/infcs/dbw-mgr/gen/meta-mysql-mgr/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-lib/framework/db"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

type NewMetaMySQLDataSourceIn struct {
	dig.In
	Conf            config.ConfigProvider
	MetaMySQLMgr    mgr.Provider `name:"meta_mysql"`
	C3ConfProvider  c3.ConfigProvider
	IdSvc           idgen.Service
	ActorClient     cli.ActorClient
	InstanceManager control.InstanceManager
	UserMgmtSvc     usermgmt.UserService
}

//func NewMetaMySQLDataSource(p NewMetaMySQLDataSourceIn) NewMySQLDataSourceOut {
//	return NewMySQLDataSourceOut{
//		Source: retryIfWhiteListNotReady(&metaMySQLImpl{
//			cnf:             p.Conf,
//			metaMysql:       p.MetaMySQLMgr,
//			C3ConfProvider:  p.C3ConfProvider,
//			instanceManager: p.InstanceManager,
//		}),
//	}
//}

func NewMetaMySQLDataSource(p NewMetaMySQLDataSourceIn) NewMySQLDataSourceOut {
	mpl := &metaMySQLImpl{
		cnf:             p.Conf,
		metaMysql:       p.MetaMySQLMgr,
		C3ConfProvider:  p.C3ConfProvider,
		instanceManager: p.InstanceManager,
		userMgmtSvc:     p.UserMgmtSvc,
	}
	mpl.ConnPool = datasource.NewPool(datasource.NewPoolIn{
		C3:    p.C3ConfProvider,
		Conf:  p.Conf,
		DsSvc: mpl,
	})
	return NewMySQLDataSourceOut{
		Source: retryIfWhiteListNotReady(mpl),
	}
}

type NewMetaMySQLDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

type metaMySQLImpl struct {
	datasource.DataSourceService
	cnf             config.ConfigProvider
	metaMysql       mgr.Provider
	C3ConfProvider  c3.ConfigProvider
	instanceManager control.InstanceManager
	ConnPool        datasource.Pool
	userMgmtSvc     usermgmt.UserService
}

func (m *metaMySQLImpl) wrapperCtx(ctx context.Context, instanceId string) context.Context {
	ins, err := m.instanceManager.GetInstance(ctx, instanceId, model.LinkType_Public.String())
	if err != nil {
		log.Warn(ctx, "get instance %s error %s", instanceId, err.Error())
		return ctx
	}
	bizCtx := fwctx.GetBizContext(ctx)
	if bizCtx == nil {
		bizCtx = fwctx.NewBizContext()
		ctx = context.WithValue(ctx, fwctx.BIZ_CONTEXT_KEY, bizCtx)
	}
	if bizCtx.Extra == nil {
		log.Warn(ctx, "get extra is null")
		extra := make(map[string]string)
		bizCtx.Extra = extra
	}
	if ins.Extra != nil {
		regionName := ins.Extra["MetaMySQLRegionName"]
		if regionName != "" {
			bizCtx.Extra["MetaMySQLRegionName"] = regionName
		}
	}
	return ctx
}

func (m *metaMySQLImpl) AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error) {
	return "", nil
}

func (m *metaMySQLImpl) UpdateWhiteList(ctx context.Context, id string, ds *shared.DataSource, ip []string) error {
	return nil
}

func (m *metaMySQLImpl) RemoveWhiteList(ctx context.Context, id string, ds *shared.DataSource, wlID string) error {
	return nil
}

func (m *metaMySQLImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	return m.FillInnerDataSource(ctx, ds)
}

func (m *metaMySQLImpl) FillInnerDataSource(ctx context.Context, ds *shared.DataSource) error {
	ins, err := m.instanceManager.GetInstance(ctx, ds.GetInstanceId(), model.LinkType_Public.String())
	if err != nil {
		if err != gorm.ErrRecordNotFound && ds.LinkType.String() != model.LinkType_Volc.String() {
			err = consts.ErrorOf(model.ErrorCode_InstanceNotFound)
			return err
		} else {
			return err
		}
	}
	ds.Address = ins.Address
	return nil
}

func (m *metaMySQLImpl) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	conn, err := m.getConn(ctx, ds)
	if err != nil {
		log.Warn(ctx, "check conn by fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	_, err = conn.Version()
	if err != nil {
		log.Warn(ctx, "get version by %v fail %v", err)
		return consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	return nil
}

func (m *metaMySQLImpl) getConn(ctx context.Context, ds *shared.DataSource) (db.Conn, error) {
	opt := &db.Options{
		Address:          ds.Address,
		DB:               ds.Db,
		User:             ds.User,
		Password:         ds.Password,
		Driver:           db.MysqlDriver,
		ParseTimeManualy: false,
	}
	if ds.ConnectTimeoutMs != 0 {
		opt.Timeout = uint(ds.ConnectTimeoutMs)
	}
	if ds.ReadTimeoutMs != 0 {
		opt.ReadTimeout = uint(ds.ReadTimeoutMs)
	}
	if ds.WriteTimeoutMs != 0 {
		opt.WriteTimeout = uint(ds.WriteTimeoutMs)
	}
	return db.NewConn(opt)
}

func (m *metaMySQLImpl) CheckDataSource(ctx context.Context, ds *shared.DataSource) error {
	return nil
}

func (m *metaMySQLImpl) KillQuery(ctx context.Context, ds *shared.DataSource, conn *shared.ConnectionInfo) error {
	panic("implement me")
}

func (m *metaMySQLImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	var (
		tenantId, userId        string
		skipPrivilegeValidation bool
	)
	if req.TenantId != "" {
		tenantId = req.TenantId
	} else {
		tenantId = fwctx.GetTenantID(ctx)
	}
	log.Info(ctx, "get metamysql instance list")
	userId = fwctx.GetUserID(ctx)
	role := m.userMgmtSvc.DescribeRole(ctx, userId, tenantId)
	// 管理员、跳过权限校验直接返回所有实例
	if !req.OwndInstancePrivilege || role == model.DbwRoleType_ADMIN {
		skipPrivilegeValidation = true
	} else {
		skipPrivilegeValidation = false
	}
	query := dao.ListQuery{
		InstanceName: req.InstanceName,
		InstanceId:   req.InstanceId,
		Mode:         model.ControlMode_Management.String(),
	}
	instances, total, err := m.instanceManager.ListInstance(ctx, m.Type().String(), tenantId, userId, model.LinkType_Public.String(), int(req.PageSize), int(req.PageNumber-1), skipPrivilegeValidation, query)
	if err != nil {
		return nil, err
	}
	log.Info(ctx, "list instance, total=%d", total)
	var instanceListModel []*model.InstanceInfo
	fp.StreamOf(instances).
		Map(func(inst *instance.CtrlInstance) *model.InstanceInfo {
			return &model.InstanceInfo{
				InstanceId:     utils.StringRef(inst.InstanceId),
				InstanceStatus: "Running",
				LinkType:       inst.Source,
				InstanceName:   utils.StringRef(inst.InstanceName),
				InstanceType:   model.InstanceType_MetaMySQL,
			}
		}).ToSlice(&instanceListModel)
	return &datasource.ListInstanceResp{
		Total:        total,
		InstanceList: instanceListModel,
	}, nil
}

func (m *metaMySQLImpl) ListInstanceLightWeight(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	return m.ListInstance(ctx, req)
}

func (m *metaMySQLImpl) selfDescribeDBInstances(ctx context.Context, req *metaMysqlModel.DescribeDBInstancesReq) (*metaMysqlModel.DescribeDBInstancesResp, error) {
	resp := &metaMysqlModel.DescribeDBInstancesResp{}
	if err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DescribeDBInstances.String(), req, resp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to get rds instances, err=%v", err)
		return nil, err
	}
	return resp, nil
}

func (m *metaMySQLImpl) ListDatabases(ctx context.Context, req *datasource.ListDatabasesReq) (*datasource.ListDatabasesResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	ret := &datasource.ListDatabasesResp{}
	var dbList []*DatabaseInfo
	sql := "select SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME from information_schema.SCHEMATA where SCHEMA_NAME !='byte_rds_meta' and 1=1 "
	var args []interface{}
	if req.Keyword != "" {
		sql += " and SCHEMA_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if !req.EnableSystemDB {
		sql += " and SCHEMA_NAME not in  (?) "
		systemDB := []string{"information_schema", "performance_schema", "mysql", "sys"}
		args = append(args, systemDB)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	if err := conn.Raw(sql, args...).Scan(&dbList); err != nil {
		return nil, err
	}

	if req.Keyword == "" {
		if err = conn.Raw("select count(1) from information_schema.SCHEMATA where SCHEMA_NAME !='byte_rds_meta' " + DBW_CONSOLE_DEFAULT_HINT).
			Scan(&ret.Total); err != nil {
			return nil, err
		}
	} else {
		sqlCount := "select count(1) from information_schema.SCHEMATA where SCHEMA_NAME like ? "
		sqlCount += DBW_CONSOLE_DEFAULT_HINT
		if err = conn.Raw(sqlCount, `%`+req.Keyword+`%`).
			Scan(&ret.Total); err != nil {
			return nil, err
		}
	}

	if err = fp.StreamOf(dbList).Map(func(db *DatabaseInfo) *shared.DatabaseInfo {
		return &shared.DatabaseInfo{
			Name:             db.SchemaName,
			CharacterSetName: db.CharacterSetName,
			CollationName:    db.CollationName,
		}
	}).ToSlice(&ret.Items); err != nil {
		return nil, err
	}
	return ret, nil
}

func (m *metaMySQLImpl) ListInstanceNodes(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {
	query := metaMysqlModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	resp := metaMysqlModel.DescribeDBInstanceDetailResp{}
	if err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DescribeDBInstanceDetail.String(), query, &resp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesResp{}
	fp.StreamOf(resp.Nodes).
		Map(func(node *metaMysqlModel.NodeDetailInfoObject) *model.NodeInfoObject {
			nodeType, _ := model.NodeTypeFromString(node.NodeType.String())
			return &model.NodeInfoObject{
				NodeId:   node.NodeId,
				NodeType: nodeType,
				CpuNum:   node.VCPU,
				MemInGiB: node.Memory,
				ZoneId:   node.ZoneId,
			}
		}).ToSlice(&ret.Nodes)
	return ret, nil
}

func (m *metaMySQLImpl) ListInstanceNodesOri(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesOriResp, error) {
	ctx = m.wrapperCtx(ctx, req.InstanceId)
	query := metaMysqlModelv1.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	resp := metaMysqlModelv1.ListInstanceNodesResp{}
	if err := m.metaMysql.Get().Call(ctx, metaMysqlModelv1.Action_ListInstanceNodes.String(), query, &resp, client.WithVersion(RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesOriResp{}
	fp.StreamOf(resp.Datas).
		Map(func(node *metaMysqlModelv1.InstanceNodeInfo) *datasource.InstanceNodeInfo {
			return &datasource.InstanceNodeInfo{
				NodeID:     node.NodeId,
				PodName:    node.NodeName,
				Role:       node.NodeRole.String(),
				Zone:       "",
				NodeStatus: node.NodeStatus.String(),
			}
		}).ToSlice(&ret.NodesInfo)
	return ret, nil
}
func (m *metaMySQLImpl) ListInstancePods(ctx context.Context, req *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	type SimplifiedNodeDetail struct {
		NodeId    string
		NodeType  string
		NodeState string
		NodeName  string
	}
	var nodeDetails []*SimplifiedNodeDetail
	instanceNodesReq := &metaMysqlModelv1.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	instanceNodesResp := &metaMysqlModelv1.ListInstanceNodesResp{}
	if err := m.metaMysql.Get().Call(ctx, metaMysqlModelv1.Action_ListInstanceNodes.String(), instanceNodesReq, instanceNodesResp, client.WithVersion(RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "ListInstanceNodes failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	log.Info(ctx, "Instance %s Nodes is %s", req.InstanceId, instanceNodesResp)
	nodeInfos := instanceNodesResp.Datas
	if err := fp.StreamOf(nodeInfos).Map(func(db *metaMysqlModelv1.InstanceNodeInfo) *SimplifiedNodeDetail {
		item := &SimplifiedNodeDetail{
			NodeId:    db.NodeId,
			NodeState: db.NodeStatus.String(),
			NodeName:  db.NodeName,
		}
		switch db.NodeRole {
		case metaMysqlModelv1.NodeRole_Master:
			item.NodeType = metaMysqlModel.NodeType_Primary.String()
		case metaMysqlModelv1.NodeRole_Slave:
			item.NodeType = metaMysqlModel.NodeType_Secondary.String()
		case metaMysqlModelv1.NodeRole_ReadOnly:
			item.NodeType = metaMysqlModel.NodeType_ReadOnly.String()
		default:
			item.NodeType = ""
		}
		return item
	}).ToSlice(&nodeDetails); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	rreq := &metaMysqlModelv1.ListInstancePodsReq{
		InstanceId: req.InstanceId,
	}
	rresp := &metaMysqlModelv1.ListInstancePodsResp{}
	log.Info(ctx, "Mysql instanceId:%s listInstancePodsReq:%s", req.InstanceId, rreq.String())
	if err := m.metaMysql.Get().Call(ctx, metaMysqlModelv1.Action_ListInstancePods.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "ListInstancePods failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	ret := &datasource.ListInstancePodsResp{}
	if err := fp.StreamOf(rresp.Datas).Map(func(db *metaMysqlModelv1.KubePod) *shared.KubePod {
		return &shared.KubePod{
			Name:        db.Name,
			Zone:        db.Zone,
			KubeCluster: db.KubeCluster,
			Region:      db.Region,
			NodeIP:      db.NodeIP,
			PodIP:       db.PodIP,
			NodePool:    db.NodePool,
			Component:   db.Component,
			Containers:  toMetaRdsKubeContainerModelToShared(db.Containers),
		}
	}).ToSlice(&ret.Data); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	res := make([]*shared.KubePod, 0, len(ret.Data))
	for _, pod := range ret.Data {
		if pod != nil {
			for _, node := range nodeDetails {
				if pod.Name == node.NodeName {
					pod.Role = node.NodeType
					pod.NodeId = node.NodeId
				}
			}
			res = append(res, pod)
		}
	}
	ret.Data = res
	ret.Total = int32(len(res))
	return ret, nil
}

func (m *metaMySQLImpl) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (*datasource.DescribeDBInstanceDetailResp, error) {
	ctx = m.wrapperCtx(ctx, req.InstanceId)
	describeDBInstanceDetailReq := &metaMysqlModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &metaMysqlModel.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	m.wrapperCtx(ctx, req.InstanceId)
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetail InstanceNotFound, err=%v", err)
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		}

		return nil, err
	}

	resp := &datasource.DescribeDBInstanceDetailResp{
		InstanceId:      describeDBInstanceDetailResp.BasicInfo.GetInstanceId(),
		InstanceName:    describeDBInstanceDetailResp.BasicInfo.GetInstanceName(),
		InstanceStatus:  describeDBInstanceDetailResp.BasicInfo.GetInstanceStatus().String(),
		RegionId:        describeDBInstanceDetailResp.BasicInfo.GetRegionId(),
		ZoneId:          describeDBInstanceDetailResp.BasicInfo.GetZoneId(),
		DBEngine:        describeDBInstanceDetailResp.BasicInfo.GetDBEngine().String(),
		DBEngineVersion: describeDBInstanceDetailResp.BasicInfo.GetDBEngineVersion().String(),
		InstanceType:    describeDBInstanceDetailResp.BasicInfo.GetInstanceType().String(),
		VCPU:            describeDBInstanceDetailResp.BasicInfo.VCPU,
		Memory:          describeDBInstanceDetailResp.BasicInfo.Memory,
		ProjectName:     describeDBInstanceDetailResp.BasicInfo.ProjectName,
		StorageSpace:    describeDBInstanceDetailResp.BasicInfo.StorageSpace,
		NodeSpec:        describeDBInstanceDetailResp.BasicInfo.NodeSpec,
	}
	if describeDBInstanceDetailResp.BasicInfo.IsSetPerconaVersion() {
		resp.DBEngineSubVersion = describeDBInstanceDetailResp.BasicInfo.GetPerconaVersion().String()
	}
	fp.StreamOf(describeDBInstanceDetailResp.Endpoints).Map(func(endpoint *metaMysqlModel.ConnectionInfoObject) *datasource.Endpoint {
		var mode datasource.EndpointMode
		switch endpoint.GetReadWriteMode() {
		case metaMysqlModel.ReadWriteMode_ReadWrite:
			mode = datasource.ReadWrite
		case metaMysqlModel.ReadWriteMode_ReadOnly:
			mode = datasource.ReadOnly
		default:
			mode = datasource.Default
		}
		edp := &datasource.Endpoint{
			Mode:                     mode,
			EndpointName:             endpoint.GetEndpointName(),
			EndpointId:               endpoint.GetEndpointId(),
			EndpointType:             endpoint.GetEndpointType().String(),
			EnableReadWriteSplitting: endpoint.GetEnableReadWriteSplitting().String(),
		}
		return edp
	}).ToSlice(&resp.Endpoints)
	return resp, nil
}

func (m *metaMySQLImpl) DescribeDBInstanceDetailForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	describeDBInstanceDetailReq := &metaMysqlModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &metaMysqlModel.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetailForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetailForPilot, err=%v", err)
			return "", err
		}
	}

	return utils.Show(describeDBInstanceDetailResp), nil
}

func (m *metaMySQLImpl) DescribeDBInstanceEndpoints(ctx context.Context, req *datasource.DescribeDBInstanceEndpointsReq) (*datasource.DescribeDBInstanceEndpointsResp, error) {
	ctx = m.wrapperCtx(ctx, req.InstanceId)
	var endpointList []*datasource.EndpointInfo
	describeDBInstanceDetailReq := &metaMysqlModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	resp := &metaMysqlModel.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetail InstanceNotFound, err=%v", err)
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		}
		return nil, err
	}
	nodeMap := make(map[metaMysqlModel.NodeType]string)
	for _, node := range resp.Nodes {
		nodeMap[node.NodeType] = node.NodeId
	}
	if err = fp.StreamOf(resp.Endpoints).Map(func(i *metaMysqlModel.ConnectionInfoObject) *datasource.EndpointInfo {
		var nodes []string
		for _, object := range i.ReadOnlyNodeWeight {
			if *object.Weight > 0 {
				if object.NodeId != "" {
					nodes = append(nodes, object.NodeId)
				} else if object.NodeType == metaMysqlModel.NodeType_Primary {
					nodes = append(nodes, nodeMap[metaMysqlModel.NodeType_Primary])
				}
			}
		}
		return &datasource.EndpointInfo{
			EndpointName:  i.EndpointName,
			EndpointID:    i.EndpointId,
			EndpointType:  i.EndpointType.String(),
			EndpointPort:  i.GetEndpointProxyPort(),
			ReadWriteMode: i.GetReadWriteMode().String(),
			NodeID:        nodes,
		}
	}).ToSlice(&endpointList); err != nil {
		return nil, err
	}
	ret := &datasource.DescribeDBInstanceEndpointsResp{
		Endpoints: endpointList,
	}
	return ret, nil
}

func (m *metaMySQLImpl) DescribeDBInstanceShardInfos(ctx context.Context, req *datasource.DescribeDBInstanceShardInfosReq) (*datasource.DescribeDBInstanceShardInfosResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeDBInstanceCluster(ctx context.Context, req *datasource.DescribeDBInstanceClusterReq) (*datasource.DescribeDBInstanceClusterResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeDBInstanceAuditCollectedPod(ctx context.Context, req *datasource.DescribeDBInstanceAuditCollectedPodReq) (*datasource.DescribeDBInstanceAuditCollectedPodResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) OpenDBInstanceAuditLog(ctx context.Context, req *datasource.OpenDBInstanceAuditLogReq) (*datasource.OpenDBInstanceAuditLogResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) CloseDBInstanceAuditLog(ctx context.Context, req *datasource.CloseDBInstanceAuditLogReq) (*datasource.CloseDBInstanceAuditLogResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) CheckDBInstanceAuditLogStatus(ctx context.Context, req *datasource.CheckDBInstanceAuditLogStatusReq) (*datasource.CheckDBInstanceAuditLogStatusResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeDBProxyConfig(ctx context.Context, req *datasource.DescribeDBProxyConfigReq) (*datasource.DescribeDBProxyConfigResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeInstanceFeatures(ctx context.Context, req *datasource.DescribeInstanceFeaturesReq) (*datasource.DescribeInstanceFeaturesResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeDBInstanceSSL(ctx context.Context, req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	return &datasource.DescribeDBInstanceSSLResp{
		SSLEnable: false,
	}, nil
}

func (m *metaMySQLImpl) DescribeSQLCCLConfig(ctx context.Context, req *datasource.DescribeSQLCCLConfigReq) (*datasource.DescribeSQLCCLConfigResp, error) {
	return &datasource.DescribeSQLCCLConfigResp{
		SQLConcurrencyControlStatus: false,
	}, nil
}

func (m *metaMySQLImpl) ModifySQLCCLConfig(ctx context.Context, req *datasource.ModifySQLCCLConfigReq) (*datasource.ModifySQLCCLConfigResp, error) {
	modifySQLConcurrencyControlConfigReq := &metaMysqlModel.ModifySQLConcurrencyControlConfigReq{
		InstanceId:                  req.InstanceId,
		EnableSQLConcurrencyControl: req.EnableSqlConcurrencyControl,
	}
	resp := &metaMysqlModel.ModifyProxySQLConcurrencyControlConfigResp{}
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_ModifySQLConcurrencyControlConfig.String(), modifySQLConcurrencyControlConfigReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call MetaRDS ModifySQLConcurrencyControlConfig, err=%v", err)
		return nil, err
	}
	return &datasource.ModifySQLCCLConfigResp{}, nil
}

func (m *metaMySQLImpl) AddSQLCCLRule(ctx context.Context, req *datasource.AddSQLCCLRuleReq) (*datasource.AddSQLCCLRuleResp, error) {
	// FIXME:2.10.2版本需要手动打开限流开关，后续版本可以去掉
	_, err := m.ModifySQLCCLConfig(ctx, &datasource.ModifySQLCCLConfigReq{
		InstanceId:                  req.InstanceId,
		EnableSqlConcurrencyControl: true,
	})
	if err != nil {
		return nil, err
	}
	cclRule := &metaMysqlModel.CCLRule{
		Type:             req.CCLRule.SqlType,
		User:             req.CCLRule.UserID,
		Host:             req.CCLRule.HostName,
		SchemaName:       req.CCLRule.SchemaName,
		TableName:        req.CCLRule.TableName,
		ConcurrencyCount: int64(req.CCLRule.ConcurrencyCount),
		KeyWords:         req.CCLRule.Keywords,
		State:            req.CCLRule.State,
		Ordered:          req.CCLRule.Ordered,
		MaxQueueSize:     req.CCLRule.MaxQueueSize,
		WaitTimeout:      req.CCLRule.WaitTimeout,
	}
	addCCLRuleReq := &metaMysqlModel.CreateSQLConcurrencyControlRuleReq{
		InstanceId: req.InstanceId,
		CCLRule:    cclRule,
	}
	resp := &metaMysqlModel.CreateSQLConcurrencyControlRuleResp{}
	err = m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_CreateSQLConcurrencyControlRule.String(), addCCLRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds CreateSQLConcurrencyControlRule, err=%+v", err)
		return nil, err
	}
	return &datasource.AddSQLCCLRuleResp{}, nil
}

func (m *metaMySQLImpl) ModifyProxyThrottleRule(ctx context.Context, req *datasource.ModifyProxyThrottleRuleReq) (*datasource.ModifyProxyThrottleRuleResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DeleteSQLCCLRule(ctx context.Context, req *datasource.DeleteSQLCCLRuleReq) (*datasource.DeleteSQLCCLRuleResp, error) {
	cclRule := &metaMysqlModel.CCLRule{
		Type:             req.CCLRule.SqlType,
		User:             req.CCLRule.UserID,
		Host:             req.CCLRule.HostName,
		SchemaName:       req.CCLRule.SchemaName,
		TableName:        req.CCLRule.TableName,
		ConcurrencyCount: int64(req.CCLRule.ConcurrencyCount),
		KeyWords:         req.CCLRule.Keywords,
		State:            req.CCLRule.State,
		Ordered:          req.CCLRule.Ordered,
		MaxQueueSize:     req.CCLRule.MaxQueueSize,
		WaitTimeout:      req.CCLRule.WaitTimeout,
	}
	deleteCCLRuleReq := &metaMysqlModel.DeleteSQLConcurrencyControlRuleReq{
		InstanceId: req.InstanceId,
		CCLRule:    cclRule,
	}
	resp := &metaMysqlModel.DeleteSQLConcurrencyControlRuleResp{}
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DeleteSQLConcurrencyControlRule.String(), deleteCCLRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds DeleteSQLConcurrencyControlRule, err=%+v", err)
		return nil, err
	}
	return &datasource.DeleteSQLCCLRuleResp{}, nil
}

// 弃用
func (m *metaMySQLImpl) FlushSQLCCLRule(ctx context.Context, req *datasource.FlushSQLCCLRuleReq) (*datasource.FlushSQLCCLRuleResp, error) {
	//flushCCLRuleReq := &metaMysqlModel.FlushSQLConcurrencyControlRuleReq{
	//	InstanceId: req.InstanceId,
	//}
	//resp := &metaMysqlModel.FlushSQLConcurrencyControlRuleResp{}
	//err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_FlushSQLConcurrencyControlRule.String(), flushCCLRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	//if err != nil {
	//	log.Warn(ctx, "failed to call Rds DeleteSQLConcurrencyControlRule, err=%+v", err)
	//	return nil, err
	//}
	return &datasource.FlushSQLCCLRuleResp{}, nil
}

func (m *metaMySQLImpl) ListSQLCCLRules(ctx context.Context, req *datasource.ListSQLCCLRulesReq) (*datasource.ListSQLCCLRulesResp, error) {
	describeCCLRuleReq := &metaMysqlModel.DescribeSQLConcurrencyControlRuleReq{
		InstanceId: req.InstanceId,
	}
	resp := &metaMysqlModel.DescribeSQLConcurrencyControlRuleResp{}
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DescribeSQLConcurrencyControlRule.String(), describeCCLRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds DeleteSQLConcurrencyControlRule, err=%+v", err)
		return nil, err
	}
	ret := &datasource.ListSQLCCLRulesResp{}
	if err := fp.StreamOf(resp.CCLRules).Map(func(rule *metaMysqlModel.CCLRuleShow) *datasource.CCLRuleInfo {
		return &datasource.CCLRuleInfo{
			RuleID:           rule.GetId(),
			UserID:           rule.GetUser(),
			HostName:         rule.GetHost(),
			Keywords:         rule.GetKeyWords(),
			SchemaName:       rule.GetSchemaName(),
			TableName:        rule.GetTableName(),
			SqlType:          rule.GetType(),
			MaxQueueSize:     rule.GetMaxQueueSize(),
			WaitTimeout:      rule.GetWaitTimeout(),
			ConcurrencyCount: int32(rule.GetConcurrencyCount()),
			State:            rule.GetState(),
			Ordered:          rule.GetOrdered(),
		}
	}).ToSlice(&ret.CCLRules); err != nil {
		return nil, err
	}
	return ret, nil
}

func (m *metaMySQLImpl) DescribeSqlFingerPrintOrKeywords(ctx context.Context, req *datasource.DescribeSqlFingerPrintOrKeywordsReq) (*datasource.DescribeSqlFingerPrintOrKeywordsResp, error) {
	panic("implement me")
}
func (self *metaMySQLImpl) DescribeSqlType(ctx context.Context, req *datasource.DescribeSqlTypeReq) (*datasource.DescribeSqlTypeResp, error) {
	ret := &datasource.DescribeSqlTypeResp{}
	sqlType, err := datasource.GetRdsSqlType(ctx, req.SqlText)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_SQLReviewParserSqlError)
	}
	ret.SqlType = sqlType
	return ret, nil
}
func (m *metaMySQLImpl) DescribeInstanceAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeInstanceProxyAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeInstanceAddressList(ctx context.Context, req *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		Type:       conv.ToSharedType(model.DSType_MetaMySQL),
		LinkType:   shared.Volc,
		InstanceId: req.InstanceId,
	}
	listInstancePodsResp, err := m.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "Get instance %s pods failed: %v", req.InstanceId, err)
		return nil, err
	}
	log.Info(ctx, "DescribeInstanceAddressList call ListInstancePods result is %s", utils.Show(listInstancePodsResp))
	//获取实例ip port
	addressList := []*datasource.DescribeInstanceAddressResp{}
	for _, pod := range listInstancePodsResp.Data {
		for _, container := range pod.Containers {
			if container.Name == "mysql" {
				ret := &datasource.DescribeInstanceAddressResp{}
				ret.Port = utils.MustStrToInt32(container.Port)
				ret.IP = pod.PodIP
				ret.NodeId = pod.NodeId
				addressList = append(addressList, ret)
			}
		}
	}
	return addressList, nil
}

func (m *metaMySQLImpl) ListTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTablesResp{}
	sql := "select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	args = []interface{}{req.DB}
	/* get count */
	counterSQL := "/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE' " // ignore_security_alert
	if req.Keyword != "" {
		counterSQL += "and TABLE_NAME like ?"
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		counterSQL += genListTableFilter(req.Filters)
	}
	if err = conn.Raw(counterSQL, args...).Scan(&ret.Total); err != nil {
		return nil, err
	}
	return ret, nil
}

func (m *metaMySQLImpl) ListAllTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListSchemaTables(ctx context.Context, req *datasource.ListSchemaTablesReq) (*datasource.ListSchemaTablesResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListTablesInfo(ctx context.Context, req *datasource.ListTablesInfoReq) (*datasource.ListTablesInfoResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeTable(ctx context.Context, req *datasource.DescribeTableReq) (*datasource.DescribeTableResp, error) {
	ret := &datasource.DescribeTableResp{}
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	/* get columns info */
	var columns []*DBColumn
	queryCols := fmt.Sprintf("desc `%s`.`%s` %s", req.DB, req.Table, DBW_CONSOLE_DEFAULT_HINT)
	err = conn.Raw(queryCols).Scan(&columns)
	if err != nil {
		log.Warn(ctx, "get columns of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}

	ret.Columns = DBColumnToTableInfoColumnInfo(columns)

	/* get index info */
	var indexs []*DBIndex
	queryIndex := fmt.Sprintf("show index from `%s`.`%s` %s", req.DB, req.Table, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(queryIndex).Scan(&indexs); err != nil {
		log.Warn(ctx, "get index of %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}

	ret.Indexs = getIndexList(indexs)
	log.Info(ctx, "get ret.Indexs %v", utils.Show(ret.Indexs))

	/* get foreignKey info */
	ret.ForeignKeys, err = getForeignKeyInfo(ctx, conn, req.DB, req.Table)
	if err != nil {
		return nil, err
	}
	log.Info(ctx, "get ret.foreignKey is: %v", utils.Show(ret.ForeignKeys))

	/* get tableOption info */
	var tableOption DBOption
	tableOptionQuery := "SELECT ENGINE, ROW_FORMAT, AVG_ROW_LENGTH, AUTO_INCREMENT, TABLE_COLLATION, CREATE_OPTIONS, " +
		"TABLE_COMMENT, TABLE_ROWS, INDEX_LENGTH+DATA_LENGTH+DATA_FREE AS TABLE_SPACE, CREATE_TIME FROM information_schema.tables WHERE TABLE_SCHEMA=? AND TABLE_NAME=?"
	err = conn.Raw(tableOptionQuery, req.DB, req.Table).Scan(&tableOption)
	if err != nil {
		log.Warn(ctx, "get tableOption from %s of %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	ret.Option, err = getTableOption(ctx, &tableOption)
	/* get column  Comment、IsAutoIncrement */
	var columnOthers []*DBColumnOther
	queryOther := fmt.Sprintf("select * from information_schema.columns where table_schema='%s' and table_name='%s' %s", req.DB, req.Table, DBW_CONSOLE_DEFAULT_HINT)
	err = conn.Raw(queryOther).Scan(&columnOthers)
	if err != nil {
		log.Warn(ctx, "get columnComments from %s of %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	getColumnOther(ctx, ret, columnOthers)
	getPrimaryOrder(ctx, ret)
	var tableName, stmt string
	var cursor db.Cursor

	var charSet, collationConn string
	definitionSQL := fmt.Sprintf("SHOW CREATE TABLE `%s`.`%s`", req.DB, req.Table)
	if cursor, err = conn.Rows(definitionSQL); err != nil {
		log.Warn(ctx, "get definition error %s", err)
		if err != nil {
			return nil, err
		}

	}
	for cursor.Next() {
		if err := cursor.Scan(&tableName, &stmt, &charSet, &collationConn); err != nil {
			if err := cursor.Scan(&tableName, &stmt); err != nil {
				return nil, err
			}
		}
	}
	ret.Definition = stmt
	return ret, err
}

func (m *metaMySQLImpl) DescribeAutoKillSessionConfig(ctx context.Context, req *datasource.DescribeAutoKillSessionConfigReq) (*datasource.DescribeAutoKillSessionConfigResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ModifyAutoKillSessionConfig(ctx context.Context, req *datasource.ModifyAutoKillSessionConfigReq) (*datasource.ModifyAutoKillSessionConfigResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeInstanceVersion(ctx context.Context, req *datasource.DescribeInstanceVersionReq) (*datasource.DescribeInstanceVersionResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListErrLogs(ctx context.Context, req *datasource.ListErrLogsReq) (*datasource.ListErrLogsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribePgTable(ctx context.Context, req *datasource.DescribePgTableReq) (*datasource.DescribePgTableResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeInstanceReplicaDelay(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (int64, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListViews(ctx context.Context, req *datasource.ListViewsReq) (*datasource.ListViewsResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListViewsResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW')"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW') and TABLE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW')", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (m *metaMySQLImpl) DescribeView(ctx context.Context, req *datasource.DescribeViewReq) (*datasource.DescribeViewResp, error) {
	ret := &datasource.DescribeViewResp{}
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	// show create info
	var viewInfo ViewInfo
	queryView := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ SHOW CREATE VIEW `%s`.`%s` %s", req.DB, req.View, DBW_CONSOLE_DEFAULT_HINT)
	err = conn.Raw(queryView).Scan(&viewInfo)
	if err != nil {
		log.Warn(ctx, "show create view `%s`.`%s` fail, err=%v", req.DB, req.View, err)
		return nil, err
	}
	ret.ViewInfo = &shared.ViewInfo{
		View:       viewInfo.View,
		CreateView: viewInfo.CreateView,
		ViewMeta:   &shared.ViewInfo_MetaInfo{},
	}

	// viewMeta
	var viewMeta ViewMeta
	queryViewMeta := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select * from information_schema.VIEWS where TABLE_SCHEMA='%s' and TABLE_NAME='%s';", req.DB, req.View)
	err = conn.Raw(queryViewMeta).Scan(&viewMeta)
	if err != nil {
		log.Warn(ctx, "/*+ DBW SQL CONSOLE DEFAULT*/ select * from information_schema.VIEWS where TABLE_SCHEMA='%s' and TABLE_NAME='%s'; fail, err=%v", req.DB, req.View, err)
		return nil, err
	}
	ret.ViewInfo.ViewMeta.Name = viewMeta.TableName
	ret.ViewInfo.ViewMeta.Definition = viewMeta.ViewDefinition
	ret.ViewInfo.ViewMeta.CheckOption = viewMeta.CheckOption
	ret.ViewInfo.ViewMeta.Definer = viewMeta.DEFINER
	ret.ViewInfo.ViewMeta.Security = viewMeta.SecurityType
	ret.ViewInfo.ViewMeta.Algorithm = getViewAlgorithm(ctx, viewInfo.CreateView)
	return ret, nil
}

func (m *metaMySQLImpl) DescribeFunction(ctx context.Context, req *datasource.DescribeFunctionReq) (*datasource.DescribeFunctionResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	functionInfo := &DBFunction{}
	ret := &datasource.DescribeFunctionResp{}

	/* get the param list */
	var paramlist []Param
	sql1 := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select PARAMETER_MODE,PARAMETER_NAME,DATA_TYPE,DTD_IDENTIFIER from information_schema.PARAMETERS where specific_schema='%s' and specific_name='%s' and ROUTINE_TYPE='FUNCTION' %s;", req.DB, req.Function, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql1).Scan(&paramlist); err != nil {
		return nil, err
	}

	/* get  function info */
	sql2 := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select *from information_schema.ROUTINES where ROUTINE_SCHEMA='%s' and SPECIFIC_NAME='%s' and ROUTINE_TYPE='FUNCTION' %s", req.DB, req.Function, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql2).Scan(functionInfo); err != nil {
		return nil, err
	}

	var paramList2 []*shared.Param
	for _, param := range paramlist {
		paramList2 = append(paramList2, &shared.Param{
			ParamName: param.ParamName,
			ParamType: param.ParamType,
			Length:    parseColumnLength(param.Length),
		})
	}

	var security shared.Security
	switch functionInfo.Security {
	case "DEFINER":
		security = shared.Definer
	case "INVOKER":
		security = shared.Invoker
	default:
		security = shared.Definer
	}

	var sqlDataAccess shared.SQLDataAccess
	switch functionInfo.SQLDataAccess {
	case "CONTAINS SQL":
		sqlDataAccess = shared.ContainsSQL
	case "NO SQL":
		sqlDataAccess = shared.NoSQL
	case "READS SQL DATA":
		sqlDataAccess = shared.ReadsSQLData
	case "MODIFIES SQL DATA":
		sqlDataAccess = shared.ModifiesSQLData
	default:
		sqlDataAccess = shared.ContainsSQL
	}

	var determine shared.Determine
	switch functionInfo.Determine {
	case "YES":
		determine = shared.DETERMINISTIC
	case "NO":
		determine = shared.NOT_DETERMINISTIC
	default:
		determine = shared.DETERMINISTIC
	}

	ret.FunctionInfo = &shared.FunctionInfo{
		FunctionName:     functionInfo.FunctionName,
		Security:         security,
		SQLDataAccess:    sqlDataAccess,
		Definition:       functionInfo.Definition,
		FuncReturnType:   functionInfo.FuncReturnType,
		FuncReturnLength: 0,
		Determine:        determine,
		Comment:          functionInfo.Comment,
		ParamList:        paramList2,
	}
	return ret, nil
}

func (m *metaMySQLImpl) DescribeProcedure(ctx context.Context, req *datasource.DescribeProcedureReq) (*datasource.DescribeProcedureResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	procedureInfo := &DBProcedure{}
	ret := &datasource.DescribeProcedureResp{}

	/* get the param list */
	var paramlist []Param
	sql1 := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select PARAMETER_MODE,PARAMETER_NAME,DATA_TYPE,DTD_IDENTIFIER from information_schema.PARAMETERS where ROUTINE_TYPE=\"PROCEDURE\" and specific_schema=\"%s\" and specific_name=\"%s\" %s;", req.DB, req.Procedure, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql1).Scan(&paramlist); err != nil {
		return nil, err
	}

	/* get procedure info */
	sql2 := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select *from information_schema.ROUTINES where ROUTINE_SCHEMA='%s' and SPECIFIC_NAME='%s' and ROUTINE_TYPE='PROCEDURE' %s", req.DB, req.Procedure, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql2).Scan(procedureInfo); err != nil {
		return nil, err
	}

	paramlist2 := make([]*shared.Param, 0)
	for _, param := range paramlist {
		paramlist2 = append(paramlist2, &shared.Param{
			ParamMode: param.ParamMode,
			ParamName: param.ParamName,
			ParamType: param.ParamType,
			Length:    parseColumnLength(param.Length),
		})
	}

	var security shared.Security
	switch procedureInfo.Security {
	case "DEFINER":
		security = shared.Definer
	case "INVOKER":
		security = shared.Invoker
	default:
		security = shared.Definer
	}

	var sqlDataAccess shared.SQLDataAccess
	switch procedureInfo.SQLDataAccess {
	case "CONTAINS SQL":
		sqlDataAccess = shared.ContainsSQL
	case "NO SQL":
		sqlDataAccess = shared.NoSQL
	case "READS SQL DATA":
		sqlDataAccess = shared.ReadsSQLData
	case "MODIFIES SQL DATA":
		sqlDataAccess = shared.ModifiesSQLData
	default:
		sqlDataAccess = shared.ContainsSQL
	}

	ret.ProcedureInfo = &shared.ProcedureInfo{
		ProcedureName: procedureInfo.ProcedureName,
		Security:      security,
		SQLDataAccess: sqlDataAccess,
		Definition:    procedureInfo.Definition,
		Comment:       procedureInfo.Comment,
		ParamList:     paramlist2,
	}
	return ret, nil
}

func (m *metaMySQLImpl) ListFunctions(ctx context.Context, req *datasource.ListFunctionsReq) (*datasource.ListFunctionsResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListFunctionsResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ SELECT `ROUTINE_NAME` FROM `information_schema`.`ROUTINES` WHERE ROUTINE_SCHEMA=? AND ROUTINE_TYPE='FUNCTION'"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and ROUTINE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	sql += DBW_CONSOLE_DEFAULT_HINT
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='FUNCTION' and ROUTINE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='FUNCTION'", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (m *metaMySQLImpl) ListProcedures(ctx context.Context, req *datasource.ListProceduresReq) (*datasource.ListProceduresResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListProceduresResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ SELECT `ROUTINE_NAME` FROM `information_schema`.`ROUTINES` WHERE ROUTINE_SCHEMA=? AND ROUTINE_TYPE='PROCEDURE'"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and ROUTINE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='PROCEDURE' and ROUTINE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='PROCEDURE'", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (m *metaMySQLImpl) ListTriggers(ctx context.Context, req *datasource.ListTriggersReq) (*datasource.ListTriggersResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTriggersResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ select TRIGGER_NAME from information_schema.triggers where TRIGGER_SCHEMA=?"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TRIGGER_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.triggers where TRIGGER_SCHEMA=? and TRIGGER_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.triggers where TRIGGER_SCHEMA=?", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (m *metaMySQLImpl) DescribeTLSConnectionInfo(ctx context.Context, req *datasource.DescribeTLSConnectionInfoReq) (*datasource.DescribeTLSConnectionInfoResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListKeyNumbers(ctx context.Context, req *datasource.ListKeyNumbersReq) (*datasource.ListKeyNumbersResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListKeys(ctx context.Context, req *datasource.ListKeysReq) (*datasource.ListKeysResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetKey(ctx context.Context, req *datasource.GetKeyReq) (*datasource.GetKeyResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListKeyMembers(ctx context.Context, req *datasource.ListKeyMembersReq) (*datasource.ListKeyMembersResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListAlterKVsCommands(ctx context.Context, req *datasource.ListAlterKVsCommandsReq) (*datasource.ListAlterKVsCommandsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) OpenTunnel(ctx context.Context, ds *shared.DataSource, TunnelID string) error {
	return nil
}

func (m *metaMySQLImpl) DescribeTrigger(ctx context.Context, req *datasource.DescribeTriggerReq) (*datasource.DescribeTriggerResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeEvent(ctx context.Context, req *datasource.DescribeEventReq) (*datasource.DescribeEventResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListEvents(ctx context.Context, req *datasource.ListEventsReq) (*datasource.ListEventsResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListEventsResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ select EVENT_NAME from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=?"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and EVENT_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Limit != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}

	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	log.Info(ctx, "sql: %s", sql)
	if req.Keyword != "" {
		if err = conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=? and EVENT_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total); err != nil {
			return nil, err
		}
	} else {
		if err = conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=?", req.DB).
			Scan(&ret.Total); err != nil {
			return nil, err
		}
	}
	return ret, nil
}

func (m *metaMySQLImpl) CreateAccount(ctx context.Context, req *datasource.CreateAccountReq) error {
	accountType, err := metaMysqlModel.AccountTypeFromString(req.AccountType)
	if err != nil {
		log.Warn(ctx, "failed to identify account type , account type=%s, err=%v", req.AccountType, err.Error())
		return err
	}
	rreq := &metaMysqlModel.CreateDBAccountReq{
		InstanceId:      req.InstanceId,
		AccountName:     req.AccountName,
		AccountPassword: req.AccountPassword,
		AccountType:     accountType,
	}
	// TODO
	if err = m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_CreateDBAccount.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to create account, instanceId=%s, err=%v", req.InstanceId, err.Error())
		if strings.Contains(err.Error(), "InvalidAccountName_Duplicate") {
			return nil
		}
		return err
	}
	return nil
}

func (m *metaMySQLImpl) CheckPrivilege(ctx context.Context, instanceId, dbName, accountName, priv string, dsType shared.DataSourceType) (bool, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeAccounts(ctx context.Context, req *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeAccounts2(ctx context.Context, req *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp2, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) CreateAccountAndGrant(ctx context.Context, instanceId, accountName, password, dbName, priv string, dsType shared.DataSourceType) error {
	panic("implement me")
}

func (m *metaMySQLImpl) ModifyAccountPrivilege(ctx context.Context, req *datasource.ModifyAccountPrivilegeReq) error {
	panic("implement me")
}

func (m *metaMySQLImpl) GrantAccountPrivilege(ctx context.Context, req *datasource.GrantAccountPrivilegeReq) error {
	var accountPrivilege = metaMysqlModel.PrivilegeType_ReadWrite
	if req.AccountPrivilege != "" {
		ap, err := metaMysqlModel.PrivilegeTypeFromString(req.AccountPrivilege)
		if err != nil {
			return ErrInvalidPrivilegeType
		}
		accountPrivilege = ap
	}
	rreq := metaMysqlModel.GrantDBAccountPrivilegeReq{
		InstanceId:  req.InstanceId,
		AccountName: req.AccountName,
		AccountPrivileges: []*metaMysqlModel.AccountPrivilegeObject{
			{
				DBName:           req.DBName,
				AccountPrivilege: accountPrivilege,
			},
		},
	}
	if err := m.metaMysql.Get().Call(
		ctx,
		metaMysqlModel.Action_GrantDBAccountPrivilege.String(),
		rreq,
		nil,
		client.WithTenantID("1"),
		client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to grant account, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
		return err
	}
	return nil
}

func (m *metaMySQLImpl) DeleteAccount(ctx context.Context, req *datasource.DeleteAccountReq) error {
	rreq := &metaMysqlModel.DeleteDBAccountReq{
		InstanceId:  req.InstanceId,
		AccountName: req.AccountName,
	}
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DeleteDBAccount.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2))
	if err == nil {
		return nil
	}
	if strings.Contains(strings.ToLower(err.Error()), "exist") || strings.Contains(strings.ToLower(err.Error()), "未找到该账号") {
		log.Warn(ctx, "delete account, instanceId=%s, account=%s, account is not exists，ignore error", req.InstanceId, req.AccountName)
		return nil
	}
	log.Warn(ctx, "failed to delete account, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
	return err
}

func (m *metaMySQLImpl) ListDatabasesWithAccount(ctx context.Context, req *datasource.ListDatabasesWithAccountReq) (*datasource.ListDatabasesWithAccountResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetAdvice(ctx context.Context, req *datasource.GetAdviceReq) (*datasource.GetAdviceResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListCharsets(ctx context.Context, req *datasource.ListCharsetsReq) (*datasource.ListCharsetsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListCollations(ctx context.Context, req *datasource.ListCollationsReq) (*datasource.ListCollationsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListSchema(ctx context.Context, req *datasource.ListSchemaReq) (*datasource.ListSchemaResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListSequence(ctx context.Context, req *datasource.ListSequenceReq) (*datasource.ListSequenceResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListPgCollations(ctx context.Context, req *datasource.ListPgCollationsReq) (*datasource.ListPgCollationsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListPgUsers(ctx context.Context, req *datasource.ListPgUsersReq) (*datasource.ListPgUsersResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListTableSpaces(ctx context.Context, req *datasource.ListTableSpacesReq) (*datasource.ListTableSpacesResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeDialogDetails(ctx context.Context, req *datasource.DescribeDialogDetailsReq) (*datasource.DescribeDialogDetailsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq) (*datasource.DescribeDialogStatisticsResp, error) {
	dialogInfos, err := m.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:        req.Source,
		Component:     req.Component,
		QueryFilter:   req.QueryFilter,
		InternalUsers: req.InternalUsers,
	})
	if err != nil {
		return nil, err
	}
	// get aggregated info
	statistics := m.getDialogStatistics(ctx, dialogInfos, req.TopN)

	ret := &datasource.DescribeDialogStatisticsResp{
		DialogStatistics: statistics,
	}
	return ret, nil
}

func (m *metaMySQLImpl) getDialogStatistics(ctx context.Context, data []*datasource.DialogInfo, topN int32) *shared.DialogStatistics {
	userInfo := make(map[string]*datasource.UserAggregatedInfo)
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	dbInfo := make(map[string]*datasource.DBAggregatedInfo)
	psmInfo := make(map[string]*datasource.PSMAggregatedInfo)
	var activeConn, totalConn int32 = 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(data).Foreach(func(d *datasource.DialogInfo) {
		totalConn += 1
		// 提取psm
		psmItem := datasource.ExtractRdsPsm(d.Info.String)
		if _, ok := psmInfo[psmItem.Psm]; !ok {
			psmInfo[psmItem.Psm] = &datasource.PSMAggregatedInfo{PSM: psmItem.Psm}
		}
		psmInfo[psmItem.Psm].TotalConn += 1

		if _, ok := userInfo[d.User]; !ok {
			userInfo[d.User] = &datasource.UserAggregatedInfo{User: d.User}
		}
		userInfo[d.User].TotalConn += 1
		ip := datasource.ExtractIP(d.Host)
		log.Info(ctx, "ExtractIP former: %s, after: %s", d.Host, ip)
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1
		if _, ok := dbInfo[d.DB]; !ok {
			dbInfo[d.DB] = &datasource.DBAggregatedInfo{DB: d.DB}
		}
		dbInfo[d.DB].TotalConn += 1
		if strings.ToLower(d.Command) != "sleep" {
			activeConn += 1
			userInfo[d.User].ActiveConn += 1
			ipInfo[ip].ActiveConn += 1
			dbInfo[d.DB].ActiveConn += 1
			psmInfo[psmItem.Psm].ActiveConn += 1
		}
	}).Run()

	var userList []*shared.UserAggregatedInfo
	var ipList []*shared.IPAggregatedInfo
	var dbList []*shared.DBAggregatedInfo
	var psmList []*shared.PSMAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(userInfo).ZipMap(func(k string, v *datasource.UserAggregatedInfo) *shared.UserAggregatedInfo {
		return &shared.UserAggregatedInfo{
			User:              v.User,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&userList)
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)
	fp.KVStreamOf(dbInfo).ZipMap(func(k string, v *datasource.DBAggregatedInfo) *shared.DBAggregatedInfo {
		return &shared.DBAggregatedInfo{
			DB:                v.DB,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&dbList)
	fp.KVStreamOf(psmInfo).ZipMap(func(k string, v *datasource.PSMAggregatedInfo) *shared.PSMAggregatedInfo {
		return &shared.PSMAggregatedInfo{
			PSM:               v.PSM,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&psmList)
	sort.Slice(userList, func(i, j int) bool {
		if userList[i].TotalConnections > userList[j].TotalConnections {
			return true
		}
		if userList[i].TotalConnections == userList[j].TotalConnections &&
			userList[i].ActiveConnections == userList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(dbList, func(i, j int) bool {
		if dbList[i].TotalConnections > dbList[j].TotalConnections {
			return true
		}
		if dbList[i].TotalConnections == dbList[j].TotalConnections &&
			dbList[i].ActiveConnections == dbList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(psmList, func(i, j int) bool {
		if psmList[i].TotalConnections > psmList[j].TotalConnections {
			return true
		}
		if psmList[i].TotalConnections == psmList[j].TotalConnections &&
			psmList[i].ActiveConnections == psmList[j].ActiveConnections {
			return true
		}
		return false
	})

	return &shared.DialogStatistics{
		DialogOver: &shared.DialogOverview{
			ActiveConnections: activeConn,
			TotalConnections:  totalConn,
		},
		UserAggregatedInfo: userList[:fp.MinInt(int(topN), len(userList))],
		IPAggregatedInfo:   ipList[:fp.MinInt(int(topN), len(ipList))],
		DBAggregatedInfo:   dbList[:fp.MinInt(int(topN), len(dbList))],
		PSMAggregatedInfo:  psmList[:fp.MinInt(int(topN), len(psmList))],
	}
}

func (m *metaMySQLImpl) DescribeEngineStatus(ctx context.Context, req *datasource.DescribeEngineStatusReq) (*datasource.DescribeEngineStatusResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) KillProcess(ctx context.Context, req *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	// 获取终端地址
	if err := m.FillInnerDataSource(ctx, req.Source); err != nil {
		log.Warn(ctx, "fill inner datasource error:%s", err.Error())
		return nil, err
	}
	// 代理是否开启读写
	isEnableReadWriteSplitting, err := m.IsEnableProxyRWMode(ctx, req.Source.InstanceId)
	if err != nil {
		return nil, err
	}
	// 获取pod地址
	pods, err := m.GetPods(ctx, req.Source.InstanceId, model.Component_DBEngine)
	if err != nil {
		return nil, err
	}
	isSupportKill := false
	ret := &datasource.KillProcessResp{}
	for _, pod := range pods {
		if pod.NodeId == req.NodeId {
			//如果节点不是主节点且没有开放代理读写分离，则不支持kill
			if pod.Role != model.NodeType_Primary.String() && !isEnableReadWriteSplitting {
				log.Warn(ctx, "node %s is not primary node and proxy rw mode is not enable", req.NodeId)
				isSupportKill = false
			} else {
				isSupportKill = true
			}
			break
		}
	}
	if isSupportKill {
		conn, err := m.getConn(ctx, req.Source)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
			return nil, err
		}
		defer conn.Close()
		hint := fmt.Sprintf("/* FORCE_NODE=%s */", req.NodeId)
		for _, processID := range req.ProcessIDs {
			sqlExec := hint + fmt.Sprintf("KILL %s", processID)
			log.Info(ctx, "node %s exec sql is %s", req.NodeId, sqlExec)
			if err = conn.Exec(sqlExec); err != nil {
				if !strings.Contains(strings.ToLower(err.Error()), strings.ToLower("unknown thread id")) {
					ret.FailInfoList = append(ret.FailInfoList, &shared.KillFailInfo{
						ProcessId:    processID,
						ErrorMessage: err.Error(),
					})
				}
			}
		}
	}
	return ret, nil
}

func (m *metaMySQLImpl) DescribeTrxAndLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) (*datasource.DescribeTrxAndLocksResp, error) {
	trxAndLockList, err := m.getAllTrxLocks(ctx, req)
	if err != nil {
		return nil, err
	}
	filtedAndLockList := m.filterTrxAndLocks(ctx, trxAndLockList, req.SearchParam, req.NodeIds)
	ret := &datasource.DescribeTrxAndLocksResp{
		Result: &shared.DescribeTrxAndLocksInfo{
			TrxAndLockList: filtedAndLockList,
			Total:          int32(len(filtedAndLockList)),
		},
	}
	return ret, nil
}
func (m *metaMySQLImpl) getAllTrxLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) ([]*shared.TrxAndLock, error) {
	var (
		trxLocksList []*shared.TrxAndLock
		querySql     string
	)
	// 获取终端地址
	if err := m.FillInnerDataSource(ctx, req.Source); err != nil {
		log.Warn(ctx, "fill inner datasource error:%s", err.Error())
		return nil, err
	}
	//获取pod列表
	pods, err := m.GetPods(ctx, req.Source.InstanceId, model.Component_DBEngine)
	if err != nil {
		return nil, err
	}
	whereConditions, args := generateTrxAndLockSearchWhereConditions(req.SearchParam)
	/* sort */
	whereConditions += ` order by ` + req.SortParam + " " + req.Order
	for _, node := range pods {
		//仅查询主节点的事务
		if node.Role != model.NodeType_Primary.String() {
			log.Warn(ctx, "node %s is not primary node and proxy rw mode is not enable", node.NodeId)
			continue
		}
		maSource := *req.Source
		//兜底,清空nodeId,确保走实例级连接池
		maSource.NodeId = ""
		conn, err := m.getConnV2(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			continue
		}
		defer m.ConnPool.Put(ctx, conn)
		var TrxAndLocklist []TrxAndLock
		// 查询事务
		hint := fmt.Sprintf("/* FORCE_NODE=%s */", node.NodeId)
		querySql = hint + TrxQuery + whereConditions
		log.Info(ctx, "node %s exec sql is %s", node.NodeId, querySql)
		if err = conn.Raw(querySql, args...).Scan(&TrxAndLocklist); err != nil {
			log.Warn(ctx, "get metaMySQL node %s TrxAndLockList error:%s", node.NodeId, err.Error())
			continue
		}
		log.Info(ctx, "node %s TrxAndLockList is %s", node.NodeId, utils.Show(TrxAndLocklist))

		// 确定内核版本
		rdsVersion := getRdsVersion(ctx, conn)
		if strings.Contains(rdsVersion, "MySQL_5_7") {
			//获取锁信息
			lockList, err := m.describeLock(ctx, conn)
			if err != nil {
				log.Warn(ctx, "get lock fail: %v", err)
				return nil, err
			}
			// 获取锁阻塞
			var waitLocks57 []*datasource.LockWait57Version
			log.Info(ctx, "execute trx 5.7 sql is %s", WaitSql57)
			if err := conn.Raw(WaitSql57).Scan(&waitLocks57); err != nil {
				log.Warn(ctx, "get 5.7 lock wait fail: %v", err)
				return nil, err
			}
			for _, TL := range TrxAndLocklist {
				var (
					lockStatus       shared.LockStatus
					trxWaitStartTime string
					blockTrxId       string
				)
				if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockHoldAndWait
				} else if TL.TrxLockStructs == 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockWait
				} else if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId == "" {
					lockStatus = shared.LockHold
				} else {
					lockStatus = shared.None
				}
				/* TrxWaitStartTime maybe NULL */
				if TL.TrxWaitStartTime.Format("2006-01-02 15:04:05") == "0001-01-01 00:00:00" {
					trxWaitStartTime = ""
				} else {
					trxWaitStartTime = TL.TrxWaitStartTime.Format("2006-01-02 15:04:05")
				}
				// 匹配事务是否存在锁阻塞
				for _, waitItem := range waitLocks57 {
					if TL.TrxRequestedLockId == waitItem.RequestingLockId {
						blockTrxId = waitItem.BlockTrxId
						break
					}
				}
				tLockList := lockList
				// 匹配事务是否持有锁
				fp.StreamOf(tLockList).Filter(func(d *shared.Lock) bool {
					return d.TrxId == TL.TrxId
				}).ToSlice(&tLockList)
				trxLocksList = append(trxLocksList, &shared.TrxAndLock{
					ProcessId:        TL.ProcessId,
					TrxId:            TL.TrxId,
					TrxStatus:        TL.TrxState,
					TrxIsoLevel:      TL.TrxIsoLevel,
					TrxStartTime:     TL.TrxStartTime.Format("2006-01-02 15:04:05"),
					TrxWaitStartTime: trxWaitStartTime,
					SqlBlocked:       TL.SqlBlocked,
					TrxTablesLocked:  TL.TrxTablesLocked,
					TrxRowsLocked:    TL.TrxRowsLocked,
					TrxRowsModified:  TL.TrxRowsModified,
					LockStatus:       lockStatus,
					LockList:         tLockList,
					TrxExecTime:      TL.TrxExecTime,
					BlockTrxId:       blockTrxId,
					NodeId:           node.NodeId,
				})
			}
		} else {
			//log.Info(ctx, "execute trx 8.0 sql is %s", WaitSql80)
			for _, TL := range TrxAndLocklist {
				var (
					lockStatus       shared.LockStatus
					trxWaitStartTime string
					//blockTrxId       string
				)
				if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockHoldAndWait
				} else if TL.TrxLockStructs == 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockWait
				} else if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId == "" {
					lockStatus = shared.LockHold
				} else {
					lockStatus = shared.None
				}
				/* TrxWaitStartTime maybe NULL */
				if TL.TrxWaitStartTime.Format("2006-01-02 15:04:05") == "0001-01-01 00:00:00" {
					trxWaitStartTime = ""
				} else {
					trxWaitStartTime = TL.TrxWaitStartTime.Format("2006-01-02 15:04:05")
				}
				// 匹配事务是否存在锁阻塞
				//for _, waitItem := range waitLocks80 {
				//	if TL.TrxRequestedLockId == waitItem.RequestingLockId {
				//		blockTrxId = waitItem.BlockTrxId
				//		break
				//	}
				//}
				//tLockList := lockList
				//// 匹配事务是否持有锁
				//fp.StreamOf(tLockList).Filter(func(d *shared.Lock) bool {
				//	return d.TrxId == TL.TrxId
				//}).ToSlice(&tLockList)
				//lockList2 := make([]*shared.Lock, 0)
				//lockList2 = append(lockList2, lockList...)
				trxLocksList = append(trxLocksList, &shared.TrxAndLock{
					ProcessId:        TL.ProcessId,
					TrxId:            TL.TrxId,
					TrxStatus:        TL.TrxState,
					TrxIsoLevel:      TL.TrxIsoLevel,
					TrxStartTime:     TL.TrxStartTime.Format("2006-01-02 15:04:05"),
					TrxWaitStartTime: trxWaitStartTime,
					SqlBlocked:       TL.SqlBlocked,
					TrxTablesLocked:  TL.TrxTablesLocked,
					TrxRowsLocked:    TL.TrxRowsLocked,
					TrxRowsModified:  TL.TrxRowsModified,
					LockStatus:       lockStatus,
					//LockList:         lockList2,
					TrxExecTime: TL.TrxExecTime,
					//BlockTrxId:       blockTrxId,
					NodeId: node.NodeId,
				})
			}
		}
	}
	return trxLocksList, nil
}
func (m *metaMySQLImpl) filterTrxAndLocks(ctx context.Context, data []*shared.TrxAndLock, queryFilter *model.TrxQueryFilter, nodeIds []string) []*shared.TrxAndLock {
	tData := data
	if len(nodeIds) > 0 {
		fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
			var existed bool
			for _, nodeId := range nodeIds {
				if d.NodeId == nodeId {
					existed = true
					break
				}
			}
			return existed
		}).ToSlice(&tData)
	}
	if queryFilter != nil {
		if trxId := queryFilter.GetTrxId(); trxId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.TrxId), strings.ToLower(trxId))
			}).ToSlice(&tData)
		}
		if pId := queryFilter.GetProcessId(); pId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.ProcessId), strings.ToLower(pId))
			}).ToSlice(&tData)
		}
		if blockTrxId := queryFilter.GetBlockTrxId(); blockTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.BlockTrxId), strings.ToLower(blockTrxId))
			}).ToSlice(&tData)
		}

		if blockSql := queryFilter.GetSqlBlocked(); blockSql != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.SqlBlocked), strings.ToLower(blockSql))
			}).ToSlice(&tData)
		}
		if queryFilter.IsSetTrxExecTime() {
			trxExecTime := queryFilter.GetTrxExecTime()
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				execTime := d.TrxExecTime
				return execTime > trxExecTime

			}).ToSlice(&tData)

		}
	}
	return tData
}

func (m *metaMySQLImpl) DescribeLockCurrentWaits(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) (*datasource.DescribeLockCurrentWaitsResp, error) {
	waitLocks, err := m.getAllWaitLocks(ctx, req)
	if err != nil {
		return nil, err
	}
	// get details info
	ret := m.filterWaitLockDetails(ctx, waitLocks, req.SearchParam, req.NodeIds)
	log.Info(ctx, "lockWaits list is %s", utils.Show(ret))
	return ret, nil
}
func (m *metaMySQLImpl) getAllWaitLocks(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) ([]*datasource.LockCurrentWaitsDetail, error) {
	var ret []*datasource.LockCurrentWaitsDetail
	// 获取终端地址
	if err := m.FillInnerDataSource(ctx, req.Source); err != nil {
		log.Warn(ctx, "fill inner datasource error:%s", err.Error())
		return nil, err
	}
	//获取pod列表
	pods, err := m.GetPods(ctx, req.Source.InstanceId, model.Component_DBEngine)
	if err != nil {
		return nil, err
	}
	//whereConditions, args := generateLockWaitSearchWhereConditions(req.SearchParam)
	//whereConditions += ` order by ` + mp[req.SortParam] + " " + req.Order
	for _, node := range pods {
		//仅查询主节点的锁阻塞
		if node.Role != model.NodeType_Primary.String() {
			log.Warn(ctx, "node %s is not primary node and proxy rw mode is not enable", node.NodeId)
			continue
		}
		maSource := *req.Source
		//兜底,清空nodeId,确保走实例级连接池
		maSource.NodeId = ""
		conn, err := m.getConnV2(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			continue
		}
		defer m.ConnPool.Put(ctx, conn)
		// 获取事务
		var trxList []*datasource.CurrentTrxDetail
		if err := conn.Raw(TrxQueryForWaitLock).Scan(&trxList); err != nil {
			return nil, err
		}
		log.Info(ctx, "node %s trxList is %s", node.NodeId, utils.Show(trxList))
		// 事务为空也就没有锁阻塞,直接返回
		if len(trxList) < 1 {
			return ret, nil
		}
		// 确定内核版本
		rdsVersion := getRdsVersion(ctx, conn)
		// 查询锁阻塞
		if strings.Contains(rdsVersion, "MySQL_5_7") {
			log.Info(ctx, "execute 5.7 sql is %s", WaitSql57)
			//获取锁阻塞
			var waitLocks57 []*datasource.LockWait57Version
			if err := conn.Raw(WaitSql57).Scan(&waitLocks57); err != nil {
				return nil, err
			}
			// 空也就没有锁阻塞,直接返回
			if len(waitLocks57) < 1 {
				return ret, nil
			}
			// 便利所有锁阻塞信息，获取相互阻塞的事务
			for _, waitItem := range waitLocks57 {
				for _, trx1 := range trxList {
					if waitItem.RequestingTrxId == trx1.TrxId {
						for _, trx2 := range trxList {
							if waitItem.BlockTrxId == trx2.TrxId {
								ret = append(ret, &datasource.LockCurrentWaitsDetail{
									RTrxMysqlThreadId:  trx1.TrxMysqlThreadId,
									RTrxId:             trx1.TrxId,
									RTrxState:          trx1.TrxState,
									RWaitingQuery:      trx1.QuerySql,
									RTrxStarted:        convertTimeStr(trx1.TrxStarted),
									RTrxWaitStarted:    convertTimeStr(trx1.TrxWaitStarted),
									RBlockedWaitSecs:   trx1.WaitSeconds,
									RTrxRowsModified:   trx1.TrxRowsModified,
									RTrxRowsLocked:     trx1.TrxRowsLocked,
									RTrxOperationState: trx1.TrxOperationState,
									BTrxMysqlThreadId:  trx2.TrxMysqlThreadId,
									BTrxId:             trx2.TrxId,
									BTrxState:          trx2.TrxState,
									BBlockingQuery:     trx2.QuerySql,
									BTrxStarted:        convertTimeStr(trx2.TrxStarted),
									BTrxWaitStarted:    convertTimeStr(trx2.TrxWaitStarted),
									BBlockingWaitSecs:  trx2.WaitSeconds,
									BTrxRowsModified:   trx2.TrxRowsModified,
									BTrxRowsLocked:     trx2.TrxRowsLocked,
									BTrxOperationState: trx2.TrxOperationState,
									DbName:             "-",
									NodeId:             node.NodeId,
								})
							}
						}
					}
				}
			}
		} else {
			log.Info(ctx, "skip 8.0 version lockWait")
			m.ConnPool.Put(ctx, conn)
			return ret, nil
		}
	}
	return ret, nil
}

func (m *metaMySQLImpl) filterWaitLockDetails(ctx context.Context, data []*datasource.LockCurrentWaitsDetail, queryFilter *model.WaitLockQueryFilter, nodeIds []string) *datasource.DescribeLockCurrentWaitsResp {
	tData := data
	// filter dialog details if desired
	if len(nodeIds) > 0 {
		fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
			var existed bool
			for _, nodeId := range nodeIds {
				if d.NodeId == nodeId {
					existed = true
					break
				}
			}
			return existed
		}).ToSlice(&tData)
	}
	var details []*model.DescribeLockCurrentWaitsDetail
	fp.StreamOf(tData[0:fp.MinInt64(10000, int64(len(tData)))]).
		Map(func(val *datasource.LockCurrentWaitsDetail) *model.DescribeLockCurrentWaitsDetail {
			return &model.DescribeLockCurrentWaitsDetail{
				RTrxMysqlThreadId:  val.RTrxMysqlThreadId,
				RTrxId:             val.RTrxId,
				RTrxState:          val.RTrxState,
				RWaitingQuery:      val.RWaitingQuery,
				RTrxStarted:        val.RTrxStarted,
				RTrxWaitStarted:    val.RTrxWaitStarted,
				RBlockedWaitSecs:   val.RBlockedWaitSecs,
				RTrxRowsModified:   val.RTrxRowsModified,
				RTrxRowsLocked:     val.RTrxRowsLocked,
				RTrxOperationState: val.RTrxOperationState,
				BTrxMysqlThreadId:  val.BTrxMysqlThreadId,
				BTrxId:             val.BTrxId,
				BTrxState:          val.BTrxState,
				BBlockingQuery:     val.BBlockingQuery,
				BTrxStarted:        val.BTrxStarted,
				BTrxWaitStarted:    val.BTrxWaitStarted,
				BBlockingWaitSecs:  val.BBlockingWaitSecs,
				BTrxRowsModified:   val.BTrxRowsModified,
				BTrxRowsLocked:     val.BTrxRowsLocked,
				BTrxOperationState: val.BTrxOperationState,
				DbName:             val.DbName,
				NodeId:             val.NodeId,
			}
		}).ToSlice(&details)

	return &datasource.DescribeLockCurrentWaitsResp{
		Result: details,
		Total:  int32(len(details)),
	}
}

func (m *metaMySQLImpl) DescribeDeadlock(ctx context.Context, req *datasource.DescribeDeadlockReq) (*datasource.DescribeDeadlockResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.DescribeDeadlockResp{}

	/* get engine innodb status */
	hint := fmt.Sprintf("/* FORCE_MASTER */") // 查主节点
	sql := hint + "show engine innodb status;"
	var statusinfo StatusInfo
	if err = conn.Raw(sql).Scan(&statusinfo); err != nil {
		return nil, err
	}

	/* DeadLock Info Str */
	reg := regexp.MustCompile(`LATEST DETECTED DEADLOCK\n------------------------\n([\s\S]*)------------\nTRANSACTIONS\n`)
	if len(reg.FindStringSubmatch(statusinfo.Status)) == 0 {
		log.Info(ctx, "there are no new Deadlock")
		retlog := &shared.DescribeDeadlockInfo{}
		retlog, err = m.getDeadLock(ctx, nil, time.Now().Unix(), req.InstanceId, req.TenantId)
		if err != nil {
			log.Warn(ctx, "get Deadlock info fail:%v", err)
			return nil, err
		}
		ret.DescribeDeadlockInfo = retlog
		return ret, nil
	}

	DLInfoStr := reg.FindStringSubmatch(statusinfo.Status)[len(reg.FindStringSubmatch(statusinfo.Status))-1]
	/* get two time */
	DeadlockCollectionTime := time.Now().Format("2006-01-02 15:04:05")
	reg = regexp.MustCompile(`(\d{4}-\d{2}-\d{2}.{9})`)
	DeadlockTime := reg.FindAllString(DLInfoStr, -1)[0]

	/* get every trx and its lock */
	DeadlockList2 := []*shared.Deadlock{}

	reg = regexp.MustCompile(consts.DeadlokcReg)
	trxs := reg.FindAllString(DLInfoStr, -1)

	for i, trx := range trxs {
		dl := &shared.Deadlock{}
		results := GetRegResults(trx, deadLockRegs)

		dl.TrxInfo = strconv.Itoa(i + 1)
		dl.ProcessId = results[0]
		dl.RelateTable = results[1]
		dl.WaitLock = results[2]
		dl.WaitIndex = results[3]
		dl.WaitLockMode = results[4]
		dl.HoldLock = results[5]
		dl.HoldLockIndex = results[6]
		dl.HoldLockMode = results[7]
		dl.Sql = results[8]
		dl.ReqType = GetSqlType(dl.Sql)

		dl.ReqType = GetSqlType(dl.Sql)
		reg = regexp.MustCompile(`WE (.*) TRANSACTION \((\d)\)`)
		if reg.FindStringSubmatch(DLInfoStr) != nil && len(reg.FindStringSubmatch(DLInfoStr)) == 3 {
			if trxnum := reg.FindStringSubmatch(DLInfoStr)[2]; trxnum == strconv.Itoa(i+1) {
				dl.TrxTreat = reg.FindStringSubmatch(DLInfoStr)[1] + " TRANSACTION " + trxnum
			} else {
				dl.TrxTreat = ""
			}
		} else {
			dl.TrxTreat = ""
		}
		DeadlockList2 = append(DeadlockList2, dl)
	}
	DeadlockInfo2 := &shared.DeadlockInfo{
		DeadlockCollectionTime: DeadlockCollectionTime,
		DeadlockTime:           DeadlockTime,
		DeadlockList:           DeadlockList2,
	}
	retlog := &shared.DescribeDeadlockInfo{}
	retlog, err = m.getDeadLock(ctx, DeadlockInfo2, time.Now().Unix(), req.InstanceId, req.TenantId)
	if err != nil {
		log.Warn(ctx, "get Deadlock info fail:%v", err)
		return nil, err
	}
	ret.DescribeDeadlockInfo = retlog
	return ret, nil
}
func (m *metaMySQLImpl) describeLock(ctx context.Context, conn datasource.Conn) ([]*shared.Lock, error) {
	ret := make([]*shared.Lock, 0)
	/* get mysql version info */
	var versionSql = "/*+ DBW DAS DEFAULT*/ select version() as version;"
	var MysqlVersion = DBVersion{}
	if err := conn.Raw(versionSql).Scan(&MysqlVersion); err != nil {
		log.Warn(ctx, "get mysql version fail %v", err)
		return nil, err
	}
	/* version = 5.7x */
	if strings.Contains(MysqlVersion.Version, "5.7") {
		var Locklist []Lock57
		if err := conn.Raw(LockQuery57).Scan(&Locklist); err != nil {
			log.Warn(ctx, "get lock info fail %v", err)
			return nil, err
		}
		LockList2 := make([]*shared.Lock, 0)
		for _, L := range Locklist {
			var lockProperty string
			var Numlockwait int64
			sql1 := fmt.Sprintf("/*+ DBW DAS DEFAULT*/ select count(1) from information_schema.INNODB_LOCK_WAITS where requested_lock_id ='%s';", L.LockId)
			if err := conn.Raw(sql1).Scan(&Numlockwait); err != nil {
				return nil, err
			}
			if Numlockwait == 0 {
				lockProperty = model.Lockstatus_LockHold.String()
			} else {
				lockProperty = model.Lockstatus_LockWait.String()
			}
			LockList2 = append(LockList2, &shared.Lock{
				LockProperty:       lockProperty,
				LockId:             L.LockId,
				LockAssociateIndex: L.LockAssociateIndex,
				LockAssociateTable: L.LockAssociateTable,
				LockType:           L.LockType,
				LockModel:          L.LockModel,
				TrxId:              L.TrxId,
			})
		}
		ret = LockList2
	}

	/* version = 8.0x */
	if strings.Contains(MysqlVersion.Version, "8.0") {
		var Locklist []Lock80
		if err := conn.Raw(LockQuery80).Scan(&Locklist); err != nil {
			log.Warn(ctx, "get lock info fail %v", err)
			return nil, err
		}
		LockList2 := make([]*shared.Lock, 0)
		for i := 0; i < len(Locklist); i++ {
			if i == len(Locklist)-1 || (Locklist[i].LockBegin != Locklist[i+1].LockBegin) {
				LockList2 = append(LockList2, &shared.Lock{
					LockProperty:       Locklist[i].LockProperty,
					LockId:             Locklist[i].LockId,
					LockAssociateIndex: Locklist[i].LockAssociateIndex,
					LockAssociateTable: Locklist[i].LockAssociateTable,
					LockType:           Locklist[i].LockType,
					LockModel:          Locklist[i].LockModel,
					TrxId:              Locklist[i].TrxId,
				})
			}
		}
		ret = LockList2
	}
	return ret, nil
}
func (m *metaMySQLImpl) getDeadLock(ctx context.Context, DeadlockInfo *shared.DeadlockInfo, timestamp int64, InstanceId string, TenantId string) (*shared.DescribeDeadlockInfo, error) {
	/* get tls client */
	var topicId string
	cnf := m.cnf.Get(ctx)
	c3cfg := m.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)

	Ak := c3cfg.TLSServiceAccessKey
	Sk := c3cfg.TLSServiceSecretKey
	regionId := cnf.TlsZone
	tlsEndpoint := cnf.TlsServiceEndpoint
	tlsclient := tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)

	/* get data from tls */
	EndTime := time.Now().Unix()
	StartTime := EndTime - 86400
	tlsDeadlockTopic := c3cfg.TLSDeadLockTopicV2
	topicSet := &datasource.TLSDeadlockTopic{}
	if err := json.Unmarshal([]byte(tlsDeadlockTopic), topicSet); err != nil {
		log.Warn(ctx, " tlsDeadlockTopic unmarshal failed %v", err)
	}
	for _, topic := range topicSet.Topics {
		if topic.InstanceType == model.DSType_MetaMySQL.String() {
			topicId = topic.TopicID
		}
	}

	retLog, err := tlsclient.SearchLogsV2(&tls.SearchLogsRequest{
		TopicID:   topicId,
		Query:     fmt.Sprintf("* | select DeadlockTime,  content where InstanceId='%s' and DeadlockCollectionTime>%s order by DeadlockCollectionTime desc limit 1", InstanceId, strconv.FormatInt(timestamp-86400, 10)),
		StartTime: StartTime,
		EndTime:   EndTime,
		Limit:     1000,
		HighLight: false,
		Context:   "",
		Sort:      "",
	})
	if err != nil {
		log.Warn(ctx, "SearchLogs fail:%v", err)
		return nil, err
	}

	/* add data to ret */
	ret := &shared.DescribeDeadlockInfo{}
	DeadlockInfoList2 := []*shared.DeadlockInfo{}

	if retLog != nil && len(retLog.AnalysisResult.Data) != 0 {
		tempmp := map[string]string{}
		for i := 0; i < len(retLog.AnalysisResult.Data); i++ {
			if _, ok := tempmp[retLog.AnalysisResult.Data[i]["DeadlockTime"].(string)]; !ok {
				tempmp[retLog.AnalysisResult.Data[i]["DeadlockTime"].(string)] = "ok"
				DeadlockInfo2 := &shared.DeadlockInfo{}
				err = json.Unmarshal([]byte(retLog.AnalysisResult.Data[i]["content"].(string)), &DeadlockInfo2)
				if err != nil {
					log.Warn(ctx, "Unmarshal fail:%v", err)
					return nil, err
				}
				DeadlockInfoList2 = append(DeadlockInfoList2, DeadlockInfo2)
			}
		}
		if DeadlockInfo != nil {
			if _, ok := tempmp[DeadlockInfo.DeadlockTime]; !ok {
				DeadlockInfoList2 = append(DeadlockInfoList2, DeadlockInfo)
			}
		}
		ret.DeadlockInfoList = DeadlockInfoList2
	} else {
		ret.DeadlockInfoList = DeadlockInfoList2
	}

	/* put log to tls */
	if DeadlockInfo != nil {
		js, _ := json.Marshal(DeadlockInfo)
		log.Info(ctx, " tls")
		_, err = tlsclient.PutLogs(&tls.PutLogsRequest{
			TopicID:      topicId,
			HashKey:      "",
			CompressType: "lz4",
			LogBody: &pb.LogGroupList{
				LogGroups: []*pb.LogGroup{
					{
						Logs: []*pb.Log{
							{
								Contents: []*pb.LogContent{
									{
										Key:   "InstanceId",
										Value: InstanceId,
									},
									{
										Key:   "DeadlockCollectionTime",
										Value: strconv.FormatInt(timestamp, 10),
									},
									{
										Key:   "DeadlockTime",
										Value: DeadlockInfo.DeadlockTime,
									},
									{
										Key:   "content",
										Value: string(js),
									},
									{
										Key:   "TenantId",
										Value: TenantId,
									},
								},
							},
						},
						Source:      "",
						LogTags:     nil,
						FileName:    "",
						ContextFlow: "",
					},
				},
			},
		})
		if err != nil {
			log.Warn(ctx, "put log to tls fail: %s", err)
			return nil, err
		}
	}
	return ret, nil
}

func (m *metaMySQLImpl) DescribeDeadlockDetect(ctx context.Context, req *datasource.DescribeDeadlockDetectReq) (*datasource.DescribeDeadlockDetectResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.DescribeDeadlockDetectResp{}
	type T struct {
		OnOff int `gorm:"column:@@innodb_deadlock_detect"`
	}
	var t T
	sql := consts.DBW_DIAGNOSIS_DEFAULT_HINT + " select @@innodb_deadlock_detect;"
	if err = conn.Raw(sql).Scan(&t); err != nil {
		log.Warn(ctx, "get Deadlock Detect fail %v", err)
		return nil, err
	}
	ret.DescribeDeadlockDetectInfo = &shared.DescribeDeadlockDetectInfo{
		OnOff: t.OnOff == 1,
	}
	return ret, nil
}

func (m *metaMySQLImpl) DescribeDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) (*datasource.DescribeDialogInfosResp, error) {
	if req.Offset < 0 || req.Limit < 0 {
		return &datasource.DescribeDialogInfosResp{}, nil
	}
	ret := &datasource.DescribeDialogInfosResp{}
	dialogInfos, err := m.getAllDialogInfos(ctx, req)
	if err != nil {
		return nil, err
	}
	ret.DialogDetails = m.filterDialogDetails(ctx, dialogInfos, req.QueryFilter, req.Offset, req.Limit)
	return ret, nil
}

func (m *metaMySQLImpl) getAllDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) ([]*datasource.DialogInfo, error) {
	var (
		dialogInfos []*datasource.DialogInfo
	)
	// 获取终端地址
	if err := m.FillInnerDataSource(ctx, req.Source); err != nil {
		log.Warn(ctx, "fill inner datasource error:%s", err.Error())
		return nil, err
	}
	// 代理是否开启读写
	isEnableReadWriteSplitting, err := m.IsEnableProxyRWMode(ctx, req.Source.InstanceId)
	if err != nil {
		return nil, err
	}
	proxyAddr := req.Source.Address

	if req.Component == model.Component_DBEngine.String() {
		// 获取所有mysqld节点
		pods, err := m.GetPods(ctx, req.Source.InstanceId, model.Component_DBEngine)
		if err != nil {
			return nil, err
		}
		for _, node := range pods {
			//若实例没有开启读写分离则跳过非主节点的会话查询
			if node.Role != model.NodeType_Primary.String() && !isEnableReadWriteSplitting {
				continue
			}
			var tempDialogInfos []*datasource.DialogInfo
			maSource := *req.Source
			maSource.Address = proxyAddr
			//兜底 确保nodeId为空,走实例级连接池
			maSource.NodeId = ""
			conn, err := m.getConnV2(ctx, &maSource)
			if err != nil {
				log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
				continue
			}
			hint := fmt.Sprintf("/* FORCE_NODE=%s */", node.NodeId)
			sql := hint + datasource.DbwDasHint + " select * from information_schema.processlist "
			if len(req.InternalUsers) > 0 {
				sql += fmt.Sprintf("WHERE USER not in ('%s')", strings.Join(req.InternalUsers, "','"))
			}
			log.Info(ctx, "Current sql is %s", sql)
			defer m.ConnPool.Put(ctx, conn)
			if err = conn.Raw(sql).Scan(&tempDialogInfos); err != nil {
				//m.ConnPool.Put(ctx, conn)
				continue
			}
			//m.ConnPool.Put(ctx, conn)
			// fill NULL column
			fp.StreamOf(tempDialogInfos).Foreach(func(d *datasource.DialogInfo) {
				if !d.Info.Valid {
					d.Info.String = "NULL"
					d.SqlTemplate, d.SqlTemplateID = datasource.GetSqlTemplate(ctx, "-")
					d.SqlType = "-"
				} else {
					d.SqlTemplate, d.SqlTemplateID = datasource.GetSqlTemplate(ctx, d.Info.String)
					sqlType, err := datasource.GetRdsSqlType(ctx, d.Info.String)
					if err != nil {
						d.SqlType = "-"
					} else {
						d.SqlType = sqlType
					}
				}
				if !d.BlockingPid.Valid {
					d.BlockingPid.String = "NULL"
				}
				d.NodeId = node.NodeId
				d.NodeType = node.Role
			}).ToSlice(&tempDialogInfos)
			dialogInfos = append(dialogInfos, tempDialogInfos...)
		}
	} else {
		// TODO: proxy版本需要大于等于2.10.2,当前不支持
		// 获取所有proxy节点
		var proxyDialList []*datasource.MySQLProxyDialog
		pods, err := m.GetPods(ctx, req.Source.InstanceId, model.Component_Proxy)
		if err != nil {
			return nil, err
		}
		for _, pod := range pods {
			for _, port := range RdsProxyPortsList {
				convertedPort := fmt.Sprintf("2%s", port) // 如3680->23680
				address := fmt.Sprintf("%s:%s", pod.PodIP, convertedPort)
				_, err := net.Dial("tcp", address) // 连通性测试
				if err == nil {
					url := fmt.Sprintf(consts.RDSProxyShowProcessList, address)
					var tmpDialInfo []*datasource.MySQLProxyDialog
					response, err := m.HttpGet(ctx, url)
					if err != nil {
						log.Warn(ctx, "get proxy %s processlist failed %+v", pod.Name, err)
						continue
					}
					if err := json.Unmarshal(response, &tmpDialInfo); err != nil {
						log.Warn(ctx, "proxy dialog res %s unmarshall failed: %v", string(response), err.Error())
						continue
					}
					for _, dialInfo := range tmpDialInfo {
						dialInfo.Port = port
						dialInfo.ProxyId = pod.Name
						proxyDialList = append(proxyDialList, dialInfo)
					}
				}
			}
		}
		if err := fp.StreamOf(proxyDialList).Map(func(db *datasource.MySQLProxyDialog) *datasource.DialogInfo {
			item := &datasource.DialogInfo{
				ProcessID: db.ProcessID,
				DB:        db.DB,
				Host:      db.Host,
				Command:   db.Command,
				User:      db.User,
				Time:      db.Time,
				State:     "", // proxy不支持
				NodeId:    db.ProxyId,
				NodeType:  "Proxy",
				Info: sql.NullString{
					String: db.Command,
					Valid:  true,
				},
				BlockingPid: sql.NullString{
					String: "NULL",
					Valid:  false,
				},
			}
			//todo: rds接口版本过低，不支持返回
			item.SqlTemplate, item.SqlTemplateID = datasource.GetSqlTemplate(ctx, db.Command)
			return item
		}).ToSlice(&dialogInfos); err != nil {
			return nil, err
		}
	}
	return dialogInfos, nil
}

func (m *metaMySQLImpl) IsEnableProxyRWMode(ctx context.Context, instanceId string) (bool, error) {
	// 判断默认终端是否开启了读写分离，若没开启读写分离则hint无法使用，仅返回主节点的会话信息
	var isEnableReadWriteSplitting bool
	insDetail, err := m.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{InstanceId: instanceId, Type: m.Type()})
	if err != nil {
		return false, err
	}
	for _, ep := range insDetail.Endpoints {
		// 默认终端
		if ep.Mode == datasource.ReadWrite && ep.EndpointType == metaMysqlModel.EndpointType_Cluster.String() {
			if ep.EnableReadWriteSplitting == metaMysqlModel.EnableSwitch_Enable.String() {
				isEnableReadWriteSplitting = true
			} else {
				isEnableReadWriteSplitting = false
			}
			break
		}
	}
	return isEnableReadWriteSplitting, nil
}

func (m *metaMySQLImpl) GetPods(ctx context.Context, instanceId string, component model.Component) ([]*shared.KubePod, error) {
	var pods []*shared.KubePod
	// 获取所有pod节点
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		InstanceId: instanceId,
	}
	listInstancePodsResp, err := m.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "get InstancePods fail: %v", err)
		return nil, err
	}
	if component == model.Component_DBEngine {
		// 获取所有mysqld节点
		for _, pod := range listInstancePodsResp.Data {
			if pod.Component == "MySQL" {
				pods = append(pods, pod)
			}
		}
		if len(pods) < 1 {
			log.Warn(ctx, "No mysqld pod found")
			return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No mysqld pod found")
		}
	}
	if component == model.Component_Proxy {
		// 获取所有proxy节点
		for _, pod := range listInstancePodsResp.Data {
			if pod.Component == "Proxy" {
				pods = append(pods, pod)
			}
		}
		if len(pods) < 1 {
			log.Warn(ctx, "No Proxy pod found")
			return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No proxy pod found")
		}
	}
	return pods, nil
}

func (m *metaMySQLImpl) filterDialogDetails(ctx context.Context, data []*datasource.DialogInfo, queryFilter *shared.DialogQueryFilter, offset int64, limit int64) *shared.DialogDetails {
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.DialogInfo) bool {
				return strings.ToLower(d.Command) == "sleep"
			}).ToSlice(&tData)
		}
		if pID := queryFilter.ProcessID; pID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.ProcessID, pID)
			}).ToSlice(&tData)
		}
		if user := queryFilter.User; user != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(strings.ToLower(d.User), strings.ToLower(user))
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.Host, host) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if fDB := queryFilter.DB; fDB != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(strings.ToLower(d.DB), strings.ToLower(fDB))
			}).ToSlice(&tData)
		}
		if command := queryFilter.Command; command != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Command), strings.ToLower(command))
			}).ToSlice(&tData)
		}
		if state := queryFilter.State; state != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.State, state)
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if nodeType := queryFilter.NodeType; nodeType != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.NodeType, nodeType)
			}).ToSlice(&tData)
		}
		if timeLimit := queryFilter.LowerExecTimeLimit; timeLimit != "" {
			limitFloat, er := strconv.ParseFloat(queryFilter.LowerExecTimeLimit, 64)
			if er == nil {
				fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
					execTime, _ := strconv.ParseFloat(d.Time, 64)
					return execTime >= limitFloat
				}).ToSlice(&tData)
			}
		}
		if info := queryFilter.Info; info != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Info.String), strings.ToLower(info))
			}).ToSlice(&tData)
		}
		if psm := queryFilter.PSM; psm != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				targetPSM := datasource.ExtractRdsPsm(d.Info.String)
				return strings.Contains(strings.ToLower(targetPSM.Psm), strings.ToLower(psm))
			}).ToSlice(&tData)
		}
		if sqlTemplate := queryFilter.SqlTemplate; sqlTemplate != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.SqlTemplate, sqlTemplate)
			}).ToSlice(&tData)
		}
		if sqlTemplateId := queryFilter.SqlTemplateID; sqlTemplateId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.SqlTemplateID, sqlTemplateId)
			}).ToSlice(&tData)
		}
	}
	// 默认按Time倒序
	datasource.SortDialog(tData, shared.DESC, "Time")

	var details []*shared.DialogDetail
	fp.StreamOf(tData[offset:fp.MinInt64(offset+limit, int64(len(tData)))]).
		Map(func(d *datasource.DialogInfo) *shared.DialogDetail {
			psmItem := datasource.ExtractRdsPsm(d.Info.String)
			return &shared.DialogDetail{
				ProcessID:     d.ProcessID,
				User:          d.User,
				Host:          d.Host,
				DB:            d.DB,
				Command:       d.Command,
				Time:          d.Time,
				State:         d.State,
				Info:          d.Info.String,
				BlockingPid:   d.BlockingPid.String,
				NodeId:        d.NodeId,
				NodeType:      d.NodeType,
				PSM:           psmItem.Psm,
				EndpointName:  d.EndpointName,
				EndpointId:    d.EndpointId,
				SqlTemplate:   d.SqlTemplate,
				SqlTemplateID: d.SqlTemplateID,
			}
		}).ToSlice(&details)

	return &shared.DialogDetails{
		Details: details,
		Total:   int32(len(tData)),
	}
}

func (m *metaMySQLImpl) DescribeTableSpace(ctx context.Context, req *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ConvertTableSpaceToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeTableColumn(ctx context.Context, req *datasource.DescribeTableInfoReq) (*shared.DescribeTableColumnResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ConvertTableColumnToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableColumnResp) *model.DescribeTableColumnResp {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeTableIndex(ctx context.Context, req *datasource.DescribeTableInfoReq) (*shared.DescribeTableIndexResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ConvertTableIndexToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableIndexResp) *model.DescribeTableIndexResp {
	panic("implement me")
}

func (m *metaMySQLImpl) FormatDescribeStorageCapacityResp(sourceType shared.DataSourceType, diskSize *datasource.GetDiskSizeResp, storageSpace float64) *model.DescribeStorageCapacityResp {
	panic("implement me")
}

func (m *metaMySQLImpl) ExecuteCCL(ctx context.Context, req *datasource.ExecuteCCLReq) (*datasource.ExecuteCCLResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) CCLShow(ctx context.Context, req *datasource.CCLShowReq) (*datasource.CCLShowResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetCpuMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetAvgCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMinCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetCpuUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMemMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetAvgMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMinMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMemUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetAvgDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMinDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetDiskAvailableDays(ctx context.Context, req *datasource.GetDiskAvailableDaysReq) (*datasource.GetDiskAvailableDaysResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetDiskFutureSize(ctx context.Context, req *datasource.GetDiskFutureSizeReq) (*datasource.GetDiskFutureSizeResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetDiskMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetDiskUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetAvgQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMinQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetAvgTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMinTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetAvgConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMinConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetConnectedRatioMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetSessionMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetAvgSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMinSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetLatestDiskUsage(ctx context.Context, req *datasource.GetLatestDiskUsageReq) (float64, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetInspectionCpuMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetInspectionMemMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetInspectionDiskMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetInspectionQpsMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetInspectionTpsMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetInspectionConnectedMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetInspectionConnRatioMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) HealthSummary(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.Resource, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListCollections(ctx context.Context, req *datasource.ListCollectionsReq) (*datasource.ListCollectionsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListIndexs(ctx context.Context, req *datasource.ListIndexesReq) (*datasource.ListIndexesResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListMongoDBs(ctx context.Context, req *datasource.ListMongoDBsReq) (*datasource.ListMongoDBsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) CreateFreeLockCorrectOrder(ctx context.Context, req *datasource.CreateFreeLockCorrectOrderReq) (*datasource.CreateFreeLockCorrectOrderResp, error) {
	//var ghostConfig *rdsModelV1.GhOstConfig
	//if req.GhostConfig != nil {
	//	var rplDelayCheckRule *metaMysqlModelv1.RplDelayCheckRule
	//	if req.GhostConfig.RplDelayCheckRule != nil {
	//		rule, err := rdsModelV1.RplDelayCheckRuleFromString(*req.GhostConfig.RplDelayCheckRule)
	//		if err != nil {
	//			log.Warn(ctx, "RplDelayCheckRuleFromString fail, error:%s,param is %v", err, req.GhostConfig.RplDelayCheckRule)
	//			return nil, err
	//		}
	//		rplDelayCheckRule = rdsModelV1.RplDelayCheckRulePtr(rule)
	//	}
	//	ghostConfig = &rdsModelV1.GhOstConfig{
	//		KillLongTxn:          req.GhostConfig.KillLongTxn,
	//		RenameDisallowWindow: req.GhostConfig.RenameDisallowWindow,
	//		RplDelayCheckRule:    rplDelayCheckRule,
	//	}
	//}
	rreq := &metaMysqlModelv1.CreateFreeLockCorrectOrderReq{
		InstanceId: req.InstanceId,
		TableName:  req.TableName,
		DBName:     req.DBName,
		ExecSQL:    req.ExecSQL,
		Comment:    req.Comment,
		//GhOstConfig: ghostConfig,
	}
	rresp := &metaMysqlModelv1.CreateFreeLockCorrectOrderResp{}
	m.wrapperCtx(ctx, req.InstanceId)
	err := m.metaMysql.Get().Call(ctx, metaMysqlModelv1.Action_CreateFreeLockCorrectOrder.String(), rreq, rresp)
	if err != nil {
		log.Warn(ctx, "CreateFreeLockCorrectOrder fail, error:%v", err)
		return nil, err
	}
	return &datasource.CreateFreeLockCorrectOrderResp{OrderId: rresp.OrderId}, nil
}

func (m *metaMySQLImpl) CreateFreeLockCorrectOrderDryRun(ctx context.Context, req *datasource.CreateFreeLockCorrectOrderReq) (*datasource.CreateFreeLockCorrectOrderDryRunResp, error) {
	/* 预检查逻辑
	    1、表长度大于59，直接报错
		2、调用MySQL的OnlineDDL接口
	*/

	// 这里判断库表长度
	tables, err := getMySQLAllTables(req.ExecSQL)
	if err != nil {
		log.Warn(ctx, "parse sql %v error %v", req.ExecSQL, err)
		return nil, err
	}
	if len(tables) > 0 {
		if len(tables[0]) > 59 {
			msg := fmt.Sprintf("table name [%v][%v] is too long,more than 59 chars", tables[0], len(tables[0]))
			log.Warn(ctx, msg)
			return &datasource.CreateFreeLockCorrectOrderDryRunResp{
				DryRunSuccess: false,
				ErrorCode:     metaMysqlModelv1.ErrorCode_PrecheckFailed_DryRunFailed.String(),
				Reason:        msg,
			}, nil
		}
	}

	dryRun := true
	//var ghostConfig *rdsModelV1.GhOstConfig
	//if req.GhostConfig != nil {
	//	var rplDelayCheckRule *rdsModelV1.RplDelayCheckRule
	//	if req.GhostConfig.RplDelayCheckRule != nil {
	//		rule, err := rdsModelV1.RplDelayCheckRuleFromString(*req.GhostConfig.RplDelayCheckRule)
	//		if err != nil {
	//			log.Warn(ctx, "RplDelayCheckRuleFromString fail, error:%s,param is %v", err, req.GhostConfig.RplDelayCheckRule)
	//			return nil, err
	//		}
	//		rplDelayCheckRule = rdsModelV1.RplDelayCheckRulePtr(rule)
	//	}
	//	ghostConfig = &rdsModelV1.GhOstConfig{
	//		KillLongTxn:          req.GhostConfig.KillLongTxn,
	//		RenameDisallowWindow: req.GhostConfig.RenameDisallowWindow,
	//		RplDelayCheckRule:    rplDelayCheckRule,
	//	}
	//}
	rreq := &metaMysqlModelv1.CreateFreeLockCorrectOrderReq{
		InstanceId: req.InstanceId,
		TableName:  req.TableName,
		DBName:     req.DBName,
		ExecSQL:    req.ExecSQL,
		Comment:    req.Comment,
		DryRun:     &dryRun,
		//GhOstConfig: ghostConfig,
	}
	rresp := &rdsModelV1.CreateFreeLockCorrectOrderResp{}
	m.wrapperCtx(ctx, req.InstanceId)
	if err := m.metaMysql.Get().Call(ctx, metaMysqlModelv1.Action_CreateFreeLockCorrectOrder.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "CreateFreeLockCorrectOrder dryRun fail, error:%v", err)
		return &datasource.CreateFreeLockCorrectOrderDryRunResp{
			DryRunSuccess: false,
			Reason:        metaMysqlModelv1.ErrorCode_PrecheckFailed_DryRunFailed.String(),
			ErrorCode:     fmt.Sprintf("create free lock correct order dryrun fail, error:%v", err),
		}, err
	}
	return &datasource.CreateFreeLockCorrectOrderDryRunResp{
		DryRunSuccess: rresp.GetDryRunSuccess(),
		Reason:        rresp.GetReason(),
		ErrorCode:     rresp.GetErrorCode()}, nil
}

func (m *metaMySQLImpl) DescribeFreeLockCorrectOrders(ctx context.Context, req *datasource.DescribeFreeLockCorrectOrdersReq) (*datasource.DescribeFreeLockCorrectOrdersResp, error) {
	rreq := &metaMysqlModelv1.ListFreeLockCorrectOrdersReq{
		InstanceId: req.InstanceId,
		OrderId:    req.OrderId,
		Offset:     (req.PageNumber - 1) * req.PageSize,
		Limit:      req.PageSize,
	}
	if req.CreateTimeStart != 0 {
		rreq.CreateTimeStart = time.Unix(int64(req.CreateTimeStart), 0).Format(consts.NormalTimeFormat)
	}
	if req.CreateTimeEnd != 0 {
		rreq.CreateTimeEnd = time.Unix(int64(req.CreateTimeEnd), 0).Format(consts.NormalTimeFormat)
	}
	if req.OrderStatus != nil {
		rreq.OrderStatus = metaMysqlModelv1.OrderStatusPtr(metaMysqlModelv1.OrderStatus(*req.OrderStatus))
	}
	m.wrapperCtx(ctx, req.InstanceId)
	rresp := &metaMysqlModelv1.ListFreeLockCorrectOrdersResp{}
	err := m.metaMysql.Get().Call(ctx, metaMysqlModelv1.Action_ListFreeLockCorrectOrders.String(), rreq, rresp)
	if err != nil {
		log.Warn(ctx, "ListFreeLockCorrectOrders fail, error:%s", err)
		return nil, err
	}
	var datas []*model.SqlTask
	if len(rresp.Datas) > 0 {
		log.Info(ctx, "ListFreeLockCorrectOrders from rds_mysql is %#v", rresp.Datas[0])
	}
	fp.StreamOf(rresp.Datas).Map(convMetaMySQLFreeLockOrder).ToSlice(&datas)
	return &datasource.DescribeFreeLockCorrectOrdersResp{
		Total: rresp.Total,
		Datas: datas,
	}, nil
}

func (m *metaMySQLImpl) StopFreeLockCorrectOrders(ctx context.Context, req *datasource.StopFreeLockCorrectOrdersReq) error {
	if req.InstanceId == "" || req.OrderId == "" {
		log.Warn(ctx, "stopDDLTask: instanceId is empty or orderId is empty,please check")
		return fmt.Errorf("参数错误")
	}
	// 这里调用rds_mysql的接口停止DDL
	ctx = log.WithTraceTag(ctx, "StopFreeLockCorrectOrder")
	rreq := &metaMysqlModelv1.StopFreeLockCorrectOrderReq{
		InstanceId: req.InstanceId,
		OrderId:    req.OrderId,
	}
	m.wrapperCtx(ctx, req.InstanceId)
	err := m.metaMysql.Get().Call(ctx, metaMysqlModelv1.Action_StopFreeLockCorrectOrder.String(), rreq, nil)
	if err != nil {
		log.Warn(ctx, "stopDDLTask: stop free lock fail, error:%s", err)
		return err
	}
	log.Info(ctx, "stopDDLTask: get result from rds api success")
	return nil
}

func (m *metaMySQLImpl) PreCheckFreeLockCorrectOrders(ctx context.Context, req *datasource.PreCheckFreeLockCorrectOrdersReq) (*datasource.PreCheckFreeLockCorrectOrdersResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetDBInnerAddress(ctx context.Context, req *datasource.GetDBInnerAddressReq) (*datasource.GetDBInnerAddressResp, error) {
	ins, err := m.instanceManager.GetInstance(ctx, req.Source.InstanceId, m.LinkType().String())
	if err != nil {
		return nil, err
	}
	req.Source.Address = ins.Address
	return &datasource.GetDBInnerAddressResp{
		Source: req.Source,
	}, nil
}

func (m *metaMySQLImpl) ExecuteDQL(ctx context.Context, req *datasource.ExecuteDQLReq) (*datasource.ExecuteDQLResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ExplainCommand(ctx context.Context, req *datasource.ExplainCommandReq) (*datasource.ExplainCommandResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	if err != nil {
		return nil, err
	}
	var res []*datasource.ExplainCommandResult
	command := "explain " + req.Command
	if err = conn.Raw(command).Scan(&res); err != nil { // 执行失败
		// 对于explainCommand报错的情况,仅捕获表不存在，如果表存在，则给影响行数直接赋值为0
		log.Warn(ctx, "execute cmd %s err:%s", command, err.Error())
		if !strings.Contains(strings.ToLower(err.Error()), "doesn't exist") {
			res = append(res, &datasource.ExplainCommandResult{
				Rows: "0",
			})
			return &datasource.ExplainCommandResp{Command: res}, nil
		}
		return nil, err
	}
	log.Info(ctx, "explain result is %#v", res[0])
	return &datasource.ExplainCommandResp{Command: res}, nil
}

func (m *metaMySQLImpl) IsMyOwnInstance(ctx context.Context, instanceId string, _ shared.DataSourceType) bool {
	ctx = m.wrapperCtx(ctx, instanceId)
	rreq := &metaMysqlModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	m.wrapperCtx(ctx, instanceId)
	rresp := &metaMysqlModel.DescribeDBInstanceDetailResp{}
	if err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DescribeDBInstanceDetail.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "describeInstance error is %s", err.Error())
		return !isMySQLInstanceNotFoundError(err)
	}
	return true
}
func (m *metaMySQLImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	ctx = m.wrapperCtx(ctx, instanceId)
	var (
		dbInstanceStatusBlackList map[string]string
		blackList                 []string
		rawBlackList              string
	)
	rreq := &metaMysqlModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	rresp := &metaMysqlModel.DescribeDBInstanceDetailResp{}
	if err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_DescribeDBInstanceDetail.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "describeInstance error is %s", err.Error())
		return err
	}
	cfg := m.cnf.Get(ctx)
	if isConnectedInstance {
		rawBlackList = cfg.DBInstanceStateWithConnectionBlackList
	} else {
		rawBlackList = cfg.DBInstanceStateWithoutConnectionBlackList
	}
	//rawBlackList = "{\"MySQL\":\"Creating,Deleting\"}"
	err := json.Unmarshal([]byte(rawBlackList), &dbInstanceStatusBlackList)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		blackList = strings.Split(dbInstanceStatusBlackList[ds.String()], ",")
	}
	currentStatus := rresp.BasicInfo.InstanceStatus.String()
	for _, item := range blackList {
		if item == currentStatus {
			log.Warn(ctx, "instance status is %s, not support", currentStatus)
			return consts.ErrorWithParam(model.ErrorCode_InstanceNotInRunningStatus, currentStatus)
		}
	}
	return nil
}

func (m *metaMySQLImpl) GetTableIndexInfo(ctx context.Context, req *datasource.GetTableIndexInfoReq) (*datasource.GetTableInfoIndexResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetTableIndexValue(ctx context.Context, req *datasource.GetIndexValueReq) (*datasource.GetIndexValueResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxConnections(ctx context.Context, req *datasource.GetMaxConnectionsReq) (int, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetLatestUsedConnection(ctx context.Context, req *datasource.GetMetricUsageReq) (int, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetLatestActiveConnection(ctx context.Context, req *datasource.GetMetricUsageReq) (int, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetCurrentBandwidth(ctx context.Context, req *datasource.GetCurrentBandwidthReq) (*datasource.InstanceBandwidthInfo, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) BandwidthScale(ctx context.Context, req *datasource.BandwidthScaleReq) error {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMinBandwidth(ctx context.Context, req *datasource.GetMinMaxBandwidthReq) (int, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetMaxBandwidth(ctx context.Context, req *datasource.GetMinMaxBandwidthReq) (int, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetDiskSize(ctx context.Context, req *datasource.GetDiskSizeReq) (*datasource.GetDiskSizeResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetCurrentMetricData(ctx context.Context, req *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) GetPreSecondMetricData(ctx context.Context, req *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeFullSQLLogConfig(ctx context.Context, req *datasource.DescribeFullSQLLogConfigReq) (*datasource.DescribeFullSQLLogConfigResp, error) {
	ctx = m.wrapperCtx(ctx, req.InstanceID)
	rreq := &metaMysqlModel.InnerDescribeFullSQLLogConfigReq{InstanceId: req.InstanceID}
	rresp := &metaMysqlModel.InnerDescribeFullSQLLogConfigResp{}
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_InnerDescribeFullSQLLogConfig.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "InnerDescribeFullSQLLogConfig fail, error:%s", err)
		return nil, err
	}
	return &datasource.DescribeFullSQLLogConfigResp{
		SQLCollectorStatus: datasource.SQLCollectorStatus(rresp.SQLCollectorStatus),
		TLSDomain:          rresp.GetTLSDomain(),
		TLSProjectId:       rresp.GetTLSProjectId(),
		TLSTopic:           rresp.GetTLSTopic(),
	}, nil
}

func (m *metaMySQLImpl) ModifyFullSQLLogConfig(ctx context.Context, req *datasource.ModifyFullSQLLogConfigReq) (*datasource.ModifyFullSQLLogConfigResp, error) {
	ctx = m.wrapperCtx(ctx, req.InstanceID)
	rreq := &metaMysqlModel.InnerModifyFullSQLLogConfigReq{
		InstanceId:         req.InstanceID,
		SQLCollectorStatus: metaMysqlModel.SQLCollectorStatusEnum(req.SQLCollectorStatus),
		DryRun:             utils.BoolRef(req.DryRun),
	}
	//rresp := &rdsModel_v2.InnerModifyFullSQLLogConfigResp{}
	err := m.metaMysql.Get().Call(ctx, metaMysqlModel.Action_InnerModifyFullSQLLogConfig.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "InnerModifyFullSQLLogConfig fail, error:%s", err)
		return nil, err
	}
	return &datasource.ModifyFullSQLLogConfigResp{}, nil
}

func (m *metaMySQLImpl) DescribeInstanceVariables(ctx context.Context, req *datasource.DescribeInstanceVariablesReq) (*datasource.DescribeInstanceVariablesResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribePrimaryKeyRange(ctx context.Context, req *datasource.DescribePrimaryKeyRangeReq) (*datasource.DescribePrimaryKeyRangeResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeSQLAdvisorTableMeta(ctx context.Context, req *datasource.DescribeSQLAdvisorTableMetaReq) (*datasource.DescribeSQLAdvisorTableMetaResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) DescribeSampleData(ctx context.Context, req *datasource.DescribeSampleDataReq) (*datasource.DescribeSampleDataResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) EnsureAccount(ctx context.Context, req *datasource.EnsureAccountReq) error {
	var err error
	// 1、调用接口获取ds.address
	if err = m.GetDatasourceAddress(ctx, req.Source); err != nil {
		log.Warn(ctx, "EnsureAccount error,getDatasourceAddress error:%s", err.Error())
		return err
	}

	//2、在这个环节判断一下DB是否有值，如果DB没值，手动指定访问performance_schema库
	//这里主要是为了防止授权失败后，没有指定DB的情况下，会默认访问information_schema库，这个库是无权限的，所有账号都能访问，从而导致连上但是没权限的问题
	newSource := *req.Source
	if req.Source.Db == "" {
		newSource.Db = "performance_schema"
	}
	conn, err := m.getConnV2(ctx, &newSource)
	defer func() {
		if conn != nil {
			m.ConnPool.Put(ctx, conn)
		}
	}()
	if err != nil && (strings.Contains(strings.ToLower(err.Error()), consts.MySQLAccountError)) {
		log.Warn(ctx, "rds get conn error %s, maybe there is no account", err.Error())
		// 创建一个新的账号,然后去连接
		err = m.resetAccount(ctx, newSource.InstanceId, shared.MetaMySQL)
		if err != nil {
			log.Warn(ctx, "resetAccount err: %v", err)
			return err
		}
	}
	return nil
}

func (m *metaMySQLImpl) getConnV2(ctx context.Context, ds *shared.DataSource) (datasource.Conn, error) {
	if ds.NodeId == "" {
		return m.ConnPool.GetInstanceAdminConn(ctx, &datasource.GetInstanceAdminConnReq{InstanceId: ds.InstanceId, Type: ds.Type, Ds: ds})
	} else {
		return m.ConnPool.GetNodeAdminConn(ctx, &datasource.GetNodeAdminConnReq{InstanceId: ds.InstanceId, Type: ds.Type, NodeId: ds.NodeId, Ds: ds})
	}
}
func (m *metaMySQLImpl) resetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	log.Info(ctx, "internal account does not exist or password incorrect, try to create a new one")
	// 我们直接调用删除接口，确保删除掉这个账号后重建
	c3cfg := m.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)

	if err := m.DeleteAccount(ctx, &datasource.DeleteAccountReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: c3cfg.DBWAccountName,
	}); err != nil {
		if !strings.Contains(err.Error(), "AccountNotExist") {
			log.Warn(ctx, "failed to delete account, err=%v", err)
			return err
		}
	}

	// 重新创建一个账号
	if err := m.CreateAccount(ctx, &datasource.CreateAccountReq{
		DSType:          dsType,
		InstanceId:      instanceId,
		AccountName:     c3cfg.DBWAccountName,
		AccountPassword: getAccountPassword(c3cfg.DbwAccountPasswordGenKey, instanceId),
		AccountType:     metaMysqlModel.AccountType_Normal.String(),
	}); err != nil {
		log.Warn(ctx, "failed to create account, err=%v", err)
		return err
	}
	// 授权系统库
	if err := m.GrantAccountPrivilege(ctx, &datasource.GrantAccountPrivilegeReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: c3cfg.DBWAccountName,
		DBName:      "sys",
	}); err != nil {
		log.Warn(ctx, "failed to grant privilege on sys, err=%v", err)
		return err
	}
	if err := m.GrantAccountPrivilege(ctx, &datasource.GrantAccountPrivilegeReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: c3cfg.DBWAccountName,
		DBName:      "performance_schema",
	}); err != nil {
		log.Warn(ctx, "failed to grant privilege on performance_schema, err=%v", err)
		return err
	}
	return nil
}
func (m *metaMySQLImpl) GetDatasourceAddress(ctx context.Context, ds *shared.DataSource) error {
	// metamysql仅支持代理地址访问,不支持直连
	if err := m.FillInnerDataSource(ctx, ds); err != nil {
		log.Warn(ctx, "fill inner datasource error:%s", err.Error())
		return err
	}
	return nil
}

func (m *metaMySQLImpl) GetInstancePrimaryNodeId(ctx context.Context, req *datasource.GetInstancePrimaryNodeIdReq) (*datasource.GetInstancePrimaryNodeIdResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ListSQLKillRules(ctx context.Context, req *datasource.ListSQLKillRulesReq) (*datasource.ListSQLKillRulesResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) ModifySQLKillRule(ctx context.Context, req *datasource.ModifySQLKillRuleReq) (*datasource.ModifySQLKillRuleResp, error) {
	panic("implement me")
}

func (m *metaMySQLImpl) Type() shared.DataSourceType {
	return shared.MetaMySQL
}

func (m *metaMySQLImpl) HttpGet(ctx context.Context, url string) ([]byte, error) {
	// 发起 GET 请求
	response, err := http.Get(url)
	if err != nil {
		log.Warn(ctx, "")
		return nil, err
	}
	defer response.Body.Close()
	// 读取响应内容
	res, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (m *metaMySQLImpl) ValidateDryRun(ctx context.Context, req *datasource.ValidateDryRunReq) *shared.ValidateResponse {
	res := &shared.ValidateResponse{ItemCn: datasource.OnlineDDLDryCn, ItemEn: model.PreCheckItem_OnlineDDLDry.String(), Success: datasource.ValidateSuccess}
	res.ItemDetail = []*shared.ItemDetail{{SqlText: "-", Result: "success"}}
	return res
}

func (m *metaMySQLImpl) LinkType() shared.LinkType {
	return shared.Public
}

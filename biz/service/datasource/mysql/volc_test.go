package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/qjpcpu/fp"
	"testing"

	connpoolMock "code.byted.org/infcs/dbw-mgr/biz/test/mocks/conn_pool"
	rdsModelV1 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"

	"github.com/influxdata/influxdb1-client/models"
	influx "github.com/influxdata/influxdb1-client/v2"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	mocks_config "code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func mockMysqlImpl() *mysqlImpl {
	return &mysqlImpl{
		mysql:       &mocks.MockProvider{},
		userMgmtSvc: &mocks.MockUserService{},
		cnf:         &mocks.MockConfigProvider{},
		ConnPool:    &mocks.MockPool{},
	}
}

type MysqlImplTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *MysqlImplTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *MysqlImplTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestMysqlImplTestSuite(t *testing.T) {
	suite.Run(t, new(MysqlImplTestSuite))
}

/*
	func TestGetSumSpace(t *testing.T) {
		impl := &mysqlImpl{}
		mok1 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mok1.UnPatch()
		mok2 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mok2.UnPatch()

		if _, err := impl.getSumSpace(&mocks.MockConn{}, ""); err != nil {
			t.Fatal("failed")
		}
	}
*/
/*func TestGetInformationSchemaTablesInfo(t *testing.T) {
	impl := &mysqlImpl{}
	mok1 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mok1.UnPatch()
	mok2 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mok2.UnPatch()

	req := &datasource.DescribeTableSpaceReq{
		OrderItem: "item",
		OrderRule: "ASC",
	}
	var num int64 = 0
	sum := &num
	if _, err := impl.getInformationSchemaTablesInfo(&mocks.MockConn{}, req, "", sum); err != nil {
		t.Fatal("failed")
	}
}*/

func (suite *MysqlImplTestSuite) TestMysqlImpl_ListInstancePods() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	cnf := mocks_config.NewMockConfigProvider(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	req := &datasource.ListInstancePodsReq{
		InstanceId: "xxx",
		Type:       conv.ToSharedType(model.DSType_MySQL),
		LinkType:   shared.Volc,
	}
	instanceNodesReq := &rdsModel.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	instanceNodesResp := &rdsModel.ListInstanceNodesResp{}
	kc.EXPECT().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), instanceNodesReq, instanceNodesResp, gomock.Any()).Return(nil)
	rreq := &rdsModel.ListInstancePodsReq{
		InstanceId: req.InstanceId,
	}
	rresp := &rdsModel.ListInstancePodsResp{}
	kc.EXPECT().Call(ctx, rdsModel.Action_ListInstancePods.String(), rreq, rresp).Return(nil)
	want := &datasource.ListInstancePodsResp{Total: 0, Data: []*shared.KubePod{}}
	mysqlImpl := mysqlImpl{
		cnf:   cnf,
		mysql: mgr,
	}
	got, _ := mysqlImpl.ListInstancePods(ctx, req)
	suite.Equal(want, got)
}

func (suite *MysqlImplTestSuite) TestMysqlImpl_ListInstancePods_1() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	cnf := mocks_config.NewMockConfigProvider(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	cnf.EXPECT().Get(gomock.Any()).Return(&config.Config{EnableRDSMySQLNewOpenAPI: false}).AnyTimes()
	req := &datasource.ListInstancePodsReq{
		InstanceId: "xxx",
		Type:       conv.ToSharedType(model.DSType_MySQL),
		LinkType:   shared.Volc,
	}
	instanceNodesReq := &rdsModel.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	instanceNodesResp := &rdsModel.ListInstanceNodesResp{}
	kc.EXPECT().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), instanceNodesReq, instanceNodesResp, gomock.Any()).Return(nil)
	rreq := &rdsModel.ListInstancePodsReq{
		InstanceId: req.InstanceId,
	}
	rresp := &rdsModel.ListInstancePodsResp{}
	kc.EXPECT().Call(ctx, rdsModel.Action_ListInstancePods.String(), rreq, rresp).Return(nil)
	want := &datasource.ListInstancePodsResp{Total: 0, Data: []*shared.KubePod{}}
	mysqlImpl := mysqlImpl{
		cnf:   cnf,
		mysql: mgr,
	}
	got, _ := mysqlImpl.ListInstancePods(ctx, req)
	suite.Equal(want, got)
}

func Test_convDsFreeLockOrder(t *testing.T) {
	type args struct {
		d *rdsModelV1.FreeLockCorrectOrder
	}
	tests := []struct {
		name    string
		args    args
		want    *model.SqlTask
		wantErr bool
	}{
		{
			name: "1",
			args: args{
				d: &rdsModelV1.FreeLockCorrectOrder{
					InstanceId:  "1",
					OrderId:     "1",
					DBName:      "1",
					TableName:   "1",
					AccountName: "1",
					ExecSQL:     "1",
					Comment:     "1",
					CreateTime:  "2008-08-01 00:00:00",
					FinishTime:  "2008-08-01 00:00:00",
					OrderStatus: 1,
					Result_:     "1",
					TaskId:      "1",
					ExecNode:    "1",
					RunningInfo: "1",
					Progress:    1,
					EventId:     "1",
				},
			},
			want: &model.SqlTask{
				InstanceId:    "1",
				OrderId:       "1",
				DBName:        "1",
				TableName:     "1",
				ExecSQL:       "1",
				Comment:       "1",
				CreateTime:    **********,
				FinishTime:    **********,
				SqlTaskStatus: 1,
				Result_:       "1",
				Progress:      1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := convDsFreeLockOrder(tt.args.d)
			if (err != nil) != tt.wantErr {
				t.Errorf("convDsFreeLockOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func (suite *MysqlImplTestSuite) TestMysqlImpl_DescribeFreeLockCorrectOrders() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	cnf := mocks_config.NewMockConfigProvider(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	cnf.EXPECT().Get(gomock.Any()).Return(&config.Config{EnableRDSMySQLNewOpenAPI: true}).AnyTimes()

	kc.EXPECT().Call(gomock.Any(), rdsModelV1.Action_ListFreeLockCorrectOrders.String(), gomock.Any(), gomock.Any()).SetArg(3,
		rdsModelV1.ListFreeLockCorrectOrdersResp{
			Total: 1,
			Datas: []*rdsModelV1.FreeLockCorrectOrder{
				{
					InstanceId:  "1",
					OrderId:     "1",
					DBName:      "1",
					TableName:   "1",
					AccountName: "1",
					ExecSQL:     "1",
					Comment:     "1",
					CreateTime:  "2008-08-01 00:00:00",
					FinishTime:  "2008-08-01 00:00:00",
					OrderStatus: 1,
					Result_:     "1",
					TaskId:      "1",
					ExecNode:    "1",
					RunningInfo: "1",
					Progress:    1,
					EventId:     "1",
				},
			},
		}).Return(nil).AnyTimes()

	m := mysqlImpl{
		mysql: mgr,
	}
	orders, err := m.DescribeFreeLockCorrectOrders(context.Background(), &datasource.DescribeFreeLockCorrectOrdersReq{
		InstanceId:      "1",
		Type:            1,
		OrderId:         "1",
		OrderStatus:     nil,
		CreateTimeStart: 1,
		CreateTimeEnd:   1,
		PageNumber:      1,
		PageSize:        1,
	})
	if err != nil {
		suite.T().Error("error")
	}
	if orders.Total != 1 {
		suite.T().Error("total not equal")
	}
	if orders.Datas[0].OrderId != "1" || orders.Datas[0].CreateTime != ********** {
		suite.T().Error("OrderId not equal")
	}
}

/*func (suite *MysqlImplTestSuite) TestDescribeTLSConnectionInfo() {
	ctx := context.Background()
	c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	c3Conf.EXPECT().GetNamespace(ctx, consts.C3ApplicationNamespace).Return(
		&c3.C3Config{Application: &c3.Application{TLSSlowLogTopic: "{\"Topics\":[{\"instance_type\":\"MySQL\", \"topic_id\":\"eff1be09bc48\"},{\"instance_type\":\"VeDBMySQL\", \"topic_id\":\"d6c6f931\"}]}"}})
	cfg := mocks_config.NewMockConfigProvider(suite.ctrl)
	cfg.EXPECT().Get(gomock.Any()).Return(&config.Config{
		TlsServiceEndpoint: "https://tls-cn-nanjing-bbit-inner.ivolces.com",
	}).AnyTimes()
	req := &datasource.DescribeTLSConnectionInfoReq{
		RegionId:   "cn-nanjing-bbit",
		InstanceId: "1111",
		Type:       shared.MySQL,
	}
	want := &datasource.DescribeTLSConnectionInfoResp{
		Endpoint: "https://tls-cn-nanjing-bbit-inner.ivolces.com",
		TopicID:  "eff1be09bc48",
	}
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	vdb := &vedbImpl{
		mysqlImpl: &mysqlImpl{
			mysql:          mgr,
			C3ConfProvider: c3Conf,
			cnf:            cfg,
		},
	}
	got, _ := vdb.DescribeTLSConnectionInfo(ctx, req)
	suite.Equal(want, got)
}

//// 新增v1白名单功能测试：更新成功和失败的场景
//func TestV1whiteListAdd(t *testing.T) {
//	provider := &mocks.MockProvider{}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock((*mysqlImpl).checkNewWhiteList).Return(utils.StringRef("1"), nil).Build()
//	defer mock1.UnPatch()
//
//	mock2 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
//	defer mock2.UnPatch()
//
//	mgrClient := &mocks.MockMgrClient{}
//	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
//	defer mock4.UnPatch()
//
//	impl := mysqlImpl{
//		cnf:   &mockConfigProviderImpl{},
//		mysql: provider,
//	}
//	list, err := impl.AddWhiteList(ctx, "string", &shared.DataSource{
//		Address:  "************",
//		Db:       "jq",
//		User:     "dbw",
//		Password: "123",
//	})
//	if err != nil {
//		t.Fatal("testV1whiteListAdd failed, addWhite List error:", err)
//	}
//
//	if list != "" {
//		t.Fatal("testV1whiteListAdd failed, list is not empty", err)
//	}
//}
//
//// 测试v2白名单，返回白名单为空，先创建后更新的场景
//func TestV2whiteListAdd1(t *testing.T) {
//	//provider := &mocks.MockProvider{}
//	ctx := context.Background()
//	mgrClient := &mocks.MockMgrClient{}
//	mockGet := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
//	defer mockGet.UnPatch()
//	mockCall := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
//	defer mockCall.UnPatch()
//
//	provider := &mocks.MockProvider{}
//
//	mock1 := mockey.Mock((*mysqlImpl).checkNewWhiteList).Return(utils.StringRef("2"), nil).Build()
//	defer mock1.UnPatch()
//
//	mock2 := mockey.Mock((*mysqlImpl).getRegionId).Return("test1", nil).Build()
//	defer mock2.UnPatch()
//
//	mock3 := mockey.Mock((*mysqlImpl).getWhiteListID).Return(nil, nil).Build()
//	defer mock3.UnPatch()
//
//	mock4 := mockey.Mock((*mysqlImpl).CreateAllowListNew).Return("wild1", nil).Build()
//	defer mock4.UnPatch()
//
//	mock5 := mockey.Mock((*mysqlImpl).AssociateAllowList).Return(nil).Build()
//	defer mock5.UnPatch()
//
//	mock6 := mockey.Mock((*mysqlImpl).isNewWhiteList).Return(true).Build()
//	defer mock6.UnPatch()
//	getAllowListMock := mockey.Mock((*mysqlImpl).getAllowLists).Return([]*vdbModel.AllowListInfo{}, nil).Build()
//	defer getAllowListMock.UnPatch()
//	findDBWAllowListMock := mockey.Mock((*mysqlImpl).findDBWAllowlist).Return([]*vdbModel.AllowListInfo{}, nil).Build()
//	defer findDBWAllowListMock.UnPatch()
//	impl := &mysqlImpl{
//		cnf:   &mockConfigProviderImpl{},
//		mysql: provider,
//	}
//	list, err := impl.AddWhiteList(ctx, "string", &shared.DataSource{
//		Address:  "************",
//		Db:       "jq",
//		User:     "dbw",
//		Password: "123",
//		InstanceId: "123",
//	})
//	if err != nil {
//		t.Fatal("testV1whiteListAdd failed, addWhite List error:", err)
//	}
//
//	if list != "wild1" {
//		t.Fatal("testV1whiteListAdd failed, list is not empty", err)
//	}
//}

// 测试更新白名单场景
func TestEnsureAllowList1(t *testing.T) {
	mgrClient := &mocks.MockMgrClient{}
	mock1 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock2.UnPatch()

	provider := &mocks.MockProvider{}
	ctx := context.Background()
	impl := mysqlImpl{
		cnf:   &mockConfigProviderImpl{},
		mysql: provider,
	}
	err := impl.ensureAllowList(ctx, "instance1", "wlid1", `**********/10`)
	if err != nil {
		t.Fatal("TestEnsureAllowList1 failed, err is:", err)
	}
}

func TestContainsIPList(t *testing.T) {
	provider := &mocks.MockProvider{}
	impl := mysqlImpl{
		cnf:   &mockConfigProviderImpl{},
		mysql: provider,
	}
	result := impl.containsIPList("*********/8,********/8,**********/10", `**********/10`)
	if result == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}

	result1 := impl.containsIPList("*********/8,11", `**********/10`)
	if result1 == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}

	result2 := impl.containsIPList("**********/10", `**********/10`)
	if result2 == false {
		t.Fatal("TestContainsIPList failed, result is: false")
	}

	result3 := impl.containsIPList("", `**********/10`)
	if result3 == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}

	result4 := impl.containsIPList("**********/10", ``)
	if result4 == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}

	result5 := impl.containsIPList("**********/10   ", `**********/10`)
	if result5 == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}
}

// 测试vpcID为空的场景，此时获取内网IP
func TestFillDataSource1(t *testing.T) {

	basicInfo := &rdsModel_v2.BasicInfoObject{
		VpcId: "",
	}
	resp := &rdsModel_v2.DescribeDBInstanceDetailResp{
		BasicInfo: basicInfo,
	}
	mock1 := mockey.Mock((*mysqlImpl).describeInstance).Return(resp, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mysqlImpl).FillInnerDataSource).Return(nil).Build()
	defer mock2.UnPatch()

	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock4.UnPatch()

	ctx := context.Background()
	provider := &mocks.MockProvider{}
	impl := mysqlImpl{
		cnf:   &mockConfigProviderImpl{},
		mysql: provider,
	}
	ds := &shared.DataSource{
		Type:     shared.MySQL,
		LinkType: 0,
		Address:  "",
		User:     "root",
		Password: "Bytedance@123",
		ExtraDsn: map[string]string{
			"connect": "direct",
		},
		ConnectTimeoutMs: 10,
		ReadTimeoutMs:    10,
		WriteTimeoutMs:   10,
		Db:               "admin",
		IdleTimeoutMs:    10,
		InstanceId:       "mysql-replica-f08df929d89f",
		CadidateAddress:  "***********:3707",
		VpcID:            "vpc-xxxx",
		MaxOpenConns:     10,
		MaxIdleConns:     10,
	}

	impl.FillDataSource(ctx, ds)

}

func (suite *MysqlImplTestSuite) TestMysqlImpl_DescribeInstanceVersion() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	req := &datasource.DescribeInstanceVersionReq{
		InstanceId: "xxx",
		Type:       shared.MySQL,
	}
	describeInstanceVersionReq := &rdsModel.InnerDescribeInstanceVersionReq{
		InstanceId: req.InstanceId,
	}
	describeInstanceVersionResp := &rdsModel.InnerDescribeInstanceVersionResp{
		EpicVersionNumber: "2.8.2",
	}
	kc.EXPECT().Call(ctx, rdsModel.Action_InnerDescribeInstanceVersion.String(), describeInstanceVersionReq, gomock.Any(), gomock.Any()).SetArg(3, *describeInstanceVersionResp).Return(nil)
	want := &datasource.DescribeInstanceVersionResp{
		Version: "2.8.2",
	}
	mysqlImpl := mysqlImpl{
		mysql: mgr,
	}
	got, _ := mysqlImpl.DescribeInstanceVersion(ctx, req)
	suite.Equal(want, got)
}

func (suite *MysqlImplTestSuite) TestMysqlImpl_DescribeDBInstanceDetail() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	req := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: "xxx",
		Type:       shared.MySQL,
	}
	describeDBInstanceDetailReq := &rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &rdsModel_v2.DescribeDBInstanceDetailResp{
		BasicInfo: &rdsModel_v2.BasicInfoObject{
			MasterInstanceId: utils.StringRef("mysql-1111"),
			InstanceStatus:   rdsModel_v2.InstanceStatus_Running,
			InstanceType:     rdsModel_v2.InstanceType_Cluster,
			DBEngine:         rdsModel_v2.DBEngine_Mysql,
			DBEngineVersion:  rdsModel_v2.DBEngineVersion_MySQL_8_0,
		},
	}
	kc.EXPECT().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, gomock.Any(), gomock.Any()).SetArg(3, *describeDBInstanceDetailResp).Return(nil)
	want := &datasource.DescribeDBInstanceDetailResp{
		MasterInstanceId: "mysql-1111",
		InstanceStatus:   "Running",
		DBEngine:         rdsModel_v2.DBEngine_Mysql.String(),
		InstanceType:     rdsModel_v2.InstanceType_Cluster.String(),
		DBEngineVersion:  rdsModel_v2.DBEngineVersion_MySQL_8_0.String(),
	}
	mysqlImpl := mysqlImpl{
		mysql: mgr,
	}
	got, _ := mysqlImpl.DescribeDBInstanceDetail(ctx, req)
	suite.Equal(want, got)
}

func (suite *MysqlImplTestSuite) TestMysqlImpl_ModifyAutoKillSessionConfig() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	req := &datasource.ModifyAutoKillSessionConfigReq{
		InstanceId:    "xxx",
		Type:          shared.MySQL,
		MaxExecTime:   utils.Int64Ref(600),
		SQLKillStatus: utils.BoolRef(true),
	}
	modifySQLKillConfigReq := &rdsModel_v2.ModifySQLKillConfigReq{
		InstanceId:    req.InstanceId,
		EnableSQLKill: req.SQLKillStatus,
		MaxExecTime:   req.MaxExecTime,
	}
	resp := &rdsModel_v2.ModifySQLKillConfigResp{}
	kc.EXPECT().Call(ctx, rdsModel_v2.Action_ModifySQLKillConfig.String(), modifySQLKillConfigReq, gomock.Any(), gomock.Any()).SetArg(3, *resp).Return(nil)
	want := &datasource.ModifyAutoKillSessionConfigResp{}
	mysqlImpl := mysqlImpl{
		mysql: mgr,
	}
	got, _ := mysqlImpl.ModifyAutoKillSessionConfig(ctx, req)
	suite.Equal(want, got)
}

func (suite *MysqlImplTestSuite) TestMysqlImpl_DescribeAutoKillSessionConfig() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	req := &datasource.DescribeAutoKillSessionConfigReq{
		InstanceId: "xxx",
		Type:       shared.MySQL,
	}
	describeSQLKillConfigReq := &rdsModel_v2.DescribeSQLKillConfigReq{
		InstanceId: req.InstanceId,
	}
	resp := &rdsModel_v2.DescribeSQLKillConfigResp{
		MaxExecTime:   60,
		SQLKillStatus: true,
		InstanceId:    "xxx",
	}
	kc.EXPECT().Call(ctx, rdsModel_v2.Action_DescribeSQLKillConfig.String(), describeSQLKillConfigReq, gomock.Any(), gomock.Any()).SetArg(3, *resp).Return(nil)
	want := &datasource.DescribeAutoKillSessionConfigResp{
		MaxExecTime:   60,
		SQLKillStatus: true,
	}
	mysqlImpl := mysqlImpl{
		mysql: mgr,
	}
	got, _ := mysqlImpl.DescribeAutoKillSessionConfig(ctx, req)
	suite.Equal(want, got)
}

// 测试vpcID不为空的场景，此时获取内网IP
func TestFillDataSource2(t *testing.T) {

	basicInfo := &rdsModel_v2.BasicInfoObject{
		VpcId: "vpc1",
	}

	var cmd = []*rdsModel_v2.ConnectionInfoObject{
		{EndpointType: 0},
		{EndpointType: 0},
	}

	resp := &rdsModel_v2.DescribeDBInstanceDetailResp{
		BasicInfo:      basicInfo,
		ConnectionInfo: cmd,
	}
	mock1 := mockey.Mock((*mysqlImpl).describeInstance).Return(resp, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mysqlImpl).FillInnerDataSource).Return(nil).Build()
	defer mock2.UnPatch()

	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock4.UnPatch()

	ctx := context.Background()
	provider := &mocks.MockProvider{}
	impl := mysqlImpl{
		cnf:   &mockConfigProviderImpl{},
		mysql: provider,
	}
	ds := &shared.DataSource{
		Type:     shared.MySQL,
		LinkType: 0,
		Address:  "",
		User:     "root",
		Password: "Bytedance@123",
		ExtraDsn: map[string]string{
			"connect": "direct",
		},
		ConnectTimeoutMs: 10,
		ReadTimeoutMs:    10,
		WriteTimeoutMs:   10,
		Db:               "admin",
		IdleTimeoutMs:    10,
		InstanceId:       "mysql-replica-f08df929d89f",
		CadidateAddress:  "***********:3707",
		VpcID:            "vpc-xxxx",
		MaxOpenConns:     10,
		MaxIdleConns:     10,
	}

	impl.FillDataSource(ctx, ds)
}

func mockConfigRes() *config.Config {
	return &config.Config{
		MgrPodCIDR: "*********/8,********/8,**********/10",
	}
}

func TestListDatabasesWithAccount(t *testing.T) {
	impl := mysqlImpl{
		cnf: &mockConfigProviderImpl{},
	}
	req := &datasource.ListDatabasesWithAccountReq{
		PageSize:   300,
		PageNumber: 2,
	}
	resp := &rdsModel.ListDatabasesResp{
		Total: 1,
		Datas: []*rdsModel.DBInfo{},
	}
	mock1 := mockey.Mock((*mysqlImpl).selfListDatabases).Return(resp, nil).Build()
	defer mock1.UnPatch()
	res, err := impl.ListDatabasesWithAccount(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, res.Total, int32(1))
}

func TestDescribeDBInstances(t *testing.T) {
	impl := mysqlImpl{
		cnf: &mockConfigProviderImpl{},
	}
	req := &rdsModel_v2.DescribeDBInstancesReq{
		PageSize:   utils.Int32Ref(1),
		PageNumber: utils.Int32Ref(1),
	}
	resp := &rdsModel_v2.DescribeDBInstancesResp{InstancesInfo: []*rdsModel_v2.InstanceInfoObject{}, Instances: []*rdsModel_v2.InstanceInfoObject{}, Total: 1}

	mock1 := mockey.Mock((*mysqlImpl).selfDescribeDBInstances).Return(resp, nil).Build()
	defer mock1.UnPatch()
	res, err := impl.DescribeDBInstances(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, res.Total, int32(1))
}

func (suite *MysqlImplTestSuite) TestMysqlImpl_ListErrLogs() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	cnf := mocks_config.NewMockConfigProvider(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	req := &datasource.ListErrLogsReq{
		InstanceId: "xxx",
		Type:       conv.ToSharedType(model.DSType_MySQL),
		Limit:      10,
		Offset:     "",
		StartTime:  1,
		EndTime:    2,
		LogLevel:   []model.ErrLogLevel{model.ErrLogLevel_Error, model.ErrLogLevel_Warning, model.ErrLogLevel_Note},
		NodeIds:    []string{"xx1", "xx2"},
	}
	errLogsReq := &rdsModel.ListErrorLogRecordsReq{
		InstanceId: req.InstanceId,
		Context:    req.Offset,
		Limit:      req.Limit,
		StartTime:  1,
		VerbosityLevel: []rdsModel.VerbosityLevel{
			rdsModel.VerbosityLevel_Error,
			rdsModel.VerbosityLevel_Warning,
			rdsModel.VerbosityLevel_Note,
		},
		EndTime:  2,
		NodeName: req.NodeIds,
	}
	errLogsResp := &rdsModel.ListErrorLogRecordsResp{
		Total:   1,
		Context: "xxx1",
		Datas: []*rdsModel.ErrorLogRecord{
			{
				CreateTime:     "2023-12-12 12:00:00",
				VerbosityLevel: rdsModel.VerbosityLevel_Note,
				ErrorInfo:      "note:xxx",
				NodeName:       "xx1",
			},
		},
	}
	kc.EXPECT().Call(ctx, rdsModel.Action_ListErrorLogRecords.String(), errLogsReq, gomock.Any(), gomock.Any()).SetArg(3, *errLogsResp).Return(nil)
	want := &datasource.ListErrLogsResp{
		Total: 1,
		ErrLogs: []*model.ErrLog{
			{
				Timestamp: **********,
				LogLevel:  model.ErrLogLevel_Note,
				NodeId:    "xx1",
				Content:   "note:xxx",
			},
		},
		Context: "xxx1",
	}
	mysqlImpl := mysqlImpl{
		cnf:   cnf,
		mysql: mgr,
	}
	got, _ := mysqlImpl.ListErrLogs(ctx, req)
	suite.Equal(want, got)
}

//func (suite *MysqlImplTestSuite) TestMysqlImpl_DescribeDialogStatistics_Succeed() {
//	mockito.PatchConvey("test describe dialog details succeed", suite.T(), func() {
//		cfg := mocks_config.NewMockConfigProvider(suite.ctrl)
//		mgrProvider := mocks.NewMockMgrProvider(suite.ctrl)
//		//poolPro := mocks.NewMockPool(suite.ctrl)
//		mgrClient := mocks.NewMockMgrClient(suite.ctrl)
//		mgrProvider.EXPECT().Get().Return(mgrClient).AnyTimes()
//		impl := NewMySQLDataSource(NewMySQLDataSourceIn{Conf: cfg, MySQLMgr: mgrProvider})
//		ctx := mocks.NewMockContext(suite.ctrl)
//		ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//		mgrProvider.EXPECT().Get().Return(mgrClient).AnyTimes()
//		mock1 := mockey.Mock((*mysqlImpl).getConnV2).Return(&mocks_connpool.MockConn{}, nil).Build()
//
//		defer mock1.UnPatch()
//		mock2 := mockey.Mock((*mocks_connpool.MockConn).Raw).Return(&mocks.MockResult{}).Build()
//		defer mock2.UnPatch()
//		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
//		defer mock3.UnPatch()
//		mock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
//		defer mock4.UnPatch()
//		req := &datasource.DescribeDialogStatisticsReq{
//			Component: model.Component_DBEngine.String(),
//			Source: &shared.DataSource{Address: "************", Db: "jq", User: "dbw",
//				Password: "123", InstanceId: "mysql-e18603bd57f6"},
//			InternalUsers: []string{"admin"},
//		}
//		mock5resp := &datasource.ListInstancePodsResp{Total: 0, Data: []*shared.KubePod{{Component: "MySQL", Role: model.NodeType_Primary.String()}}}
//		mock5 := mockey.Mock((*mysqlImpl).ListInstancePods).Return(mock5resp, nil).Build()
//		defer mock5.UnPatch()
//
//		instanceNodesReq := &rdsModel.ListInstanceNodesReq{
//			InstanceId: req.Source.InstanceId,
//		}
//		instanceNodesResp := &rdsModel.ListInstanceNodesResp{}
//		mgrClient.EXPECT().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), instanceNodesReq, instanceNodesResp, gomock.Any()).Return(nil)
//		resp, err := impl.Source.DescribeDialogStatistics(ctx, req)
//		So(err, ShouldEqual, nil)
//		So(resp, ShouldResemble, &datasource.DescribeDialogStatisticsResp{
//			DialogStatistics: &shared.DialogStatistics{
//				DialogOver: &shared.DialogOverview{
//					TotalConnections:  0,
//					ActiveConnections: 0,
//				},
//				UserAggregatedInfo: nil,
//				DBAggregatedInfo:   nil,
//				IPAggregatedInfo:   nil,
//			},
//		})
//	})
//}

func Test_addDsn(t *testing.T) {
	PatchConvey("addDsn", t, func() {
		PatchConvey("case 1", func() {
			mysql := mysqlImpl{}
			ds := &shared.DataSource{Type: shared.MySQL}
			ds = mysql.addDsn(ds)
			mm, _ := strconv.ParseBool(ds.ExtraDsn["multiStatements"])
			So(mm, ShouldBeTrue)
			So(ds.MaxIdleConns, ShouldBeZeroValue)
			So(ds.MaxOpenConns, ShouldEqual, 1)
		})
		PatchConvey("case 2", func() {
			mysql := mysqlImpl{}
			ds := &shared.DataSource{Type: shared.MySQL, ExtraDsn: map[string]string{"11": "11"}}
			ds = mysql.addDsn(ds)
			mm, _ := strconv.ParseBool(ds.ExtraDsn["multiStatements"])
			So(mm, ShouldBeTrue)
			So(ds.MaxIdleConns, ShouldBeZeroValue)
			So(ds.MaxOpenConns, ShouldEqual, 1)
		})
		PatchConvey("case 3", func() {
			mysql := mysqlImpl{}
			ds := &shared.DataSource{Type: shared.Postgres, ExtraDsn: map[string]string{"11": "11"}}
			ds = mysql.addDsn(ds)
			mm, _ := strconv.ParseBool(ds.ExtraDsn["multiStatements"])
			So(mm, ShouldBeFalse)
			So(ds.MaxIdleConns, ShouldBeZeroValue)
			So(ds.MaxOpenConns, ShouldEqual, 1)
		})
	})
}

func TestMySQLConvertTableSpaceToModel(t *testing.T) {
	impl := &mysqlImpl{}
	tableStats := []*shared.TableStat{{Name: "aaa", DB: "db_test"}}
	mockResp := &shared.DescribeTableSpaceResp{
		Total:      1,
		TableStats: tableStats,
	}
	resp := impl.ConvertTableSpaceToModel(context.Background(), shared.MySQL, mockResp)
	assert.Equal(t, resp.Total, int32(1))
	assert.Equal(t, resp.TableStats[0].DB, "db_test")
}

func TestNumericConversion(t *testing.T) {
	assert.Equal(t, numericConversion(1000), 0.01)
}

func TestGetInformationSchemaTablesInfoOrderInfo(t *testing.T) {
	impl := &mysqlImpl{}
	res := impl.getInformationSchemaTablesInfoOrderInfo("name", "DESC")
	assert.Equal(t, res, " Name DESC ")
}

func (suite *MysqlImplTestSuite) TestMysqlImpl_ListInstance() {
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	mgr := mocks.NewMockMgrProvider(suite.ctrl)
	kc := mocks.NewMockMgrClient(suite.ctrl)
	mgr.EXPECT().Get().Return(kc).AnyTimes()
	req := &datasource.ListInstanceReq{
		InstanceId:      "xxx",
		Type:            shared.MySQL,
		InstanceName:    "11",
		CreateTimeStart: "**********",
		DBEngineVersion: "MySQL_5_7",
		InstanceStatus:  "Running",
		Tags: []*model.TagObject{
			{
				Key:   "dbw",
				Value: "11",
			},
		},
	}
	describeDBInstanceSpecsReq := &rdsModel_v2.DescribeDBInstanceSpecsReq{}
	resp := &rdsModel_v2.DescribeDBInstanceSpecsResp{
		InstanceSpecsInfo: []*rdsModel_v2.InstanceSpecsInfoObject{
			{
				VCPU:     2,
				Memory:   2,
				SpecCode: "30",
			},
		},
	}
	kc.EXPECT().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceSpecs.String(), describeDBInstanceSpecsReq, gomock.Any(), gomock.Any()).SetArg(3, *resp).Return(nil)
	describeDBInstancesResp := &rdsModel_v2.DescribeDBInstancesResp{
		Total: 1,
		Instances: []*rdsModel_v2.InstanceInfoObject{
			{
				InstanceStatus: rdsModel_v2.InstanceStatus_Running,
				InstanceId:     "xxx",
				InstanceName:   "11",
				InstanceType:   rdsModel_v2.InstanceType_DoubleNode,
				ZoneId:         "cn-guilin-a;cn-guilin-b",
				Tags: []*rdsModel_v2.TagObject{
					{
						Key:   "dbw",
						Value: utils.StringRef("11"),
					},
				},
				DBEngineVersion: rdsModel_v2.DBEngineVersion_MySQL_5_7,
				ProjectName:     "default",
				NodeNumber:      2,
				AddressObject: []*rdsModel_v2.AddressObject{
					{
						NetworkType: rdsModel_v2.NetworkType_Private,
					},
				},
				NodeSpec: "30",
			},
		},
		InstancesInfo: []*rdsModel_v2.InstanceInfoObject{
			{
				InstanceStatus: rdsModel_v2.InstanceStatus_Running,
				InstanceId:     "xxx",
				InstanceName:   "11",
				InstanceType:   rdsModel_v2.InstanceType_DoubleNode,
				ZoneId:         "cn-guilin-a;cn-guilin-b",
				Tags: []*rdsModel_v2.TagObject{
					{
						Key:   "dbw",
						Value: utils.StringRef("11"),
					},
				},
				DBEngineVersion: rdsModel_v2.DBEngineVersion_MySQL_5_7,
				ProjectName:     "default",
				NodeNumber:      2,
				AddressObject: []*rdsModel_v2.AddressObject{
					{
						NetworkType: rdsModel_v2.NetworkType_Private,
					},
				},
				NodeSpec: "30",
			},
		},
	}
	mock1 := mockey.Mock((*mysqlImpl).DescribeDBInstances).Return(describeDBInstancesResp, nil).Build()
	defer mock1.UnPatch()
	mysqlImpl := mysqlImpl{
		mysql: mgr,
	}
	_, err := mysqlImpl.ListInstance(ctx, req)
	suite.Equal(err, nil)
}

func Test_mysqlImpl_GetDataSize(t *testing.T) {
	/*	describeDBInstancesResp := &datasource.GetDiskSizeResp{}
		mock1 := mockey.Mock((*mysqlImpl).GetDataSize).Return(describeDBInstancesResp, nil).Build()
		defer mock1.UnPatch()
		impl := &mysqlImpl{}
		impl.GetDataSize()*/

//}

func Test_mysqlImpl_GetDataSize2(t *testing.T) {
	ctx := context.Background()
	req := &datasource.GetDiskSizeReq{
		InstanceId: "test-instance-id",
	}

	impl := &mysqlImpl{}

	// Mock getMeasurements to return non-empty results
	// Mock getMeasurements to return non-empty results using the correct structures
	mockGetMeasurements := mockey.Mock((*mysqlImpl).getMeasurements).To(
		func(_self *mysqlImpl, ctx context.Context, instanceId string, measurementName string) (*influx.Response, error) {
			return &influx.Response{
				Results: []influx.Result{
					{
						Series: []models.Row{
							{
								Name:    measurementName,
								Columns: []string{"time", "value"},
								Values: [][]interface{}{
									{"2023-01-01T00:00:00Z", 123.0},
								},
							},
						},
					},
				},
			}, nil
		},
	).Build()
	defer mockGetMeasurements.UnPatch()

	// Mock GetDBLogSize to return predefined sizes based on the table name
	mockGetDBLogSize := mockey.Mock((*mysqlImpl).GetDBLogSize).To(
		func(_self *mysqlImpl, ctx context.Context, req *datasource.GetDiskSizeReq, tableName string) (float64, error) {
			switch tableName {
			case "tob_rds_disk_redo_log_usage_bytes":
				return 100.0, nil
			case "tob_rds_disk_undo_log_usage_bytes":
				return 200.0, nil
			case "tob_rds_disk_relay_log_usage_bytes":
				return 300.0, nil
			case "tob_rds_disk_temp_file_usage_bytes":
				return 400.0, nil
			case "tob_rds_disk_system_data_usage_bytes":
				return 500.0, nil
			case "tob_rds_disk_audit_log_usage_bytes":
				return 600.0, nil
			case "tob_rds_disk_user_data_usage_bytes":
				return 700.0, nil
			case "tob_rds_disk_other_usage_bytes":
				return 800.0, nil
			case "tob_rds_disk_slow_log_usage_bytes":
				return 900.0, nil
			case "tob_rds_disk_binlog_usage_bytes":
				return 1000.0, nil
			case "tob_rds_disk_err_log_usage_bytes":
				return 1100.0, nil
			case "tob_rds_disk_usage_bytes":
				return 1200.0, nil
			default:
				return 0.0, nil
			}
		},
	).Build()
	defer mockGetDBLogSize.UnPatch()

	// Call the method under test
	resp, err := impl.GetDataSize(ctx, req)
	if err != nil {
		t.Fatalf("GetDataSize failed: %v", err)
	}

	// Assert the returned values
	assert.Equal(t, 700.0, resp.UserDataUsageSize)
	assert.Equal(t, 400.0, resp.TmpUsedStorage)
	assert.Equal(t, 500.0, resp.SysUsedStorage)
	assert.Equal(t, 100.0, resp.RedoLogSize)
	assert.Equal(t, 200.0, resp.UndoLogSize)
	assert.Equal(t, 300.0, resp.RelayLogSize)
	assert.Equal(t, 600.0, resp.AuditLogSize)
	assert.Equal(t, 900.0, resp.DiskSlowLogSize)
	assert.Equal(t, 1200.0, resp.UsedStorage)
	assert.Equal(t, 1000.0, resp.DiskBinLogSize)
	assert.Equal(t, 1100.0, resp.DiskErrorLogSize)
	assert.Equal(t, 800.0, resp.OtherUsageSize)
}

func Test_mysqlImpl_ListAllTables(t *testing.T) {
	ctx := context.Background()
	req := &datasource.ListTablesReq{
		Limit: 123,
	}
	mock1 := mockey.Mock((*mysqlImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()

	impl := &mysqlImpl{}
	impl.ListAllTables(ctx, req)
}

func Test_MySQLModifySQLKillRule(t *testing.T) {
	mysqlImpl := mockMysqlImpl()
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock4.UnPatch()
	_, err := mysqlImpl.ModifySQLKillRule(ctx, &datasource.ModifySQLKillRuleReq{
		InstanceId: "111",
		Action:     "Add",
		KillRule: []*datasource.KillRuleInfo{
			{
				SqlType:     "select,delete",
				NodeType:    "Primary,ReadOnly",
				MaxExecTime: 1,
			},
		},
	})
	assert.Nil(t, err)

	_, err1 := mysqlImpl.ModifySQLKillRule(ctx, &datasource.ModifySQLKillRuleReq{
		InstanceId: "111",
		Action:     "Stop",
		KillRule: []*datasource.KillRuleInfo{
			{
				SqlType:     "select,delete",
				NodeType:    "Primary,ReadOnly",
				MaxExecTime: 1,
			},
		},
	})
	assert.Nil(t, err1)
	_, err2 := mysqlImpl.ModifySQLKillRule(ctx, &datasource.ModifySQLKillRuleReq{
		InstanceId: "111",
		Action:     "Delete",
		KillRule: []*datasource.KillRuleInfo{
			{
				SqlType:     "select,delete",
				NodeType:    "Primary,ReadOnly",
				MaxExecTime: 1,
			},
		},
	})
	assert.Nil(t, err2)
}

func Test_MySQLListInstanceLightWeight(t *testing.T) {
	mysql := mockMysqlImpl()
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock4.UnPatch()

	_, err := mysql.ListInstanceLightWeight(ctx, &datasource.ListInstanceReq{InstanceId: "mysql-xxx",
		LinkType: shared.Volc, PageSize: 10, PageNumber: 1, InstanceName: "test1", TenantId: "2133333",
		CreateTimeEnd: "2024-12-26T15:04:05Z", CreateTimeStart: "2024-12-26T13:04:05Z"})
	assert.Equal(t, nil, err)
}
func Test_MySQLListInstanceLightWeight2(t *testing.T) {
	mysql := mockMysqlImpl()
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock4.UnPatch()

	_, err := mysql.ListInstanceLightWeight(ctx, &datasource.ListInstanceReq{InstanceId: "mysql-xxx",
		LinkType: shared.Volc, PageSize: 10, PageNumber: 1, InstanceName: "test1", TenantId: "1",
		CreateTimeEnd: "2024-12-26T15:04:05Z", CreateTimeStart: "2024-12-26T13:04:05Z"})
	assert.Equal(t, nil, err)
}
func Test_MySQLGetManagedAccountAndPwd(t *testing.T) {
	mysql := mockMysqlImpl()
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockUserService).GetDBAccount).Return(nil).Build()
	defer baseMock1.UnPatch()
	_, err := mysql.GetManagedAccountAndPwd(ctx, &shared.DataSource{
		InstanceId: "mysql-xxx",
	})
	assert.Equal(t, nil, err)
}

func Test_MySQLKillProcess1(t *testing.T) {
	mysql := mockMysqlImpl()
	mysql.ConnPool = &mocks.MockPool{}
	conn := &connpoolMock.MockConn{}
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConfigProvider).Get).Return(&config.Config{EnableNewConnectionPool: true}).Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockPool).GetInstanceAdminConn).Return(conn, nil).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*connpoolMock.MockConn).Exec).Return(fmt.Errorf("kill failed")).Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
	defer baseMock4.UnPatch()
	_, err := mysql.KillProcess(ctx, &datasource.KillProcessReq{
		Source: &shared.DataSource{
			InstanceId: "mysql-xxx",
			Type:       shared.MySQL,
		},
		ProcessIDs: []string{"111", "222"},
		NodeId:     "mysql-xxx-1",
	})
	assert.Equal(t, nil, err)
}
func Test_MySQLKillProcess2(t *testing.T) {
	mysql := mockMysqlImpl()
	mysql.ConnPool = &mocks.MockPool{}
	conn := &connpoolMock.MockConn{}
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConfigProvider).Get).Return(&config.Config{EnableNewConnectionPool: false}).Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockPool).GetInstanceAdminConn).Return(conn, nil).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*connpoolMock.MockConn).Exec).Return(fmt.Errorf("kill failed")).Build()
	defer baseMock3.UnPatch()
	_, err := mysql.KillProcess(ctx, &datasource.KillProcessReq{
		Source: &shared.DataSource{
			InstanceId: "mysql-xxx",
			Type:       shared.MySQL,
		},
		ProcessIDs: []string{"111", "222"},
		NodeId:     "mysql-xxx-1",
	})
	assert.NotEqual(t, nil, err)
}

func TestDescribeTableSpaceAutoIncr(t *testing.T) {
	impl := &mysqlImpl{}

	ctx := context.Background()
	req := &datasource.DescribeTableSpaceReq{
		Source: &shared.DataSource{Address: "127.0.0.1"},
		Limit:  10,
		Offset: 0,
	}

	// Mock `getConn` to return a mock connection
	mockConn := &mocks.MockConn{}
	mok1 := mockey.Mock((*mysqlImpl).getConn).Return(mockConn, nil).Build()
	defer mok1.UnPatch()

	// Mock `getTablesInfoAutoIncr` to return sample data
	mockTableInfo := []*TableStatAutoIncr{
		{
			DBName:             "test_db",
			TableName:          "test_table",
			ColumnName:         "id",
			DataType:           "BIGINT",
			MaxValue:           10000,
			AutoIncrement:      9000,
			AutoIncrementRatio: 90.0,
		},
	}
	mok2 := mockey.Mock((*mysqlImpl).getTablesInfoAutoIncr).Return(mockTableInfo, nil).Build()
	defer mok2.UnPatch()

	// Mock `Close` on the mock connection
	mok3 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mok3.UnPatch()

	// Call the function under test
	resp, err := impl.DescribeTableSpaceAutoIncr(ctx, req)

	// Assertions
	assert.Nil(t, err, "DescribeTableSpaceAutoIncr should not return an error")
	assert.NotNil(t, resp, "Response should not be nil")
	assert.Equal(t, int32(len(mockTableInfo)), resp.Total, "Total count should match the mock table list")
	assert.Equal(t, mockTableInfo[0].DBName, resp.TableStatAutoIncr[0].DBName, "DBName should match")
}

func TestFilterTrxAndLocks(t *testing.T) {
	mockey.PatchConvey("filter by trxId", t, func() {
		v := &mysqlImpl{}
		trxAndLocks := []*shared.TrxAndLock{{TrxId: "abcde"}, {TrxId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{TrxId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{TrxId: "abcde"}})
	})
	mockey.PatchConvey("filter by processId", t, func() {
		v := &mysqlImpl{}
		trxAndLocks := []*shared.TrxAndLock{{ProcessId: "abcde"}, {ProcessId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{ProcessId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{ProcessId: "abcde"}})
	})
	mockey.PatchConvey("filter by blockTrxId", t, func() {
		v := &mysqlImpl{}
		trxAndLocks := []*shared.TrxAndLock{{BlockTrxId: "abcde"}, {BlockTrxId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{BlockTrxId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{BlockTrxId: "abcde"}})
	})
	mockey.PatchConvey("filter by sqlBlocked", t, func() {
		v := &mysqlImpl{}
		trxAndLocks := []*shared.TrxAndLock{{SqlBlocked: "abcde"}, {SqlBlocked: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{SqlBlocked: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{SqlBlocked: "abcde"}})
	})
	mockey.PatchConvey("filter by trxExecTime", t, func() {
		v := &mysqlImpl{}
		trxAndLocks := []*shared.TrxAndLock{{TrxExecTime: 100}, {TrxExecTime: 1000}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{TrxExecTime: utils.Int32Ref(100)}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{TrxExecTime: 1000}})
	})
	mockey.PatchConvey("filter by nodeIds", t, func() {
		v := &mysqlImpl{}
		trxAndLocks := []*shared.TrxAndLock{{NodeId: "abcde"}, {NodeId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, nil, []string{"abcde"})
		So(got, ShouldResemble, []*shared.TrxAndLock{{NodeId: "abcde"}})
	})
}
func TestFilterWaitLockDetails(t *testing.T) {
	impl := &mysqlImpl{}
	mockey.PatchConvey("filterWaitLockDetails", t, func() {
		mockey.PatchConvey("filter by nodeIds", func() {
			resp := impl.filterWaitLockDetails(context.TODO(), []*datasource.LockCurrentWaitsDetail{
				{
					NodeId:             "1",
					RTrxMysqlThreadId:  "111",
					RTrxId:             "111",
					RTrxOperationState: "xxx",
					RTrxRowsLocked:     "1",
					RTrxRowsModified:   "1",
					RTrxStarted:        "xxx",
					RTrxState:          "xxx",
					RTrxWaitStarted:    "xxx",
					RWaitingQuery:      "xxx",
					RBlockedWaitSecs:   "10",
					BTrxId:             "xxx",
					BBlockingQuery:     "xxx",
					BBlockingWaitSecs:  "10",
					BTrxMysqlThreadId:  "222",
					BTrxOperationState: "xxxx",
					BTrxRowsLocked:     "1",
					BTrxRowsModified:   "1",
					BTrxStarted:        "xxx",
					BTrxState:          "xxx",
					BTrxWaitStarted:    "xxx",
				},
				{NodeId: "2",
					RTrxMysqlThreadId:  "111",
					RTrxId:             "111",
					RTrxOperationState: "xxx",
					RTrxRowsLocked:     "1",
					RTrxRowsModified:   "1",
					RTrxStarted:        "xxx",
					RTrxState:          "xxx",
					RTrxWaitStarted:    "xxx",
					RWaitingQuery:      "xxx",
					RBlockedWaitSecs:   "10",
					BTrxId:             "xxx",
					BBlockingQuery:     "xxx",
					BBlockingWaitSecs:  "10",
					BTrxMysqlThreadId:  "222",
					BTrxOperationState: "xxxx",
					BTrxRowsLocked:     "1",
					BTrxRowsModified:   "1",
					BTrxStarted:        "xxx",
					BTrxState:          "xxx",
					BTrxWaitStarted:    "xxx",
				},
				{NodeId: "3"},
			}, &model.WaitLockQueryFilter{}, []string{"1", "2"})
			So(resp.Total, ShouldEqual, 2)
			So(resp.Result[0].NodeId, ShouldEqual, "1")
			So(resp.Result[1].NodeId, ShouldEqual, "2")
		})
	})
}
func TestGenerateLockWaitSearchWhereConditions(t *testing.T) {
	mockey.PatchConvey("运行成功", t, func() {
		searchParam := &model.WaitLockQueryFilter{
			RTrxState:      utils.StringRef(model.Trxstatus_RUNNING.String()),
			BTrxState:      utils.StringRef(model.Trxstatus_LOCKWAIT.String()),
			RTrxId:         utils.StringRef("123"),
			BTrxId:         utils.StringRef("456"),
			RWaitingQuery:  utils.StringRef("select"),
			BBlockingQuery: utils.StringRef("update"),
		}
		where, args := generateLockWaitSearchWhereConditions(searchParam)
		So(where, ShouldEqual, " where 1  and r_trx_state=? and b_trx_state=? and r_trx_id like ? and b_trx_id like ? and r_waiting_query like ? and b_blocking_query like ?")
		So(args, ShouldResemble, []interface{}{"RUNNING", "LOCK WAIT", "%123%", "%456%", "%select%", "%update%"})
	})
	mockey.PatchConvey("searchParam为nil", t, func() {
		where, args := generateLockWaitSearchWhereConditions(nil)
		So(where, ShouldEqual, "")
		So(args, ShouldBeNil)
	})
}
func TestGenerateTrxAndLockSearchWhereConditions(t *testing.T) {
	mockey.PatchConvey("TestGenerateTrxAndLockSearchWhereConditions", t, func() {
		mockey.PatchConvey("searchParam is nil", func() {
			where, args := generateTrxAndLockSearchWhereConditions(nil)
			So(where, ShouldEqual, "")
			So(args, ShouldBeNil)
		})
		mockey.PatchConvey("searchParam is not nil", func() {
			mockey.PatchConvey("only trxStatus", func() {
				searchParam := &model.TrxQueryFilter{
					TrxStatus: utils.StringRef(model.Trxstatus_RUNNING.String()),
				}
				where, args := generateTrxAndLockSearchWhereConditions(searchParam)
				So(where, ShouldEqual, " where 1  and TrxStatus=?")
				So(args, ShouldResemble, []interface{}{"RUNNING"})
			})
			mockey.PatchConvey("lockStatus--lockHold", func() {
				searchParam := &model.TrxQueryFilter{
					LockStatus: model.LockstatusPtr(model.Lockstatus_LockHold),
				}
				where, _ := generateTrxAndLockSearchWhereConditions(searchParam)
				So(where, ShouldEqual, " where 1  and TrxLockStructs>0")
			})
			mockey.PatchConvey("lockStatus-lockWait", func() {
				searchParam := &model.TrxQueryFilter{
					LockStatus: model.LockstatusPtr(model.Lockstatus_LockWait),
				}
				where, _ := generateTrxAndLockSearchWhereConditions(searchParam)
				So(where, ShouldEqual, " where 1  and ISNULL(trx_requested_lock_id)=0")
			})
			mockey.PatchConvey("lockStatus-LockHoldAndWait", func() {
				searchParam := &model.TrxQueryFilter{
					LockStatus: model.LockstatusPtr(model.Lockstatus_LockHoldAndWait),
				}
				where, _ := generateTrxAndLockSearchWhereConditions(searchParam)
				So(where, ShouldEqual, " where 1  and TrxLockStructs>0 and ISNULL(trx_requested_lock_id)=0")
			})
			mockey.PatchConvey("lockStatus-None", func() {
				searchParam := &model.TrxQueryFilter{
					LockStatus: model.LockstatusPtr(model.Lockstatus_None),
				}
				where, _ := generateTrxAndLockSearchWhereConditions(searchParam)
				So(where, ShouldEqual, " where 1  and TrxLockStructs=0")
			})
			mockey.PatchConvey("隔离级别", func() {
				searchParam := &model.TrxQueryFilter{
					TrxIsoLevel: utils.StringRef("xxx"),
				}
				where, args := generateTrxAndLockSearchWhereConditions(searchParam)
				So(where, ShouldEqual, " where 1  and TrxIsoLevel=?")
				So(args, ShouldResemble, []interface{}{"xxx"})
			})
		})
	})
}

func TestGetAllWaitLocks(t *testing.T) {
	ctx := context.TODO()
	conn := &connpoolMock.MockConn{}
	req := &datasource.DescribeLockCurrentWaitsReq{
		Source: &shared.DataSource{
			Type:       shared.MySQL,
			InstanceId: "mysql-xxx",
		},
	}
	impl := mockMysqlImpl()
	impl.ConnPool = &mocks.MockPool{}
	mockey.PatchConvey("test getAllWaitLocks", t, func() {
		mockey.Mock((*mysqlImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
			Data: []*shared.KubePod{
				{
					Component: "MySQL",
					PodIP:     "*******",
					NodeId:    "mysql-xxx-master0",
					Role:      "Primary",
				},
			},
		}, nil).Build()
		mock1 := mockey.Mock((*mysqlImpl).getConnV2).Return(conn, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
		defer mock4.UnPatch()
		_, err := impl.getAllWaitLocks(ctx, req)
		So(err, ShouldBeNil)
	})
}
func TestGetAllTrxAndLocks(t *testing.T) {
	ctx := context.TODO()
	conn := &connpoolMock.MockConn{}
	req := &datasource.DescribeTrxAndLocksReq{
		Source: &shared.DataSource{
			Type:       shared.MySQL,
			InstanceId: "mysql-xxx",
		},
	}
	impl := mockMysqlImpl()
	impl.ConnPool = &mocks.MockPool{}
	mockey.PatchConvey("test getAllTrxLocks", t, func() {
		mockey.Mock((*mysqlImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
			Data: []*shared.KubePod{
				{
					Component: "MySQL",
					PodIP:     "*******",
					NodeId:    "mysql-xxx-master0",
					Role:      "Primary",
				},
			},
		}, nil).Build()
		//res := []TrxAndLock{
		//	{
		//		ProcessId: "1",
		//		TrxId: "1",
		//		TrxLockStructs: 1,
		//		TrxIsoLevel: "xxx",
		//		TrxExecTime: 10,
		//		TrxState: "xxx",
		//		TrxStartTime: time.Now(),
		//		TrxWaitStartTime: time.Now(),
		//		TrxRowsLocked: 1,
		//		TrxRowsModified: 1,
		//		TrxTablesLocked: 1,
		//		TrxRequestedLockId: "xxx",
		//	},
		//}
		mock1 := mockey.Mock((*mysqlImpl).getConnV2).Return(conn, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
		defer mock4.UnPatch()
		_, err := impl.getAllTrxLocks(ctx, req)
		So(err, ShouldBeNil)
	})
}
func (suite *MysqlImplTestSuite) TestRdsCheckInstanceState() {
	ctx := context.Background()
	mockey.PatchConvey("normal", suite.T(), func() {
		cfg := mocks.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&config.Config{
			DBInstanceStateWithoutConnectionBlackList: "{\"MySQL\":\"Creating,Deleting\"}",
			DBInstanceStateWithConnectionBlackList:    "{\"MySQL\":\"Creating,Deleting\"}",
		}).AnyTimes()
		mgrProvider := mocks.NewMockMgrProvider(suite.ctrl)
		mgrClient := mocks.NewMockMgrClient(suite.ctrl)
		mgrClient.EXPECT().Call(gomock.Any(), rdsModel_v2.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, rdsModel_v2.DescribeDBInstanceDetailResp{
			BasicInfo: &rdsModel_v2.BasicInfoObject{
				InstanceStatus: rdsModel_v2.InstanceStatus_Creating,
			},
		}).Return(nil).AnyTimes()
		mgrProvider.EXPECT().Get().Return(mgrClient).AnyTimes()

		h := NewMySQLDataSource(NewMySQLDataSourceIn{
			Conf:     cfg,
			MySQLMgr: mgrProvider,
		})
		got := h.Source.CheckInstanceState(ctx, "mysql-xxx", shared.MySQL, true)
		suite.NotEmpty(got)
	})
}

func (suite *MysqlImplTestSuite) TestRdsDescribeSQLCCLConfig() {
	ctx := context.Background()
	mockey.PatchConvey("normal", suite.T(), func() {
		cfg := mocks.NewMockConfigProvider(suite.ctrl)
		mgrProvider := mocks.NewMockMgrProvider(suite.ctrl)
		mgrClient := mocks.NewMockMgrClient(suite.ctrl)
		mgrClient.EXPECT().Call(gomock.Any(), rdsModel_v2.Action_DescribeSQLConcurrencyControlConfig.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, rdsModel_v2.DescribeSQLConcurrencyControlConfigResp{
			SQLConcurrencyControlStatus: true,
		}).Return(nil).AnyTimes()
		mgrProvider.EXPECT().Get().Return(mgrClient).AnyTimes()

		h := NewMySQLDataSource(NewMySQLDataSourceIn{
			Conf:     cfg,
			MySQLMgr: mgrProvider,
		})
		_, err := h.Source.DescribeSQLCCLConfig(ctx, &datasource.DescribeSQLCCLConfigReq{
			InstanceId: "mysql-xxx",
			Type:       shared.MySQL,
		})
		suite.Nil(err)
	})
}
func Test_MysqlDescribeLock(t *testing.T) {
	ctx := context.Background()
	conn := &connpoolMock.MockConn{}
	impl := mockMysqlImpl()
	impl.ConnPool = &mocks.MockPool{}
	mockey.PatchConvey("Test describeLock", t, func() {
		mockey.PatchConvey("Test get mysql version failed", func() {
			mock1 := mockey.Mock((*mysqlImpl).getConnV2).Return(conn, nil).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(errors.New("get mysql version failed")).Build()
			defer mock3.UnPatch()
			_, err := impl.describeLock(ctx, conn)
			So(err, ShouldNotBeNil)
		})
		mockey.PatchConvey("Test get mysql version success", func() {
			mock1 := mockey.Mock((*mysqlImpl).getConnV2).Return(conn, nil).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
			defer mock3.UnPatch()
			_, err := impl.describeLock(ctx, conn)
			So(err, ShouldBeNil)
		})
	})
}

func Test_filterDialogDetails(t *testing.T) {
	ctx := context.Background()
	v := &mysqlImpl{}
	data := []*datasource.DialogInfo{
		{
			ProcessID:     "123",
			User:          "user1",
			Host:          "host1",
			DB:            "db1",
			Command:       "SELECT",
			Time:          "10.5",
			State:         "active",
			Info:          sql.NullString{String: "info1", Valid: true},
			BlockingPid:   sql.NullString{String: "456", Valid: true},
			NodeId:        "node1",
			NodeType:      "type1",
			EndpointName:  "endpoint1",
			EndpointId:    "endpointId1",
			SqlTemplate:   "template1",
			SqlTemplateID: "templateId1",
			SqlType:       "type1",
		},
		// Add more test data as needed
	}
	queryFilter := &shared.DialogQueryFilter{
		ShowSleepConnection: "false",
		ProcessID:           "123",
		User:                "user1",
		Host:                "host1",
		DB:                  "db1",
		Command:             "SELECT",
		State:               "active",
		NodeId:              "node1",
		NodeType:            "type1",
		LowerExecTimeLimit:  "10.0",
		Info:                "info1",
		PSM:                 "psm1",
		SqlTemplate:         "template1",
		SqlTemplateID:       "templateId1",
		SqlType:             "type1",
		NodeIds:             []string{"node1"},
		IsFuzzy:             false,
	}
	offset := int64(0)
	limit := int64(10)

	mockey.PatchConvey("Test filterDialogDetails with nil queryFilter", t, func() {
		actual := v.filterDialogDetails(ctx, data, nil, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldEqual, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and ShowSleepConnection false", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and ProcessID", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and User", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and Host", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and DB", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and Command", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and State", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and NodeId", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and NodeType", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and LowerExecTimeLimit", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and Info", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and PSM", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and SqlTemplate", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and SqlTemplateID", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and SqlType", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and NodeIds", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})

	mockey.PatchConvey("Test filterDialogDetails with queryFilter and IsFuzzy", t, func() {
		actual := v.filterDialogDetails(ctx, data, queryFilter, offset, limit)
		So(actual, ShouldNotBeNil)
		So(len(actual.Details), ShouldBeLessThanOrEqualTo, fp.MinInt64(limit, int64(len(data))))
	})
}

func Test_RdsDescribeSqlType(t *testing.T) {
	ctx := context.Background()
	impl := &mysqlImpl{}
	mockey.PatchConvey("Test DescribeSqlType", t, func() {
		mockey.PatchConvey("Test GetRdsSqlType failed", func() {
			req := &datasource.DescribeSqlTypeReq{SqlText: "SELECT * FROM "}
			resp, err := impl.DescribeSqlType(ctx, req)
			So(resp, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})

		mockey.PatchConvey("Test GetRdsSqlType success", func() {
			expectedSqlType := "SELECT"
			req := &datasource.DescribeSqlTypeReq{SqlText: "SELECT * FROM users;"}
			resp, err := impl.DescribeSqlType(ctx, req)
			So(resp.SqlType, ShouldEqual, expectedSqlType)
			So(err, ShouldBeNil)
		})
	})
}

func Test_filterIpList(t *testing.T) {
	mockey.PatchConvey("Test filterIpList with fuzzy matching", t, func() {
		slice := []string{"***********", "********", "**********"}
		mockey.PatchConvey("Test fuzzy matching with host containing item", func() {
			host := "***********:8080"
			isFuzzy := true
			result := filterIpList(slice, host, isFuzzy)
			So(result, ShouldBeTrue)
		})
		mockey.PatchConvey("Test fuzzy matching with item containing host", func() {
			host := "168.1.1"
			isFuzzy := true
			result := filterIpList(slice, host, isFuzzy)
			So(result, ShouldBeTrue)
		})
		mockey.PatchConvey("Test fuzzy matching with no match", func() {
			host := "***********:8080"
			isFuzzy := true
			result := filterIpList(slice, host, isFuzzy)
			So(result, ShouldBeFalse)
		})
	})

	mockey.PatchConvey("Test filterIpList with exact matching", t, func() {
		slice := []string{"***********", "********", "**********"}
		mockey.PatchConvey("Test exact matching with matching IP", func() {
			host := "***********:8080"
			isFuzzy := false
			result := filterIpList(slice, host, isFuzzy)
			So(result, ShouldBeTrue)
		})
		mockey.PatchConvey("Test exact matching with non-matching IP", func() {
			host := "***********:8080"
			isFuzzy := false
			result := filterIpList(slice, host, isFuzzy)
			So(result, ShouldBeFalse)
		})
		mockey.PatchConvey("Test exact matching with invalid host format", func() {
			host := "***********"
			isFuzzy := false
			result := filterIpList(slice, host, isFuzzy)
			So(result, ShouldBeFalse)
		})
	})
}

func Test_exactContains(t *testing.T) {
	mockey.PatchConvey("Test exactContains with isFuzzy = false", t, func() {
		slice := []string{"apple", "banana", "cherry"}
		target := "banana"

		mockey.PatchConvey("Test target exists in slice", func() {
			result := exactContains(slice, target, false)
			So(result, ShouldBeTrue)
		})

		mockey.PatchConvey("Test target does not exist in slice", func() {
			target := "grape"
			result := exactContains(slice, target, false)
			So(result, ShouldBeFalse)
		})
	})

	mockey.PatchConvey("Test exactContains with isFuzzy = true", t, func() {
		slice := []string{"apple", "banana", "cherry"}
		target := "ban"

		mockey.PatchConvey("Test target partially matches an item in slice", func() {
			result := exactContains(slice, target, true)
			So(result, ShouldBeFalse)
		})

		mockey.PatchConvey("Test target partially matches an item in slice (reverse)", func() {
			target := ""
			result := exactContains(slice, target, true)
			So(result, ShouldBeFalse)
		})

		mockey.PatchConvey("Test target does not match any item in slice", func() {
			target := "grape"
			result := exactContains(slice, target, true)
			So(result, ShouldBeFalse)
		})

		mockey.PatchConvey("Test target matches an item in slice (case insensitive)", func() {
			target := "BANANA"
			result := exactContains(slice, target, true)
			So(result, ShouldBeTrue)
		})

	})

	mockey.PatchConvey("Test exactContains with empty slice", t, func() {
		slice := []string{}
		target := "banana"

		mockey.PatchConvey("Test isFuzzy = false", func() {
			result := exactContains(slice, target, false)
			So(result, ShouldBeFalse)
		})

		mockey.PatchConvey("Test isFuzzy = true", func() {
			result := exactContains(slice, target, true)
			So(result, ShouldBeFalse)
		})
	})

	mockey.PatchConvey("Test exactContains with nil slice", t, func() {
		var slice []string
		target := "banana"

		mockey.PatchConvey("Test isFuzzy = false", func() {
			result := exactContains(slice, target, false)
			So(result, ShouldBeFalse)
		})

		mockey.PatchConvey("Test isFuzzy = true", func() {
			result := exactContains(slice, target, true)
			So(result, ShouldBeFalse)
		})
	})

	mockey.PatchConvey("Test exactContains with target longer than any item in slice", t, func() {
		slice := []string{"apple", "banana", "cherry"}
		target := "banana split"

		mockey.PatchConvey("Test isFuzzy = false", func() {
			result := exactContains(slice, target, false)
			So(result, ShouldBeFalse)
		})

		mockey.PatchConvey("Test isFuzzy = true", func() {
			result := exactContains(slice, target, true)
			So(result, ShouldBeTrue)
		})
	})
}

package mysql

import (
	vdbModel "code.byted.org/infcs/dbw-mgr/gen/vedb-mgr/2022-01-01/kitex_gen/infcs/bytendb/model"
	"context"
	"errors"
	"fmt"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/monitor/influxdb"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	connpoolMock "code.byted.org/infcs/dbw-mgr/biz/test/mocks/conn_pool"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	. "code.byted.org/luoshiqi/mockito"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type VedbImplTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func mockvedbImpl() *vedbImpl {
	return &vedbImpl{
		mysqlImpl: &mysqlImpl{
			mysql:       &mocks.MockProvider{},
			userMgmtSvc: &mocks.MockUserService{},
			cnf:         &mocks.MockConfigProvider{},
			ConnPool:    &mocks.MockPool{},
		},
	}
}
func (suite *VedbImplTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *VedbImplTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestVedbImplTestSuite(t *testing.T) {
	suite.Run(t, new(VedbImplTestSuite))
}
func mockVeDBImpl() *vedbImpl {
	return &vedbImpl{}
}

//func (suite *VedbImplTestSuite) TestDescribeInstanceAddress() {
//	PatchConvey("get return err", suite.T(), func() {
//		ctx := context.Background()
//		req := &datasource.DescribeInstanceAddressReq{
//			Type:       shared.VeDBMySQL,
//			InstanceId: "xxx",
//			LinkType:   shared.Volc,
//			NodeType:   model.NodeType_Secondary.String(),
//		}
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		rresp := &vdbModel.DescribeMasterDBEngineConnectionResp{
//			DBEnginePort:   3306,
//			DBEngineDomain: "xxx.org",
//		}
//		kc.EXPECT().Call(ctx, vdbModel.Action_DescribeMasterDBEngineConnection.String(), gomock.Any(), gomock.Any()).SetArg(3, *rresp).Return(nil).AnyTimes()
//
//		resp := &datasource.DescribeInstanceAddressResp{
//			IP:   "xxx.org",
//			Port: 3306,
//		}
//		vdb := &vedbImpl{
//			&mysqlImpl{
//				mysql: mgr,
//			},
//		}
//		r, _ := vdb.DescribeInstanceAddress(ctx, req)
//		So(r, ShouldResemble, resp)
//	})
//	PatchConvey("get rds api err", suite.T(), func() {
//		ctx := context.Background()
//		req := &datasource.DescribeInstanceAddressReq{
//			Type:       shared.VeDBMySQL,
//			InstanceId: "xxx",
//			LinkType:   shared.Volc,
//			NodeType:   model.NodeType_Secondary.String(),
//		}
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		rresp := &vdbModel.DescribeMasterDBEngineConnectionResp{
//			DBEngineDomain: "xxx.org",
//			DBEnginePort:   3306,
//		}
//		kc.EXPECT().Call(ctx, vdbModel.Action_DescribeMasterDBEngineConnection.String(), gomock.Any(), gomock.Any()).SetArg(3, *rresp).Return(errors.New("")).AnyTimes()
//		vdb := &vedbImpl{
//			&mysqlImpl{
//				mysql: mgr,
//			},
//		}
//		_, err := vdb.DescribeInstanceAddress(ctx, req)
//		So(err, ShouldResemble, errors.New(""))
//	})
//}

//func (suite *VedbImplTestSuite) TestListInstancePods() {
//	PatchConvey("get return correct", suite.T(), func() {
//		ctx := context.Background()
//		req := &datasource.ListInstancePodsReq{
//			Type:       shared.VeDBMySQL,
//			InstanceId: "xxx",
//			LinkType:   shared.Volc,
//		}
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		rresp := &vdbModel.ListInstancePodsResp{
//			Total: 2,
//			Datas: []*vdbModel.KubePod{
//				{
//					PodIP:       "*******",
//					NodeIP:      "*******",
//					Zone:        "xxx",
//					Region:      "xxx",
//					NodePool:    "",
//					KubeCluster: "cluster1",
//					Labels: map[string]string{
//						"role": "leader",
//					},
//					Containers: []*vdbModel.KubeContainer{
//						{
//							Name:  "mysql",
//							Ports: map[string]int32{},
//						},
//						{
//							Name:  "agent",
//							Ports: map[string]int32{},
//						},
//					},
//				},
//				{
//					PodIP: "*******",
//					Containers: []*vdbModel.KubeContainer{
//						{
//							Name:  "mysql",
//							Ports: map[string]int32{},
//						},
//						{
//							Name:  "agent",
//							Ports: map[string]int32{},
//						},
//					},
//				},
//			},
//		}
//		kc.EXPECT().Call(ctx, vdbModel.Action_ListInstancePods.String(), gomock.Any(), gomock.Any()).SetArg(3, *rresp).Return(nil).AnyTimes()
//		resp := &datasource.ListInstancePodsResp{}
//		vdb := &vedbImpl{
//			&mysqlImpl{
//				mysql: mgr,
//			},
//		}
//		r, _ := vdb.ListInstancePods(ctx, req)
//		So(r, ShouldNotEqual, resp)
//	})
//}

/*func (suite *VedbImplTestSuite) TestDescribeTLSConnectionInfo() {
	PatchConvey("return correct", suite.T(), func() {
		ctx := context.Background()
		c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
		c3Conf.EXPECT().GetNamespace(ctx, consts.C3ApplicationNamespace).Return(
			&config.C3Config{Application: &c3.Application{TLSSlowLogTopic: "{\"Topics\":[{\"instance_type\":\"MySQL\", \"topic_id\":\"eff1be09bc48\"},{\"instance_type\":\"VeDBMySQL\", \"topic_id\":\"d6c6f931\"}]}"}})
		cfg := mocks_config.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&config.Config{
			TlsServiceEndpoint: "https://tls-cn-nanjing-bbit-inner.ivolces.com",
		}).AnyTimes()
		req := &datasource.DescribeTLSConnectionInfoReq{
			RegionId:   "cn-nanjing-bbit",
			InstanceId: "1111",
			Type:       shared.VeDBMySQL,
		}
		resp := &datasource.DescribeTLSConnectionInfoResp{
			Endpoint: "https://tls-cn-nanjing-bbit-inner.ivolces.com",
			TopicID:  "d6c6f931",
		}
		mgr := mocks.NewMockMgrProvider(suite.ctrl)
		kc := mocks.NewMockMgrClient(suite.ctrl)
		mgr.EXPECT().Get().Return(kc).AnyTimes()
		vdb := &vedbImpl{
			mysqlImpl: &mysqlImpl{
				mysql:          mgr,
				C3ConfProvider: c3Conf,
				cnf:            cfg,
			},
		}
		r, _ := vdb.DescribeTLSConnectionInfo(ctx, req)
		So(r, ShouldResemble, resp)
	})
}*/

//func (suite *VedbImplTestSuite) TestListInstanceNodes() {
//	PatchConvey("return correct", suite.T(), func() {
//		ctx := context.Background()
//		req := &datasource.ListInstanceNodesReq{
//			InstanceId: "xxx",
//			DSType:     shared.VeDBMySQL,
//		}
//		rreq := &vdbModel.DescribeDBInstanceDetailReq{
//			InstanceId: utils.StringRef("xxx"),
//		}
//		rresp := &vdbModel.DescribeDBInstanceDetailResp{
//			Nodes: []*vdbModel.NodeObject{
//				{
//					NodeId:   "xxx-1",
//					ZoneId:   "sy-1",
//					NodeType: "Primary",
//					VCPU:     1,
//					Memory:   2,
//					NodeSpec: "",
//				},
//			},
//		}
//		resp := &datasource.ListInstanceNodesResp{
//			Nodes: []*model.NodeInfoObject{
//				{
//					NodeId:   "xxx-1",
//					NodeType: model.NodeType_Primary,
//					CpuNum:   1,
//					MemInGiB: 2,
//					ZoneId:   "sy-1",
//				},
//			},
//		}
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		kc.EXPECT().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), rreq, gomock.Any()).SetArg(3, *rresp).Return(nil).AnyTimes()
//		vdb := &vedbImpl{
//			&mysqlImpl{
//				mysql: mgr,
//			},
//		}
//		r, _ := vdb.ListInstanceNodes(ctx, req)
//		So(r, ShouldResemble, resp)
//	})
//}

//func (suite *VedbImplTestSuite) TestListInstance() {
//	PatchConvey("return correct", suite.T(), func() {
//		ctx := context.Background()
//		cnf := mocks_config.NewMockConfigProvider(suite.ctrl)
//		req := &datasource.ListInstanceReq{
//			Type:            4,
//			LinkType:        shared.Volc,
//			PageNumber:      1,
//			PageSize:        20,
//			InstanceName:    "xxx",
//			InstanceId:      "111",
//			RegionId:        "cn-nanjing-bbit",
//			InstanceStatus:  "Running",
//			DBEngineVersion: "MySQL_8_0",
//			ZoneId:          "111",
//			TenantId:        "21000746",
//		}
//		//rreq := &vdbModel.DescribeDBInstancesReq{
//		//	InstanceId: utils.StringRef("xxx"),
//		//	PageSize:   10,
//		//	PageNumber: 1,
//		//}
//		//rresp := &vdbModel.DescribeDBInstancesResp{
//		//	Total: 1,
//		//	Instances: []*vdbModel.InstanceObject{
//		//		{
//		//			InstanceId:      "111",
//		//			InstanceName:    "xxx",
//		//			InstanceStatus:  "Running",
//		//			RegionId:        "cn-nanjing-bbit",
//		//			ZoneIds:         "111",
//		//			DBEngineVersion: "MySQL_8_0",
//		//			Nodes: []*vdbModel.NodeObject{
//		//				{
//		//					NodeId:   "111",
//		//					ZoneId:   "aaa",
//		//					NodeType: "Primary",
//		//					VCPU:     1,
//		//					Memory:   2,
//		//					NodeSpec: "",
//		//				},
//		//			},
//		//			ProjectName: "default",
//		//			TenantId:    utils.StringRef("21000746"),
//		//			Tags: []*vdbModel.TagObject{
//		//				{
//		//					Key:   "dbw",
//		//					Value: utils.StringRef("111"),
//		//				},
//		//			},
//		//		},
//		//	},
//		//}
//		//resp := &datasource.ListInstanceResp{
//		//	Total: 1,
//		//	InstanceList: []*model.InstanceInfo{
//		//		{
//		//			InstanceId:     utils.StringRef("111"),
//		//			InstanceName:   utils.StringRef("xxx"),
//		//			InstanceStatus: "Running",
//		//			Zone:           "111",
//		//			InstanceSpec: &model.InstanceSpec{
//		//				CpuNum:     1,
//		//				MemInGiB:   2,
//		//				NodeNumber: 1,
//		//			},
//		//			DBEngineVersion: "MySQL_8_0",
//		//			InternalAddress: "-",
//		//			AccessSource:    "云数据库 veDB MySQL 版",
//		//			HasReadOnlyNode: true,
//		//			ProjectName:     utils.StringRef("default"),
//		//			Tags: []*model.TagObject{
//		//				{
//		//					Key:   "dbw",
//		//					Value: "111",
//		//				},
//		//			},
//		//		},
//		//	},
//		//}
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//		vdb := &vedbImpl{
//			&mysqlImpl{
//				mysql: mgr,
//				cnf:   cnf,
//			},
//		}
//		_, err := vdb.ListInstance(ctx, req)
//		//convey.So(r, convey.ShouldResemble, resp)
//		So(err, ShouldBeNil)
//	})
//
//	PatchConvey("return deleting", suite.T(), func() {
//		ctx := context.Background()
//		cnf := mocks_config.NewMockConfigProvider(suite.ctrl)
//		describeDBInstanceDetailResp := &vdbModel.DescribeDBInstanceDetailResp{}
//		json.Unmarshal([]byte("{\n  \"InstanceDetail\": {\n    \"InstanceId\": \"vedbm-znjkcj0ord6f\",\n    \"InstanceName\": \"DO_NOT_DELETE_wanglujun_8c64g_v2\",\n    \"InstanceStatus\": \"Deleting\",\n    \"RegionId\": \"cn-chongqing-sdv\",\n    \"ZoneIds\": \"cn-chongqing-b\",\n    \"DBEngineVersion\": \"MySQL_8_0\",\n    \"CreateTime\": \"2023-08-07T16:13:47Z\",\n    \"StorageUsedGiB\": 34.951,\n    \"VpcId\": \"vpc-3jhizogw3t3b43pncmfsypkqo\",\n    \"SubnetId\": \"\",\n    \"TimeZone\": \"UTC +08:00\",\n    \"ProjectName\": \"\",\n    \"PrimaryDBAccount\": \"\",\n    \"LowerCaseTableNames\": \"1\",\n    \"MaintenanceWindow\": {\n      \"MaintenanceTime\": \"18:00Z-21:59Z\",\n      \"DayKind\": \"Week\",\n      \"DayOfWeek\": [\n        \"Monday\",\n        \"Tuesday\",\n        \"Wednesday\",\n        \"Thursday\",\n        \"Friday\",\n        \"Saturday\",\n        \"Sunday\"\n      ],\n      \"DayOfMonth\": []\n    },\n    \"InstanceStructures\": [\n      {\n        \"SubInstanceType\": \"Primary\",\n        \"ZoneIds\": \"cn-chongqing-b\",\n        \"Nodes\": null\n      }\n    ]\n  },\n  \"Endpoints\": [],\n  \"Nodes\": [],\n  \"ChargeDetail\": {\n    \"ChargeType\": \"PostPaid\",\n    \"OverdueTime\": \"\",\n    \"OverdueReclaimTime\": \"\",\n    \"ChargeStatus\": \"Recycled\"\n  }\n}"), describeDBInstanceDetailResp)
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		kc.EXPECT().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any()).SetArg(3, *describeDBInstanceDetailResp).Return(nil).AnyTimes()
//
//		vdb := &vedbImpl{
//			&mysqlImpl{
//				mysql: mgr,
//				cnf:   cnf,
//			},
//		}
//		r, err := vdb.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
//			InstanceId: "111",
//			Type:       shared.VeDBMySQL,
//		})
//		So(r, ShouldBeNil)
//		So(err, ShouldResemble, errors.New("InvalidInstanceId.NotFound"))
//	})
//
//	PatchConvey("return deleted", suite.T(), func() {
//		ctx := context.Background()
//		cnf := mocks_config.NewMockConfigProvider(suite.ctrl)
//		describeDBInstanceDetailResp := &vdbModel.DescribeDBInstanceDetailResp{}
//		json.Unmarshal([]byte("{\n        \"ChargeDetail\": {\n            \"ChargeStatus\": \"Recycled\",\n            \"ChargeType\": \"PostPaid\",\n            \"OverdueReclaimTime\": \"\",\n            \"OverdueTime\": \"\"\n        },\n        \"Endpoints\": [],\n        \"InstanceDetail\": {\n            \"CreateTime\": \"2023-08-10T14:26:15Z\",\n            \"DBEngineVersion\": \"MySQL_8_0\",\n            \"InstanceId\": \"vedbm-ga0sj3be1u8j\",\n            \"InstanceName\": \"DBW_DEL_TEST\",\n            \"InstanceStatus\": \"Deleted\",\n            \"InstanceStructures\": [\n                {\n                    \"Nodes\": null,\n                    \"SubInstanceType\": \"Primary\",\n                    \"ZoneIds\": \"cn-chongqing-c\"\n                }\n            ],\n            \"LowerCaseTableNames\": \"1\",\n            \"MaintenanceWindow\": {\n                \"DayKind\": \"Week\",\n                \"DayOfMonth\": [],\n                \"DayOfWeek\": [\n                    \"Monday\",\n                    \"Tuesday\",\n                    \"Wednesday\",\n                    \"Thursday\",\n                    \"Friday\",\n                    \"Saturday\",\n                    \"Sunday\"\n                ],\n                \"MaintenanceTime\": \"18:00Z-21:59Z\"\n            },\n            \"PrimaryDBAccount\": \"\",\n            \"ProjectName\": \"\",\n            \"RegionId\": \"cn-chongqing-sdv\",\n            \"StorageUsedGiB\": 98.89,\n            \"SubnetId\": \"\",\n            \"TimeZone\": \"UTC +08:00\",\n            \"VpcId\": \"vpc-3jhizogw3t3b43pncmfsypkqo\",\n            \"ZoneIds\": \"cn-chongqing-c\"\n        },\n        \"Nodes\": []\n    }"), describeDBInstanceDetailResp)
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		kc.EXPECT().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any()).SetArg(3, *describeDBInstanceDetailResp).Return(nil).AnyTimes()
//
//		vdb := &vedbImpl{
//			&mysqlImpl{
//				mysql: mgr,
//				cnf:   cnf,
//			},
//		}
//		r, err := vdb.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
//			InstanceId: "111",
//			Type:       shared.VeDBMySQL,
//		})
//		So(r, ShouldBeNil)
//		So(err, ShouldResemble, errors.New("InvalidInstanceId.NotFound"))
//	})
//}

//func (suite *VedbImplTestSuite) TestFillDataSource() {
//	PatchConvey("return correct", suite.T(), func() {
//		ctx := context.Background()
//		cnf := mocks_config.NewMockConfigProvider(suite.ctrl)
//		req := &shared.DataSource{
//			Type:       4,
//			LinkType:   shared.Volc,
//			InstanceId: "xxx",
//		}
//		rreq := &vdbModel.DescribeDBInstanceConnectionReq{
//			InstanceId: "xxx",
//		}
//		rresp := &vdbModel.DescribeDBInstanceConnectionResp{
//			StorageInnerDomain: utils.StringRef("*******"),
//			StorageInnerPort:   utils.StringRef("3065"),
//		}
//		rreq1 := &vdbModel.DescribeMasterDBEngineConnectionReq{
//			InstanceId: utils.StringRef("xxx"),
//		}
//		rresp1 := &vdbModel.DescribeMasterDBEngineConnectionResp{}
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		kc.EXPECT().Call(ctx, vdbModel.Action_DescribeInstanceProxyInnerConnection.String(), gomock.Any(), gomock.Any()).SetArg(3, *rresp).SetArg(2, *rreq).Return(nil).AnyTimes()
//		kc.EXPECT().Call(ctx, vdbModel.Action_DescribeMasterDBEngineConnection.String(), gomock.Any(), gomock.Any()).SetArg(3, *rresp1).SetArg(2, *rreq1).Return(nil).AnyTimes()
//
//		vdb := &vedbImpl{
//			mysqlImpl{
//				mysql: mgr,
//				cnf:   cnf,
//			},
//		}
//		err := vdb.FillDataSource(ctx, req)
//		convey.So(err, convey.ShouldResemble, nil)
//	})
//}

//func (suite *VedbImplTestSuite) TestGrantAccountPrivilege() {
//	PatchConvey("return correct", suite.T(), func() {
//		ctx := context.Background()
//		cnf := mocks.NewMockConfigProvider(suite.ctrl)
//		req := &datasource.GrantAccountPrivilegeReq{
//			InstanceId: "xxx",
//			AccountName: "test1",
//			DBName: "db1",
//		}
//		rreq := &vdbModel.GrantDBAccountPrivilegeReq{
//			InstanceId:  utils.StringRef(req.InstanceId),
//			AccountName: utils.StringRef(req.AccountName),
//			AccountPrivileges: []*vdbModel.AccountPrivilegeObject{
//				{
//					DBName:           utils.StringRef(req.DBName),
//					AccountPrivilege: utils.StringRef(vdbModel.ReadWriteMode_ReadOnly.String()),
//				},
//			},
//		}
//		mgr := mocks.NewMockMgrProvider(suite.ctrl)
//		kc := mocks.NewMockMgrClient(suite.ctrl)
//		mgr.EXPECT().Get().Return(kc).AnyTimes()
//		kc.EXPECT().Call(ctx, vdbModel.Action_GrantDBAccountPrivilege.String(),gomock.Any(),gomock.Any(),gomock.Any()).SetArg(2, *rreq).Return(nil)
//		vdb := &vedbImpl{
//			mysqlImpl{
//				mysql: mgr,
//				cnf:   cnf,
//			},
//		}
//		err := vdb.GrantAccountPrivilege(ctx, req)
//		convey.So(err, convey.ShouldResemble, nil)
//	})
//}

func TestVeDBInstanceDelete(t *testing.T) {
	type args struct {
		InstanceStatus string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "delete",
			args: args{
				InstanceStatus: model.VeDBMySQLInstanceStatus_Deleting.String(),
			},
			want: true,
		},
		{
			name: "delete",
			args: args{
				InstanceStatus: model.VeDBMySQLInstanceStatus_Deleted.String(),
			},
			want: true,
		},
		{
			name: "delete",
			args: args{
				InstanceStatus: model.VeDBMySQLInstanceStatus_Restarting.String(),
			},
			want: false,
		},
		{
			name: "delete",
			args: args{
				InstanceStatus: model.VeDBMySQLInstanceStatus_Running.String(),
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsVeDBInstanceDelete(tt.args.InstanceStatus); got != tt.want {
				t.Errorf("IsVeDBInstanceDelete() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVeDBConvertTableSpaceToModel(t *testing.T) {
	impl := &vedbImpl{}
	tableStats := []*shared.TableStat{{Name: "aaa", DB: "db_test"}}
	mockResp := &shared.DescribeTableSpaceResp{
		Total:      1,
		TableStats: tableStats,
	}
	resp := impl.ConvertTableSpaceToModel(context.Background(), shared.VeDBMySQL, mockResp)
	assert.Equal(t, resp.Total, int32(1))
	assert.Equal(t, resp.TableStats[0].DB, "db_test")
}

func TestVeDBFormatDescribeStorageCapacityResp(t *testing.T) {
	vedb := &vedbImpl{}
	res := vedb.FormatDescribeStorageCapacityResp(shared.VeDBMySQL, &datasource.GetDiskSizeResp{UsedStorage: 123}, 123)
	assert.Equal(t, res.UsedSize, float64(123))
}
func Test_VeDBListInstanceLightWeight(t *testing.T) {
	vedb := &vedbImpl{
		mysqlImpl: mockMysqlImpl(),
	}
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock4.UnPatch()

	_, err := vedb.ListInstanceLightWeight(ctx, &datasource.ListInstanceReq{InstanceId: "mysql-xxx",
		LinkType: shared.Volc, PageSize: 10, PageNumber: 1, InstanceName: "test1", TenantId: "2133333",
		CreateTimeEnd: "2024-12-26T15:04:05Z", CreateTimeStart: "2024-12-26T13:04:05Z"})
	assert.Equal(t, nil, err)
}
func (suite *VedbImplTestSuite) TestVedbImpl_ListInstanceLightWeight() {
	PatchConvey("return correct", suite.T(), func() {
		ctx := context.Background()
		req := &datasource.ListInstanceReq{
			Type:            4,
			LinkType:        shared.Volc,
			PageNumber:      1,
			PageSize:        20,
			InstanceName:    "xxx",
			InstanceId:      "111",
			RegionId:        "cn-nanjing-bbit",
			InstanceStatus:  "Running",
			DBEngineVersion: "MySQL_8_0",
			ZoneId:          "111",
		}
		rresp := vdbModel.DescribeDBInstancesResp{
			Total: 1,
			Instances: []*vdbModel.InstanceObject{
				{
					InstanceId:      "111",
					InstanceName:    "xxx",
					InstanceStatus:  "Running",
					RegionId:        "cn-nanjing-bbit",
					ZoneIds:         "111",
					DBEngineVersion: "MySQL_8_0",
					Nodes: []*vdbModel.NodeObject{
						{
							NodeId:   "111",
							ZoneId:   "aaa",
							NodeType: "Primary",
							VCPU:     1,
							Memory:   2,
							NodeSpec: "",
						},
					},
					ProjectName: "default",
					TenantId:    utils.StringRef("21000746"),
					Tags: []*vdbModel.TagObject{
						{
							Key:   "dbw",
							Value: utils.StringRef("111"),
						},
					},
				},
			},
		}
		mgr := mocks.NewMockMgrProvider(suite.ctrl)
		kc := mocks.NewMockMgrClient(suite.ctrl)
		mgr.EXPECT().Get().Return(kc).AnyTimes()
		kc.EXPECT().Call(gomock.Any(), vdbModel.Action_DescribeDBInstances.String(), gomock.Any(), gomock.Any()).SetArg(3, rresp).Return(nil)
		vdb := &vedbImpl{
			mysqlImpl: &mysqlImpl{
				mysql: mgr,
			},
		}
		_, err := vdb.ListInstanceLightWeight(ctx, req)
		So(err, ShouldBeNil)
	})
}
func Test_VeDBListInstanceLightWeight2(t *testing.T) {
	vedb := &vedbImpl{
		mysqlImpl: mockMysqlImpl(),
	}
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock4.UnPatch()

	_, err := vedb.ListInstanceLightWeight(ctx, &datasource.ListInstanceReq{InstanceId: "mysql-xxx",
		LinkType: shared.Volc, PageSize: 10, PageNumber: 1, InstanceName: "test1", TenantId: "1",
		CreateTimeEnd: "2024-12-26T15:04:05Z", CreateTimeStart: "2024-12-26T13:04:05Z"})
	assert.Equal(t, nil, err)
}
func TestGetDiskSize(t *testing.T) {
	mock := mockey.Mock((*influxdb.RdsMonitor).DoQueryVeDB).Return(initDataPoints3(), nil).Build()
	defer mock.UnPatch()
	vedb := &vedbImpl{
		mysqlImpl: &mysqlImpl{mysql: &mocks.MockMgrProvider{}},
	}
	_, _ = vedb.GetDiskSize(context.Background(), &datasource.GetDiskSizeReq{})
}

func (suite *VedbImplTestSuite) TestAddSQLCCLRule() {
	c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	ctx := context.Background()
	vedb := &vedbImpl{
		mysqlImpl: &mysqlImpl{
			C3ConfProvider: c3Conf,
		},
	}
	c3Conf.EXPECT().GetNamespace(ctx, consts.C3ApplicationNamespace).Return(
		&config.C3Config{Application: config.Application{DBWAccountName: "dbw_admin"}})

	mock1 := mockey.Mock((*vedbImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
		Total: 1,
		Data: []*shared.KubePod{
			{
				Region:      "cn-chongqing-sdv",
				Zone:        "",
				KubeCluster: "mix-panel-azb",
				NodePool:    "vedbm-computer-pool-azb",
				Name:        "vedbm-au6h3h3gu4kp-0",
				NodeIP:      "",
				PodIP:       "***********",
				Containers: []*shared.KubeContainer{
					{
						Cpu:  "8",
						Mem:  "32Gi",
						Name: "ndb-dbengine",
						Port: "22004",
					},
				},
				Role:      "Primary",
				Component: "dbengine",
				NodeId:    "",
			},
		},
	}, nil).Build()
	defer mock1.UnPatch()
	mock5 := mockey.Mock((*vedbImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock5.UnPatch()

	mok1 := mockey.Mock((*mocks.MockConn).Exec).Return(nil).Build()
	defer mok1.UnPatch()

	mok2 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mok2.UnPatch()
	_, err := vedb.AddSQLCCLRule(ctx, &datasource.AddSQLCCLRuleReq{
		InstanceId: "vedbm-dwazrmlgsbp4",
		Type:       shared.VeDBMySQL,
		CCLRule:    &datasource.CCLRuleInfo{},
	})
	suite.Equal(err, nil)
}

//	func (suite *VedbImplTestSuite) TestAddSQLCCLRule1() {
//		c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
//		ctx := context.Background()
//		vedb := &vedbImpl{
//			mysqlImpl: mysqlImpl{
//				C3ConfProvider: c3Conf,
//			},
//		}
//		c3Conf.EXPECT().GetNamespace(ctx, consts.C3ApplicationNamespace).Return(
//			&c3.C3Config{Application: &c3.Application{DBWAccountName: "dbw_admin"}})
//
//		mock1 := mockey.Mock((*vedbImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
//			Total: 1,
//			Data: []*shared.KubePod{
//				{
//					Region:      "cn-chongqing-sdv",
//					Zone:        "",
//					KubeCluster: "mix-panel-azb",
//					NodePool:    "vedbm-computer-pool-azb",
//					Name:        "vedbm-au6h3h3gu4kp-0",
//					NodeIP:      "",
//					PodIP:       "***********",
//					Containers: []*shared.KubeContainer{
//						{
//							Cpu:  "8",
//							Mem:  "32Gi",
//							Name: "ndb-dbengine",
//							Port: "22004",
//						},
//					},
//					Role:      "Primary",
//					Component: "dbengine",
//					NodeId:    "",
//				},
//			},
//		}, nil).Build()
//		defer mock1.UnPatch()
//		mock2 := mockey.Mock((*vedbImpl).getConn).Return(nil, fmt.Errorf("err0")).Build()
//		defer mock2.UnPatch()
//
//		mok21 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
//		defer mok21.UnPatch()
//
//		mock3 := mockey.Mock((*vedbImpl).CheckAccountPrivilege).Return(false, nil).Build()
//		defer mock3.UnPatch()
//
//		mock4 := mockey.Mock((*vedbImpl).resetAccount).Return(nil).Build()
//		defer mock4.UnPatch()
//
//		mock5 := mockey.Mock((*vedbImpl).getConn).Return(&mocks.MockConn{},nil).Build()
//		defer mock5.UnPatch()
//
//		mok1 := mockey.Mock((*mocks.MockConn).Exec).Return(nil).Build()
//		defer mok1.UnPatch()
//
//		mok2 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
//		defer mok2.UnPatch()
//		_, err := vedb.AddSQLCCLRule(ctx, &datasource.AddSQLCCLRuleReq{
//			InstanceId: "vedbm-dwazrmlgsbp4",
//			Type:       shared.VeDBMySQL,
//			CCLRule:    &datasource.CCLRuleInfo{},
//		})
//		suite.Equal(err, nil)
//	}
func (suite *VedbImplTestSuite) TestDeleteSQLCCLRule() {
	c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	ctx := context.Background()
	vedb := &vedbImpl{
		mysqlImpl: &mysqlImpl{
			C3ConfProvider: c3Conf,
		},
	}
	c3Conf.EXPECT().GetNamespace(ctx, consts.C3ApplicationNamespace).Return(
		&config.C3Config{Application: config.Application{DBWAccountName: "dbw_admin"}})

	mock1 := mockey.Mock((*vedbImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
		Total: 1,
		Data: []*shared.KubePod{
			{
				Region:      "cn-chongqing-sdv",
				Zone:        "",
				KubeCluster: "mix-panel-azb",
				NodePool:    "vedbm-computer-pool-azb",
				Name:        "vedbm-au6h3h3gu4kp-0",
				NodeIP:      "",
				PodIP:       "***********",
				Containers: []*shared.KubeContainer{
					{
						Cpu:  "8",
						Mem:  "32Gi",
						Name: "ndb-dbengine",
						Port: "22004",
					},
				},
				Role:      "Primary",
				Component: "dbengine",
				NodeId:    "",
			},
		},
	}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*vedbImpl).GetCCLRuleIdByKeywords).Return(1, nil).Build()
	defer mock2.UnPatch()
	mock5 := mockey.Mock((*vedbImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock5.UnPatch()

	mok1 := mockey.Mock((*mocks.MockConn).Exec).Return(nil).Build()
	defer mok1.UnPatch()

	mok2 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mok2.UnPatch()
	_, err := vedb.DeleteSQLCCLRule(ctx, &datasource.DeleteSQLCCLRuleReq{
		InstanceId: "vedbm-dwazrmlgsbp4",
		Type:       shared.VeDBMySQL,
		CCLRule: &datasource.CCLRuleInfo{
			RuleID: 111222,
		},
	})
	suite.Equal(err, nil)
}

func (suite *VedbImplTestSuite) TestListSQLCCLRule() {
	c3Conf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	ctx := context.Background()
	vedb := &vedbImpl{
		mysqlImpl: &mysqlImpl{
			C3ConfProvider: c3Conf,
		},
	}
	c3Conf.EXPECT().GetNamespace(ctx, consts.C3ApplicationNamespace).Return(
		&config.C3Config{Application: config.Application{DBWAccountName: "dbw_admin"}})

	mock1 := mockey.Mock((*vedbImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
		Total: 1,
		Data: []*shared.KubePod{
			{
				Region:      "cn-chongqing-sdv",
				Zone:        "",
				KubeCluster: "mix-panel-azb",
				NodePool:    "vedbm-computer-pool-azb",
				Name:        "vedbm-au6h3h3gu4kp-0",
				NodeIP:      "",
				PodIP:       "***********",
				Containers: []*shared.KubeContainer{
					{
						Cpu:  "8",
						Mem:  "32Gi",
						Name: "ndb-dbengine",
						Port: "22004",
					},
				},
				Role:      "Primary",
				Component: "dbengine",
				NodeId:    "",
			},
		},
	}, nil).Build()
	defer mock1.UnPatch()
	mock5 := mockey.Mock((*vedbImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock5.UnPatch()
	mok1 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mok1.UnPatch()
	mok2 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mok2.UnPatch()

	mok3 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mok3.UnPatch()
	_, err := vedb.ListSQLCCLRules(ctx, &datasource.ListSQLCCLRulesReq{
		InstanceId: "vedbm-dwazrmlgsbp4",
		Type:       shared.VeDBMySQL,
	})
	suite.Equal(err, nil)

}

//func (suite *VedbImplTestSuite) TestDescribeInstanceVersion() {
//	ctx := mocks.NewMockContext(suite.ctrl)
//	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	mgr := mocks.NewMockMgrProvider(suite.ctrl)
//	kc := mocks.NewMockMgrClient(suite.ctrl)
//	mgr.EXPECT().Get().Return(kc).AnyTimes()
//	req := &datasource.DescribeInstanceVersionReq{
//		InstanceId: "xxx",
//		Type:       shared.VeDBMySQL,
//	}
//	describeInstanceVersionReq := &vdbModel.DescribeDBInstanceVersionReq{
//		InstanceId: req.InstanceId,
//	}
//	describeInstanceVersionResp := &vdbModel.DescribeDBInstanceVersionResp{
//		DBMinorVersion: "3.1",
//	}
//	kc.EXPECT().Call(ctx, vdbModel.Action_DescribeDBInstanceVersion.String(), describeInstanceVersionReq, gomock.Any(), gomock.Any()).SetArg(3, *describeInstanceVersionResp).Return(nil)
//	want := &datasource.DescribeInstanceVersionResp{
//		Version: "3.1",
//	}
//	vdb := &vedbImpl{
//		&mysqlImpl{
//			mysql: mgr,
//		},
//	}
//	got, _ := vdb.DescribeInstanceVersion(ctx, req)
//	suite.Equal(want, got)
//}

//func (suite *VedbImplTestSuite) TestDescribeInstanceFeatures() {
//	ctx := mocks.NewMockContext(suite.ctrl)
//	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	mgr := mocks.NewMockMgrProvider(suite.ctrl)
//	kc := mocks.NewMockMgrClient(suite.ctrl)
//	mgr.EXPECT().Get().Return(kc).AnyTimes()
//	req := &datasource.DescribeInstanceFeaturesReq{
//		InstanceId: "xxx",
//		Type:       shared.VeDBMySQL,
//	}
//	getDBWFeatureGatesReq := &vdbModel.GetDBWFeatureGatesReq{
//		InstanceId: utils.StringRef(req.InstanceId),
//	}
//	getDBWFeatureGatesResp := &vdbModel.GetDBWFeatureGatesResp{
//		FeatureGates: []vdbModel.Feature{
//			vdbModel.Feature_SessionManagement,
//			vdbModel.Feature_SQLThrottling,
//			vdbModel.Feature_ContinuousKill,
//		},
//	}
//	kc.EXPECT().Call(ctx, vdbModel.Action_GetDBWFeatureGates.String(), getDBWFeatureGatesReq, gomock.Any(), gomock.Any()).SetArg(3, *getDBWFeatureGatesResp).Return(nil)
//	want := &datasource.DescribeInstanceFeaturesResp{
//		Features: []model.InstanceFeatureType{
//			model.InstanceFeatureType_DialogMgmt,
//			model.InstanceFeatureType_SQLThrottling,
//			model.InstanceFeatureType_ContinuousKill,
//		},
//	}
//	vdb := &vedbImpl{
//		&mysqlImpl{
//			mysql: mgr,
//		},
//	}
//	got, _ := vdb.DescribeInstanceFeatures(ctx, req)
//	suite.Equal(want, got)
//}

//func (suite *VedbImplTestSuite) TestCheckAccountPrivilege() {
//	ctx := mocks.NewMockContext(suite.ctrl)
//	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	mgr := mocks.NewMockMgrProvider(suite.ctrl)
//	kc := mocks.NewMockMgrClient(suite.ctrl)
//	mgr.EXPECT().Get().Return(kc).AnyTimes()
//	req := &datasource.CheckDBWAccountReq{
//		InstanceId:  "xxx",
//		AccountName: "dbw_admin",
//		DSType:      shared.VeDBMySQL,
//	}
//	describeDBWAccountReq := &vdbModel.DescribeDBWAccountReq{
//		InstanceId:  utils.StringRef(req.InstanceId),
//		AccountName: utils.StringRef(req.AccountName),
//	}
//	describeDBWAccountResp := &vdbModel.DescribeDBWAccountResp{
//		AccountPrivileges: utils.StringRef("GRANT PROCESS ON *.* TO `dbw_admin`@`%`;GRANT CONNECTION_ADMIN ON *.* TO `dbw_admin`@`%`;GRANT ALL PRIVILEGES ON `db1`.* TO `dbw_admin`@`%`;GRANT SELECT, LOCK TABLES, SHOW VIEW ON `mysql`.* TO `dbw_admin`@`%`;GRANT SELECT, LOCK TABLES, SHOW VIEW ON `sys`.* TO `dbw_admin`@`%`;GRANT SELECT, LOCK TABLES ON `performance_schema`.* TO `dbw_admin`@`%`;GRANT EXECUTE ON FUNCTION `sys`.`format_statement` TO `dbw_admin`@`%`;GRANT EXECUTE ON FUNCTION `sys`.`ps_thread_account` TO `dbw_admin`@`%`;GRANT EXECUTE ON FUNCTION `sys`.`quote_identifier` TO `dbw_admin`@`%"),
//	}
//	kc.EXPECT().Call(ctx, vdbModel.Action_DescribeDBWAccount.String(), describeDBWAccountReq, gomock.Any(), gomock.Any()).SetArg(3, *describeDBWAccountResp).Return(nil)
//	want := false
//	vdb := &vedbImpl{
//		&mysqlImpl{
//			mysql: mgr,
//		},
//	}
//	got, _ := vdb.CheckAccountPrivilege(ctx, req)
//	suite.Equal(want, got)
//}

//func (suite *VedbImplTestSuite) TestCreateAccount() {
//	ctx := mocks.NewMockContext(suite.ctrl)
//	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	mgr := mocks.NewMockMgrProvider(suite.ctrl)
//	kc := mocks.NewMockMgrClient(suite.ctrl)
//	mgr.EXPECT().Get().Return(kc).AnyTimes()
//	req := &datasource.CreateAccountReq{
//		InstanceId:      "xxx",
//		AccountName:     "dbw_admin",
//		DSType:          shared.VeDBMySQL,
//		AccountPassword: "********",
//	}
//	createDBWAccountReq := &vdbModel.CreateDBWAccountReq{
//		InstanceId:      utils.StringRef(req.InstanceId),
//		AccountName:     utils.StringRef(req.AccountName),
//		AccountPassword: utils.StringRef(req.AccountPassword),
//	}
//	kc.EXPECT().Call(ctx, vdbModel.Action_CreateDBWAccount.String(), createDBWAccountReq, nil).Return(nil)
//	vdb := &vedbImpl{
//		&mysqlImpl{
//			mysql: mgr,
//		},
//	}
//	err := vdb.CreateAccount(ctx, req)
//	suite.Nil(err)
//}

//func (suite *VedbImplTestSuite) TestDeleteAccount() {
//	ctx := mocks.NewMockContext(suite.ctrl)
//	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	mgr := mocks.NewMockMgrProvider(suite.ctrl)
//	kc := mocks.NewMockMgrClient(suite.ctrl)
//	mgr.EXPECT().Get().Return(kc).AnyTimes()
//	req := &datasource.DeleteAccountReq{
//		InstanceId:  "xxx",
//		AccountName: "dbw_admin",
//		DSType:      shared.VeDBMySQL,
//	}
//	deleteDBWAccountReq := &vdbModel.DeleteDBAccountReq{
//		InstanceId:  utils.StringRef(req.InstanceId),
//		AccountName: utils.StringRef(req.AccountName),
//	}
//	kc.EXPECT().Call(ctx, vdbModel.Action_DeleteDBAccount.String(), deleteDBWAccountReq, nil).Return(nil)
//	vdb := &vedbImpl{
//		&mysqlImpl{
//			mysql: mgr,
//		},
//	}
//	err := vdb.DeleteAccount(ctx, req)
//	suite.Nil(err)
//}

func Test_VedbImpl_ListAllTables(t *testing.T) {
	ctx := context.Background()
	req := &datasource.ListTablesReq{
		Limit: 123,
	}
	mock1 := mockey.Mock((*vedbImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()

	impl := &vedbImpl{}
	impl.ListAllTables(ctx, req)
}

func Test_VeDBModifySQLKillRule(t *testing.T) {
	vedbImpl := mockvedbImpl()
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock4.UnPatch()
	_, err := vedbImpl.ModifySQLKillRule(ctx, &datasource.ModifySQLKillRuleReq{
		InstanceId: "111",
		Action:     "Add",
		KillRule: []*datasource.KillRuleInfo{
			{
				SqlType:     "select,delete",
				NodeType:    "Primary,ReadOnly",
				MaxExecTime: 1,
			},
		},
	})
	assert.Nil(t, err)

	_, err1 := vedbImpl.ModifySQLKillRule(ctx, &datasource.ModifySQLKillRuleReq{
		InstanceId: "111",
		Action:     "Stop",
		KillRule: []*datasource.KillRuleInfo{
			{
				SqlType:     "select,delete",
				NodeType:    "Primary,ReadOnly",
				MaxExecTime: 1,
			},
		},
	})
	assert.Nil(t, err1)
	_, err2 := vedbImpl.ModifySQLKillRule(ctx, &datasource.ModifySQLKillRuleReq{
		InstanceId: "111",
		Action:     "Delete",
		KillRule: []*datasource.KillRuleInfo{
			{
				SqlType:     "select,delete",
				NodeType:    "Primary,ReadOnly",
				MaxExecTime: 1,
			},
		},
	})
	assert.Nil(t, err2)
}

func Test_VeDBGetManagedAccountAndPwd(t *testing.T) {
	vedb := mockvedbImpl()
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockUserService).GetDBAccount).Return(nil).Build()
	defer baseMock1.UnPatch()
	_, err := vedb.GetManagedAccountAndPwd(ctx, &shared.DataSource{
		InstanceId: "ndb-xxx",
	})
	assert.Equal(t, nil, err)
}
func Test_VeDBKillProcess1(t *testing.T) {
	vedb := mockvedbImpl()
	vedb.ConnPool = &mocks.MockPool{}
	conn := &connpoolMock.MockConn{}
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConfigProvider).Get).Return(&config.Config{EnableNewConnectionPool: true}).Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockPool).GetInstanceAdminConn).Return(conn, nil).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*connpoolMock.MockConn).Exec).Return(fmt.Errorf("kill failed")).Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
	defer baseMock4.UnPatch()
	mock5 := mockey.Mock((*vedbImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
		Total: 1,
		Data: []*shared.KubePod{
			{
				PodIP:  "*******",
				Role:   "Primary",
				NodeId: "ndb-xxx-0",
				Containers: []*shared.KubeContainer{
					{
						Name: "ndb-dbengine",
						Port: "3306",
					},
					{
						Name: "proxy",
						Port: "3679",
					},
				},
			},
		},
	}, nil).Build()
	defer mock5.UnPatch()
	_, err := vedb.KillProcess(ctx, &datasource.KillProcessReq{
		Source: &shared.DataSource{
			InstanceId: "mysql-xxx",
			Type:       shared.VeDBMySQL,
		},
		ProcessIDs: []string{"111", "222"},
		NodeId:     "ndb-xxx-1",
	})
	assert.Equal(t, nil, err)
}

//func Test_VeDBKillProcess2(t *testing.T) {
//	vedb := mockvedbImpl()
//	vedb.ConnPool = &mocks.MockPool{}
//	conn := &connpoolMock.MockConn{}
//	ctx := &mocks.MockContext{}
//	baseMock0 := mockey.Mock(log.Log).Return().Build()
//	defer baseMock0.UnPatch()
//	baseMock1 := mockey.Mock((*mocks.MockConfigProvider).Get).Return(&config.Config{EnableNewConnectionPool: false}).Build()
//	defer baseMock1.UnPatch()
//	baseMock2 := mockey.Mock((*mocks.MockPool).GetInstanceAdminConn).Return(conn, nil).Build()
//	defer baseMock2.UnPatch()
//	baseMock3 := mockey.Mock((*connpoolMock.MockConn).Exec).Return(fmt.Errorf("kill failed")).Build()
//	defer baseMock3.UnPatch()
//	_, err := vedb.KillProcess(ctx, &datasource.KillProcessReq{
//		Source: &shared.DataSource{
//			InstanceId: "mysql-xxx",
//			Type:       shared.VeDBMySQL,
//		},
//		ProcessIDs: []string{"111", "222"},
//		NodeId:     "ndb-xxx-1",
//	})
//	assert.NotEqual(t, nil, err)
//}

func Test_VeDBEnsureAccount(t *testing.T) {
	vedb := mockvedbImpl()
	vedb.ConnPool = &mocks.MockPool{}
	conn := &connpoolMock.MockConn{}
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mock1 := mockey.Mock((*vedbImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
		Total: 1,
		Data: []*shared.KubePod{
			{
				PodIP:  "*******",
				Role:   "Primary",
				NodeId: "ndb-xxx-0",
				Containers: []*shared.KubeContainer{
					{
						Name: "ndb-dbengine",
						Port: "3306",
					},
					{
						Name: "proxy",
						Port: "3679",
					},
				},
			},
		},
	}, nil).Build()
	defer mock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockPool).GetNodeAdminConn).Return(conn, nil).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*connpoolMock.MockConn).Exec).Return(fmt.Errorf("err")).Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
	defer baseMock4.UnPatch()
	err := vedb.EnsureAccount(ctx, &datasource.EnsureAccountReq{
		Source: &shared.DataSource{
			InstanceId: "ndb-xxx",
			Type:       shared.VeDBMySQL,
		},
	})
	assert.Nil(t, err)
}

func TestVedbFilterTrxAndLocks(t *testing.T) {
	mockey.PatchConvey("filter by trxId", t, func() {
		v := &vedbImpl{}
		trxAndLocks := []*shared.TrxAndLock{{TrxId: "abcde"}, {TrxId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{TrxId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{TrxId: "abcde"}})
	})
	mockey.PatchConvey("filter by processId", t, func() {
		v := &vedbImpl{}
		trxAndLocks := []*shared.TrxAndLock{{ProcessId: "abcde"}, {ProcessId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{ProcessId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{ProcessId: "abcde"}})
	})
	mockey.PatchConvey("filter by blockTrxId", t, func() {
		v := &vedbImpl{}
		trxAndLocks := []*shared.TrxAndLock{{BlockTrxId: "abcde"}, {BlockTrxId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{BlockTrxId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{BlockTrxId: "abcde"}})
	})
	mockey.PatchConvey("filter by sqlBlocked", t, func() {
		v := &vedbImpl{}
		trxAndLocks := []*shared.TrxAndLock{{SqlBlocked: "abcde"}, {SqlBlocked: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{SqlBlocked: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{SqlBlocked: "abcde"}})
	})
	mockey.PatchConvey("filter by trxExecTime", t, func() {
		v := &vedbImpl{}
		trxAndLocks := []*shared.TrxAndLock{{TrxExecTime: 100}, {TrxExecTime: 1000}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{TrxExecTime: utils.Int32Ref(100)}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{TrxExecTime: 1000}})
	})
	mockey.PatchConvey("filter by nodeIds", t, func() {
		v := &vedbImpl{}
		trxAndLocks := []*shared.TrxAndLock{{NodeId: "abcde"}, {NodeId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, nil, []string{"abcde"})
		So(got, ShouldResemble, []*shared.TrxAndLock{{NodeId: "abcde"}})
	})
}
func TestVeDBFilterWaitLockDetails(t *testing.T) {
	impl := &vedbImpl{}
	mockey.PatchConvey("filterWaitLockDetails", t, func() {
		mockey.PatchConvey("filter by nodeIds", func() {
			resp := impl.filterWaitLockDetails(context.TODO(), []*datasource.LockCurrentWaitsDetail{
				{
					NodeId:             "1",
					RTrxMysqlThreadId:  "111",
					RTrxId:             "111",
					RTrxOperationState: "xxx",
					RTrxRowsLocked:     "1",
					RTrxRowsModified:   "1",
					RTrxStarted:        "xxx",
					RTrxState:          "xxx",
					RTrxWaitStarted:    "xxx",
					RWaitingQuery:      "xxx",
					RBlockedWaitSecs:   "10",
					BTrxId:             "xxx",
					BBlockingQuery:     "xxx",
					BBlockingWaitSecs:  "10",
					BTrxMysqlThreadId:  "222",
					BTrxOperationState: "xxxx",
					BTrxRowsLocked:     "1",
					BTrxRowsModified:   "1",
					BTrxStarted:        "xxx",
					BTrxState:          "xxx",
					BTrxWaitStarted:    "xxx",
				},
				{NodeId: "2",
					RTrxMysqlThreadId:  "111",
					RTrxId:             "111",
					RTrxOperationState: "xxx",
					RTrxRowsLocked:     "1",
					RTrxRowsModified:   "1",
					RTrxStarted:        "xxx",
					RTrxState:          "xxx",
					RTrxWaitStarted:    "xxx",
					RWaitingQuery:      "xxx",
					RBlockedWaitSecs:   "10",
					BTrxId:             "xxx",
					BBlockingQuery:     "xxx",
					BBlockingWaitSecs:  "10",
					BTrxMysqlThreadId:  "222",
					BTrxOperationState: "xxxx",
					BTrxRowsLocked:     "1",
					BTrxRowsModified:   "1",
					BTrxStarted:        "xxx",
					BTrxState:          "xxx",
					BTrxWaitStarted:    "xxx",
				},
				{NodeId: "3"},
			}, &model.WaitLockQueryFilter{}, []string{"1", "2"})
			So(resp.Total, ShouldEqual, 2)
			So(resp.Result[0].NodeId, ShouldEqual, "1")
			So(resp.Result[1].NodeId, ShouldEqual, "2")
		})
	})
}
func TestVeDBGetAllWaitLocks(t *testing.T) {
	ctx := context.TODO()
	conn := &connpoolMock.MockConn{}
	req := &datasource.DescribeLockCurrentWaitsReq{
		Source: &shared.DataSource{
			Type:       shared.VeDBMySQL,
			InstanceId: "vedbm-xxx",
		},
	}
	impl := mockvedbImpl()
	impl.ConnPool = &mocks.MockPool{}
	mockey.PatchConvey("test getAllWaitLocks", t, func() {
		mockey.Mock((*vedbImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
			Data: []*shared.KubePod{
				{
					Component: "dbengine",
					PodIP:     "*******",
					NodeId:    "vedbm-xxx-master0",
					Role:      "Primary",
					Containers: []*shared.KubeContainer{
						{
							Name: "dbengine",
							Port: "3679",
						},
					},
				},
			},
		}, nil).Build()
		mock1 := mockey.Mock((*vedbImpl).getConnV2).Return(conn, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
		defer mock4.UnPatch()
		_, err := impl.getAllWaitLocks(ctx, req)
		So(err, ShouldBeNil)
	})
}
func TestVeDBGetAllTrxAndLocks(t *testing.T) {
	ctx := context.TODO()
	conn := &connpoolMock.MockConn{}
	req := &datasource.DescribeTrxAndLocksReq{
		Source: &shared.DataSource{
			Type:       shared.VeDBMySQL,
			InstanceId: "vedbm-xxx",
		},
	}
	impl := mockvedbImpl()
	impl.ConnPool = &mocks.MockPool{}
	mockey.PatchConvey("test getAllTrxLocks", t, func() {
		mockey.Mock((*vedbImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
			Data: []*shared.KubePod{
				{
					Component: "dbengine",
					PodIP:     "*******",
					NodeId:    "vedbm-xxx-master0",
					Role:      "Primary",
					Containers: []*shared.KubeContainer{
						{
							Name: "dbengine",
							Port: "3679",
						},
					},
				},
			},
		}, nil).Build()
		mock1 := mockey.Mock((*vedbImpl).getConnV2).Return(conn, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
		defer mock4.UnPatch()
		_, err := impl.getAllTrxLocks(ctx, req)
		So(err, ShouldBeNil)
	})
}

//func (suite *VedbImplTestSuite) TestNdbCheckInstanceState() {
//	ctx := context.Background()
//	mockey.PatchConvey("normal", suite.T(), func() {
//		cfg := mocks.NewMockConfigProvider(suite.ctrl)
//		cfg.EXPECT().Get(gomock.Any()).Return(&config.Config{
//			DBInstanceStateWithoutConnectionBlackList: "{\"VeDBMySQL\":\"Creating,Deleting\"}",
//			DBInstanceStateWithConnectionBlackList:    "{\"VeDBMySQL\":\"Creating,Deleting\"}",
//		}).AnyTimes()
//		mgrProvider := mocks.NewMockMgrProvider(suite.ctrl)
//		mgrClient := mocks.NewMockMgrClient(suite.ctrl)
//		mgrClient.EXPECT().Call(gomock.Any(), vdbModel.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, vdbModel.DescribeDBInstanceDetailResp{
//			InstanceDetail: &vdbModel.InstanceDetailObject{
//				InstanceStatus: "Creating",
//			},
//		}).Return(nil).AnyTimes()
//		mgrProvider.EXPECT().Get().Return(mgrClient).AnyTimes()
//
//		h := NewVeDBMySQLDataSource(NewVeDBMySQLDataSourceIn{
//			Conf:    cfg,
//			VeDBMgr: mgrProvider,
//		})
//		got := h.Source.CheckInstanceState(ctx, "vedbm-xxx", shared.VeDBMySQL, true)
//		suite.NotEmpty(got)
//	})
//}

func Test_describeLock(t *testing.T) {
	ctx := context.Background()
	//TrxId := "12345"
	conn := &connpoolMock.MockConn{}
	impl := mockvedbImpl()

	impl.ConnPool = &mocks.MockPool{}
	PatchConvey("Test describeLock", t, func() {
		PatchConvey("Test get mysql version failed", func() {
			mock1 := mockey.Mock((*vedbImpl).getConnV2).Return(conn, nil).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(errors.New("get mysql version failed")).Build()
			defer mock3.UnPatch()
			_, err := impl.describeLock(ctx, conn)
			So(err, ShouldNotBeNil)
		})
		PatchConvey("Test get mysql version success", func() {
			mock1 := mockey.Mock((*vedbImpl).getConnV2).Return(conn, nil).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
			defer mock3.UnPatch()
			_, err := impl.describeLock(ctx, conn)
			So(err, ShouldBeNil)
		})
	})
}
func Test_VeDBDescribeSqlType(t *testing.T) {
	ctx := context.Background()
	impl := mockvedbImpl()
	mockey.PatchConvey("Test DescribeSqlType", t, func() {
		mockey.PatchConvey("Test GetRdsSqlType failed", func() {
			req := &datasource.DescribeSqlTypeReq{SqlText: "SELECT * FROM "}
			resp, err := impl.DescribeSqlType(ctx, req)
			So(resp, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})

		mockey.PatchConvey("Test GetRdsSqlType success", func() {
			expectedSqlType := "SELECT"
			req := &datasource.DescribeSqlTypeReq{SqlText: "SELECT * FROM users;"}
			resp, err := impl.DescribeSqlType(ctx, req)
			So(resp.SqlType, ShouldEqual, expectedSqlType)
			So(err, ShouldBeNil)
		})
	})
}

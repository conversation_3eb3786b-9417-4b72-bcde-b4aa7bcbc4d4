package mysql

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"net"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/lib"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"golang.org/x/sync/errgroup"
	"k8s.io/utils/strings/slices"

	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/service/bytebrain"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/ds_utils"
	"code.byted.org/infcs/dbw-mgr/biz/service/diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/monitor/influxdb"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"

	//"code.byted.org/infcs/dbw-mgr/biz/tenant"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/db"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"github.com/qjpcpu/fp"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	innerUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
)

const DBW_SOLE_BYTE_RDS_GROUP_NAME = `DBW_Sole_Group_Name_For_RDS`
const RDS_MySQL_Version_V2 = `2022-01-01`
const RDS_MySQL_Version_V1 = `2018-01-01`
const (
	DBW_CONSOLE_DEFAULT_HINT = " /*+ DBW SQL CONSOLE DEFAULT*/ "
)

var RdsProxyPortsList = []string{"3679", "3680", "3681", "3682", "3683", "3684", "3685", "3686", "3687", "3688", "3689", "3690", "3691", "3692", "3693", "3694", "3695"}
var (
	ErrInvalidPrivilegeType = errors.New("invalid privilege type")
	deadLockRegs            = []string{
		`MySQL thread id (\d*),`,
		`table (.*) trx id`,
		`LOCK TO BE GRANTED:\n(.*) space`,
		`LOCK TO BE GRANTED:\n.*index (.*) of`,
		`LOCK TO BE GRANTED:\n.* lock.mode (\w*)`,
		`HOLDS THE LOCK\(S\):\n(.*) space`,
		`HOLDS THE LOCK\(S\):\n.*index (.*) of`,
		`HOLDS THE LOCK\(S\):\n.* lock.mode (\w*)`,
		`MySQL.*\n([\s\S]*?)\*\*\* \([1-9]\)`,
	}
)

const (
	TrxQuery = "/*+ DBW DAS DEFAULT*/ SELECT * FROM (select trx_id AS TrxId, trx_state AS TrxStatus, trx_mysql_thread_id AS ProcessId," +
		" trx_started AS TrxStartTime,trx_wait_started AS TrxWaitStartTime,UNIX_TIMESTAMP(NOW())-UNIX_TIMESTAMP(trx_started) as TrxExecTime," +
		"trx_query AS SqlBlocked,trx_isolation_level AS TrxIsoLevel,trx_tables_locked AS TrxTablesLocked,trx_rows_locked AS TrxRowsLocked," +
		"trx_lock_structs AS TrxLockStructs,trx_rows_modified AS TrxRowsModified,trx_requested_lock_id " +
		"from information_schema.INNODB_TRX limit 10000) as subQuery "
	WaitSql57            = "/*+ DBW DAS DEFAULT*/ select blocking_trx_id,requesting_trx_id,requested_lock_id from information_schema.INNODB_LOCK_WAITS limit 200"
	WaitSql80            = "/*+ DBW DAS DEFAULT*/ select BLOCKING_ENGINE_TRANSACTION_ID,REQUESTING_ENGINE_TRANSACTION_ID,REQUESTING_ENGINE_LOCK_ID from performance_schema.data_lock_waits limit 200"
	LockQuery57          = "/*+ DBW DAS DEFAULT*/ select * from information_schema.INNODB_LOCKS limit 10000"
	LockQuery80          = "/*+ DBW DAS DEFAULT*/ select * from performance_schema.data_locks limit 10000"
	TrxQueryForWaitLock  = "/*+ DBW DAS DEFAULT*/ select trx_mysql_thread_id,trx_id,trx_state,SUBSTRING(trx_query, 1, 5120) as QuerySql,trx_started,trx_wait_started,TIMESTAMPDIFF(second, trx_wait_started, NOW()) as WaitSeconds,trx_rows_modified,trx_rows_locked,trx_operation_state from INFORMATION_SCHEMA.INNODB_TRX limit 10000"
	MySQL57WaitLockQuery = "/*+ DBW DAS DEFAULT*/ SELECT * FROM (select r.trx_mysql_thread_id as r_trx_mysql_thread_id, r.trx_id as r_trx_id, " +
		"r.trx_state as r_trx_state, SUBSTRING(r.trx_query, 1, 5120) as r_waiting_query, r.trx_started as r_trx_started, " +
		"r.trx_wait_started as r_trx_wait_started, TIMESTAMPDIFF(second, r.trx_wait_started, NOW()) as r_blocked_wait_secs , " +
		"r.trx_rows_modified as r_trx_rows_modified, r.TRX_ROWS_LOCKED as r_trx_rows_locked, r.trx_operation_state as r_trx_operation_state," +
		" b.trx_mysql_thread_id as b_trx_mysql_thread_id, b.trx_id as b_trx_id, b.trx_state as b_trx_state, " +
		"SUBSTRING(b.trx_query, 1, 5120) as b_blocking_query, b.trx_started as b_trx_started, b.trx_wait_started as b_trx_wait_started, " +
		"TIMESTAMPDIFF(second, b.trx_wait_started, NOW()) as b_blocking_wait_secs, b.trx_rows_modified as b_trx_rows_modified, " +
		"b.TRX_ROWS_LOCKED as b_trx_rows_locked, b.trx_operation_state as b_trx_operation_state " +
		"from INFORMATION_SCHEMA.INNODB_LOCK_WAITS w inner join INFORMATION_SCHEMA.INNODB_TRX b on b.trx_id = w.blocking_trx_id " +
		"inner join INFORMATION_SCHEMA.INNODB_TRX r on r.trx_id = w.requesting_trx_id) " +
		"as subQuery "
)

type NewMySQLDataSourceIn struct {
	dig.In
	Conf            config.ConfigProvider
	MySQLMgr        mgr.Provider `name:"mysql"`
	C3ConfProvider  c3.ConfigProvider
	L               location.Location
	MonitorClient   *influxdb.RdsMonitor
	ShuttleSvc      shuttle.PGWShuttleService
	ByteBrainClient bytebrain.ByteBrainService
	IdSvc           idgen.Service
	ActorClient     cli.ActorClient
	UserMgmtSvc     usermgmt.UserService
}

type NewMySQLDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewMySQLDataSource(p NewMySQLDataSourceIn) NewMySQLDataSourceOut {
	mpl := &mysqlImpl{
		DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
		cnf:               p.Conf,
		mysql:             p.MySQLMgr,
		C3ConfProvider:    p.C3ConfProvider,
		L:                 p.L,
		MonitorClient:     p.MonitorClient,
		ShuttleSvc:        p.ShuttleSvc,
		ByteBrainClient:   p.ByteBrainClient,
		IdSvc:             p.IdSvc,
		ActorClient:       p.ActorClient,
		userMgmtSvc:       p.UserMgmtSvc,
	}
	mpl.ConnPool = datasource.NewPool(datasource.NewPoolIn{
		C3:    p.C3ConfProvider,
		Conf:  p.Conf,
		DsSvc: mpl,
	})
	return NewMySQLDataSourceOut{
		Source: retryIfWhiteListNotReady(mpl),
	}
}

type mysqlImpl struct {
	datasource.DataSourceService
	cnf                 config.ConfigProvider
	mysql               mgr.Provider
	C3ConfProvider      c3.ConfigProvider
	L                   location.Location
	MonitorClient       *influxdb.RdsMonitor
	ByteBrainClient     bytebrain.ByteBrainService
	HealthSummaryMetric map[string]func(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error)
	ShuttleSvc          shuttle.PGWShuttleService
	IdSvc               idgen.Service
	ActorClient         cli.ActorClient
	ConnPool            datasource.Pool
	userMgmtSvc         usermgmt.UserService
	mutex               sync.Mutex
}

func (self *mysqlImpl) DescribeFullSQLLogConfig(ctx context.Context, req *datasource.DescribeFullSQLLogConfigReq) (*datasource.DescribeFullSQLLogConfigResp, error) {
	rreq := &rdsModel_v2.InnerDescribeFullSQLLogConfigReq{InstanceId: req.InstanceID}
	rresp := &rdsModel_v2.InnerDescribeFullSQLLogConfigResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_InnerDescribeFullSQLLogConfig.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "InnerDescribeFullSQLLogConfig fail, error:%s", err)
		return nil, err
	}
	return &datasource.DescribeFullSQLLogConfigResp{
		SQLCollectorStatus: datasource.SQLCollectorStatus(rresp.SQLCollectorStatus),
		TLSDomain:          rresp.GetTLSDomain(),
		TLSProjectId:       rresp.GetTLSProjectId(),
		TLSTopic:           rresp.GetTLSTopic(),
	}, nil
}

func (self *mysqlImpl) ModifyFullSQLLogConfig(ctx context.Context, req *datasource.ModifyFullSQLLogConfigReq) (*datasource.ModifyFullSQLLogConfigResp, error) {
	rreq := &rdsModel_v2.InnerModifyFullSQLLogConfigReq{
		InstanceId:         req.InstanceID,
		SQLCollectorStatus: rdsModel_v2.SQLCollectorStatusEnum(req.SQLCollectorStatus),
		DryRun:             utils.BoolRef(req.DryRun),
	}
	//rresp := &rdsModel_v2.InnerModifyFullSQLLogConfigResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_InnerModifyFullSQLLogConfig.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "InnerModifyFullSQLLogConfig fail, error:%s", err)
		return nil, err
	}
	return &datasource.ModifyFullSQLLogConfigResp{}, nil
}

// DescribeTLSConnectionInfo DescribeTLSConnectionInfo 已弃用
func (self *mysqlImpl) DescribeTLSConnectionInfo(ctx context.Context, req *datasource.DescribeTLSConnectionInfoReq) (*datasource.DescribeTLSConnectionInfoResp, error) {
	var topicId string
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, req.RegionId)
	c3cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	if self.cnf.Get(ctx).EnableNewVersionSlowLogTopic {
		tlsSlowLogTopic := c3cfg.TLSRdsSlowLogTopicV2
		topicSet := &datasource.TLSSlowLogTopicV2{}
		if err := json.Unmarshal([]byte(tlsSlowLogTopic), topicSet); err != nil {
			log.Warn(ctx, " tlsSlowLogTopic unmarshal failed %v", err)
		}
		instanceId := req.InstanceId
		lastChar := instanceId[len(instanceId)-1:] + "$"
		for _, topic := range topicSet.Topics {
			if topic.InstanceType == req.Type.String() {
				for _, item := range topic.TopicList {
					if item.MatchedRegex == lastChar {
						tlsSlowLogTopic = item.TopicID
						break
					}
				}
			}
		}
	} else {
		tlsSlowLogTopic := c3cfg.TLSSlowLogTopic
		topicSet := &datasource.TLSSlowLogTopic{}
		if err := json.Unmarshal([]byte(tlsSlowLogTopic), topicSet); err != nil {
			log.Warn(ctx, " tlsSlowLogTopic unmarshal failed %v", err)
		}
		for _, topic := range topicSet.Topics {
			if topic.InstanceType == req.Type.String() {
				topicId = topic.TopicID
			}
		}

	}

	resp := &datasource.DescribeTLSConnectionInfoResp{
		Endpoint: tlsEndpoint,
		TopicID:  topicId,
	}
	log.Info(ctx, "DescribeTLSConnectionInfo: get tlsConfig from rds: %#v", resp)
	return resp, nil
}

func (self *mysqlImpl) Type() shared.DataSourceType {
	return shared.MySQL
}

func (self *mysqlImpl) describeInstance(ctx context.Context, instanceId string) (*rdsModel_v2.DescribeDBInstanceDetailResp, error) {
	rreq := &rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}

	rresp := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	return rresp, self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2))
}

func (self *mysqlImpl) checkNewWhiteList(ctx context.Context, instanceId string) (*string, error) {
	resp, err := self.describeInstance(ctx, instanceId)
	if err != nil {
		return nil, err
	}
	return resp.BasicInfo.AllowListVersion, err
}

func (self *mysqlImpl) isNewWhiteList(ctx context.Context, version *string) bool {
	versionIsV2orInitial := func(v string) bool {
		if v == rdsModel_v2.AllowListVersion_v2.String() ||
			v == rdsModel_v2.AllowListVersion_initial.String() {
			return true
		}
		return false
	}

	if version != nil && versionIsV2orInitial(*version) {
		return true
	}
	return false
}

// AddWhiteList
// 如果是v2白名单，查询白名单信息
// 若不存在白名单组，那么就创建一个白名单组，更新为MgrPodCIDR内的网段信息
// 若存在白名单组，那么就更新白名单组，更新为MgrPodCIDR内的网段信息
// 如果是v1白名单，直接创建白名单（忽略已存在情况下报错的场景）
// /*
func (self *mysqlImpl) AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error) {
	if ds.LinkType != shared.Volc {
		return "", nil
	}
	//查询rds，获取白名单版本号
	version, err := self.checkNewWhiteList(ctx, ds.InstanceId)
	if err != nil {
		log.Warn(ctx, "mysqlImpl: check new white list err: %v", err.Error())
		return "", err
	}
	//v2白名单逻辑
	if self.isNewWhiteList(ctx, version) {
		allowList := "**********/10"
		if self.cnf.Get(ctx).EnableRDSInternalConnectSwitch || ds.Address != "" {
			allowList = self.cnf.Get(ctx).MgrPodCIDR
		}
		//如果命中了租户ID的白名单才能走shuttle，否则走内网直连方式
		if !innerUtils.IsTenantEnabledFromCtx(ctx, self.cnf.Get(ctx).RDSShuttleTenantIdList) {
			allowList = self.cnf.Get(ctx).MgrPodCIDR
		}
		description := "DBW internal sole white list for rds"
		regionId, err := self.getRegionId(ctx, ds.InstanceId)
		if err != nil {
			log.Warn(ctx, "mysqlImpl.getRegionId failed: %s", err.Error())
			return "", err
		}
		log.Info(ctx, "get instance [%s] with region [%s]", ds.InstanceId, regionId)
		instanceAllowlists, err := self.getAllowLists(ctx, utils.StringRef(regionId), utils.StringRef(ds.InstanceId))
		if err != nil {
			return "", err
		}
		instanceAllowlistIDs := self.findDBWAllowlist(instanceAllowlists)
		if len(instanceAllowlistIDs) > 0 {
			for _, allowInfo := range instanceAllowlistIDs {
				self.ensureAllowList(ctx, ds.InstanceId, allowInfo.AllowListId, allowList)
			}
			return "", nil
		}

		allowlists, err := self.getAllowLists(ctx, utils.StringRef(regionId), nil)
		if err != nil {
			return "", err
		}
		var wlID string
		dbwAllowList := self.findDBWAllowlist(allowlists)
		// has allowlist, associate it or update ips of allowlist if necessary
		for _, allowlist := range dbwAllowList {
			if allowlist.AssociatedInstanceNum < 500 {
				wlID = allowlist.AllowListId
				break
			}
		}

		if wlID == "" {
			wlID, err = self.CreateAllowListNew(ctx, &rdsModel_v2.CreateAllowListReq{
				AllowListName: self.genWhiteListName(dbwAllowList),
				AllowListType: "IPv4",
				AllowListDesc: utils.StringRef(description),
				AllowList:     utils.StringRef(allowList),
			})
			if err != nil {
				log.Warn(ctx, "mysqlImpl: create allow list failed %s", err.Error())
				return "", err
			}
		}
		err = self.AssociateAllowList(ctx, &rdsModel_v2.AssociateAllowListReq{
			AllowListIds: []string{wlID},
			InstanceIds:  []string{ds.InstanceId},
		})
		if err != nil {
			log.Warn(ctx, "associate allow list failed %s", err.Error())
			return "", err
		}
		return wlID, nil
	}

	//v1白名单处理逻辑
	groupName := self.makeNewGroupID(consts.RDSV1WhiteListNameSuffix)
	req := &rdsModel.CreateDBInstanceIPListReq{
		InstanceId: ds.InstanceId,
		GroupName:  groupName,
		IPList:     strings.Split(self.cnf.Get(ctx).MgrPodCIDR, ","),
	}
	log.Info(ctx, "mysqlImpl.CreateDBInstanceIPList req=%s", utils.Show(req))
	err1 := self.mysql.Get().Call(ctx, rdsModel.Action_InnerCreateDBInstanceIPList.String(), req, nil)
	// 忽略白名单已存在错误
	if err1 != nil && strings.Contains(err1.Error(), `InvalidSecurityIPList_Duplicate`) {
		log.Info(ctx, "RDS V1 WhiteList Name [%s] Already Exists, Ingore Err [%v]", groupName, err1.Error())
		err1 = nil
	}
	return "", err1
}

func (self *mysqlImpl) CreateAllowList(ctx context.Context, req *rdsModel_v2.CreateAllowListReq) (id string, err error) {
	log.Info(ctx, "mysqlImpl.CreateAllowList req=%s", utils.Show(req))

	originResp := &rdsModel_v2.CreateAllowListResp{}
	err = self.mysql.Get().Call(
		ctx,
		rdsModel_v2.Action_CreateAllowList.String(),
		&req,
		originResp,
		client.WithVersion(RDS_MySQL_Version_V2),
	)
	if err != nil {
		return "", err
	}

	id = originResp.AllowListId
	log.Info(ctx, "mysqlImpl.CreateAllowList: get wlID [%s]", id)
	return id, nil
}

func (self *mysqlImpl) CreateAllowListNew(ctx context.Context, req *rdsModel_v2.CreateAllowListReq) (id string, err error) {
	log.Info(ctx, "mysqlImpl.CreateAllowList req=%s", utils.Show(req))

	originResp := &rdsModel_v2.CreateAllowListResp{}
	err = self.mysql.Get().Call(
		ctx,
		rdsModel_v2.Action_CreateAllowList.String(),
		&req,
		originResp,
		client.WithVersion(RDS_MySQL_Version_V2),
	)
	if err != nil {
		return "", err
	}

	id = originResp.AllowListId
	log.Info(ctx, "mysqlImpl.CreateAllowList: get wlID [%s]", id)
	return id, nil
}

func (self *mysqlImpl) ensureAllowList(ctx context.Context, instanceId string, wlID string, allowList string) (err error) {
	req := &rdsModel_v2.DescribeAllowListDetailReq{
		AllowListId: wlID,
	}
	resp := &rdsModel_v2.DescribeAllowListDetailResp{}
	err = self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeAllowListDetail.String(), req, resp, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "describe allow list [%s] failed: %s", wlID, err.Error())
		return
	}

	log.Info(ctx, "mysqlImpl.ensureAllowList: get white list AllowList is [%s], MgrPodCIDR list is [%s]", resp.AllowList, allowList)
	if !self.containsIPList(resp.AllowList, allowList) {
		rreq := &rdsModel_v2.ModifyAllowListReq{
			AllowListName:    DBW_SOLE_BYTE_RDS_GROUP_NAME,
			AllowListId:      wlID,
			ModifyMode:       rdsModel_v2.ModifyMode_Cover,
			AllowList:        utils.StringRef(allowList),
			ApplyInstanceNum: utils.Int32Ref(int32(len(resp.AssociatedInstances))),
		}
		// 如果发现白名单配置值（100段）和v2白名单内的信息不一致，那就更新，但是要忽略更新报错的场景
		log.Info(ctx, "ensureAllowList: modify instance [%s] allowList from [%s] to [%s]", resp.AllowList, allowList)
		err = self.mysql.Get().Call(ctx, rdsModel_v2.Action_ModifyAllowList.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2))
		if err != nil {
			log.Warn(ctx, "modify instance [%s] allowList [%s] with ip [%s] failed: %s", instanceId, wlID, self.cnf.Get(ctx).MgrPodCIDR, err.Error())
			err = nil
			return
		}
	}
	return
}

func (self *mysqlImpl) containsIPList(curCIDR, targetCIDR string) bool {
	curCIDRList := strings.Split(curCIDR, ",")
	if len(curCIDRList) < 1 {
		return false
	}
	// maybe not set the param, to alarm
	if targetCIDR == "" {
		log.Error(context.TODO(), "the MgrPodCIDR not set")
	}
	targetCIDRList := strings.Split(targetCIDR, ",")

	return listsEqual(curCIDRList, targetCIDRList)
}

func listsEqual(list1, list2 []string) bool {
	// 如果两个列表长度不同，直接返回 false
	if len(list1) != len(list2) {
		return false
	}

	// 将两个列表中的元素排序
	sort.Strings(list1)
	sort.Strings(list2)

	// 比较排序后的列表是否相等
	for i := 0; i < len(list1); i++ {
		if list1[i] != list2[i] {
			return false
		}
	}

	// 如果所有元素都相等，返回 true
	return true
}

func (self *mysqlImpl) getRegionId(ctx context.Context, instanceId string) (string, error) {
	resp, err := self.describeInstance(ctx, instanceId)
	if err != nil {
		log.Warn(ctx, "rdsImpl.getRegionId: %v", err)
		return "", err
	}
	return resp.BasicInfo.RegionId, nil
}

func (self *mysqlImpl) AssociateAllowList(ctx context.Context, req *rdsModel_v2.AssociateAllowListReq) (err error) {
	log.Info(ctx, "mysqlImpl.AssociateAllowList req=%s", utils.Show(req))

	err = self.mysql.Get().Call(
		ctx,
		rdsModel_v2.Action_AssociateAllowList.String(),
		&req,
		nil,
		client.WithVersion(RDS_MySQL_Version_V2),
	)

	return err
}

func (self *mysqlImpl) getAllowLists(ctx context.Context, regionId, instanceId *string) ([]*rdsModel_v2.AllowListObject, error) {
	req := &rdsModel_v2.DescribeAllowListsReq{
		RegionId: regionId,
	}
	if instanceId != nil {
		req.InstanceId = instanceId
	}
	resp := &rdsModel_v2.DescribeAllowListsResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeAllowLists.String(), req, resp, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		return nil, err
	}
	return resp.AllowLists, nil
}

func (self *mysqlImpl) findDBWAllowlist(allowlists []*rdsModel_v2.AllowListObject) []*rdsModel_v2.AllowListObject {
	// 从中根据group name找到wlid
	var dbwAllowList []*rdsModel_v2.AllowListObject
	fp.StreamOf(allowlists).Reject(func(allowList *rdsModel_v2.AllowListObject) bool {
		return !strings.HasPrefix(allowList.AllowListName, DBW_SOLE_BYTE_RDS_GROUP_NAME)
	}).ToSlice(&dbwAllowList)
	sort.Slice(dbwAllowList, func(i, j int) bool {
		return dbwAllowList[i].AllowListName < dbwAllowList[j].AllowListName
	})
	return dbwAllowList
}

func (self *mysqlImpl) genWhiteListName(createdAllowLists []*rdsModel_v2.AllowListObject) (nextName string) {
	if len(createdAllowLists) == 0 {
		// in order compatible, first name use old group name
		return DBW_SOLE_BYTE_RDS_GROUP_NAME
	}
	lastAllowList := createdAllowLists[len(createdAllowLists)-1]
	if lastAllowList.AllowListName == DBW_SOLE_BYTE_RDS_GROUP_NAME {
		nextName = DBW_SOLE_BYTE_RDS_GROUP_NAME + "_" + "1"
		return
	}
	nameItems := strings.Split(lastAllowList.AllowListName, "_")
	indexNum, _ := strconv.Atoi(nameItems[len(nameItems)-1])
	return fmt.Sprintf("%s_%d", DBW_SOLE_BYTE_RDS_GROUP_NAME, indexNum+1)
}

func (self *mysqlImpl) UpdateWhiteList(ctx context.Context, id string, ds *shared.DataSource, ip []string) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	if self.cnf.Get(ctx).EnableRDSNewWhiteList {
		version, err := self.checkNewWhiteList(ctx, ds.InstanceId)
		if err != nil {
			log.Warn(ctx, "mysqlImpl: check new white list err: %v", err.Error())
			return err
		}
		if self.isNewWhiteList(ctx, version) {
			return nil
		}
	}
	// for unique v1 white list group name, don't need to update
	log.Info(ctx, "Empty Op For Update RDS V1 WhiteList")
	return nil
}

func (self *mysqlImpl) RemoveWhiteList(ctx context.Context, id string, ds *shared.DataSource, wlID string) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	if self.cnf.Get(ctx).EnableRDSNewWhiteList {
		version, err := self.checkNewWhiteList(ctx, ds.InstanceId)
		if err != nil {
			log.Warn(ctx, "mysqlImpl: check new white list err: %v", err.Error())
			return err
		}
		if self.isNewWhiteList(ctx, version) {
			if !self.cnf.Get(ctx).EnableRemoveWhiteList {
				log.Info(ctx, "Do not Disassociate White List")
				return nil
			}
			err := self.DisassociateAllowList(ctx, &rdsModel_v2.DisassociateAllowListReq{
				AllowListIds: []string{wlID},
				InstanceIds:  []string{ds.InstanceId},
			})
			if err != nil {
				log.Warn(ctx, "mysqlImpl: Disassociate error %s", err.Error())
				return err
			}

			return nil
		}
	}
	// for unique v1 white list group name, do not remove
	log.Info(ctx, "Empty Op For Remove RDS V1 WhiteList")
	return nil
}

func (self *mysqlImpl) DisassociateAllowList(ctx context.Context, req *rdsModel_v2.DisassociateAllowListReq) (err error) {
	log.Info(ctx, "mysqlImpl: DisassociateAllowList req=%s", utils.Show(req))

	err = self.mysql.Get().Call(
		ctx,
		rdsModel_v2.Action_DisassociateAllowList.String(),
		&req,
		nil,
		client.WithVersion(RDS_MySQL_Version_V2),
	)

	return err
}

/*
获取mysql vpcID：

	首先调用接口获取实例的vpcID
	若vpcID为空，说明当前实例没有vpc，那么就得走老的内网连接的方式，查询内网IP。若当前实例为v1白名单的场景，也得走内网IP查询的方式
	若vpcID不为空，则保存vpcID和对应的域名端口信息
*/
func (self *mysqlImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType != shared.Volc {
		return nil
	}

	//如果启动内网连接开关，那么直接获取内网IP
	//【临时】额外增加ds.Address不等于空，用于处理 mysql限流场景下必须用内网直连方式
	if self.cnf.Get(ctx).EnableRDSInternalConnectSwitch || ds.Address != "" {
		log.Info(ctx, "get rds %s instance info, EnableRDSInternalConnectSwitch is true, back internal connect, vpcId is : %s", ds.InstanceId, ds.VpcID)
		ds.VpcID = ""
		return self.FillInnerDataSource(ctx, ds)
	}

	//如果命中了租户ID的白名单才能走shuttle，否则走内网直连方式
	if !innerUtils.IsTenantEnabledFromCtx(ctx, self.cnf.Get(ctx).RDSShuttleTenantIdList) {
		log.Info(ctx, "get rds %s instance info, RDSShuttleTenantIdList check is false", ds.InstanceId)
		ds.VpcID = ""
		return self.FillInnerDataSource(ctx, ds)
	}

	instanceInfo, err := self.describeInstance(ctx, ds.InstanceId)
	if err != nil {
		log.Warn(ctx, "get rds %s instance info error, err:%s", ds.InstanceId, err)
		return err
	}

	//查询rds，获取白名单版本号
	version, err := self.checkNewWhiteList(ctx, ds.InstanceId)
	if err != nil {
		log.Warn(ctx, "mysqlImpl: check new white list err: %v", err.Error())
		return err
	}

	//如果vpcID为空，查询内网IP，走内网IP直连的方式。或者v1白名单的场景下，也走内网直连（这种场景要清除vpcID）
	ds.VpcID = instanceInfo.BasicInfo.VpcId
	if ds.VpcID == "" || !self.isNewWhiteList(ctx, version) {
		log.Info(ctx, "get rds %s instance, mysql FillDataSource get vpcId is: %s ", ds.VpcID)
		ds.VpcID = ""
		return self.FillInnerDataSource(ctx, ds)
	}

	for _, connectionInfo := range instanceInfo.ConnectionInfo {
		// 这里需要判断一下是否是默认终端,不是默认终端的，不能要
		if connectionInfo.EndpointType != rdsModel_v2.EndpointType_Cluster {
			continue
		}
		for _, connection := range connectionInfo.Address {
			if connection.NetworkType == rdsModel_v2.NetworkType_Private {
				ds.Address = fmt.Sprintf("%s:%s", connection.IPAddress, connection.Port)
			}
		}
	}

	if ds.Address == "" {
		log.Warn(ctx, "fill rds instance %s address error", ds.InstanceId)
		return errors.New("get address error")
	}
	return nil
}

func (self *mysqlImpl) FillInnerDataSource(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	domain, port, err := self.DescribeInnerConnectionInfo(ctx, ds)
	if err != nil {
		log.Warn(ctx, "get rds %s connection fail %v", ds.InstanceId, err)
		return err
	}
	if domain == "" || port == "" {
		return fmt.Errorf("can't resolve rds %s address, get empty storage inner info ", ds.InstanceId)
	}
	// instance that created by multiCloudPlatform, must has proxy
	if tenant.IsRDSMultiCloudPlatform(ctx, self.cnf) && ds_utils.GetEndpointModeByPSM(ds.Address) == datasource.ReadOnly {
		port, err = self.getReadonlyEndpointProxyPort(ctx, ds.InstanceId)
		if err != nil {
			log.Warn(ctx, "get readonly endpoint port error %s", err)
			return err
		}
	}
	address := fmt.Sprintf("%s:%s", domain, port)
	log.Info(ctx, "get rds %s address %s", ds.InstanceId, address)
	ds.Address = address

	_ = self.setCandidateAddress(ctx, ds)

	return nil

}

func (self *mysqlImpl) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType != shared.Volc {
		return nil
	}

	req := &rdsModel.ListInstanceNodesReq{
		InstanceId: ds.InstanceId,
	}
	resp := &rdsModel.ListInstanceNodesResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), req, resp); err != nil {
		log.Warn(ctx, "get rds %s connection fail %v", ds.InstanceId, err)
		return err
	}
	var node *rdsModel.InstanceNodeInfo
	fp.StreamOf(resp.Datas).
		SortBy(func(n1, n2 *rdsModel.InstanceNodeInfo) bool {
			return n1.NodeRole < n2.NodeRole
		}).
		First().
		To(&node)
	if node == nil {
		log.Info(ctx, "no such instance %s", ds.InstanceId)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, "instance not found:"+ds.InstanceId)
	}
	log.Info(ctx, "get rds internal address %s", node.NodeInternalIP)
	nds := new(shared.DataSource)
	*nds = *ds
	nds.Address = node.NodeInternalIP
	ds = nds

	conn, err := self.getConn(ctx, ds)
	if err != nil {
		log.Warn(ctx, "check conn by fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	_, err = conn.Version()
	if err != nil {
		log.Warn(ctx, "get version by %v fail %v", err)
		return consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	return nil
}

func (self *mysqlImpl) CheckDataSource(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	conn, err := self.getConn(ctx, ds)
	if err != nil {
		log.Warn(ctx, "failed to check data source, instanceId=%s, err=%v", ds.InstanceId, err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	_, err = conn.Version()
	if err != nil {
		log.Warn(ctx, "get version by fail %v", err)
		return consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	return nil
}

func (self *mysqlImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}
	// 运维面用新接口调用
	if req.TenantId == "0" || req.TenantId == "1" {
		return self.ListInstanceLightWeight(ctx, req)
	}

	// get mapping of spec code to detail
	specReq := &rdsModel_v2.DescribeDBInstanceSpecsReq{}
	specResp := &rdsModel_v2.DescribeDBInstanceSpecsResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceSpecs.String(), specReq, specResp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to get rds instance, err=%v", err)
		return nil, err
	}
	var specMap map[string]*model.InstanceSpec
	fp.StreamOf(specResp.GetInstanceSpecsInfo()).
		ToSetBy(func(specInfo *rdsModel_v2.InstanceSpecsInfoObject) (string, *model.InstanceSpec) {
			return specInfo.GetSpecCode(), &model.InstanceSpec{
				CpuNum:   specInfo.GetVCPU(),
				MemInGiB: float64(specInfo.GetMemory()),
			}
		}).To(&specMap)

	// get rds instances
	desReq := &rdsModel_v2.DescribeDBInstancesReq{
		PageNumber: utils.Int32Ref(req.PageNumber),
		PageSize:   utils.Int32Ref(req.PageSize),
	}
	if req.InstanceName != "" {
		desReq.InstanceName = utils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		desReq.InstanceId = utils.StringRef(req.InstanceId)
	}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		desReq.CreateTimeStart = utils.StringRef(req.CreateTimeStart)
		desReq.CreateTimeEnd = utils.StringRef(req.CreateTimeEnd)
	}
	// 如果是运维面账号，则不需要传这个值
	if req.TenantId != "0" && req.TenantId != "1" {
		desReq.AccountId = utils.StringRef(req.TenantId)
	}
	var status rdsModel_v2.InstanceStatus
	var err error
	if req.InstanceStatus != "" {
		status, err = rdsModel_v2.InstanceStatusFromString(req.InstanceStatus)
		if err != nil {
			log.Warn(ctx, "failed to get InstanceStatus from string %s", req.InstanceStatus)
		}
		desReq.InstanceStatus = &status
	}
	if req.DBEngineVersion != "" {
		version, err := rdsModel_v2.DBEngineVersionFromString(req.DBEngineVersion)
		if err != nil {
			log.Warn(ctx, "failed to get DBEngineVersion from string %s", req.DBEngineVersion)
		}
		desReq.DBEngineVersion = &version
	}
	if req.ZoneId != "" {
		desReq.ZoneId = utils.StringRef(req.ZoneId)
	}
	if len(req.Tags) > 0 {
		tagFilter := make([]*rdsModel_v2.TagFilterObject, 0)
		for _, tag := range req.Tags {
			tagFilter = append(tagFilter, &rdsModel_v2.TagFilterObject{
				Key:   tag.Key,
				Value: utils.StringRef(tag.Value),
			})
		}
		desReq.TagFilters = tagFilter
	}
	if req.ProjectName != "" {
		desReq.ProjectName = utils.StringRef(req.ProjectName)
	}
	desResp, err := self.DescribeDBInstances(ctx, desReq)
	if err != nil {
		return nil, err
	}
	desRespInfo := desResp.Instances
	fp.StreamOf(desRespInfo).Reject(func(inst *rdsModel_v2.InstanceInfoObject) bool {
		return inst.InstanceStatus == rdsModel_v2.InstanceStatus_Released
	}).ToSlice(&desRespInfo)
	resp := &datasource.ListInstanceResp{Total: int64(desResp.Total)}
	fp.StreamOf(desRespInfo).
		Map(func(inst *rdsModel_v2.InstanceInfoObject) *model.InstanceInfo {
			// get instance private address
			var (
				privateDomain, privatePort string
			)
			targetTags := make([]*model.TagObject, 0)
			fp.StreamOf(inst.GetAddressObject()).Foreach(func(addr *rdsModel_v2.AddressObject) {
				if addr.GetNetworkType() == rdsModel_v2.NetworkType_Private {
					privateDomain = addr.GetDomain()
					privatePort = addr.GetPort()
				}
			}).Run()
			internalAddress := "-"
			if privateDomain != "" && privatePort != "" {
				internalAddress = fmt.Sprintf("%s:%s", privateDomain, privatePort)
			}
			instStatus := inst.InstanceStatus.String()
			hasReadOnlyNodes := false
			if inst.NodeNumber > 2 && inst.InstanceType == rdsModel_v2.InstanceType_DoubleNode {
				hasReadOnlyNodes = true
			}
			if inst.IsSetTags() {
				for _, tag := range inst.GetTags() {
					targetTags = append(targetTags, &model.TagObject{
						Key:   tag.Key,
						Value: *tag.Value,
					})
				}
			}
			accountId := req.TenantId
			if inst.GetAccountId() != "" {
				accountId = inst.GetAccountId()
			}
			var instanceSpec *model.InstanceSpec
			// 如果inst.GetNodeSpec不在specMap中，那么就不设置instanceSpec
			if specMap[inst.GetNodeSpec()] == nil {
				log.Info(ctx, "get rds instance spec code %s not in specMap", inst.GetNodeSpec())
			} else {
				instanceSpec = &model.InstanceSpec{
					CpuNum:     specMap[inst.GetNodeSpec()].GetCpuNum(),
					MemInGiB:   specMap[inst.GetNodeSpec()].GetMemInGiB(),
					NodeNumber: inst.GetNodeNumber(),
				}
			}
			subType, _ := model.SubInstanceTypeFromString(inst.GetInstanceType().String())
			return &model.InstanceInfo{
				InstanceId:      utils.StringRef(inst.InstanceId),
				InstanceName:    utils.StringRef(inst.InstanceName),
				InstanceStatus:  instStatus,
				Zone:            inst.ZoneId,
				InstanceSpec:    instanceSpec,
				DBEngineVersion: inst.GetDBEngineVersion().String(),
				InternalAddress: internalAddress,
				AccessSource:    "云数据库 MySQL版",
				HasReadOnlyNode: hasReadOnlyNodes,
				ProjectName:     utils.StringRef(inst.ProjectName),
				Tags:            targetTags,
				SubInstanceType: &subType,
				AccountId:       utils.StringRef(accountId),
				CreateTime:      utils.StringRef(inst.GetCreateTime()),
				InstanceType:    model.InstanceType_MySQL,
				LinkType:        model.LinkType_Volc,
			}
		}).ToSlice(&resp.InstanceList)
	return resp, nil
}

func (self *mysqlImpl) ListInstanceLightWeight(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}

	// get mapping of spec code to detail
	specReq := &rdsModel_v2.DescribeDBInstanceSpecsReq{}
	specResp := &rdsModel_v2.DescribeDBInstanceSpecsResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceSpecs.String(), specReq, specResp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to get rds instance, err=%v", err)
		return nil, err
	}
	var specMap map[string]*model.InstanceSpec
	fp.StreamOf(specResp.GetInstanceSpecsInfo()).
		ToSetBy(func(specInfo *rdsModel_v2.InstanceSpecsInfoObject) (string, *model.InstanceSpec) {
			return specInfo.GetSpecCode(), &model.InstanceSpec{
				CpuNum:   specInfo.GetVCPU(),
				MemInGiB: float64(specInfo.GetMemory()),
			}
		}).To(&specMap)

	// get rds instances
	desReq := &rdsModel_v2.InnerDescribeDBInstancesReq{
		PageNumber: utils.Int32Ref(req.PageNumber),
		PageSize:   utils.Int32Ref(req.PageSize),
	}
	if req.InstanceName != "" {
		desReq.InstanceName = utils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		desReq.InstanceId = utils.StringRef(req.InstanceId)
	}
	// 如果是运维面账号，则不需要传这个值
	if req.TenantId != "0" && req.TenantId != "1" {
		desReq.AccountId = utils.StringRef(req.TenantId)
	}
	var err error
	desResp, err := self.selfDescribeDBInstancesInner(ctx, desReq)
	if err != nil {
		return nil, err
	}
	desRespInfo := desResp.Instances
	//遍历desRespInfo，打印里面的InstanceInfo

	fp.StreamOf(desRespInfo).Reject(func(inst *rdsModel_v2.InnerInfoObject) bool {
		return inst.InstanceInfo.InstanceStatus == rdsModel_v2.InstanceStatus_Released
	}).ToSlice(&desRespInfo)
	resp := &datasource.ListInstanceResp{Total: int64(desResp.Total)}
	fp.StreamOf(desRespInfo).
		Map(func(inst *rdsModel_v2.InnerInfoObject) *model.InstanceInfo {
			// get instance private address
			var (
				privateDomain, privatePort string
			)
			targetTags := make([]*model.TagObject, 0)
			fp.StreamOf(inst.InstanceInfo.GetAddressObject()).Foreach(func(addr *rdsModel_v2.AddressObject) {
				if addr.GetNetworkType() == rdsModel_v2.NetworkType_Private {
					privateDomain = addr.GetDomain()
					privatePort = addr.GetPort()
				}
			}).Run()
			internalAddress := "-"
			if privateDomain != "" && privatePort != "" {
				internalAddress = fmt.Sprintf("%s:%s", privateDomain, privatePort)
			}
			instStatus := inst.InstanceInfo.InstanceStatus.String()
			hasReadOnlyNodes := false
			if inst.InstanceInfo.NodeNumber > 2 && inst.InstanceInfo.InstanceType == rdsModel_v2.InstanceType_DoubleNode {
				hasReadOnlyNodes = true
			}
			if inst.InstanceInfo.IsSetTags() {
				for _, tag := range inst.InstanceInfo.GetTags() {
					targetTags = append(targetTags, &model.TagObject{
						Key:   tag.Key,
						Value: *tag.Value,
					})
				}
			}
			accountId := req.TenantId
			if inst.InstanceInfo.GetAccountId() != "" {
				accountId = inst.InstanceInfo.GetAccountId()
			}
			var instanceSpec *model.InstanceSpec
			// 如果inst.GetNodeSpec不在specMap中，那么就不设置instanceSpec
			if specMap[inst.InstanceInfo.GetNodeSpec()] == nil {
				log.Info(ctx, "get rds instance spec code %s not in specMap", inst.InstanceInfo.GetNodeSpec())
			} else {
				instanceSpec = &model.InstanceSpec{
					CpuNum:     specMap[inst.InstanceInfo.GetNodeSpec()].GetCpuNum(),
					MemInGiB:   specMap[inst.InstanceInfo.GetNodeSpec()].GetMemInGiB(),
					NodeNumber: inst.InstanceInfo.GetNodeNumber(),
				}
			}
			subType, _ := model.SubInstanceTypeFromString(inst.InstanceInfo.GetInstanceType().String())
			return &model.InstanceInfo{
				InstanceId:      utils.StringRef(inst.InstanceInfo.InstanceId),
				InstanceName:    utils.StringRef(inst.InstanceInfo.InstanceName),
				InstanceStatus:  instStatus,
				Zone:            inst.InstanceInfo.ZoneId,
				InstanceSpec:    instanceSpec,
				DBEngineVersion: inst.InstanceInfo.GetDBEngineVersion().String(),
				InternalAddress: internalAddress,
				AccessSource:    "云数据库 MySQL版",
				HasReadOnlyNode: hasReadOnlyNodes,
				ProjectName:     utils.StringRef(inst.InstanceInfo.ProjectName),
				Tags:            targetTags,
				SubInstanceType: &subType,
				AccountId:       utils.StringRef(accountId),
				CreateTime:      utils.StringRef(inst.InstanceInfo.GetCreateTime()),
				InstanceType:    model.InstanceType_MySQL,
				LinkType:        model.LinkType_Volc,
			}
		}).ToSlice(&resp.InstanceList)
	return resp, nil
}
func (self *mysqlImpl) makeNewGroupID(id string) string {
	return `dbw-` + id
}

func (self *mysqlImpl) getGroupID(id string) string {
	return `byte_rds_system_internal_dbw-` + id
}

func (self *mysqlImpl) getConn(ctx context.Context, ds *shared.DataSource) (db.Conn, error) {
	parseTimeManualy := false
	if val, ok := ds.ExtraDsn["parseTime"]; ok {
		parseTimeManualy = convertStringToBool(val)
	}
	opt := &db.Options{
		Address:          ds.Address,
		DB:               ds.Db,
		User:             ds.User,
		Password:         ds.Password,
		Driver:           db.MysqlDriver,
		ParseTimeManualy: parseTimeManualy,
	}
	if ds.ConnectTimeoutMs != 0 {
		opt.Timeout = uint(ds.ConnectTimeoutMs)
	}
	if ds.ReadTimeoutMs != 0 {
		opt.ReadTimeout = uint(ds.ReadTimeoutMs)
	}
	if ds.WriteTimeoutMs != 0 {
		opt.WriteTimeout = uint(ds.WriteTimeoutMs)
	}
	self.parseAddress(ds, opt)
	return db.NewConn(opt)
}

func (self *mysqlImpl) GetManagedAccountAndPwd(ctx context.Context, req *shared.DataSource) (*datasource.GetManagedAccountAndPwdResp, error) {
	convertedDs := &model.DataSource{
		Type:       model.DSType_MySQL,
		InstanceId: utils.StringRef(req.InstanceId),
		LinkType:   model.LinkType_Volc,
	}
	if err := self.userMgmtSvc.GetDBAccount(ctx, convertedDs); err != nil {
		return nil, err
	}
	log.Info(ctx, "userName:%s,pwd:%s", convertedDs.GetUsername(), convertedDs.GetPassword())
	return &datasource.GetManagedAccountAndPwdResp{
			AccountName:     convertedDs.GetUsername(),
			AccountPassword: convertedDs.GetPassword()},
		nil

}
func (self *mysqlImpl) getConnV2(ctx context.Context, ds *shared.DataSource) (datasource.Conn, error) {
	if ds.NodeId == "" {
		return self.ConnPool.GetInstanceAdminConn(ctx, &datasource.GetInstanceAdminConnReq{InstanceId: ds.InstanceId, Type: ds.Type, Ds: ds})
	} else {
		return self.ConnPool.GetNodeAdminConn(ctx, &datasource.GetNodeAdminConnReq{InstanceId: ds.InstanceId, Type: ds.Type, NodeId: ds.NodeId, Ds: ds})
	}
}

func (self *mysqlImpl) parseAddress(ds *shared.DataSource, opts *db.Options) {
	address := ds.Address
	if strings.Count(address, ":") > 2 && !strings.HasPrefix(address, "[") {
		colons := strings.Split(address, ":")
		opts.Host = strings.Join(colons[:len(colons)-1], ":")
		log.Info(context.TODO(), "host is %s", opts.Host)
		port, _ := strconv.Atoi(colons[len(colons)-1])
		opts.Port = uint(port)
		opts.Address = ""
	}
}

type DatabaseInfo struct {
	SchemaName       string `gorm:"column:SCHEMA_NAME"`
	CharacterSetName string `gorm:"column:DEFAULT_CHARACTER_SET_NAME"`
	CollationName    string `gorm:"column:DEFAULT_COLLATION_NAME"`
}

func (self *mysqlImpl) ListDatabases(ctx context.Context, req *datasource.ListDatabasesReq) (*datasource.ListDatabasesResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
	}
	defer conn.Close()

	ret := &datasource.ListDatabasesResp{}
	var dbList []*DatabaseInfo
	sql := "select SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME from information_schema.SCHEMATA where SCHEMA_NAME !='byte_rds_meta' and 1=1 "
	var args []interface{}
	if req.Keyword != "" {
		sql += " and SCHEMA_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if !req.EnableSystemDB {
		sql += " and SCHEMA_NAME not in  (?) "
		systemDB := []string{"information_schema", "performance_schema", "mysql", "sys"}
		args = append(args, systemDB)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	if err := conn.Raw(sql, args...).Scan(&dbList); err != nil {
		return nil, err
	}

	if req.Keyword == "" {
		if err = conn.Raw("select count(1) from information_schema.SCHEMATA where SCHEMA_NAME !='byte_rds_meta' " + DBW_CONSOLE_DEFAULT_HINT).
			Scan(&ret.Total); err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
		}
	} else {
		sqlCount := "select count(1) from information_schema.SCHEMATA where SCHEMA_NAME like ? "
		sqlCount += DBW_CONSOLE_DEFAULT_HINT
		if err = conn.Raw(sqlCount, `%`+req.Keyword+`%`).
			Scan(&ret.Total); err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
		}
	}

	if err = fp.StreamOf(dbList).Map(func(db *DatabaseInfo) *shared.DatabaseInfo {
		return &shared.DatabaseInfo{
			Name:             db.SchemaName,
			CharacterSetName: db.CharacterSetName,
			CollationName:    db.CollationName,
		}
	}).ToSlice(&ret.Items); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *mysqlImpl) ListTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTablesResp{}
	sql := "select TABLE_NAME, TABLE_COMMENT from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	if err = conn.Raw(sql, args...).Scan(&ret.Tables); err != nil {
		return nil, err
	}
	sql = "select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args = []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	args = []interface{}{req.DB}
	/* get count */
	counterSQL := "/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE' " // ignore_security_alert
	if req.Keyword != "" {
		counterSQL += "and TABLE_NAME like ?"
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		counterSQL += genListTableFilter(req.Filters)
	}
	if err = conn.Raw(counterSQL, args...).Scan(&ret.Total); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *mysqlImpl) ListAllTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	log.Info(ctx, "ListAllTables-enter %s", utils.Show(req))
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTablesResp{}
	sql := "select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys') and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args := []interface{}{}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	log.Info(ctx, "ListAllTables-sql:%s,args:%s", utils.Show(sql), utils.Show(args))
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		log.Warn(ctx, "Scan to sql %s fail %v", sql, err)
		return nil, err
	}
	args = []interface{}{req.DB}
	/* get count */
	counterSQL := "/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE' " // ignore_security_alert
	if req.Keyword != "" {
		counterSQL += "and TABLE_NAME like ?"
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		counterSQL += genListTableFilter(req.Filters)
	}
	log.Info(ctx, "ListAllTables-counterSQL:%s,args:%s", utils.Show(counterSQL), utils.Show(args))
	if err = conn.Raw(counterSQL, args...).Scan(&ret.Total); err != nil {
		log.Warn(ctx, "Scan to counterSQL %s fail %v", counterSQL, err)
		return nil, err
	}
	return ret, nil
}

func (self *mysqlImpl) ExecuteCCL(ctx context.Context, req *datasource.ExecuteCCLReq) (*datasource.ExecuteCCLResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ExecuteCCLResp{}
	err = libutils.Retry(ctx, func(ctx context.Context) error {
		return conn.Raw(req.Commend).Scan(&ret.Result)
	}, libutils.RetryMaxTimes(3, 300*time.Millisecond))
	if err != nil {
		errorInfo := fmt.Sprintf("%v", err)
		if strings.Contains(errorInfo, "3249") {
			log.Info(ctx, "rule is repeat")
			return ret, nil
		}
		return ret, err
	}
	return ret, nil
}

type CCLRuleInfo struct {
	ID                    string `gorm:"column:SCHEMA_NAME"`
	TYPE                  string `gorm:"column:DEFAULT_CHARACTER_SET_NAME"`
	USER                  string `gorm:"column:DEFAULT_COLLATION_NAME"`
	HOST                  string `gorm:"column:DEFAULT_COLLATION_NAME"`
	SCHEMA                string `gorm:"column:DEFAULT_COLLATION_NAME"`
	TABLE                 string `gorm:"column:DEFAULT_COLLATION_NAME"`
	KEYWORDS              string `gorm:"column:DEFAULT_COLLATION_NAME"`
	STATE                 string `gorm:"column:DEFAULT_COLLATION_NAME"`
	ORDERED               string `gorm:"column:DEFAULT_COLLATION_NAME"`
	CONCURRENCY_COUNT     string `gorm:"column:DEFAULT_COLLATION_NAME"`
	WAIT_TIMEOUT          string `gorm:"column:DEFAULT_COLLATION_NAME"`
	MAX_WAIT_THREAD_COUNT string `gorm:"column:DEFAULT_COLLATION_NAME"`
	MATCHED               string `gorm:"column:DEFAULT_COLLATION_NAME"`
	RUNNING               string `gorm:"column:DEFAULT_COLLATION_NAME"`
	WAITTING              string `gorm:"column:DEFAULT_COLLATION_NAME"`
}

func (self *mysqlImpl) CCLShow(ctx context.Context, req *datasource.CCLShowReq) (*datasource.CCLShowResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.CCLShowResp{}
	var ruleInfo []*CCLRuleInfo
	if err = libutils.Retry(ctx, func(ctx context.Context) error {
		return conn.Raw(req.Commend).Scan(&ruleInfo)
	}, libutils.RetryMaxTimes(3, 300*time.Millisecond)); err != nil {
		return nil, err
	}
	if err = fp.StreamOf(ruleInfo).Map(func(db *CCLRuleInfo) *shared.CCLRuleInfo {
		return &shared.CCLRuleInfo{
			Id:                 db.ID,
			Type:               db.TYPE,
			User:               db.USER,
			Host:               db.HOST,
			Schema:             db.SCHEMA,
			Table:              db.TABLE,
			Keywords:           db.KEYWORDS,
			State:              db.STATE,
			Ordered:            db.ORDERED,
			ConcurrencyCount:   db.CONCURRENCY_COUNT,
			WaitTimeout:        db.WAIT_TIMEOUT,
			MaxWaitThreadCount: db.MAX_WAIT_THREAD_COUNT,
			Matched:            db.MATCHED,
			Running:            db.RUNNING,
			Waiting:            db.WAITTING,
		}
	}).ToSlice(&ret.Result); err != nil {
		return nil, err
	}
	return ret, nil
}

type DBColumn struct {
	Field   string
	Type    string
	Null    string
	Key     string
	Default *string
	Extra   string
}

type DBIndex struct {
	Non_unique   int
	Key_name     string
	Seq_in_index int
	Column_name  string
	Null         string
	Index_type   string
}

type DBOption struct {
	ENGINE          string
	ROW_FORMAT      string
	AVG_ROW_LENGTH  string
	AUTO_INCREMENT  string
	TABLE_COLLATION string
	CREATE_OPTIONS  string
	TABLE_COMMENT   string
	TABLE_ROWS      string
	TABLE_SPACE     string
	CREATE_TIME     string
}

type DBColumnOther struct {
	COLUMN_NAME    string
	COLUMN_COMMENT string
	EXTRA          string
}

type DBVersion struct {
	Version string
}

type RawForeignKey struct {
	ConstraintName        string `gorm:"column:CONSTRAINT_NAME"`
	TableSchema           string `gorm:"column:TABLE_SCHEMA"`
	TableName             string `gorm:"column:TABLE_NAME"`
	ColumnName            string `gorm:"column:COLUMN_NAME"`
	OrdinalPosition       string `gorm:"column:ORDINAL_POSITION"`
	ReferencedTableSchema string `gorm:"column:REFERENCED_TABLE_SCHEMA"`
	ReferencedTableName   string `gorm:"column:REFERENCED_TABLE_NAME"`
	ReferencedColumnName  string `gorm:"column:REFERENCED_COLUMN_NAME"`
	UpdateRule            string `gorm:"column:UPDATE_RULE"`
	DeleteRule            string `gorm:"column:DELETE_RULE"`
}

func (self *mysqlImpl) DescribeTable(ctx context.Context, req *datasource.DescribeTableReq) (*datasource.DescribeTableResp, error) {
	ret := &datasource.DescribeTableResp{}
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	/* get columns info */
	var columns []*DBColumn
	queryCols := fmt.Sprintf("desc `%s`.`%s` %s", req.DB, req.Table, DBW_CONSOLE_DEFAULT_HINT)
	err = conn.Raw(queryCols).Scan(&columns)
	if err != nil {
		log.Warn(ctx, "get columns of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}

	ret.Columns = DBColumnToTableInfoColumnInfo(columns)

	/* get index info */
	var indexs []*DBIndex
	queryIndex := fmt.Sprintf("show index from `%s`.`%s` %s", req.DB, req.Table, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(queryIndex).Scan(&indexs); err != nil {
		log.Warn(ctx, "get index of %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}

	ret.Indexs = getIndexList(indexs)
	log.Info(ctx, "get ret.Indexs %v", utils.Show(ret.Indexs))

	/* get foreignKey info */
	ret.ForeignKeys, err = getForeignKeyInfo(ctx, conn, req.DB, req.Table)
	if err != nil {
		return nil, err
	}
	log.Info(ctx, "get ret.foreignKey is: %v", utils.Show(ret.ForeignKeys))

	/* get tableOption info */
	var tableOption DBOption
	tableOptionQuery := "SELECT ENGINE, ROW_FORMAT, AVG_ROW_LENGTH, AUTO_INCREMENT, TABLE_COLLATION, CREATE_OPTIONS, " +
		"TABLE_COMMENT, TABLE_ROWS, INDEX_LENGTH+DATA_LENGTH+DATA_FREE AS TABLE_SPACE, CREATE_TIME FROM information_schema.tables WHERE TABLE_SCHEMA=? AND TABLE_NAME=?"
	err = conn.Raw(tableOptionQuery, req.DB, req.Table).Scan(&tableOption)
	if err != nil {
		log.Warn(ctx, "get tableOption from %s of %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	ret.Option, err = getTableOption(ctx, &tableOption)
	/* get column  Comment、IsAutoIncrement */
	var columnOthers []*DBColumnOther
	queryOther := fmt.Sprintf("select * from information_schema.columns where table_schema='%s' and table_name='%s' %s", req.DB, req.Table, DBW_CONSOLE_DEFAULT_HINT)
	err = conn.Raw(queryOther).Scan(&columnOthers)
	if err != nil {
		log.Warn(ctx, "get columnComments from %s of %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	getColumnOther(ctx, ret, columnOthers)
	getPrimaryOrder(ctx, ret)
	var tableName, stmt string
	var cursor db.Cursor

	var charSet, collationConn string
	definitionSQL := fmt.Sprintf("SHOW CREATE TABLE `%s`.`%s`", req.DB, req.Table)
	if cursor, err = conn.Rows(definitionSQL); err != nil {
		log.Warn(ctx, "get definition error %s", err)
		if err != nil {
			return nil, err
		}

	}
	for cursor.Next() {
		if err := cursor.Scan(&tableName, &stmt, &charSet, &collationConn); err != nil {
			if err := cursor.Scan(&tableName, &stmt); err != nil {
				return nil, err
			}
		}
	}
	ret.Definition = stmt
	if !slices.Contains(sysDatabaseList, req.DB) {
		lib.FillTableInfo(ctx, stmt, ret)
	}
	return ret, err
}

func (self *mysqlImpl) ListTablesInfo(ctx context.Context, req *datasource.ListTablesInfoReq) (*datasource.ListTablesInfoResp, error) {
	resp, err := self.ListTables(ctx, &datasource.ListTablesReq{
		Source: req.Source,
		DB:     req.Database,
		Offset: int64(0),
		Limit:  int64(1000),
	})
	if err != nil {
		return nil, err
	}
	var tablesInfo []*datasource.TableInfo
	for _, table := range resp.Tables {
		resp, err := self.DescribeTable(ctx, &datasource.DescribeTableReq{
			Source: req.Source,
			DB:     req.Database,
			Table:  table.Name,
		})
		if err != nil {
			log.Warn(ctx, "list table %s error err%s", table, err)
			return nil, err
		}
		tableInfo := &datasource.TableInfo{
			Name:        table.Name,
			Columns:     resp.Columns,
			Indexes:     resp.Indexs,
			ForeignKeys: resp.ForeignKeys,
			Option:      &resp.Option,
			Definition:  resp.Definition,
			Comment:     resp.Comment,
		}
		tablesInfo = append(tablesInfo, tableInfo)
	}
	return &datasource.ListTablesInfoResp{
		Tables: tablesInfo,
	}, nil
}
func (self *mysqlImpl) ListViews(ctx context.Context, req *datasource.ListViewsReq) (*datasource.ListViewsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListViewsResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW')"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW') and TABLE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW')", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

type ViewInfo struct {
	View       string
	CreateView string `gorm:"column:Create View"`
}

type ViewMeta struct {
	TableName      string `gorm:"column:TABLE_NAME"`
	ViewDefinition string `gorm:"column:VIEW_DEFINITION"`
	CheckOption    string `gorm:"column:CHECK_OPTION"`
	DEFINER        string `gorm:"column:DEFINER"`
	SecurityType   string `gorm:"column:SECURITY_TYPE"`
}

func (self *mysqlImpl) DescribeView(ctx context.Context, req *datasource.DescribeViewReq) (*datasource.DescribeViewResp, error) {
	ret := &datasource.DescribeViewResp{}
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	// show create info
	var viewInfo ViewInfo
	queryView := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ SHOW CREATE VIEW `%s`.`%s` %s", req.DB, req.View, DBW_CONSOLE_DEFAULT_HINT)
	err = conn.Raw(queryView).Scan(&viewInfo)
	if err != nil {
		log.Warn(ctx, "show create view `%s`.`%s` fail, err=%v", req.DB, req.View, err)
		return nil, err
	}
	ret.ViewInfo = &shared.ViewInfo{
		View:       viewInfo.View,
		CreateView: viewInfo.CreateView,
		ViewMeta:   &shared.ViewInfo_MetaInfo{},
	}

	// viewMeta
	var viewMeta ViewMeta
	queryViewMeta := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select * from information_schema.VIEWS where TABLE_SCHEMA='%s' and TABLE_NAME='%s';", req.DB, req.View)
	err = conn.Raw(queryViewMeta).Scan(&viewMeta)
	if err != nil {
		log.Warn(ctx, "/*+ DBW SQL CONSOLE DEFAULT*/ select * from information_schema.VIEWS where TABLE_SCHEMA='%s' and TABLE_NAME='%s'; fail, err=%v", req.DB, req.View, err)
		return nil, err
	}
	ret.ViewInfo.ViewMeta.Name = viewMeta.TableName
	ret.ViewInfo.ViewMeta.Definition = viewMeta.ViewDefinition
	ret.ViewInfo.ViewMeta.CheckOption = viewMeta.CheckOption
	ret.ViewInfo.ViewMeta.Definer = viewMeta.DEFINER
	ret.ViewInfo.ViewMeta.Security = viewMeta.SecurityType
	ret.ViewInfo.ViewMeta.Algorithm = getViewAlgorithm(ctx, viewInfo.CreateView)
	return ret, nil
}

type DBFunction struct {
	FunctionName   string `gorm:"column:SPECIFIC_NAME"`
	Security       string `gorm:"column:SECURITY_TYPE"`
	SQLDataAccess  string `gorm:"column:SQL_DATA_ACCESS"`
	Definition     string `gorm:"column:ROUTINE_DEFINITION"`
	Comment        string `gorm:"column:ROUTINE_COMMENT"`
	FuncReturnType string `gorm:"column:DATA_TYPE"`
	Determine      string `gorm:"column:IS_DETERMINISTIC"`
}

func (self *mysqlImpl) DescribeFunction(ctx context.Context, req *datasource.DescribeFunctionReq) (*datasource.DescribeFunctionResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	functionInfo := &DBFunction{}
	ret := &datasource.DescribeFunctionResp{}

	/* get the param list */
	var paramlist []Param
	sql1 := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select PARAMETER_MODE,PARAMETER_NAME,DATA_TYPE,DTD_IDENTIFIER from information_schema.PARAMETERS where specific_schema='%s' and specific_name='%s' and ROUTINE_TYPE='FUNCTION' %s;", req.DB, req.Function, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql1).Scan(&paramlist); err != nil {
		return nil, err
	}

	/* get  function info */
	sql2 := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select *from information_schema.ROUTINES where ROUTINE_SCHEMA='%s' and SPECIFIC_NAME='%s' and ROUTINE_TYPE='FUNCTION' %s", req.DB, req.Function, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql2).Scan(functionInfo); err != nil {
		return nil, err
	}

	var paramList2 []*shared.Param
	for _, param := range paramlist {
		paramList2 = append(paramList2, &shared.Param{
			ParamName: param.ParamName,
			ParamType: param.ParamType,
			Length:    parseColumnLength(param.Length),
		})
	}

	var security shared.Security
	switch functionInfo.Security {
	case "DEFINER":
		security = shared.Definer
	case "INVOKER":
		security = shared.Invoker
	default:
		security = shared.Definer
	}

	var sqlDataAccess shared.SQLDataAccess
	switch functionInfo.SQLDataAccess {
	case "CONTAINS SQL":
		sqlDataAccess = shared.ContainsSQL
	case "NO SQL":
		sqlDataAccess = shared.NoSQL
	case "READS SQL DATA":
		sqlDataAccess = shared.ReadsSQLData
	case "MODIFIES SQL DATA":
		sqlDataAccess = shared.ModifiesSQLData
	default:
		sqlDataAccess = shared.ContainsSQL
	}

	var determine shared.Determine
	switch functionInfo.Determine {
	case "YES":
		determine = shared.DETERMINISTIC
	case "NO":
		determine = shared.NOT_DETERMINISTIC
	default:
		determine = shared.DETERMINISTIC
	}

	ret.FunctionInfo = &shared.FunctionInfo{
		FunctionName:     functionInfo.FunctionName,
		Security:         security,
		SQLDataAccess:    sqlDataAccess,
		Definition:       functionInfo.Definition,
		FuncReturnType:   functionInfo.FuncReturnType,
		FuncReturnLength: 0,
		Determine:        determine,
		Comment:          functionInfo.Comment,
		ParamList:        paramList2,
	}
	return ret, nil
}

type DBProcedure struct {
	ProcedureName string `gorm:"column:SPECIFIC_NAME"`
	Security      string `gorm:"column:SECURITY_TYPE"`
	SQLDataAccess string `gorm:"column:SQL_DATA_ACCESS"`
	Definition    string `gorm:"column:ROUTINE_DEFINITION"`
	Comment       string `gorm:"column:ROUTINE_COMMENT"`
}

type Param struct {
	ParamMode string `gorm:"column:PARAMETER_MODE"` //only for procedure
	ParamName string `gorm:"column:PARAMETER_NAME"`
	ParamType string `gorm:"column:DATA_TYPE"`
	Length    string `gorm:"column:DTD_IDENTIFIER"`
}

func (self *mysqlImpl) DescribeProcedure(ctx context.Context, req *datasource.DescribeProcedureReq) (*datasource.DescribeProcedureResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	procedureInfo := &DBProcedure{}
	ret := &datasource.DescribeProcedureResp{}

	/* get the param list */
	var paramlist []Param
	sql1 := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select PARAMETER_MODE,PARAMETER_NAME,DATA_TYPE,DTD_IDENTIFIER from information_schema.PARAMETERS where ROUTINE_TYPE=\"PROCEDURE\" and specific_schema=\"%s\" and specific_name=\"%s\" %s;", req.DB, req.Procedure, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql1).Scan(&paramlist); err != nil {
		return nil, err
	}

	/* get procedure info */
	sql2 := fmt.Sprintf("/*+ DBW SQL CONSOLE DEFAULT*/ select *from information_schema.ROUTINES where ROUTINE_SCHEMA='%s' and SPECIFIC_NAME='%s' and ROUTINE_TYPE='PROCEDURE' %s", req.DB, req.Procedure, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql2).Scan(procedureInfo); err != nil {
		return nil, err
	}

	paramlist2 := make([]*shared.Param, 0)
	for _, param := range paramlist {
		paramlist2 = append(paramlist2, &shared.Param{
			ParamMode: param.ParamMode,
			ParamName: param.ParamName,
			ParamType: param.ParamType,
			Length:    parseColumnLength(param.Length),
		})
	}

	var security shared.Security
	switch procedureInfo.Security {
	case "DEFINER":
		security = shared.Definer
	case "INVOKER":
		security = shared.Invoker
	default:
		security = shared.Definer
	}

	var sqlDataAccess shared.SQLDataAccess
	switch procedureInfo.SQLDataAccess {
	case "CONTAINS SQL":
		sqlDataAccess = shared.ContainsSQL
	case "NO SQL":
		sqlDataAccess = shared.NoSQL
	case "READS SQL DATA":
		sqlDataAccess = shared.ReadsSQLData
	case "MODIFIES SQL DATA":
		sqlDataAccess = shared.ModifiesSQLData
	default:
		sqlDataAccess = shared.ContainsSQL
	}

	ret.ProcedureInfo = &shared.ProcedureInfo{
		ProcedureName: procedureInfo.ProcedureName,
		Security:      security,
		SQLDataAccess: sqlDataAccess,
		Definition:    procedureInfo.Definition,
		Comment:       procedureInfo.Comment,
		ParamList:     paramlist2,
	}
	return ret, nil
}

func (self *mysqlImpl) ListFunctions(ctx context.Context, req *datasource.ListFunctionsReq) (*datasource.ListFunctionsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListFunctionsResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ SELECT `ROUTINE_NAME` FROM `information_schema`.`ROUTINES` WHERE ROUTINE_SCHEMA=? AND ROUTINE_TYPE='FUNCTION'"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and ROUTINE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	sql += DBW_CONSOLE_DEFAULT_HINT
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='FUNCTION' and ROUTINE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='FUNCTION'", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *mysqlImpl) ListProcedures(ctx context.Context, req *datasource.ListProceduresReq) (*datasource.ListProceduresResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListProceduresResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ SELECT `ROUTINE_NAME` FROM `information_schema`.`ROUTINES` WHERE ROUTINE_SCHEMA=? AND ROUTINE_TYPE='PROCEDURE'"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and ROUTINE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='PROCEDURE' and ROUTINE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='PROCEDURE'", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *mysqlImpl) ListTriggers(ctx context.Context, req *datasource.ListTriggersReq) (*datasource.ListTriggersResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTriggersResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ select TRIGGER_NAME from information_schema.triggers where TRIGGER_SCHEMA=?"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TRIGGER_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.triggers where TRIGGER_SCHEMA=? and TRIGGER_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.triggers where TRIGGER_SCHEMA=?", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

type DBEvent struct {
	EventName      string `gorm:"column:EVENT_NAME"`
	ScheduleMethod string `gorm:"column:EVENT_TYPE"`
	TimeStamp      string `gorm:"column:EXECUTE_AT"`
	IntervalUnit   string `gorm:"column:INTERVAL_FIELD"`
	IntervalNumber int32  `gorm:"column:INTERVAL_VALUE"`
	Preserve       string `gorm:"column:ON_COMPLETION"`
	EventState     string `gorm:"column:STATUS"`
	Do             string `gorm:"column:EVENT_DEFINITION"`
	Comment        string `gorm:"column:EVENT_COMMENT"`
	StartTime      string `gorm:"column:STARTS"`
	EndTime        string `gorm:"column:ENDS"`
}

func (self *mysqlImpl) DescribeEvent(ctx context.Context, req *datasource.DescribeEventReq) (*datasource.DescribeEventResp, error) {
	ret := &datasource.DescribeEventResp{}
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	/* get event info */
	eventInfo := &DBEvent{}
	sql := fmt.Sprintf("select * from information_schema.EVENTS where EVENT_SCHEMA='%s' and EVENT_NAME='%s' %s;", req.DB, req.Name, DBW_CONSOLE_DEFAULT_HINT)
	if err = conn.Raw(sql).Scan(&eventInfo); err != nil {
		return nil, err
	}
	log.Info(ctx, "eventInfo ret: %v", eventInfo)

	var scheduleMethod shared.ScheduleMethod
	switch eventInfo.ScheduleMethod {
	case "ONE TIME":
		scheduleMethod = shared.FixedTime
	case "RECURRING":
		scheduleMethod = shared.Cycle
	}

	//TODO 有些是可选字段
	var intervalUnit shared.IntervalUnit
	switch eventInfo.IntervalUnit {
	case "YEAR":
		intervalUnit = shared.Year
	case "MONTH":
		intervalUnit = shared.Month
	case "WEEK":
		intervalUnit = shared.Week
	case "DAY":
		intervalUnit = shared.DAY
	case "HOUR":
		intervalUnit = shared.Hour
	case "MINUTE":
		intervalUnit = shared.Minute
	case "SECOND":
		intervalUnit = shared.Second
	default:
		intervalUnit = shared.NoneUnit
	}

	var eventState shared.EventState
	switch eventInfo.EventState {
	case "ENABLED":
		eventState = shared.Enable
	case "DISABLED":
		eventState = shared.Disable
	case "SLAVESIDE_DISABLED":
		eventState = shared.DisableOnSlave
	}

	ret.EventInfo = &shared.EventInfo{
		EventName:      eventInfo.EventName,
		ScheduleMethod: scheduleMethod,
		TimeStamp:      eventInfo.TimeStamp,
		IntervalUnit:   intervalUnit,
		IntervalNumber: eventInfo.IntervalNumber,
		Preserve:       eventInfo.Preserve == "PRESERVE",
		EventState:     eventState,
		Do:             eventInfo.Do,
		Comment:        eventInfo.Comment,
		StartTime:      eventInfo.StartTime,
		EndTime:        eventInfo.EndTime,
	}
	return ret, nil
}

type TriggerInfo struct {
	TriggerName  string `gorm:"column:TRIGGER_NAME"`
	TriggerTime  string `gorm:"column:ACTION_TIMING"`      //触发时间，before/after
	TriggerEvent string `gorm:"column:EVENT_MANIPULATION"` //触发事件，insert/delete/update
	TriggerTable string `gorm:"column:EVENT_OBJECT_TABLE"`
	Definition   string `gorm:"column:ACTION_STATEMENT"`
}

func (self *mysqlImpl) DescribeTrigger(ctx context.Context, req *datasource.DescribeTriggerReq) (*datasource.DescribeTriggerResp, error) {
	ret := &datasource.DescribeTriggerResp{}
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	/* get trigger info */
	var triggerInfo TriggerInfo
	queryView := fmt.Sprintf("select * from information_schema.TRIGGERS where TRIGGER_SCHEMA='%s' and TRIGGER_NAME='%s' %s;", req.DB, req.Trigger, DBW_CONSOLE_DEFAULT_HINT)
	err = conn.Raw(queryView).Scan(&triggerInfo)
	if err != nil {
		log.Warn(ctx, "get trigger:`%s`.`%s` info fail, err=%v", req.DB, req.Trigger, err)
		return nil, err
	}

	var triggerTime shared.TriggerTime
	switch triggerInfo.TriggerTime {
	case "BEFORE":
		triggerTime = shared.Before
	case "AFTER":
		triggerTime = shared.After
	}

	var triggerEvent shared.TriggerEvent
	switch triggerInfo.TriggerEvent {
	case "INSERT":
		triggerEvent = shared.Insert
	case "UPDATE":
		triggerEvent = shared.Update
	case "DELETE":
		triggerEvent = shared.Delete
	}

	ret.TriggerInfo = &shared.TriggerInfo{
		TriggerName:  triggerInfo.TriggerName,
		TriggerTime:  triggerTime,
		TriggerEvent: triggerEvent,
		TriggerTable: triggerInfo.TriggerTable,
		Definition:   triggerInfo.Definition,
	}

	return ret, nil
}

func (self *mysqlImpl) ListEvents(ctx context.Context, req *datasource.ListEventsReq) (*datasource.ListEventsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListEventsResp{}
	sql := "/*+ DBW SQL CONSOLE DEFAULT*/ select EVENT_NAME from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=?"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and EVENT_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Limit != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}

	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	log.Info(ctx, "sql: %s", sql)
	if req.Keyword != "" {
		if err = conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=? and EVENT_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total); err != nil {
			return nil, err
		}
	} else {
		if err = conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=?", req.DB).
			Scan(&ret.Total); err != nil {
			return nil, err
		}
	}
	return ret, nil
}

func (self *mysqlImpl) setCandidateAddress(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType == shared.Volc {
		req := &rdsModel.ListInstanceNodesReq{
			InstanceId: ds.InstanceId,
		}
		resp := &rdsModel.ListInstanceNodesResp{}
		if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), req, resp); err != nil {
			log.Warn(ctx, "get rds %s connection fail %v", ds.InstanceId, err)
			return err
		}
		var node *rdsModel.InstanceNodeInfo
		fp.StreamOf(resp.Datas).
			SortBy(func(n1, n2 *rdsModel.InstanceNodeInfo) bool {
				return n1.NodeRole < n2.NodeRole
			}).
			First().
			To(&node)
		if node == nil {
			log.Info(ctx, "no such instance %s", ds.InstanceId)
			return consts.ErrorWithParam(model.ErrorCode_UnknownCreateSessionError, "The instance not found: ds.InstanceId")
		}
		log.Info(ctx, "get rds internal address %s", node.NodeInternalIP)
		ds.CadidateAddress = node.NodeInternalIP
		return nil
	}
	return nil
}

func (self *mysqlImpl) ListCharsets(ctx context.Context, req *datasource.ListCharsetsReq) (*datasource.ListCharsetsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListCharsetsResp{}
	sql := "SELECT character_set_name from information_schema.character_sets"

	if req.Keyword != "" {
		sql += fmt.Sprintf(" WHERE character_set_name LIKE '%%%s%%'", req.Keyword)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	sql += ";"

	if err = conn.Raw(sql).Scan(&ret.Items); err != nil {
		return nil, err
	}
	ret.Total = int64(len(ret.Items))
	log.Info(ctx, "get Charsets result is: %v", utils.Show(ret))
	return ret, nil
}

func (self *mysqlImpl) ListCollations(ctx context.Context, req *datasource.ListCollationsReq) (*datasource.ListCollationsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListCollationsResp{}
	sql := "SELECT collation_name from information_schema.collations"

	if req.Keyword != "" {
		sql += fmt.Sprintf(" WHERE collation_name LIKE '%%%s%%'", req.Keyword)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	sql += ";"
	log.Info(ctx, sql)
	if err = conn.Raw(sql).Scan(&ret.Items); err != nil {
		return nil, err
	}
	ret.Total = int64(len(ret.Items))
	log.Info(ctx, "get collations %s", fmt.Sprintf("%#v", ret))
	return ret, nil
}

func (self *mysqlImpl) KillQuery(ctx context.Context, req *shared.DataSource, connection *shared.ConnectionInfo) error {
	conn, err := self.getConn(ctx, req)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Address, err)
		return err
	}
	defer conn.Close()
	log.Info(ctx, "kill query %v", connection.OuterConnectionId)
	return conn.Exec(`KILL QUERY ?`, connection.OuterConnectionId)
}

func parseColumnLength(columnType string) string {
	var length = ``
	if strings.Contains(columnType, "(") {
		var tmp = strings.Split(columnType, "(")[1] //  remove (
		length = strings.Split(tmp, ")")[0]         //  remove )
	} else if columnType == "varchar" || columnType == "char" {
		length = `1`
	}

	if strings.Split(columnType, "(")[0] == "year" {
		length = ``
	}
	return length
}

func parseColumnType(columnType string) string {
	var ret = strings.Split(columnType, "(")[0]
	return ret
}

func (self *mysqlImpl) CreateAccount(ctx context.Context, req *datasource.CreateAccountReq) error {
	accountType, err := rdsModel_v2.AccountTypeFromString(req.AccountType)
	if err != nil {
		log.Warn(ctx, "failed to identify account type , account type=%s, err=%v", req.AccountType, err.Error())
		return err
	}
	rreq := &rdsModel_v2.CreateDBAccountReq{
		InstanceId:      req.InstanceId,
		AccountName:     req.AccountName,
		AccountPassword: req.AccountPassword,
		AccountType:     accountType,
	}
	// TODO
	if err = self.mysql.Get().Call(ctx, rdsModel_v2.Action_CreateDBAccount.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to create account, instanceId=%s, err=%v", req.InstanceId, err.Error())
		if strings.Contains(err.Error(), "InvalidAccountName_Duplicate") {
			return nil
		}
		return err
	}
	return nil
}

func (self *mysqlImpl) DescribeAccounts(ctx context.Context, req *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp, error) {
	rreq := &rdsModel_v2.DescribeDBAccountsReq{
		InstanceId:  req.InstanceId,
		AccountName: utils.StringRef(req.AccountName),
		PageSize:    utils.Int32Ref(req.PageSize),
		PageNumber:  utils.Int32Ref(req.PageNumber),
	}
	rresp := &rdsModel_v2.DescribeDBAccountsResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBAccounts.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to describe accounts, instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	return &datasource.DescribeAccountResp{
		Total:        rresp.Total,
		AccountsInfo: rresp.AccountsInfo,
	}, nil
}

func (self *mysqlImpl) ModifyAccountPrivilege(ctx context.Context, req *datasource.ModifyAccountPrivilegeReq) error {
	rreq := &rdsModel_v2.ModifyDBAccountPrivilegeReq{
		InstanceId:  req.InstanceId,
		AccountName: req.AccountName,
	}
	fp.StreamOf(req.ModifyAccountPrivilegesInfo).Map(func(i *datasource.ModifyAccountPrivilegeInfo) *rdsModel_v2.ModifyAccountPrivilegesInfo {
		res := &rdsModel_v2.ModifyAccountPrivilegesInfo{
			DBName: i.DBName,
		}
		res.ActionType, _ = rdsModel_v2.ActionTypeFromString(i.ActionType)
		res.Privilege, _ = rdsModel_v2.PrivilegeTypeFromString(i.Privilege)
		return res
	}).ToSlice(&rreq.ModifyAccountPrivilegesInfo)
	//rresp := &rdsModel_v2.ModifyDBAccountPrivilegeResp{}
	if err := self.mysql.Get().Call(
		ctx,
		rdsModel_v2.Action_ModifyDBAccountPrivilege.String(),
		rreq,
		nil,
		client.WithTenantID("1"),
		client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to modify account privilege, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
		return err
	}
	return nil
}

func (self *mysqlImpl) GrantAccountPrivilege(ctx context.Context, req *datasource.GrantAccountPrivilegeReq) error {
	var accountPrivilege = rdsModel_v2.PrivilegeType_ReadWrite
	if req.AccountPrivilege != "" {
		ap, err := rdsModel_v2.PrivilegeTypeFromString(req.AccountPrivilege)
		if err != nil {
			return ErrInvalidPrivilegeType
		}
		accountPrivilege = ap
	}
	rreq := rdsModel_v2.GrantDBAccountPrivilegeReq{
		InstanceId:  req.InstanceId,
		AccountName: req.AccountName,
		AccountPrivileges: []*rdsModel_v2.AccountPrivilegeObject{
			{
				DBName:           req.DBName,
				AccountPrivilege: accountPrivilege,
			},
		},
	}
	if err := self.mysql.Get().Call(
		ctx,
		rdsModel_v2.Action_GrantDBAccountPrivilege.String(),
		rreq,
		nil,
		client.WithTenantID("1"),
		client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to grant account, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
		return err
	}
	return nil
}

func (self *mysqlImpl) DeleteAccount(ctx context.Context, req *datasource.DeleteAccountReq) error {
	rreq := &rdsModel_v2.DeleteDBAccountReq{
		InstanceId:  req.InstanceId,
		AccountName: req.AccountName,
	}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DeleteDBAccount.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2))
	if err == nil {
		return nil
	}
	if strings.Contains(strings.ToLower(err.Error()), "exist") || strings.Contains(strings.ToLower(err.Error()), "未找到该账号") {
		log.Warn(ctx, "delete account, instanceId=%s, account=%s, account is not exists，ignore error", req.InstanceId, req.AccountName)
		return nil
	}
	log.Warn(ctx, "failed to delete account, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
	return err
}

func (self *mysqlImpl) ListDatabasesWithAccount(ctx context.Context, req *datasource.ListDatabasesWithAccountReq) (*datasource.ListDatabasesWithAccountResp, error) {
	status := rdsModel.DBStatus_Running
	rreq := &rdsModel.ListDatabasesReq{
		InstanceId: req.InstanceId,
		DBName:     req.DBName,
		Limit:      req.PageSize,
		Offset:     (req.PageNumber - 1) * req.PageNumber,
		DBStatus:   &status,
	}
	rresp, err := self.ListAllDatabases(ctx, req, rreq)
	if err != nil {
		return nil, err
	}
	ret := &datasource.ListDatabasesWithAccountResp{
		Total:   rresp.Total,
		DBInfos: rresp.Datas,
	}
	return ret, nil
}

func (self *mysqlImpl) ListAllDatabases(ctx context.Context, req *datasource.ListDatabasesWithAccountReq, rreq *rdsModel.ListDatabasesReq) (*rdsModel.ListDatabasesResp, error) {
	pageInfo, err := ds_utils.FormatLowerPageInfo(req.PageNumber, req.PageSize)
	if err != nil {
		return nil, err
	}
	result := &rdsModel.ListDatabasesResp{Total: int32(math.MaxInt32), Datas: []*rdsModel.DBInfo{}}
	rreq.Limit = pageInfo.PageSize
	for pageNumber := pageInfo.PageNumLower; pageNumber <= pageInfo.PageNumUpper && (pageNumber-1)*pageInfo.PageSize < result.Total; pageNumber++ {
		rreq.Offset = (pageNumber - 1) * pageNumber
		resp, err := self.selfListDatabases(ctx, rreq)
		if err != nil {
			return nil, err
		}
		result.Total = resp.Total
		result.Datas = append(result.Datas, resp.Datas...)
	}
	if result.Total == int32(math.MaxInt32) {
		result.Total = 0
	}
	return result, nil
}

func (self *mysqlImpl) selfListDatabases(ctx context.Context, req *rdsModel.ListDatabasesReq) (*rdsModel.ListDatabasesResp, error) {
	resp := &rdsModel.ListDatabasesResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListDatabases.String(), req, resp); err != nil {
		log.Warn(ctx, "failed to list databases, instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	return resp, nil
}

type DBExplain struct {
	ID           int32
	SelectType   string
	Table        string
	Partitions   string
	Type         string
	PossibleKeys string
	Key          string
	KeyLen       string
	Ref          string
	Rows         string
	Filtered     string
	Extra        string
}

func (self *mysqlImpl) GetAdvice(ctx context.Context, req *datasource.GetAdviceReq) (*datasource.GetAdviceResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.GetAdviceResp{}
	// changeDB
	execSql := fmt.Sprintf("use `%s`", req.DB)
	err = conn.Exec(execSql)
	if err != nil {
		log.Warn(ctx, "Change DB %s fail because %s", req.DB, err)
		return nil, err
	}
	// get Explain
	queryExplain := fmt.Sprintf("%s explain %s", DBW_CONSOLE_DEFAULT_HINT, req.Sql)
	var explainInfos []*DBExplain
	err = conn.Raw(queryExplain).Scan(&explainInfos)
	if err != nil {
		log.Warn(ctx, "get explain from DB %s fail, because %v", req.DB, err)
		return nil, err
	}
	err = fp.StreamOf(explainInfos).Map(func(e *DBExplain) *shared.AdviceInfo_ExplainInfo {
		return &shared.AdviceInfo_ExplainInfo{
			Id: e.ID, SelectType: e.SelectType, Table: e.Table,
			Partitions: e.Partitions, Type: e.Type, PossibleKeys: e.PossibleKeys,
			Key: e.Key, KeyLen: e.KeyLen, Ref: e.Ref,
			Rows: e.Rows, Filtered: e.Filtered, Extra: e.Extra}
	}).ToSlice(&ret.Explains)
	if err != nil {
		return nil, err
	}
	return ret, nil
}
func (self *mysqlImpl) DescribeDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq,
) (*datasource.DescribeDialogInfosResp, error) {
	if req.Offset < 0 || req.Limit < 0 {
		return &datasource.DescribeDialogInfosResp{}, nil
	}
	ret := &datasource.DescribeDialogInfosResp{}
	dialogInfos, err := self.getAllDialogInfos(ctx, req)
	if err != nil {
		return nil, err
	}
	// get details info
	ret.DialogDetails = self.filterDialogDetails(ctx, dialogInfos, req.QueryFilter, req.Offset, req.Limit)
	return ret, nil

}
func (self *mysqlImpl) getAllDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) ([]*datasource.DialogInfo, error) {
	var dialogInfos []*datasource.DialogInfo
	// 获取所有pod节点
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "get InstancePods fail: %v", err)
		return nil, err
	}

	if req.Component == model.Component_DBEngine.String() {
		// 获取所有mysqld节点
		var pods []*shared.KubePod
		for _, pod := range listInstancePodsResp.Data {
			if pod.Component == "MySQL" {
				pods = append(pods, pod)
			}
		}
		if len(pods) < 1 {
			log.Warn(ctx, "No mysqld pod found")
			return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No mysqld pod found")
		}
		for _, node := range pods {
			var tempDialogInfos []*datasource.DialogInfo
			maSource := *req.Source
			maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, "3306")
			maSource.NodeId = node.NodeId
			conn, err := self.getConnV2(ctx, &maSource)
			if err != nil {
				log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
				if conn != nil {
					self.ConnPool.Put(ctx, conn)
				}
				continue
			}
			sql := consts.DBW_DIAGNOSIS_DEFAULT_HINT + " select * from information_schema.processlist "
			if len(req.InternalUsers) > 0 {
				sql += fmt.Sprintf("WHERE USER not in ('%s')", strings.Join(req.InternalUsers, "','"))
			}
			if err = conn.Raw(sql).Scan(&tempDialogInfos); err != nil {
				self.ConnPool.Put(ctx, conn)
				continue
			}
			self.ConnPool.Put(ctx, conn)
			// fill NULL column
			fp.StreamOf(tempDialogInfos).Foreach(func(d *datasource.DialogInfo) {
				if !d.Info.Valid {
					d.Info.String = "NULL"
					d.SqlTemplate, d.SqlTemplateID = datasource.GetSqlTemplate(ctx, "-")
					d.SqlType = "-"
				} else {
					d.SqlTemplate, d.SqlTemplateID = datasource.GetSqlTemplate(ctx, d.Info.String)
					sqlType, err := datasource.GetRdsSqlType(ctx, d.Info.String)
					if err != nil {
						d.SqlType = "-"
					} else {
						d.SqlType = sqlType
					}
				}
				if !d.BlockingPid.Valid {
					d.BlockingPid.String = "NULL"
				}
				d.NodeId = node.NodeId
				d.NodeType = node.Role
			}).ToSlice(&tempDialogInfos)
			dialogInfos = append(dialogInfos, tempDialogInfos...)
		}
	} else {
		// 查询proxy会话
		// 获取所有proxy节点
		var proxyDialList []*datasource.MySQLProxyDialog
		var pods []*shared.KubePod
		for _, pod := range listInstancePodsResp.Data {
			if pod.Component == "Proxy" {
				pods = append(pods, pod)
			}
		}
		if len(pods) < 1 {
			log.Warn(ctx, "No Proxy pod found")
			return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No proxy pod found")
		}
		for _, pod := range pods {
			for _, port := range RdsProxyPortsList {
				convertedPort := fmt.Sprintf("2%s", port) // 如3680->23680
				address := fmt.Sprintf("%s:%s", pod.PodIP, convertedPort)
				_, err := net.Dial("tcp", address) // 连通性测试
				if err == nil {
					url := fmt.Sprintf(consts.RDSProxyShowProcessList, address)
					var tmpDialInfo []*datasource.MySQLProxyDialog
					response, err := self.HttpGet(ctx, url)
					if err != nil {
						log.Warn(ctx, "get proxy %s processlist failed %+v", pod.Name, err)
						continue
					}
					if err := json.Unmarshal(response, &tmpDialInfo); err != nil {
						log.Warn(ctx, "proxy dialog res %s unmarshall failed: %v", string(response), err.Error())
						continue
					}
					for _, dialInfo := range tmpDialInfo {
						dialInfo.Port = port
						dialInfo.ProxyId = pod.Name
						proxyDialList = append(proxyDialList, dialInfo)
					}
				}
			}
		}
		if err := fp.StreamOf(proxyDialList).Map(func(db *datasource.MySQLProxyDialog) *datasource.DialogInfo {
			item := &datasource.DialogInfo{
				ProcessID: db.ProcessID,
				DB:        db.DB,
				Host:      db.Host,
				Command:   db.Command,
				User:      db.User,
				Time:      db.Time,
				State:     "", // proxy不支持
				NodeId:    db.ProxyId,
				NodeType:  "Proxy",
				Info: sql.NullString{
					String: db.Command,
					Valid:  true,
				},
			}
			item.SqlTemplate, item.SqlTemplateID = datasource.GetSqlTemplate(ctx, db.Command)
			sqlType, err := datasource.GetRdsSqlType(ctx, db.Command)
			if err != nil {
				item.SqlType = "-"
			} else {
				item.SqlType = sqlType
			}
			//endpointItem := self.getEndpointDetail(ctx, req.Source.InstanceId, db.Port)
			//item.EndpointId = endpointItem.EndpointId
			//item.EndpointName = endpointItem.EndpointName
			return item
		}).ToSlice(&dialogInfos); err != nil {
			return nil, err
		}
		// filter internal user
		dialogInfos = self.filterUser(ctx, dialogInfos, req.InternalUsers)
		// fill NULL column
		fp.StreamOf(dialogInfos).Foreach(func(d *datasource.DialogInfo) {
			if !d.Info.Valid {
				d.Info.String = "NULL"
			}
			if !d.BlockingPid.Valid {
				d.BlockingPid.String = "NULL"
			}
		}).ToSlice(&dialogInfos)
	}
	return dialogInfos, nil
}
func (self *mysqlImpl) filterHost(ctx context.Context, data []*datasource.DialogInfo, ipNets []string) []*datasource.DialogInfo {
	if len(ipNets) == 0 {
		return data
	}
	var ret []*datasource.DialogInfo
	var internalIPs []*net.IPNet
	for _, subnet := range ipNets {
		if _, ipNet, er := net.ParseCIDR(subnet); er == nil {
			internalIPs = append(internalIPs, ipNet)
		}
	}
	fp.Stream0Of(data).Reject(func(d *datasource.DialogInfo) bool {
		ip := net.ParseIP(strings.Split(d.Host, ":")[0])
		if ip == nil {
			return false
		}
		for _, ipNet := range internalIPs {
			if ipNet.Contains(ip) {
				return true
			}
		}
		return false
	}).ToSlice(&ret)
	return ret
}

//func (self *mysqlImpl) getRRDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq, pods *datasource.ListInstancePodsResp) ([]*datasource.DialogInfo, error) {
//	// 判断有没有只读节点
//	instanceNodesReq := &rdsModel.ListInstanceNodesReq{
//		InstanceId: req.Source.InstanceId,
//	}
//	instanceNodesResp := &rdsModel.ListInstanceNodesResp{}
//	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), instanceNodesReq, instanceNodesResp, client.WithVersion(RDS_MySQL_Version_V1)); err != nil {
//		log.Warn(ctx, "ListInstanceNodes failed instanceId=%s, err=%v", req.Source.InstanceId, err.Error())
//		return nil, err
//	}
//	log.Info(ctx, "Instance %s Nodes is %s", req.Source.InstanceId, instanceNodesResp)
//
//	var rrNodes []*rdsModel.InstanceNodeInfo
//	for _, node := range instanceNodesResp.Datas {
//		if node.NodeRole == rdsModel.NodeRole_ReadOnly {
//			rrNodes = append(rrNodes, node)
//		}
//	}
//	if len(rrNodes) == 0 {
//		return nil, nil
//	}
//
//	// 拿到rr podip去填充source
//	podIpMap, err := self.GetRRPodInfo(ctx, req.Source.InstanceId, rrNodes, pods)
//	if err != nil || len(podIpMap) != len(rrNodes) {
//		log.Error(ctx, "get GetRRPodInfo fail: %v", err)
//		return nil, err
//	}
//	// 直连rr pod，执行sql
//	var rrdialogInfos []*datasource.DialogInfo
//	for _, podInfo := range podIpMap {
//		rrSource := *req.Source
//		rrSource.Address = podInfo.podIp
//		src := self.addDsn(&rrSource)
//
//		// 只读pod上执行查询
//		conn, err := self.getConn(ctx, src)
//		if err != nil {
//			log.Warn(ctx, "connect to datasource %s fail %v", src.Address, err)
//			return nil, err
//		}
//		defer conn.Close()
//		sql := "/*+ DBW SQL CONSOLE DEFAULT*/ select p.*, IF(ISNULL(tl.blocking_pid),rl.blocking_pid,tl.blocking_pid) As BLOCKINGPID" +
//			" from information_schema.processlist p LEFT JOIN sys.schema_table_lock_waits tl ON p.Id=tl.waiting_pid " +
//			"LEFT JOIN sys.innodb_lock_waits rl ON p.Id=rl.waiting_pid "
//		if len(req.InternalUsers) > 0 {
//			sql += fmt.Sprintf("WHERE USER not in ('%s')", strings.Join(req.InternalUsers, "','"))
//		}
//		var dialogInfos []*datasource.DialogInfo
//		if err = conn.Raw(sql).Scan(&dialogInfos); err != nil {
//			return nil, err
//		}
//		rrdialogInfos = append(rrdialogInfos, dialogInfos...)
//	}
//
//	return rrdialogInfos, nil
//}
//
//func (self *mysqlImpl) getRRDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq, detail []*rdsModel.InstanceNodeInfo, pods *datasource.ListInstancePodsResp) ([]*datasource.DialogInfo, error) {
//	// 判断有没有只读节点
//	var rrNodes []*rdsModel.InstanceNodeInfo
//	log.Info(ctx, "DescribeDialogInfos getRRDialogInfos1: %s", detail)
//	for _, node := range detail {
//		if node.NodeRole == rdsModel.NodeRole_ReadOnly {
//			rrNodes = append(rrNodes, node)
//		}
//	}
//	log.Info(ctx, "DescribeDialogInfos getRRDialogInfos2: %s", rrNodes)
//
//	if len(rrNodes) == 0 {
//		return nil, nil
//	}
//
//	// 拿到rr podip去填充source
//	podIpMap, err := self.GetRRPodInfo(ctx, req.Source.InstanceId, rrNodes, pods)
//	if err != nil || len(podIpMap) != len(rrNodes) {
//		log.Error(ctx, "get GetRRPodInfo fail: %v", err)
//		return nil, err
//	}
//	// 直连rr pod，执行sql
//	var rrdialogInfos []*datasource.DialogInfo
//	for _, podInfo := range podIpMap {
//		rrSource := *req.Source
//		rrSource.Address = podInfo.podIp
//		src := self.addDsn(&rrSource)
//		// 只读pod上执行查询
//		conn, err := self.getConn(ctx, src)
//		if err != nil {
//			log.Warn(ctx, "connect to datasource %s fail %v", src.Address, err)
//			return nil, err
//		}
//		defer conn.Close()
//		sql := "/*+ DBW SQL CONSOLE DEFAULT*/ select p.*, IF(ISNULL(tl.blocking_pid),rl.blocking_pid,tl.blocking_pid) As BLOCKINGPID" +
//			" from information_schema.processlist p LEFT JOIN sys.schema_table_lock_waits tl ON p.Id=tl.waiting_pid " +
//			"LEFT JOIN sys.innodb_lock_waits rl ON p.Id=rl.waiting_pid "
//		if len(req.InternalUsers) > 0 {
//			sql += fmt.Sprintf("WHERE USER not in ('%s')", strings.Join(req.InternalUsers, "','"))
//		}
//		var dialogInfos []*datasource.DialogInfo
//		if err = conn.Raw(sql).Scan(&dialogInfos); err != nil {
//			return nil, err
//		}
//		for i, _ := range dialogInfos {
//			dialogInfos[i].NodeType = model.NodeRole_ReadOnly.String()
//			dialogInfos[i].NodeId = podInfo.nodeId
//		}
//		rrdialogInfos = append(rrdialogInfos, dialogInfos...)
//	}
//
//	return rrdialogInfos, nil
//}
//
//type rrPodMap struct {
//	podIp  string
//	nodeId string
//}
//
//func (self *mysqlImpl) GetRRPodInfo(ctx context.Context, instanceID string, nodeList []*rdsModel.InstanceNodeInfo, pods *datasource.ListInstancePodsResp) ([]rrPodMap, error) {
//	var ipMap []rrPodMap
//	podList := pods.Data
//	for _, pod := range podList {
//		if pod.Component == "MySQL" && pod.Role == model.NodeType_ReadOnly.String() && pod.PodIP != "" {
//			for _, node := range nodeList {
//				if pod.Name == node.NodeName {
//					ipMap = append(ipMap, rrPodMap{
//						podIp:  pod.PodIP,
//						nodeId: node.NodeId,
//					})
//				}
//			}
//		}
//	}
//	return ipMap, nil
//}

func (self *mysqlImpl) addDsn(ds *shared.DataSource) *shared.DataSource {
	if ds == nil {
		return ds
	}
	if ds.ExtraDsn == nil {
		ds.ExtraDsn = map[string]string{}
	}
	switch ds.Type {
	case shared.MySQL:
		ds.ExtraDsn["multiStatements"] = "true"
	}
	ds.MaxOpenConns = 1
	ds.MaxIdleConns = 0
	return ds
}

//func (self *mysqlImpl) GiveBackInstanceSession(ctx context.Context, cli cli.ActorClient, instanceId string, sessionId string) {
//	cli.KindOf(consts.SessionMgrActorKind).
//		Send(ctx, instanceId, &shared.GiveBackSession{
//			InstanceId: instanceId,
//			SessionId:  sessionId,
//		})
//	log.Info(ctx, "give back session %s", sessionId)
//}

func (self *mysqlImpl) filterUser(ctx context.Context, data []*datasource.DialogInfo, users []string) []*datasource.DialogInfo {
	if len(users) == 0 {
		return data
	}
	var ret []*datasource.DialogInfo
	fp.StreamOf(data).Reject(func(d *datasource.DialogInfo) bool {
		for _, user := range users {
			if d.User == user {
				return true
			}
		}
		return false
	}).ToSlice(&ret)
	return ret
}

func (self *mysqlImpl) getDialogStatistics(ctx context.Context, data []*datasource.DialogInfo, topN int32) *shared.DialogStatistics {
	userInfo := make(map[string]*datasource.UserAggregatedInfo)
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	dbInfo := make(map[string]*datasource.DBAggregatedInfo)
	psmInfo := make(map[string]*datasource.PSMAggregatedInfo)
	var activeConn, totalConn int32 = 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(data).Foreach(func(d *datasource.DialogInfo) {
		totalConn += 1
		// 提取psm
		psmItem := datasource.ExtractRdsPsm(d.Info.String)
		if _, ok := psmInfo[psmItem.Psm]; !ok {
			psmInfo[psmItem.Psm] = &datasource.PSMAggregatedInfo{PSM: psmItem.Psm}
		}
		psmInfo[psmItem.Psm].TotalConn += 1

		if _, ok := userInfo[d.User]; !ok {
			userInfo[d.User] = &datasource.UserAggregatedInfo{User: d.User}
		}
		userInfo[d.User].TotalConn += 1
		ip := datasource.ExtractIP(d.Host)
		log.Info(ctx, "ExtractIP former: %s, after: %s", d.Host, ip)
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1
		if _, ok := dbInfo[d.DB]; !ok {
			dbInfo[d.DB] = &datasource.DBAggregatedInfo{DB: d.DB}
		}
		dbInfo[d.DB].TotalConn += 1
		if strings.ToLower(d.Command) != "sleep" {
			activeConn += 1
			userInfo[d.User].ActiveConn += 1
			ipInfo[ip].ActiveConn += 1
			dbInfo[d.DB].ActiveConn += 1
			psmInfo[psmItem.Psm].ActiveConn += 1
		}
	}).Run()

	var userList []*shared.UserAggregatedInfo
	var ipList []*shared.IPAggregatedInfo
	var dbList []*shared.DBAggregatedInfo
	var psmList []*shared.PSMAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(userInfo).ZipMap(func(k string, v *datasource.UserAggregatedInfo) *shared.UserAggregatedInfo {
		return &shared.UserAggregatedInfo{
			User:              v.User,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&userList)
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)
	fp.KVStreamOf(dbInfo).ZipMap(func(k string, v *datasource.DBAggregatedInfo) *shared.DBAggregatedInfo {
		return &shared.DBAggregatedInfo{
			DB:                v.DB,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&dbList)
	fp.KVStreamOf(psmInfo).ZipMap(func(k string, v *datasource.PSMAggregatedInfo) *shared.PSMAggregatedInfo {
		return &shared.PSMAggregatedInfo{
			PSM:               v.PSM,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&psmList)
	sort.Slice(userList, func(i, j int) bool {
		if userList[i].TotalConnections > userList[j].TotalConnections {
			return true
		}
		if userList[i].TotalConnections == userList[j].TotalConnections &&
			userList[i].ActiveConnections == userList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(dbList, func(i, j int) bool {
		if dbList[i].TotalConnections > dbList[j].TotalConnections {
			return true
		}
		if dbList[i].TotalConnections == dbList[j].TotalConnections &&
			dbList[i].ActiveConnections == dbList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(psmList, func(i, j int) bool {
		if psmList[i].TotalConnections > psmList[j].TotalConnections {
			return true
		}
		if psmList[i].TotalConnections == psmList[j].TotalConnections &&
			psmList[i].ActiveConnections == psmList[j].ActiveConnections {
			return true
		}
		return false
	})

	return &shared.DialogStatistics{
		DialogOver: &shared.DialogOverview{
			ActiveConnections: activeConn,
			TotalConnections:  totalConn,
		},
		UserAggregatedInfo: userList[:fp.MinInt(int(topN), len(userList))],
		IPAggregatedInfo:   ipList[:fp.MinInt(int(topN), len(ipList))],
		DBAggregatedInfo:   dbList[:fp.MinInt(int(topN), len(dbList))],
		PSMAggregatedInfo:  psmList[:fp.MinInt(int(topN), len(psmList))],
	}
}

func (self *mysqlImpl) filterDialogDetails(ctx context.Context, data []*datasource.DialogInfo, queryFilter *shared.DialogQueryFilter, offset int64, limit int64) *shared.DialogDetails {
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.DialogInfo) bool {
				return strings.ToLower(d.Command) == "sleep"
			}).ToSlice(&tData)
		}
		if pID := queryFilter.ProcessID; pID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.ProcessID, pID)
			}).ToSlice(&tData)
		}
		if user := queryFilter.User; user != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return exactContains(strings.Split(user, ","), d.User, queryFilter.GetIsFuzzy())
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return filterIpList(strings.Split(host, ","), d.Host, queryFilter.GetIsFuzzy())
			}).ToSlice(&tData)
		}
		if fDB := queryFilter.DB; fDB != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return exactContains(strings.Split(fDB, ","), d.DB, queryFilter.GetIsFuzzy())
			}).ToSlice(&tData)
		}
		if command := queryFilter.Command; command != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Command), strings.ToLower(command))
			}).ToSlice(&tData)
		}
		if state := queryFilter.State; state != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.State, state)
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if nodeType := queryFilter.NodeType; nodeType != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.NodeType, nodeType)
			}).ToSlice(&tData)
		}
		if timeLimit := queryFilter.LowerExecTimeLimit; timeLimit != "" {
			//limitInt, er := strconv.Atoi(queryFilter.LowerExecTimeLimit)
			limitFloat, er := strconv.ParseFloat(queryFilter.LowerExecTimeLimit, 64)
			if er == nil {
				fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
					execTime, _ := strconv.ParseFloat(d.Time, 64)
					return execTime >= limitFloat
				}).ToSlice(&tData)
			}
		}
		if info := queryFilter.Info; info != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Info.String), strings.ToLower(info))
			}).ToSlice(&tData)
		}
		if psm := queryFilter.PSM; psm != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				targetPSM := datasource.ExtractRdsPsm(d.Info.String)
				return strings.Contains(strings.ToLower(targetPSM.Psm), strings.ToLower(psm))
			}).ToSlice(&tData)
		}
		if sqlTemplate := queryFilter.SqlTemplate; sqlTemplate != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.SqlTemplate, sqlTemplate)
			}).ToSlice(&tData)
		}
		if sqlTemplateId := queryFilter.SqlTemplateID; sqlTemplateId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.SqlTemplateID, sqlTemplateId)
			}).ToSlice(&tData)
		}
		if sqlType := queryFilter.SqlType; sqlType != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return exactContains(strings.Split(sqlType, ","), d.SqlType, queryFilter.GetIsFuzzy())
			}).ToSlice(&tData)
		}
		// 过滤多节点(支持shard)
		if nodeIdList := queryFilter.NodeIds; len(nodeIdList) > 0 {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				var existed bool
				for _, nodeId := range nodeIdList {
					if d.NodeId == nodeId {
						existed = true
						break
					}
				}
				return existed
			}).ToSlice(&tData)
		}
	}
	// 默认按Time倒序
	datasource.SortDialog(tData, shared.DESC, "Time")
	var details []*shared.DialogDetail
	fp.StreamOf(tData[offset:fp.MinInt64(offset+limit, int64(len(tData)))]).
		Map(func(d *datasource.DialogInfo) *shared.DialogDetail {
			psmItem := datasource.ExtractRdsPsm(d.Info.String)
			return &shared.DialogDetail{
				ProcessID:     d.ProcessID,
				User:          d.User,
				Host:          d.Host,
				DB:            d.DB,
				Command:       d.Command,
				Time:          d.Time,
				State:         d.State,
				Info:          d.Info.String,
				BlockingPid:   d.BlockingPid.String,
				NodeId:        d.NodeId,
				NodeType:      d.NodeType,
				PSM:           psmItem.Psm,
				EndpointName:  d.EndpointName,
				EndpointId:    d.EndpointId,
				SqlTemplate:   d.SqlTemplate,
				SqlTemplateID: d.SqlTemplateID,
			}
		}).ToSlice(&details)

	return &shared.DialogDetails{
		Details: details,
		Total:   int32(len(details)),
	}
}

// host格式为IP:port，需要提取Ip
func filterIpList(slice []string, host string, isFuzzy bool) bool {
	if isFuzzy {
		// 模糊查询host
		for _, item := range slice {
			if strings.Contains(strings.ToLower(host), strings.ToLower(item)) ||
				strings.Contains(strings.ToLower(item), strings.ToLower(host)) {
				return true
			}
		}
		return false
	} else {
		//精确匹配IP,忽略port
		addrSlice := strings.Split(host, ":")
		// 兜底
		if len(addrSlice) < 2 {
			return false
		}
		targetIp := strings.Join(addrSlice[:len(addrSlice)-1], ":")
		for _, item := range slice {
			if item == targetIp {
				return true
			}
		}
		return false
	}
}
func exactContains(slice []string, target string, isFuzzy bool) bool {
	if isFuzzy {
		for _, item := range slice {
			if strings.Contains(strings.ToLower(target), strings.ToLower(item)) {
				return true
			}
		}
		return false
	} else {
		for _, item := range slice {
			if item == target {
				return true
			}
		}
		return false
	}
}
func (self *mysqlImpl) filterTrxAndLocks(ctx context.Context, data []*shared.TrxAndLock, queryFilter *model.TrxQueryFilter, nodeIds []string) []*shared.TrxAndLock {
	tData := data
	if len(nodeIds) > 0 {
		fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
			var existed bool
			for _, nodeId := range nodeIds {
				if d.NodeId == nodeId {
					existed = true
					break
				}
			}
			return existed
		}).ToSlice(&tData)
	}
	if queryFilter != nil {
		if trxId := queryFilter.GetTrxId(); trxId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.TrxId), strings.ToLower(trxId))
			}).ToSlice(&tData)
		}
		if pId := queryFilter.GetProcessId(); pId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.ProcessId), strings.ToLower(pId))
			}).ToSlice(&tData)
		}
		if blockTrxId := queryFilter.GetBlockTrxId(); blockTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.BlockTrxId), strings.ToLower(blockTrxId))
			}).ToSlice(&tData)
		}

		if blockSql := queryFilter.GetSqlBlocked(); blockSql != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.SqlBlocked), strings.ToLower(blockSql))
			}).ToSlice(&tData)
		}
		if queryFilter.IsSetTrxExecTime() {
			trxExecTime := queryFilter.GetTrxExecTime()
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				execTime := d.TrxExecTime
				return execTime > trxExecTime

			}).ToSlice(&tData)

		}
	}
	return tData
}

func (self *mysqlImpl) filterWaitLockDetails(ctx context.Context, data []*datasource.LockCurrentWaitsDetail, queryFilter *model.WaitLockQueryFilter, nodeIds []string) *datasource.DescribeLockCurrentWaitsResp {
	mp1 := map[string]string{
		"RUNNING":      "RUNNING",
		"LOCKWAIT":     "LOCK WAIT",
		"ROLLING_BACK": "ROLLING BACK",
		"COMMITTING":   "COMMITTING",
	}
	tData := data
	if len(nodeIds) > 0 {
		fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
			var existed bool
			for _, nodeId := range nodeIds {
				if d.NodeId == nodeId {
					existed = true
					break
				}
			}
			return existed
		}).ToSlice(&tData)
	}
	if queryFilter != nil {
		if rTrxState := queryFilter.GetRTrxState(); rTrxState != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.RTrxState), strings.ToLower(mp1[rTrxState]))
			}).ToSlice(&tData)
		}
		if bTrxState := queryFilter.GetBTrxState(); bTrxState != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.BTrxState), strings.ToLower(mp1[bTrxState]))
			}).ToSlice(&tData)
		}
		if rTrxId := queryFilter.GetRTrxId(); rTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.RTrxId), strings.ToLower(rTrxId))
			}).ToSlice(&tData)
		}
		if bTrxId := queryFilter.GetBTrxId(); bTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.BTrxId), strings.ToLower(bTrxId))
			}).ToSlice(&tData)
		}

		if rWaitingQuery := queryFilter.GetRWaitingQuery(); rWaitingQuery != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.RWaitingQuery), strings.ToLower(rWaitingQuery))
			}).ToSlice(&tData)
		}
		if blockingQuery := queryFilter.GetBBlockingQuery(); blockingQuery != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.BBlockingQuery), strings.ToLower(blockingQuery))
			}).ToSlice(&tData)
		}
	}
	var details []*model.DescribeLockCurrentWaitsDetail
	fp.StreamOf(tData[0:fp.MinInt64(10000, int64(len(tData)))]).
		Map(func(val *datasource.LockCurrentWaitsDetail) *model.DescribeLockCurrentWaitsDetail {
			return &model.DescribeLockCurrentWaitsDetail{
				RTrxMysqlThreadId:  val.RTrxMysqlThreadId,
				RTrxId:             val.RTrxId,
				RTrxState:          val.RTrxState,
				RWaitingQuery:      val.RWaitingQuery,
				RTrxStarted:        val.RTrxStarted,
				RTrxWaitStarted:    val.RTrxWaitStarted,
				RBlockedWaitSecs:   val.RBlockedWaitSecs,
				RTrxRowsModified:   val.RTrxRowsModified,
				RTrxRowsLocked:     val.RTrxRowsLocked,
				RTrxOperationState: val.RTrxOperationState,
				BTrxMysqlThreadId:  val.BTrxMysqlThreadId,
				BTrxId:             val.BTrxId,
				BTrxState:          val.BTrxState,
				BBlockingQuery:     val.BBlockingQuery,
				BTrxStarted:        val.BTrxStarted,
				BTrxWaitStarted:    val.BTrxWaitStarted,
				BBlockingWaitSecs:  val.BBlockingWaitSecs,
				BTrxRowsModified:   val.BTrxRowsModified,
				BTrxRowsLocked:     val.BTrxRowsLocked,
				BTrxOperationState: val.BTrxOperationState,
				DbName:             val.DbName,
				NodeId:             val.NodeId,
			}
		}).ToSlice(&details)

	return &datasource.DescribeLockCurrentWaitsResp{
		Result: details,
		Total:  int32(len(details)),
	}
}
func (self *mysqlImpl) DescribeDialogDetails(ctx context.Context, req *datasource.DescribeDialogDetailsReq,
) (*datasource.DescribeDialogDetailsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.DescribeDialogDetailsResp{}

	sql := "/*+ DBW DAS DEFAULT*/ select p.*, IF(ISNULL(tl.blocking_pid),rl.blocking_pid,tl.blocking_pid) As BLOCKINGPID" +
		" from information_schema.processlist p LEFT JOIN sys.schema_table_lock_waits tl ON p.Id=tl.blocking_pid " +
		"LEFT JOIN sys.innodb_lock_waits rl ON p.Id=rl.blocking_pid "

	var whereConds []string
	if len(req.InternalUsers) > 0 {
		whereConds = append(whereConds, fmt.Sprintf("USER not in ('%s')", strings.Join(req.InternalUsers, "','")))
	}
	if req.QueryFilter != nil {
		filter := req.QueryFilter
		if filter.GetShowSleepConnection() == "false" {
			whereConds = append(whereConds, "COMMAND != 'SLEEP'")
		}
		if filter.ProcessID != "" {
			whereConds = append(whereConds, fmt.Sprintf("ID like '%%%s%%'", filter.ProcessID))
		}
		if filter.User != "" {
			whereConds = append(whereConds, fmt.Sprintf("USER like '%%%s%%'", filter.User))
		}
		if filter.Host != "" {
			whereConds = append(whereConds, fmt.Sprintf("HOST like '%%%s%%'", filter.Host))
		}
		if filter.DB != "" {
			whereConds = append(whereConds, fmt.Sprintf("DB like '%%%s%%'", filter.DB))
		}
		if filter.Command != "" {
			whereConds = append(whereConds, fmt.Sprintf("COMMAND like '%%%s%%'", filter.Command))
		}
		if filter.LowerExecTimeLimit != "" {
			whereConds = append(whereConds, fmt.Sprintf("TIME > %v", filter.LowerExecTimeLimit))
		}
		if filter.State != "" {
			whereConds = append(whereConds, fmt.Sprintf("STATE like '%%%s%%'", filter.State))
		}
		if filter.Info != "" {
			if filter.Info == "NULL" {
				whereConds = append(whereConds, fmt.Sprintf("INFO is null"))
			} else {
				whereConds = append(whereConds, fmt.Sprintf("INFO like '%%%s%%'", filter.Info))
			}
		}
	}
	whereSql := ""
	if len(whereConds) > 0 {
		whereSql = "WHERE " + strings.Join(whereConds, " AND ")
		sql += whereSql
	}
	//if req.Limit >= 0 && req.Offset >= 0 {
	//	sql += fmt.Sprintf(" LIMIT %v,%v", req.Offset, req.Limit)
	//}

	var dialogInfos []*datasource.DialogInfo
	if err = conn.Raw(sql).Scan(&dialogInfos); err != nil {
		return nil, err
	}

	// filter internal ip
	dialogInfos = self.filterHost(ctx, dialogInfos, req.InternalIPs)

	ret.Total = int32(len(dialogInfos))
	targetDialog := dialogInfos[req.Offset:fp.MinInt64(req.Offset+req.Limit, int64(len(dialogInfos)))]

	fp.StreamOf(targetDialog).Map(func(d *datasource.DialogInfo) *shared.DialogDetail {
		info := "NULL"
		if d.Info.Valid {
			info = d.Info.String
		}
		blockingPid := "NULL"
		if d.BlockingPid.Valid {
			blockingPid = d.BlockingPid.String
		}
		return &shared.DialogDetail{
			ProcessID:   d.ProcessID,
			User:        d.User,
			Host:        d.Host,
			DB:          d.DB,
			Command:     d.Command,
			Time:        d.Time,
			State:       d.State,
			Info:        info,
			BlockingPid: blockingPid,
		}
	}).ToSlice(&ret.DialogDetails)

	return ret, nil
}

func (self *mysqlImpl) HttpGet(ctx context.Context, url string) ([]byte, error) {
	// 发起 GET 请求
	response, err := http.Get(url)
	if err != nil {
		log.Warn(ctx, "")
		return nil, err
	}
	defer response.Body.Close()
	// 读取响应内容
	res, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	return res, nil
}

type Status struct {
	Name  string `gorm:"column:Variable_name"`
	Value int32  `gorm:"column:Value"`
}

func (self *mysqlImpl) DescribeDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq,
) (ret *datasource.DescribeDialogStatisticsResp, err error) {
	dialogInfos, err := self.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:        req.Source,
		Component:     req.Component,
		QueryFilter:   req.QueryFilter,
		InternalUsers: req.InternalUsers,
	})
	if err != nil {
		return nil, err
	}
	// get aggregated info
	statistics := self.getDialogStatistics(ctx, dialogInfos, req.TopN)

	ret = &datasource.DescribeDialogStatisticsResp{
		DialogStatistics: statistics,
	}
	return ret, nil
}

type EngineStatus struct {
	Type   string `gorm:"column:Type"`
	Name   string `gorm:"column:Name"`
	Status string `gorm:"column:Status"`
}

func (self *mysqlImpl) DescribeEngineStatus(ctx context.Context, req *datasource.DescribeEngineStatusReq) (*datasource.DescribeEngineStatusResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	sql := "/*+ DBW DAS DEFAULT*/ show engine innodb status"
	status := &EngineStatus{}
	if err = conn.Raw(sql).Scan(status); err != nil {
		return nil, err
	}
	ret := &datasource.DescribeEngineStatusResp{
		EngineStatus: &shared.EngineStatus{
			Type:   status.Type,
			Name:   status.Name,
			Status: status.Status,
		},
	}
	return ret, nil
}

func (self *mysqlImpl) KillProcess(ctx context.Context, req *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	cnf := self.cnf.Get(ctx)
	ret := &datasource.KillProcessResp{}
	if cnf.EnableNewConnectionPool {
		conn, err := self.getConnV2(ctx, req.Source)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
			return nil, err
		}
		for _, processID := range req.ProcessIDs {
			if err = conn.Exec("KILL ?", processID); err != nil {
				// 不存在的threadId报错不统计
				if !strings.Contains(strings.ToLower(err.Error()), strings.ToLower("unknown thread id")) {
					ret.FailInfoList = append(ret.FailInfoList, &shared.KillFailInfo{
						ProcessId:    processID,
						ErrorMessage: err.Error(),
					})
				}
			}
		}
		defer self.ConnPool.Put(ctx, conn)
		return ret, nil
	} else {
		conn, err := self.getConn(ctx, req.Source)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
			return nil, err
		}
		defer conn.Close()
		for _, processID := range req.ProcessIDs {
			if err = conn.Exec("KILL ?", processID); err != nil {
				ret.FailInfoList = append(ret.FailInfoList, &shared.KillFailInfo{
					ProcessId:    processID,
					ErrorMessage: err.Error(),
				})
			}
		}
		return ret, nil
	}
}

func (self *mysqlImpl) DescribeTrxAndLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) (*datasource.DescribeTrxAndLocksResp, error) {
	trxAndLockList, err := self.getAllTrxLocks(ctx, req)
	if err != nil {
		return nil, err
	}
	filtedAndLockList := self.filterTrxAndLocks(ctx, trxAndLockList, req.SearchParam, req.NodeIds)
	ret := &datasource.DescribeTrxAndLocksResp{
		Result: &shared.DescribeTrxAndLocksInfo{
			TrxAndLockList: filtedAndLockList,
			Total:          int32(len(filtedAndLockList)),
		},
	}
	return ret, nil
}

func getRdsVersion(ctx context.Context, conn datasource.Conn) string {
	// 查询mysql内核版本
	var versionSql = "/*+ DBW DAS DEFAULT*/ select version() as version;"
	var MysqlVersion = DBVersion{}
	if err := conn.Raw(versionSql).Scan(&MysqlVersion); err != nil {
		log.Warn(ctx, "get mysql version fail %v", err)
		return "MySQL_5_7"
	}
	/* version = 5.7x */
	if strings.Contains(MysqlVersion.Version, "5.7") {
		return "MySQL_5_7"
	}
	if strings.Contains(MysqlVersion.Version, "8.0") {
		return "MySQL_8_0"
	} else {
		return "MySQL_5_7" // 其他情况默认按5.7内核版本查询
	}
}

func (self *mysqlImpl) DescribeLockCurrentWaits(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) (*datasource.DescribeLockCurrentWaitsResp, error) {
	waitLocks, err := self.getAllWaitLocks(ctx, req)
	if err != nil {
		return nil, err
	}
	// get details info
	ret := self.filterWaitLockDetails(ctx, waitLocks, req.SearchParam, req.NodeIds)
	log.Info(ctx, "lockWaits list is %s", utils.Show(ret))
	return ret, nil
}
func (self *mysqlImpl) getAllWaitLocks(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) ([]*datasource.LockCurrentWaitsDetail, error) {
	var waitLockList []*datasource.LockCurrentWaitsDetail
	mp := map[string]string{
		"RTrxStarted":      "r_trx_started",
		"RTrxWaitStarted":  "r_trx_wait_started",
		"RTrxRowsLocked":   "r_trx_rows_locked",
		"RTrxRowsModified": "r_trx_rows_modified",
		"RBlockedWaitSecs": "r_blocked_wait_secs",
	}
	// 获取所有pod节点
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "get InstancePods fail: %v", err)
		return nil, err
	}
	// 获取所有mysqld节点
	var pods []*shared.KubePod
	for _, pod := range listInstancePodsResp.Data {
		if pod.Component == "MySQL" && pod.Role == rdsModel_v2.NodeType_Primary.String() {
			pods = append(pods, pod)
		}
	}
	if len(pods) < 1 {
		log.Warn(ctx, "No mysqld pod found")
		return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No mysqld pod found")
	}
	whereConditions, args := generateLockWaitSearchWhereConditions(req.SearchParam)
	whereConditions += ` order by ` + mp[req.SortParam] + " " + req.Order
	for _, node := range pods {
		var (
			tempLocks []*datasource.LockCurrentWaitsDetail
			querySql  string
		)
		rdsVersion := DBVersion{}
		versionSql := datasource.DbwDasHint + "select version() as version;"
		maSource := *req.Source
		maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, "3306")
		maSource.NodeId = node.NodeId
		conn, err := self.getConnV2(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			if conn != nil {
				self.ConnPool.Put(ctx, conn)
			}
			continue
		}
		if err := conn.Raw(versionSql).Scan(&rdsVersion); err != nil {
			log.Warn(ctx, "get rds version fail %v", err)
			return nil, err
		}
		if strings.Contains(rdsVersion.Version, "5.7") {
			querySql = MySQL57WaitLockQuery + whereConditions + " limit 10000" // ignore_security_alert
		} else {
			log.Info(ctx, "skip 8.0 version lockWait")
			self.ConnPool.Put(ctx, conn)
			return waitLockList, nil
			//querySql = MySQL80WaitLockQuery + whereConditions + " limit 10000" // ignore_security_alert
		}
		log.Info(ctx, "exec sql is %s", querySql)
		if err = conn.Raw(querySql, args...).Scan(&tempLocks); err != nil {
			self.ConnPool.Put(ctx, conn)
			continue
		}
		self.ConnPool.Put(ctx, conn)
		// fill NULL column
		fp.StreamOf(tempLocks).Foreach(func(d *datasource.LockCurrentWaitsDetail) {
			d.NodeId = node.NodeId
			d.RTrxWaitStarted = convertTimeStr(d.RTrxWaitStarted)
			d.RTrxStarted = convertTimeStr(d.RTrxStarted)
			d.BTrxStarted = convertTimeStr(d.BTrxStarted)
		}).ToSlice(&tempLocks)
		waitLockList = append(waitLockList, tempLocks...)
	}
	return waitLockList, nil
}
func (self *mysqlImpl) getAllTrxLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) ([]*shared.TrxAndLock, error) {
	var (
		pods         []*shared.KubePod
		trxLocksList []*shared.TrxAndLock
		querySql     string
	)
	// 获取所有pod节点
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "get InstancePods fail: %v", err)
		return nil, err
	}
	// 仅查询主节点的事务
	for _, pod := range listInstancePodsResp.Data {
		if pod.Component == "MySQL" && pod.Role == rdsModel_v2.NodeType_Primary.String() {
			pods = append(pods, pod)
		}
	}
	if len(pods) < 1 {
		log.Warn(ctx, "No mysqld pod found")
		return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No mysqld pod found")
	}
	whereConditions, args := generateTrxAndLockSearchWhereConditions(req.SearchParam)
	/* sort */
	if req.SortParam != "<UNSET>" {
		whereConditions += ` order by ` + req.SortParam + " " + req.Order
	}
	for _, node := range pods {
		maSource := *req.Source
		maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, "3306")
		maSource.NodeId = node.NodeId
		conn, err := self.getConnV2(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			if conn != nil {
				self.ConnPool.Put(ctx, conn)
			}
			continue
		}
		// 确定内核版本
		rdsVersion := getRdsVersion(ctx, conn)
		var TrxAndLocklist []TrxAndLock
		// 查询事务
		querySql = TrxQuery + whereConditions
		log.Info(ctx, "exec sql is %s", querySql)
		if err = conn.Raw(querySql, args...).Scan(&TrxAndLocklist); err != nil {
			self.ConnPool.Put(ctx, conn)
			continue
		}
		log.Info(ctx, "node %s TrxAndLocklist is %s", node.NodeId, utils.Show(TrxAndLocklist))
		if strings.Contains(rdsVersion, "MySQL_5_7") {
			var waitLocks57 []*datasource.LockWait57Version
			log.Info(ctx, "execute trx 5.7 sql is %s", WaitSql57)
			//获取锁信息
			lockList, err := self.describeLock(ctx, conn)
			if err != nil {
				log.Warn(ctx, "get lock fail: %v", err)
				self.ConnPool.Put(ctx, conn)
				return nil, err
			}
			// 查询是否存在锁阻塞
			if err := conn.Raw(WaitSql57).Scan(&waitLocks57); err != nil {
				log.Warn(ctx, "get 5.7 lock wait fail: %v", err)
				self.ConnPool.Put(ctx, conn)
				return nil, err
			}
			for _, TL := range TrxAndLocklist {
				var (
					lockStatus       shared.LockStatus
					trxWaitStartTime string
					blockTrxId       string
				)
				if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockHoldAndWait
				} else if TL.TrxLockStructs == 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockWait
				} else if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId == "" {
					lockStatus = shared.LockHold
				} else {
					lockStatus = shared.None
				}
				/* TrxWaitStartTime maybe NULL */
				if TL.TrxWaitStartTime.Format("2006-01-02 15:04:05") == "0001-01-01 00:00:00" {
					trxWaitStartTime = ""
				} else {
					trxWaitStartTime = TL.TrxWaitStartTime.Format("2006-01-02 15:04:05")
				}
				// 匹配事务是否存在锁阻塞
				for _, waitItem := range waitLocks57 {
					if TL.TrxRequestedLockId == waitItem.RequestingLockId {
						blockTrxId = waitItem.BlockTrxId
						break
					}
				}
				tLockList := lockList
				// 匹配事务是否持有锁
				fp.StreamOf(tLockList).Filter(func(d *shared.Lock) bool {
					return d.TrxId == TL.TrxId
				}).ToSlice(&tLockList)
				//lockList2 := make([]*shared.Lock, 0)
				//lockList2 = append(lockList2, tLockList...)
				trxLocksList = append(trxLocksList, &shared.TrxAndLock{
					ProcessId:        TL.ProcessId,
					TrxId:            TL.TrxId,
					TrxStatus:        TL.TrxState,
					TrxIsoLevel:      TL.TrxIsoLevel,
					TrxStartTime:     TL.TrxStartTime.Format("2006-01-02 15:04:05"),
					TrxWaitStartTime: trxWaitStartTime,
					SqlBlocked:       TL.SqlBlocked,
					TrxTablesLocked:  TL.TrxTablesLocked,
					TrxRowsLocked:    TL.TrxRowsLocked,
					TrxRowsModified:  TL.TrxRowsModified,
					LockStatus:       lockStatus,
					LockList:         tLockList,
					TrxExecTime:      TL.TrxExecTime,
					BlockTrxId:       blockTrxId,
					NodeId:           node.NodeId,
				})
			}
			self.ConnPool.Put(ctx, conn)
		} else {
			////获取锁信息
			//lockList, err := self.describeLock(ctx, conn)
			//if err != nil {
			//	log.Warn(ctx, "get lock fail: %v", err)
			//	self.ConnPool.Put(ctx, conn)
			//	return nil, err
			//}
			//var waitLocks80 []*datasource.LockWait80Version
			//if err := conn.Raw(WaitSql80).Scan(&waitLocks80); err != nil {
			//	log.Warn(ctx, "get 5.7 lock wait fail: %v", err)
			//	self.ConnPool.Put(ctx, conn)
			//	return nil, err
			//}
			for _, TL := range TrxAndLocklist {
				var (
					lockStatus       shared.LockStatus
					trxWaitStartTime string
					//blockTrxId       string
				)
				if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockHoldAndWait
				} else if TL.TrxLockStructs == 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockWait
				} else if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId == "" {
					lockStatus = shared.LockHold
				} else {
					lockStatus = shared.None
				}
				/* TrxWaitStartTime maybe NULL */
				if TL.TrxWaitStartTime.Format("2006-01-02 15:04:05") == "0001-01-01 00:00:00" {
					trxWaitStartTime = ""
				} else {
					trxWaitStartTime = TL.TrxWaitStartTime.Format("2006-01-02 15:04:05")
				}
				//// 匹配事务是否存在锁阻塞
				//for _, waitItem := range waitLocks80 {
				//	if TL.TrxRequestedLockId == waitItem.RequestingLockId {
				//		blockTrxId = waitItem.BlockTrxId
				//		break
				//	}
				//}
				//tLockList := lockList
				//// 匹配事务是否持有锁
				//fp.StreamOf(tLockList).Filter(func(d *shared.Lock) bool {
				//	return d.TrxId == TL.TrxId
				//}).ToSlice(&tLockList)
				//lockList2 := make([]*shared.Lock, 0)
				//lockList2 = append(lockList2, lockList...)
				trxLocksList = append(trxLocksList, &shared.TrxAndLock{
					ProcessId:        TL.ProcessId,
					TrxId:            TL.TrxId,
					TrxStatus:        TL.TrxState,
					TrxIsoLevel:      TL.TrxIsoLevel,
					TrxStartTime:     TL.TrxStartTime.Format("2006-01-02 15:04:05"),
					TrxWaitStartTime: trxWaitStartTime,
					SqlBlocked:       TL.SqlBlocked,
					TrxTablesLocked:  TL.TrxTablesLocked,
					TrxRowsLocked:    TL.TrxRowsLocked,
					TrxRowsModified:  TL.TrxRowsModified,
					LockStatus:       lockStatus,
					//LockList:         lockList2,
					TrxExecTime: TL.TrxExecTime,
					//BlockTrxId:       blockTrxId,
					NodeId: node.NodeId,
				})
			}
			self.ConnPool.Put(ctx, conn)
		}
	}
	return trxLocksList, nil
}
func generateTrxAndLockSearchWhereConditions(searchParam *model.TrxQueryFilter) (string, []interface{}) {
	args := []interface{}{}
	mp1 := map[string]string{
		"RUNNING":      "RUNNING",
		"LOCKWAIT":     "LOCK WAIT",
		"ROLLING_BACK": "ROLLING BACK",
		"COMMITTING":   "COMMITTING",
	}
	if searchParam == nil {
		return "", nil
	}
	// where条件
	whereConditions := " where 1 "
	// TrxStatus
	/* RUNNING, LOCK WAIT, ROLLING BACK, COMMITTING */
	if searchParam.GetTrxStatus() != "" {
		whereConditions += " and TrxStatus=?"
		args = append(args, mp1[searchParam.GetTrxStatus()])
	}
	// 锁状态
	/* hold and wait lock,wait lock,hold lock */
	if searchParam.GetLockStatus().String() != "<UNSET>" {
		lockStatus := searchParam.GetLockStatus().String()
		if lockStatus == "LockHoldAndWait" {
			whereConditions += " and TrxLockStructs>0 and ISNULL(trx_requested_lock_id)=0"
		} else if lockStatus == "LockWait" {
			whereConditions += " and ISNULL(trx_requested_lock_id)=0"
		} else if lockStatus == "LockHold" {
			whereConditions += " and TrxLockStructs>0"
		} else if lockStatus == "None" {
			whereConditions += " and TrxLockStructs=0"
		}
	}
	//事务隔离级别
	if searchParam.GetTrxIsoLevel() != "" {
		whereConditions += " and TrxIsoLevel=?"
		args = append(args, searchParam.GetTrxIsoLevel())
	}
	return whereConditions, args
}
func generateLockWaitSearchWhereConditions(searchParam *model.WaitLockQueryFilter) (string, []interface{}) {
	args := []interface{}{}
	mp1 := map[string]string{
		"RUNNING":      "RUNNING",
		"LOCKWAIT":     "LOCK WAIT",
		"ROLLING_BACK": "ROLLING BACK",
		"COMMITTING":   "COMMITTING",
	}
	if searchParam == nil {
		return "", nil
	}
	// where条件
	whereConditions := " where 1 "
	// RTrxStatus
	/* RUNNING, LOCK WAIT, ROLLING BACK, COMMITTING */
	if searchParam.GetRTrxState() != "" {
		whereConditions += " and r_trx_state=?"
		args = append(args, mp1[searchParam.GetRTrxState()])
	}
	// BTrxStatus
	if searchParam.GetBTrxState() != "" {
		whereConditions += " and b_trx_state=?"
		args = append(args, mp1[searchParam.GetBTrxState()])
	}
	// 事务id
	if searchParam.GetRTrxId() != "" {
		whereConditions += " and r_trx_id like ?"
		args = append(args, "%"+searchParam.GetRTrxId()+"%")
	}
	// 阻塞事务id
	if searchParam.GetBTrxId() != "" {
		whereConditions += " and b_trx_id like ?"
		args = append(args, "%"+searchParam.GetBTrxId()+"%")
	}
	//请求的sql
	if searchParam.GetRWaitingQuery() != "" {
		whereConditions += " and r_waiting_query like ?"
		args = append(args, "%"+searchParam.GetRWaitingQuery()+"%")
	}
	//阻塞的sql
	if searchParam.GetBBlockingQuery() != "" {
		whereConditions += " and b_blocking_query like ?"
		args = append(args, "%"+searchParam.GetBBlockingQuery()+"%")
	}
	return whereConditions, args
}

func (self *mysqlImpl) DescribeDeadlock(ctx context.Context, req *datasource.DescribeDeadlockReq) (*datasource.DescribeDeadlockResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.DescribeDeadlockResp{}

	/* get engine innodb status */
	command := "/*+ DBW DAS DEFAULT*/ show engine innodb status;"
	var statusinfo StatusInfo
	if err = conn.Raw(command).Scan(&statusinfo); err != nil {
		return nil, err
	}

	/* DeadLock Info Str */
	reg := regexp.MustCompile(`LATEST DETECTED DEADLOCK\n------------------------\n([\s\S]*)------------\nTRANSACTIONS\n`)
	if len(reg.FindStringSubmatch(statusinfo.Status)) == 0 {
		log.Info(ctx, "there are no new Deadlock")
		retlog := &shared.DescribeDeadlockInfo{}
		retlog, err = self.getDeadLock(ctx, nil, time.Now().Unix(), req.InstanceId, req.TenantId)
		if err != nil {
			log.Warn(ctx, "get Deadlock info fail:%v", err)
			return nil, err
		}
		ret.DescribeDeadlockInfo = retlog
		return ret, nil
	}

	DLInfoStr := reg.FindStringSubmatch(statusinfo.Status)[len(reg.FindStringSubmatch(statusinfo.Status))-1]
	/* get two time */
	DeadlockCollectionTime := time.Now().Format("2006-01-02 15:04:05")
	reg = regexp.MustCompile(`(\d{4}-\d{2}-\d{2}.{9})`)
	DeadlockTime := reg.FindAllString(DLInfoStr, -1)[0]

	/* get every trx and its lock */
	var DeadlockList2 []*shared.Deadlock

	reg = regexp.MustCompile(consts.DeadlokcReg)
	trxs := reg.FindAllString(DLInfoStr, -1)

	for i, trx := range trxs {
		dl := &shared.Deadlock{}
		results := GetRegResults(trx, deadLockRegs)

		dl.TrxInfo = strconv.Itoa(i + 1)
		dl.ProcessId = results[0]
		dl.RelateTable = results[1]
		dl.WaitLock = results[2]
		dl.WaitIndex = results[3]
		dl.WaitLockMode = results[4]
		dl.HoldLock = results[5]
		dl.HoldLockIndex = results[6]
		dl.HoldLockMode = results[7]
		dl.Sql = results[8]
		dl.ReqType = GetSqlType(dl.Sql)
		reg = regexp.MustCompile(`WE (.*) TRANSACTION \((\d)\)`)
		if reg.FindStringSubmatch(DLInfoStr) != nil && len(reg.FindStringSubmatch(DLInfoStr)) == 3 {
			if trxnum := reg.FindStringSubmatch(DLInfoStr)[2]; trxnum == strconv.Itoa(i+1) {
				dl.TrxTreat = reg.FindStringSubmatch(DLInfoStr)[1] + " TRANSACTION " + trxnum
			} else {
				dl.TrxTreat = ""
			}
		} else {
			dl.TrxTreat = ""
		}
		DeadlockList2 = append(DeadlockList2, dl)
	}
	DeadlockInfo2 := &shared.DeadlockInfo{
		DeadlockCollectionTime: DeadlockCollectionTime,
		DeadlockTime:           DeadlockTime,
		DeadlockList:           DeadlockList2,
	}
	retlog := &shared.DescribeDeadlockInfo{}
	retlog, err = self.getDeadLock(ctx, DeadlockInfo2, time.Now().Unix(), req.InstanceId, req.TenantId)
	if err != nil {
		log.Warn(ctx, "get Deadlock info fail:%v", err)
		return nil, err
	}
	ret.DescribeDeadlockInfo = retlog
	return ret, nil
}

func (self *mysqlImpl) DescribeDeadlockDetect(ctx context.Context, req *datasource.DescribeDeadlockDetectReq) (*datasource.DescribeDeadlockDetectResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.DescribeDeadlockDetectResp{}
	type T struct {
		OnOff int `gorm:"column:@@innodb_deadlock_detect"`
	}
	var t T
	sql := "/*+ DBW DAS DEFAULT*/ select @@innodb_deadlock_detect;"
	if err = conn.Raw(sql).Scan(&t); err != nil {
		log.Warn(ctx, "get Deadlock Detect fail %v", err)
		return nil, err
	}
	ret.DescribeDeadlockDetectInfo = &shared.DescribeDeadlockDetectInfo{
		OnOff: t.OnOff == 1,
	}
	return ret, nil
}

func (self *mysqlImpl) getDeadLock(ctx context.Context, DeadlockInfo *shared.DeadlockInfo, timestamp int64, InstanceId string, TenantId string) (*shared.DescribeDeadlockInfo, error) {
	/* get tls client */
	var topicId string
	cnf := self.cnf.Get(ctx)
	c3Cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	regionId := cnf.TlsZone
	tlsEndpoint := cnf.TlsServiceEndpoint
	tlsclient := tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)

	if cnf.EnableNewVersionDeadLockTopic {
		tlsDeadlockTopic := c3Cfg.TLSDeadLockTopicV2
		topicSet := &datasource.TLSDeadlockTopic{}
		if err := json.Unmarshal([]byte(tlsDeadlockTopic), topicSet); err != nil {
			log.Warn(ctx, " tlsDeadlockTopic unmarshal failed %v", err)
		}
		for _, topic := range topicSet.Topics {
			if topic.InstanceType == model.DSType_MySQL.String() {
				topicId = topic.TopicID
			}
		}
	} else {
		topicId = c3Cfg.TLSDeadLockTopic
	}
	/* get data from tls */
	EndTime := time.Now().Unix()
	StartTime := EndTime - 86400
	log.Info(ctx, "start search logs from tls")
	retLog, err := tlsclient.SearchLogs(&tls.SearchLogsRequest{
		TopicID:   topicId,
		Query:     fmt.Sprintf("* | select DeadlockTime, content where InstanceId='%s' and DeadlockCollectionTime>%s order by DeadlockCollectionTime desc limit 1", InstanceId, strconv.FormatInt(timestamp-86400, 10)),
		StartTime: StartTime,
		EndTime:   EndTime,
		Limit:     1000,
		HighLight: false,
		Context:   "",
		Sort:      "",
	})
	if err != nil {
		log.Warn(ctx, "SearchLogs fail:%v", err)
		return nil, err
	}
	log.Info(ctx, "search logs from tls end")
	/* add data to ret */
	ret := &shared.DescribeDeadlockInfo{}
	var DeadlockInfoList2 []*shared.DeadlockInfo

	if retLog != nil && len(retLog.AnalysisResult.Data) != 0 {
		tempmp := map[string]string{}
		for i := 0; i < len(retLog.AnalysisResult.Data); i++ {
			if _, ok := tempmp[retLog.AnalysisResult.Data[i]["DeadlockTime"].(string)]; !ok {
				tempmp[retLog.AnalysisResult.Data[i]["DeadlockTime"].(string)] = "ok"
				DeadlockInfo2 := &shared.DeadlockInfo{}
				err = json.Unmarshal([]byte(retLog.AnalysisResult.Data[i]["content"].(string)), &DeadlockInfo2)
				if err != nil {
					log.Warn(ctx, "Unmarshal fail:%v", err)
					return nil, err
				}
				DeadlockInfoList2 = append(DeadlockInfoList2, DeadlockInfo2)
			}
		}
		if DeadlockInfo != nil {
			if _, ok := tempmp[DeadlockInfo.DeadlockTime]; !ok {
				DeadlockInfoList2 = append(DeadlockInfoList2, DeadlockInfo)
			}
		}
		ret.DeadlockInfoList = DeadlockInfoList2
	} else {
		if DeadlockInfo != nil {
			ret.DeadlockInfoList = append(ret.DeadlockInfoList, DeadlockInfo)
		}
	}

	/* put log to tls */
	if DeadlockInfo != nil {
		js, _ := json.Marshal(DeadlockInfo)
		log.Info(ctx, " tls")
		_, err = tlsclient.PutLogs(&tls.PutLogsRequest{
			TopicID:      topicId,
			HashKey:      "",
			CompressType: "lz4",
			LogBody: &pb.LogGroupList{
				LogGroups: []*pb.LogGroup{
					{
						Logs: []*pb.Log{
							{
								Contents: []*pb.LogContent{
									{
										Key:   "InstanceId",
										Value: InstanceId,
									},
									{
										Key:   "DeadlockCollectionTime",
										Value: strconv.FormatInt(timestamp, 10),
									},
									{
										Key:   "DeadlockTime",
										Value: DeadlockInfo.DeadlockTime,
									},
									{
										Key:   "content",
										Value: string(js),
									},
									{
										Key:   "TenantId",
										Value: TenantId,
									},
								},
							},
						},
						Source:      "",
						LogTags:     nil,
						FileName:    "",
						ContextFlow: "",
					},
				},
			},
		})
		if err != nil {
			log.Warn(ctx, "put log to tls fail: %s", err)
			return nil, err
		}
	}
	log.Info(ctx, "success handle deadlock request")
	return ret, nil
}

//func (self *mysqlImpl) searchDeadLockFromTLS(ctx context.Context, instanceId string) {
//	c3Cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
//	Ak := c3Cfg.TLSServiceAccessKey
//	Sk := c3Cfg.TLSServiceSecretKey
//	regionId := self.L.RegionID()
//	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
//	tlsclient := tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)
//
//	/* get data from tls */
//	EndTime := time.Now().Unix()
//	StartTime := EndTime - 86400
//	logs, err := tlsclient.SearchLogs(&tls.SearchLogsRequest{
//		TopicID:   c3Cfg.TLSDeadLockTopic,
//		Query:     fmt.Sprintf("* | select DeadlockTime,  content where InstanceId='%s'", instanceId),
//		StartTime: StartTime,
//		EndTime:   EndTime,
//		Limit:     1000,
//		HighLight: false,
//		Context:   "",
//		Sort:      "",
//	})
//	if err != nil {
//		log.Warn(ctx, "SearchLogs fail:%v", err)
//		return
//	}
//	if logs == nil && len(logs.AnalysisResult.Data) == 0 {
//		return
//	}
//	//var deadLocks []*shared.DeadlockInfo
//	for i := 0; i < len(logs.AnalysisResult.Data); i++ {
//		//if _, ok := tempmp[logs.AnalysisResult.Data[i]["DeadlockTime"].(string)]; !ok {
//		//tempmp[logs.AnalysisResult.Data[i]["DeadlockTime"].(string)] = "ok"
//		DeadlockInfo2 := &shared.DeadlockInfo{}
//		err = json.Unmarshal([]byte(logs.AnalysisResult.Data[i]["content"].(string)), &DeadlockInfo2)
//		if err != nil {
//			log.Warn(ctx, "Unmarshal fail:%v", err)
//			return
//		}
//		//DeadlockInfoList2 = append(DeadlockInfoList2, DeadlockInfo2)
//	}
//}

func (self *mysqlImpl) describeLock(ctx context.Context, conn datasource.Conn) ([]*shared.Lock, error) {
	ret := make([]*shared.Lock, 0)
	/* get mysql version info */
	var versionSql = "/*+ DBW DAS DEFAULT*/ select version() as version;"
	var MysqlVersion = DBVersion{}
	if err := conn.Raw(versionSql).Scan(&MysqlVersion); err != nil {
		log.Warn(ctx, "get mysql version fail %v", err)
		return nil, err
	}
	//log.Info(ctx, "mysql version is %v", MysqlVersion.Version)
	/* version = 5.7x */
	if strings.Contains(MysqlVersion.Version, "5.7") {
		var Locklist []Lock57
		if err := conn.Raw(LockQuery57).Scan(&Locklist); err != nil {
			return nil, err
		}
		LockList2 := make([]*shared.Lock, 0)
		for _, L := range Locklist {
			var lockProperty string
			var Numlockwait int64
			sql1 := fmt.Sprintf("/*+ DBW DAS DEFAULT*/ select count(1) from information_schema.INNODB_LOCK_WAITS where requested_lock_id ='%s';", L.LockId)
			if err := conn.Raw(sql1).Scan(&Numlockwait); err != nil {
				return nil, err
			}
			if Numlockwait == 0 {
				lockProperty = model.Lockstatus_LockHold.String()
			} else {
				lockProperty = model.Lockstatus_LockWait.String()
			}
			LockList2 = append(LockList2, &shared.Lock{
				LockProperty:       lockProperty,
				LockId:             L.LockId,
				LockAssociateIndex: L.LockAssociateIndex,
				LockAssociateTable: L.LockAssociateTable,
				LockType:           L.LockType,
				LockModel:          L.LockModel,
				TrxId:              L.TrxId,
			})
		}
		ret = LockList2
	}

	/* version = 8.0x */
	if strings.Contains(MysqlVersion.Version, "8.0") {
		var Locklist []Lock80
		if err := conn.Raw(LockQuery80).Scan(&Locklist); err != nil {
			return nil, err
		}
		LockList2 := make([]*shared.Lock, 0)
		for i := 0; i < len(Locklist); i++ {
			if i == len(Locklist)-1 || (Locklist[i].LockBegin != Locklist[i+1].LockBegin) {
				LockList2 = append(LockList2, &shared.Lock{
					LockProperty:       Locklist[i].LockProperty,
					LockId:             Locklist[i].LockId,
					LockAssociateIndex: Locklist[i].LockAssociateIndex,
					LockAssociateTable: Locklist[i].LockAssociateTable,
					LockType:           Locklist[i].LockType,
					LockModel:          Locklist[i].LockModel,
					TrxId:              Locklist[i].TrxId,
				})
			}
		}
		ret = LockList2
	}
	return ret, nil
}

func (self *mysqlImpl) ListInstanceNodes(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {

	query := rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	resp := rdsModel_v2.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), query, &resp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesResp{}
	fp.StreamOf(resp.Nodes).
		Map(func(node *rdsModel_v2.NodeDetailInfoObject) *model.NodeInfoObject {
			nodeType, _ := model.NodeTypeFromString(node.NodeType.String())
			return &model.NodeInfoObject{
				NodeId:   node.NodeId,
				NodeType: nodeType,
				CpuNum:   node.VCPU,
				MemInGiB: node.Memory,
				ZoneId:   node.ZoneId,
			}
		}).ToSlice(&ret.Nodes)
	return ret, nil
}

func (self *mysqlImpl) ListInstanceNodesOri(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesOriResp, error) {

	query := rdsModel.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	resp := rdsModel.ListInstanceNodesResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), query, &resp, client.WithVersion(RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesOriResp{}
	fp.StreamOf(resp.Datas).
		Map(func(node *rdsModel.InstanceNodeInfo) *datasource.InstanceNodeInfo {
			return &datasource.InstanceNodeInfo{
				NodeID:     node.NodeId,
				PodName:    node.NodeName,
				Role:       node.NodeRole.String(),
				Zone:       "",
				NodeStatus: node.NodeStatus.String(),
			}
		}).ToSlice(&ret.NodesInfo)
	return ret, nil
}

func (self *mysqlImpl) DescribeDBProxyConfig(ctx context.Context, req *datasource.DescribeDBProxyConfigReq) (*datasource.DescribeDBProxyConfigResp, error) {
	if !self.cnf.Get(ctx).EnableDescribeDBProxy {
		return &datasource.DescribeDBProxyConfigResp{IsProxyEnable: true}, nil
	}
	describeDBProxyConfigReq := &rdsModel_v2.DescribeDBProxyConfigReq{
		InstanceId: req.InstanceId,
	}
	describeDBProxyConfigResp := &rdsModel_v2.DescribeDBProxyConfigResp{}
	log.Info(ctx, "describeDBProxyConfigReq", describeDBProxyConfigReq.String())
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBProxyConfig.String(), describeDBProxyConfigReq, describeDBProxyConfigResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call DescribeDBProxyConfig, err=%v", err)
		return nil, err
	}
	log.Info(ctx, "DescribeDBProxyConfigResp is %s", describeDBProxyConfigResp)
	isProxyEnable := false
	switch describeDBProxyConfigResp.GetDBProxyStatus() {
	case rdsModel_v2.DBProxyStatus_Creating:
		isProxyEnable = true
	case rdsModel_v2.DBProxyStatus_Deleting:
		isProxyEnable = false
	case rdsModel_v2.DBProxyStatus_Running:
		isProxyEnable = true
	case rdsModel_v2.DBProxyStatus_Shutdown:
		isProxyEnable = false
	default:
		log.Error(ctx, "GetDBProxyStatus unknown error:%s %s", describeDBProxyConfigResp.GetDBProxyStatus(), describeDBProxyConfigResp.String())
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	return &datasource.DescribeDBProxyConfigResp{IsProxyEnable: isProxyEnable}, nil
}

func (self *mysqlImpl) DescribeSQLCCLConfig(ctx context.Context, req *datasource.DescribeSQLCCLConfigReq) (*datasource.DescribeSQLCCLConfigResp, error) {
	describeSQLConcurrencyControlConfigReq := &rdsModel_v2.DescribeSQLConcurrencyControlConfigReq{
		InstanceId: req.InstanceId,
	}
	resp := &rdsModel_v2.DescribeSQLConcurrencyControlConfigResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeSQLConcurrencyControlConfig.String(), describeSQLConcurrencyControlConfigReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds DescribeSQLConcurrencyControlConfig, err=%v", err)
		return nil, err
	}
	return &datasource.DescribeSQLCCLConfigResp{
		SQLConcurrencyControlStatus: resp.GetSQLConcurrencyControlStatus(),
	}, nil
}

func (self *mysqlImpl) ModifySQLCCLConfig(ctx context.Context, req *datasource.ModifySQLCCLConfigReq) (*datasource.ModifySQLCCLConfigResp, error) {
	//modifySQLConcurrencyControlConfigReq := &rdsModel_v2.ModifySQLConcurrencyControlConfigReq{
	//	InstanceId:                  req.InstanceId,
	//	EnableSQLConcurrencyControl: req.EnableSqlConcurrencyControl,
	//}
	//resp := &rdsModel_v2.ModifyProxySQLConcurrencyControlConfigResp{}
	//err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_ModifySQLConcurrencyControlConfig.String(), modifySQLConcurrencyControlConfigReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	//if err != nil {
	//	log.Warn(ctx, "failed to call Rds ModifySQLConcurrencyControlConfig, err=%v", err)
	//	return nil, err
	//}
	return &datasource.ModifySQLCCLConfigResp{}, nil
}

func (self *mysqlImpl) AddSQLCCLRule(ctx context.Context, req *datasource.AddSQLCCLRuleReq) (*datasource.AddSQLCCLRuleResp, error) {
	cclRule := &rdsModel_v2.CCLRule{
		Type:             req.CCLRule.SqlType,
		User:             req.CCLRule.UserID,
		Host:             req.CCLRule.HostName,
		SchemaName:       req.CCLRule.SchemaName,
		TableName:        req.CCLRule.TableName,
		ConcurrencyCount: int64(req.CCLRule.ConcurrencyCount),
		KeyWords:         req.CCLRule.Keywords,
		State:            req.CCLRule.State,
		Ordered:          req.CCLRule.Ordered,
		MaxQueueSize:     req.CCLRule.MaxQueueSize,
		WaitTimeout:      req.CCLRule.WaitTimeout,
	}
	addCCLRuleReq := &rdsModel_v2.CreateSQLConcurrencyControlRuleReq{
		InstanceId: req.InstanceId,
		CCLRule:    cclRule,
	}
	resp := &rdsModel_v2.CreateSQLConcurrencyControlRuleResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_CreateSQLConcurrencyControlRule.String(), addCCLRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds CreateSQLConcurrencyControlRule, err=%+v", err)
		return nil, err
	}
	return &datasource.AddSQLCCLRuleResp{}, nil
}

func (self *mysqlImpl) DeleteSQLCCLRule(ctx context.Context, req *datasource.DeleteSQLCCLRuleReq) (*datasource.DeleteSQLCCLRuleResp, error) {
	cclRule := &rdsModel_v2.CCLRule{
		Type:             req.CCLRule.SqlType,
		User:             req.CCLRule.UserID,
		Host:             req.CCLRule.HostName,
		SchemaName:       req.CCLRule.SchemaName,
		TableName:        req.CCLRule.TableName,
		ConcurrencyCount: int64(req.CCLRule.ConcurrencyCount),
		KeyWords:         req.CCLRule.Keywords,
		State:            req.CCLRule.State,
		Ordered:          req.CCLRule.Ordered,
		MaxQueueSize:     req.CCLRule.MaxQueueSize,
		WaitTimeout:      req.CCLRule.WaitTimeout,
	}
	deleteCCLRuleReq := &rdsModel_v2.DeleteSQLConcurrencyControlRuleReq{
		InstanceId: req.InstanceId,
		CCLRule:    cclRule,
	}
	resp := &rdsModel_v2.DeleteSQLConcurrencyControlRuleResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DeleteSQLConcurrencyControlRule.String(), deleteCCLRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds DeleteSQLConcurrencyControlRule, err=%+v", err)
		return nil, err
	}
	return &datasource.DeleteSQLCCLRuleResp{}, nil
}

// 弃用
func (self *mysqlImpl) FlushSQLCCLRule(ctx context.Context, req *datasource.FlushSQLCCLRuleReq) (*datasource.FlushSQLCCLRuleResp, error) {
	//flushCCLRuleReq := &rdsModel_v2.FlushSQLConcurrencyControlRuleReq{
	//	InstanceId: req.InstanceId,
	//}
	//resp := &rdsModel_v2.FlushSQLConcurrencyControlRuleResp{}
	//err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_FlushSQLConcurrencyControlRule.String(), flushCCLRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	//if err != nil {
	//	log.Warn(ctx, "failed to call Rds DeleteSQLConcurrencyControlRule, err=%+v", err)
	//	return nil, err
	//}
	return &datasource.FlushSQLCCLRuleResp{}, nil
}

func (self *mysqlImpl) ListSQLCCLRules(ctx context.Context, req *datasource.ListSQLCCLRulesReq) (*datasource.ListSQLCCLRulesResp, error) {
	describeCCLRuleReq := &rdsModel_v2.DescribeSQLConcurrencyControlRuleReq{
		InstanceId: req.InstanceId,
	}
	resp := &rdsModel_v2.DescribeSQLConcurrencyControlRuleResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeSQLConcurrencyControlRule.String(), describeCCLRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds DeleteSQLConcurrencyControlRule, err=%+v", err)
		return nil, err
	}
	ret := &datasource.ListSQLCCLRulesResp{}
	if err := fp.StreamOf(resp.CCLRules).Map(func(rule *rdsModel_v2.CCLRuleShow) *datasource.CCLRuleInfo {
		return &datasource.CCLRuleInfo{
			RuleID:           rule.GetId(),
			UserID:           rule.GetUser(),
			HostName:         rule.GetHost(),
			Keywords:         rule.GetKeyWords(),
			SchemaName:       rule.GetSchemaName(),
			TableName:        rule.GetTableName(),
			SqlType:          rule.GetType(),
			MaxQueueSize:     rule.GetMaxQueueSize(),
			WaitTimeout:      rule.GetWaitTimeout(),
			ConcurrencyCount: int32(rule.GetConcurrencyCount()),
			State:            rule.GetState(),
			Ordered:          rule.GetOrdered(),
			Rejected:         rule.GetRejected(),
		}
	}).ToSlice(&ret.CCLRules); err != nil {
		return nil, err
	}
	return ret, nil
}
func (self *mysqlImpl) DescribeSqlFingerPrintOrKeywords(ctx context.Context, req *datasource.DescribeSqlFingerPrintOrKeywordsReq) (*datasource.DescribeSqlFingerPrintOrKeywordsResp, error) {
	sqlThrottleType, _ := rdsModel.SQLThrottleTypeFromString(req.ObjectType)
	rreq := &rdsModel.InnerGetFingerprintAndKeywordReq{
		Sql:             req.SqlText,
		SQLThrottleType: rdsModel.SQLThrottleTypePtr(sqlThrottleType),
	}
	rresp := &rdsModel.InnerGetFingerprintAndKeywordResp{}
	err := self.mysql.Get().Call(ctx, rdsModel.Action_InnerGetFingerprintAndKeyword.String(), rreq, rresp, client.WithVersion(consts.RDS_MySQL_Version_V1))
	if err != nil {
		log.Warn(ctx, "failed to call Rds InnerGetFingerprintAndKeyword, err=%+v", err)
		return nil, err
	}
	ret := &datasource.DescribeSqlFingerPrintOrKeywordsResp{
		FingerPrint: rresp.GetFingerPrint(),
		Keywords:    rresp.GetKeyword(),
	}
	return ret, nil
}
func (self *mysqlImpl) DescribeSqlType(ctx context.Context, req *datasource.DescribeSqlTypeReq) (*datasource.DescribeSqlTypeResp, error) {
	ret := &datasource.DescribeSqlTypeResp{}
	sqlType, err := datasource.GetRdsSqlType(ctx, req.SqlText)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_SQLReviewParserSqlError)
	}
	ret.SqlType = sqlType
	return ret, nil
}
func (self *mysqlImpl) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (resp *datasource.DescribeDBInstanceDetailResp, err error) {
	describeDBInstanceDetailReq := &rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err = self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetail InstanceNotFound, err=%v", err)
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		}

		return nil, err
	}

	resp = &datasource.DescribeDBInstanceDetailResp{
		InstanceId:       describeDBInstanceDetailResp.BasicInfo.GetInstanceId(),
		InstanceName:     describeDBInstanceDetailResp.BasicInfo.GetInstanceName(),
		InstanceStatus:   describeDBInstanceDetailResp.BasicInfo.GetInstanceStatus().String(),
		RegionId:         describeDBInstanceDetailResp.BasicInfo.GetRegionId(),
		ZoneId:           describeDBInstanceDetailResp.BasicInfo.GetZoneId(),
		DBEngine:         describeDBInstanceDetailResp.BasicInfo.GetDBEngine().String(),
		DBEngineVersion:  describeDBInstanceDetailResp.BasicInfo.GetDBEngineVersion().String(),
		InstanceType:     describeDBInstanceDetailResp.BasicInfo.GetInstanceType().String(),
		VCPU:             describeDBInstanceDetailResp.BasicInfo.VCPU,
		Memory:           describeDBInstanceDetailResp.BasicInfo.Memory,
		ProjectName:      describeDBInstanceDetailResp.BasicInfo.ProjectName,
		StorageSpace:     describeDBInstanceDetailResp.BasicInfo.StorageSpace,
		NodeSpec:         describeDBInstanceDetailResp.BasicInfo.NodeSpec,
		MasterInstanceId: describeDBInstanceDetailResp.BasicInfo.GetMasterInstanceId(),
		StorageType:      describeDBInstanceDetailResp.BasicInfo.GetStorageType().String(),
	}
	if describeDBInstanceDetailResp.BasicInfo.IsSetPerconaVersion() {
		resp.DBEngineSubVersion = describeDBInstanceDetailResp.BasicInfo.GetPerconaVersion().String()
	}
	if describeDBInstanceDetailResp.NodeDetailInfo != nil && len(describeDBInstanceDetailResp.NodeDetailInfo) > 0 {
		var infos []*datasource.NodeInfo
		for _, node := range describeDBInstanceDetailResp.NodeDetailInfo {
			info := datasource.NodeInfo{
				NodeId:   node.NodeId,
				RegionId: node.RegionId,
				ZoneId:   node.ZoneId,
				NodeIP:   node.NodeId,
			}
			infos = append(infos, &info)
		}
		resp.NodeInfos = infos
	}

	fp.StreamOf(describeDBInstanceDetailResp.Endpoints).Map(func(endpoint *rdsModel_v2.ConnectionInfoObject) *datasource.Endpoint {
		edp := &datasource.Endpoint{
			Mode:      endpointMode(endpoint.ReadWriteMode),
			ProxyPort: endpoint.GetEndpointProxyPort(),
		}
		return edp
	}).ToSlice(&resp.Endpoints)
	return resp, nil
}

func (self *mysqlImpl) DescribeDBInstanceEndpoints(ctx context.Context, req *datasource.DescribeDBInstanceEndpointsReq) (*datasource.DescribeDBInstanceEndpointsResp, error) {
	var endpointList []*datasource.EndpointInfo
	describeDBInstanceDetailReq := &rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	resp := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetail InstanceNotFound, err=%v", err)
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		}
		return nil, err
	}
	nodeMap := make(map[rdsModel_v2.NodeType]string)
	for _, node := range resp.Nodes {
		nodeMap[node.NodeType] = node.NodeId
	}
	if err = fp.StreamOf(resp.Endpoints).Map(func(i *rdsModel_v2.ConnectionInfoObject) *datasource.EndpointInfo {
		var nodes []string
		for _, object := range i.ReadOnlyNodeWeight {
			if *object.Weight > 0 {
				if object.NodeId != "" {
					nodes = append(nodes, object.NodeId)
				} else if object.NodeType == rdsModel_v2.NodeType_Primary {
					nodes = append(nodes, nodeMap[rdsModel_v2.NodeType_Primary])
				}
			}
		}
		return &datasource.EndpointInfo{
			EndpointName:  i.EndpointName,
			EndpointID:    i.EndpointId,
			EndpointType:  i.EndpointType.String(),
			EndpointPort:  i.GetEndpointProxyPort(),
			ReadWriteMode: i.GetReadWriteMode().String(),
			NodeID:        nodes,
		}
	}).ToSlice(&endpointList); err != nil {
		return nil, err
	}
	ret := &datasource.DescribeDBInstanceEndpointsResp{
		Endpoints: endpointList,
	}
	return ret, nil
}

func (self *mysqlImpl) DescribeDBInstanceCluster(ctx context.Context, req *datasource.DescribeDBInstanceClusterReq) (*datasource.DescribeDBInstanceClusterResp, error) {
	listInstancePodsReq := &rdsModel.ListInstancePodsReq{
		InstanceId: req.InstanceId,
	}
	listInstancePodsResp := &rdsModel.ListInstancePodsResp{}
	log.Info(ctx, "listInstancePodsReq:%s", listInstancePodsReq.String())
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstancePods.String(), listInstancePodsReq, listInstancePodsResp); err != nil {
		log.Warn(ctx, "ListInstancePods: get instance %s pod from rds mgr fail %v", req.InstanceId, err)
		return nil, err
	}
	kubeClusters := make(map[string]string)
	for _, rdsPod := range listInstancePodsResp.GetDatas() {
		kubeClusters[rdsPod.KubeCluster] = rdsPod.KubeCluster
	}

	if len(kubeClusters) == 0 {
		log.Warn(ctx, "len zone is 0")
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	multiAz := len(kubeClusters) > 1
	return &datasource.DescribeDBInstanceClusterResp{
		MultiAZ:      multiAz,
		AzClusterMap: kubeClusters,
	}, nil
}

func (self *mysqlImpl) DescribeDBInstanceAuditCollectedPod(ctx context.Context, req *datasource.DescribeDBInstanceAuditCollectedPodReq) (*datasource.DescribeDBInstanceAuditCollectedPodResp, error) {
	listInstancePodsReq := &rdsModel.ListInstancePodsReq{
		InstanceId: req.InstanceId,
	}
	listInstancePodsResp := &rdsModel.ListInstancePodsResp{}
	log.Info(ctx, "listInstancePodsReq:%s", listInstancePodsReq.String())
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstancePods.String(), listInstancePodsReq, listInstancePodsResp); err != nil {
		log.Warn(ctx, "ListInstancePods: get instance %s pod from rds mgr fail %v", req.InstanceId, err)
		return nil, err
	}
	proxyConfig, err := self.DescribeDBProxyConfig(ctx, &datasource.DescribeDBProxyConfigReq{
		InstanceId: req.InstanceId,
		Type:       req.Type,
	})
	if err != nil {
		log.Warn(ctx, "DescribeDBProxyConfig get instance %s from rds mgr fail %v", req.InstanceId, err)
		return nil, err
	}
	var (
		ports      []string
		portmap    = make(map[string]bool)
		cpuRequest string
	)
	if proxyConfig.IsProxyEnable {
		for _, podInfo := range listInstancePodsResp.Datas {
			if podInfo.Component != "Proxy" {
				continue
			}
			for _, containers := range podInfo.Containers {
				if containers.Name != "proxy" {
					continue
				}
				cpuRequest = containers.Cpu
				for _, proxyPort := range strings.Split(containers.Port, " ") {
					intPort, _ := strconv.Atoi(proxyPort)
					if intPort < 10000 {
						portmap[proxyPort] = true
					}
				}
			}
		}
	} else {
		for _, podInfo := range listInstancePodsResp.Datas {
			if podInfo.Component != "MySQL" {
				continue
			}
			for _, containers := range podInfo.Containers {
				if containers.Name != "mysql" {
					continue
				}
				cpuRequest = containers.Cpu
				for _, proxyPort := range strings.Split(containers.Port, " ") {
					intPort, _ := strconv.Atoi(proxyPort)
					if intPort < 10000 {
						portmap[proxyPort] = true
					}
				}
			}
		}
	}

	for k, _ := range portmap {
		if k != "" {
			ports = append(ports, k)
		}
	}
	return &datasource.DescribeDBInstanceAuditCollectedPodResp{
		Port:       ports,
		CpuRequest: cpuRequest,
	}, nil
}

func (self *mysqlImpl) DescribeDBInstanceSSL(ctx context.Context, req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	proxyConfigResp, err := self.DescribeDBProxyConfig(
		ctx,
		&datasource.DescribeDBProxyConfigReq{
			InstanceId: req.InstanceId,
			Type:       req.Type,
		},
	)
	if err != nil {
		return nil, err
	}
	if !proxyConfigResp.IsProxyEnable {
		return &datasource.DescribeDBInstanceSSLResp{
			SSLEnable: false,
		}, nil
	}
	rdsReq := &rdsModel_v2.DescribeDBInstanceSSLReq{InstanceId: req.InstanceId}
	rdsResp := &rdsModel_v2.DescribeDBInstanceSSLResp{}
	log.Info(ctx, "DescribeDBInstanceSSLReq:%s", rdsReq.String())
	if err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceSSL.String(), rdsReq, rdsResp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceSSL, err=%v", err)
		return nil, err
	}
	log.Info(ctx, "DescribeDBInstanceSSLResp:%s", rdsResp.String())
	return &datasource.DescribeDBInstanceSSLResp{
		InstanceId:    rdsResp.InstanceId,
		SSLEnable:     rdsResp.SSLEnable,
		IsValid:       rdsResp.IsValidField,
		SSLExpireTime: rdsResp.SSLExpireTime,
	}, nil
}

func (self *mysqlImpl) ListInstancePods(ctx context.Context, req *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	type SimplifiedNodeDetail struct {
		NodeId    string
		NodeType  string
		NodeState string
		NodeName  string
	}
	var nodeDetails []*SimplifiedNodeDetail
	instanceNodesReq := &rdsModel.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	instanceNodesResp := &rdsModel.ListInstanceNodesResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), instanceNodesReq, instanceNodesResp, client.WithVersion(RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "ListInstanceNodes failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	log.Info(ctx, "Instance %s Nodes is %s", req.InstanceId, instanceNodesResp)
	nodeInfos := instanceNodesResp.Datas
	if err := fp.StreamOf(nodeInfos).Map(func(db *rdsModel.InstanceNodeInfo) *SimplifiedNodeDetail {
		item := &SimplifiedNodeDetail{
			NodeId:    db.NodeId,
			NodeState: db.NodeStatus.String(),
			NodeName:  db.NodeName,
		}
		switch db.NodeRole {
		case rdsModel.NodeRole_Master:
			item.NodeType = rdsModel_v2.NodeType_Primary.String()
		case rdsModel.NodeRole_Slave:
			item.NodeType = rdsModel_v2.NodeType_Secondary.String()
		case rdsModel.NodeRole_ReadOnly:
			item.NodeType = rdsModel_v2.NodeType_ReadOnly.String()
		default:
			item.NodeType = ""
		}
		return item
	}).ToSlice(&nodeDetails); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	rreq := &rdsModel.ListInstancePodsReq{
		InstanceId: req.InstanceId,
	}
	rresp := &rdsModel.ListInstancePodsResp{}
	log.Info(ctx, "Mysql instanceId:%s listInstancePodsReq:%s", req.InstanceId, rreq.String())
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstancePods.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "ListInstancePods failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	ret := &datasource.ListInstancePodsResp{}
	if err := fp.StreamOf(rresp.Datas).Map(func(db *rdsModel.KubePod) *shared.KubePod {
		// 兼容ipv6
		ip := datasource.ConvertIPV6(db.PodIP)
		return &shared.KubePod{
			Name:        db.Name,
			Zone:        db.Zone,
			KubeCluster: db.KubeCluster,
			Region:      db.Region,
			NodeIP:      db.NodeIP,
			PodIP:       ip,
			NodePool:    db.NodePool,
			Component:   db.Component,
			Containers:  toRdsKubeContainerModelToShared(db.Containers),
			NameSpace:   db.Namespace,
		}
	}).ToSlice(&ret.Data); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	res := make([]*shared.KubePod, 0)
	for _, pod := range ret.Data {
		if pod != nil {
			for _, node := range nodeDetails {
				if pod.Name == node.NodeName {
					pod.Role = node.NodeType
					pod.NodeId = node.NodeId
				}
			}
			res = append(res, pod)
		}
	}
	ret.Data = res
	ret.Total = int32(len(res))
	return ret, nil
}

func (self *mysqlImpl) DescribeInstanceAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		Type:       conv.ToSharedType(model.DSType_MySQL),
		LinkType:   shared.Volc,
		InstanceId: req.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "Get instance %s pods failed: %v", req.InstanceId, err)
		return nil, err
	}
	log.Info(ctx, "DescribeInstanceAddress call ListInstancePods result is %s", utils.Show(listInstancePodsResp))
	//获取实例ip port
	ret := &datasource.DescribeInstanceAddressResp{}
	for _, pod := range listInstancePodsResp.Data {
		if pod.Role == req.NodeType {
			ret.IP = pod.PodIP
			for _, container := range pod.Containers {
				if container.Name == "mysql" {
					ret.Port = utils.MustStrToInt32(container.Port)
				}
			}
		}
	}
	return ret, nil
}

func (self *mysqlImpl) DescribeInstanceAddressList(ctx context.Context, req *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		Type:       conv.ToSharedType(model.DSType_MySQL),
		LinkType:   shared.Volc,
		InstanceId: req.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "Get instance %s pods failed: %v", req.InstanceId, err)
		return nil, err
	}
	log.Info(ctx, "DescribeInstanceAddressList call ListInstancePods result is %s", utils.Show(listInstancePodsResp))
	//获取实例ip port
	var addressList []*datasource.DescribeInstanceAddressResp
	for _, pod := range listInstancePodsResp.Data {
		for _, container := range pod.Containers {
			if container.Name == "mysql" {
				ret := &datasource.DescribeInstanceAddressResp{}
				ret.Port = utils.MustStrToInt32(container.Port)
				ret.IP = pod.PodIP
				ret.NodeId = pod.NodeId
				addressList = append(addressList, ret)
			}
		}
	}
	return addressList, nil
}

func (self *mysqlImpl) GetDiskSize(ctx context.Context, req *datasource.GetDiskSizeReq) (*datasource.GetDiskSizeResp, error) {
	instanceNodesReq := &rdsModel.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	instanceNodesResp := &rdsModel.ListInstanceNodesResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), instanceNodesReq, instanceNodesResp, client.WithVersion(RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "ListInstanceNodes failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	resp := &datasource.GetDiskSizeResp{}
	for _, nodeInfo := range instanceNodesResp.Datas {
		if nodeInfo.NodeRole == rdsModel.NodeRole_Master {
			resp.DiskDataSize = numericConversion(nodeInfo.DiskDataSize)
			resp.DiskLogSize = numericConversion(nodeInfo.DiskLogSize)
			resp.DiskBinLogSize = numericConversion(nodeInfo.DiskBinLogSize)
			resp.DiskErrorLogSize = numericConversion(nodeInfo.DiskErrorLogSize)
			resp.DiskSlowLogSize = numericConversion(nodeInfo.DiskSlowLogSize)
		}
	}

	dataSize, err := self.GetDataSize(ctx, req)
	log.Info(ctx, "GetDiskSize-dataSize is %s", utils.Show(dataSize))

	if err != nil {
		log.Warn(ctx, "Get GetDataSize %s  failed: %v", req.InstanceId, err)
		return nil, err
	}
	if dataSize != nil {
		resp.DiskDataSize = 0
		resp.DiskLogSize = 0
		resp.OtherLogSize = dataSize.OtherLogSize
		resp.RedoLogSize = dataSize.RedoLogSize
		resp.UndoLogSize = dataSize.UndoLogSize
		resp.SysUsedStorage = dataSize.SysUsedStorage
		resp.TmpUsedStorage = dataSize.TmpUsedStorage
		resp.RelayLogSize = dataSize.RelayLogSize
		resp.AuditLogSize = dataSize.AuditLogSize
		resp.UserDataUsageSize = dataSize.UserDataUsageSize
		resp.OtherUsageSize = dataSize.OtherUsageSize
		resp.UsedStorage = dataSize.UsedStorage
		resp.DiskSlowLogSize = dataSize.DiskSlowLogSize
		resp.DiskBinLogSize = dataSize.DiskBinLogSize
		resp.DiskErrorLogSize = dataSize.DiskErrorLogSize

		resp.IsNew = 1
	} else {
		resp.IsNew = 0
		log.Warn(ctx, "get dataSize nil")
	}
	return resp, nil
}

func (self *mysqlImpl) GetUsedSize(ctx context.Context, req *datasource.GetDiskSizeReq) (int64, error) {
	instanceNodesReq := &rdsModel.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	instanceNodesResp := &rdsModel.ListInstanceNodesResp{}
	if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListInstanceNodes.String(), instanceNodesReq, instanceNodesResp, client.WithVersion(RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "ListInstanceNodes failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return 0, err
	}
	usedSize := int64(0)

	for _, nodeInfo := range instanceNodesResp.Datas {
		if nodeInfo.NodeRole == rdsModel.NodeRole_Master {
			usedSize = nodeInfo.DiskDataSize + nodeInfo.DiskLogSize
		}
	}
	return usedSize, nil
}

func (self *mysqlImpl) GetDataSize(ctx context.Context, req *datasource.GetDiskSizeReq) (*datasource.GetDiskSizeResp, error) {
	var (
		RedoLogSize, UndoLogSize, SysUsedStorage, TmpUsedStorage, RelayLogSize, AuditLogSize,
		UsedStorage, UserDataUsageSize, DiskSlowLogSize, DiskBinLogSize, DiskErrorLogSize, OtherUsageSize float64
	)
	var mu sync.Mutex

	log.Info(ctx, "GetDiskSize-req is %s", utils.Show(req))
	measurements, err := self.getMeasurements(ctx, req.InstanceId, "tob_rds_disk_user_data_usage_bytes")
	if err != nil {
		log.Warn(ctx, "Get getMeasurements %s failed: %v", req.InstanceId, err)
		return nil, err
	}

	if len(measurements.Results) == 0 || len(measurements.Results[0].Series) == 0 {
		log.Info(ctx, "Get getMeasurements %s no data return", req.InstanceId)
		return nil, nil
	}

	getLogSize := func(tableName string) (float64, error) {
		measurements, err := self.getMeasurements(ctx, req.InstanceId, tableName)
		if err != nil {
			log.Warn(ctx, "Get %s measurementExists %s failed: %v", tableName, req.InstanceId, err)
			return 0, err
		}
		if len(measurements.Results) == 0 || len(measurements.Results[0].Series) == 0 {
			log.Info(ctx, "Get getMeasurements %s no data return", req.InstanceId)
			return 0, nil
		}
		return self.GetDBLogSize(ctx, req, tableName)
	}

	var g errgroup.Group

	// Concurrently execute getLogSize calls
	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_redo_log_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get RedoLogSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		RedoLogSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_undo_log_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get UndoLogSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		UndoLogSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_relay_log_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get RelayLogSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		RelayLogSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_temp_file_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get TmpUsedStorage %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		TmpUsedStorage = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_system_data_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get SysUsedStorage %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		SysUsedStorage = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_audit_log_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get AuditLogSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		AuditLogSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_user_data_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get UserDataUsageSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		UserDataUsageSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_other_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get OtherUsageSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		OtherUsageSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_slow_log_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get DiskSlowLogSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		DiskSlowLogSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_binlog_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get DiskBinLogSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		DiskBinLogSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_err_log_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get DiskErrorLogSize %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		DiskErrorLogSize = size
		mu.Unlock()
		return nil
	})

	g.Go(func() error {
		size, err := getLogSize("tob_rds_disk_usage_bytes")
		if err != nil {
			log.Warn(ctx, "Get UsedStorage %s failed: %v", req.InstanceId, err)
			return err
		}
		mu.Lock()
		UsedStorage = size
		mu.Unlock()
		return nil
	})

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		return nil, err
	}

	return &datasource.GetDiskSizeResp{
		UserDataUsageSize: UserDataUsageSize,
		TmpUsedStorage:    TmpUsedStorage,
		SysUsedStorage:    SysUsedStorage,
		RedoLogSize:       RedoLogSize,
		UndoLogSize:       UndoLogSize,
		RelayLogSize:      RelayLogSize,
		AuditLogSize:      AuditLogSize,
		DiskSlowLogSize:   DiskSlowLogSize,
		UsedStorage:       UsedStorage,
		DiskBinLogSize:    DiskBinLogSize,
		DiskErrorLogSize:  DiskErrorLogSize,
		OtherUsageSize:    OtherUsageSize,
	}, nil
}

func (self *mysqlImpl) DescribeTableSpace(ctx context.Context, req *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceResp, error) {
	log.Info(ctx, "DescribeTableSpace-enter1-ds %s", utils.Show(req))
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	// tableInfo
	whereCase := " WHERE tables.TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'byte_rds_meta') AND tables.TABLE_TYPE = 'BASE TABLE' "
	if len(req.Database) > 0 {
		whereCase += " AND tables.TABLE_SCHEMA = '" + req.Database + "' "
	}

	if len(req.TableName) > 0 {
		whereCase += " AND tables.TABLE_NAME = '" + req.TableName + "' "
	}
	sumSpace, err := self.getSumSpace(conn, whereCase)

	if err != nil {
		return nil, err
	}
	log.Info(ctx, "DescribeTableSpace-enter1-whereCase %s", whereCase)
	tableInfoList, err := self.getInformationSchemaTablesInfo(conn, req, whereCase, sumSpace)
	if err != nil {
		return nil, err
	}
	// sum
	countSQl := "/*+ DBW SQL CONSOLE DEFAULT*/ SELECT COUNT(1) FROM information_schema.tables as tables" + whereCase // ignore_security_alert
	ret := &shared.DescribeTableSpaceResp{}
	if err = conn.Raw(countSQl).
		Scan(&ret.Total); err != nil {
		return nil, err
	}
	ret.TableStats = CovertTableSpaceInfo(tableInfoList)
	return ret, nil
}

func (self *mysqlImpl) ConvertTableSpaceToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp {
	var tableStats []*model.TableStat
	for _, value := range resp.TableStats {
		tableStat := &model.TableStat{
			Name:                   value.Name,
			DB:                     value.DB,
			TableSpace:             &value.TableSpace,
			TableSpaceRatio:        &value.TableSpaceRatio,
			Engine:                 &value.Engine,
			IndexLength:            &value.IndexLength,
			DataLength:             &value.DataLength,
			TableRows:              &value.TableRows,
			SpaceFragmentationRate: &value.SpaceFragmentationRate,
			AutoIdUsedRate:         &value.AutoIdUsedRate,
			AvgRowLength:           &value.AvgRowLength,
		}
		tableStats = append(tableStats, tableStat)
	}
	return &model.DescribeTableSpaceResp{
		Total:      resp.Total,
		TableStats: tableStats,
	}
}

func (self *mysqlImpl) FormatDescribeStorageCapacityResp(sourceType shared.DataSourceType, diskSize *datasource.GetDiskSizeResp, storageSpace float64) *model.DescribeStorageCapacityResp {

	var usedSize float64 // 根据实际类型初始化

	if diskSize != nil {
		if diskSize.IsNew == 1 {
			usedSize = diskSize.UsedStorage
		} else {
			usedSize = diskSize.DiskLogSize + diskSize.DiskDataSize
		}
	} else {
		// 处理 diskSize 为 nil 的情况，例如：
		usedSize = diskSize.DiskLogSize + diskSize.DiskDataSize
	}

	otherLogSize := diskSize.DiskLogSize - diskSize.DiskSlowLogSize -
		diskSize.DiskBinLogSize - diskSize.DiskErrorLogSize
	if otherLogSize < 0 {
		otherLogSize = 0
	}

	resp := &model.DescribeStorageCapacityResp{
		UsedSize:         diagnosis.DecimalFloat(usedSize),
		DiskDataSize:     diagnosis.DecimalFloat(diskSize.DiskDataSize),
		DiskLogSize:      diagnosis.DecimalFloat(diskSize.DiskLogSize),
		DiskSlowLogSize:  diagnosis.DecimalPointFloat(diskSize.DiskSlowLogSize),
		DiskBinLogSize:   diagnosis.DecimalPointFloat(diskSize.DiskBinLogSize),
		DiskErrorLogSize: diagnosis.DecimalPointFloat(diskSize.DiskErrorLogSize),
		FreeSize:         diagnosis.DecimalPointFloat(storageSpace - usedSize),
		OtherLogSize:     diagnosis.DecimalPointFloat(otherLogSize),
		StorageSpace:     diagnosis.DecimalPointFloat(storageSpace),

		RedoLogSize:    diagnosis.DecimalPointFloat(diskSize.RedoLogSize),
		UndoLogSize:    diagnosis.DecimalPointFloat(diskSize.UndoLogSize),
		SysUsedStorage: diagnosis.DecimalPointFloat(diskSize.SysUsedStorage),
		TmpUsedStorage: diagnosis.DecimalPointFloat(diskSize.TmpUsedStorage),
		//RelayLogSize:      diagnosis.DecimalPointFloat(diskSize.RelayLogSize),
		AuditLogSize:      diagnosis.DecimalPointFloat(diskSize.AuditLogSize),
		UserDataUsageSize: diagnosis.DecimalPointFloat(diskSize.UserDataUsageSize),
		OtherUsageSize:    diagnosis.DecimalPointFloat(diskSize.OtherUsageSize),
		IsNew:             &diskSize.IsNew,
	}
	return resp
}

func (self *mysqlImpl) getSumSpace(conn db.Conn, whereCase string) (*int64, error) {
	sumSql := "/*+ DBW SQL CONSOLE DEFAULT*/ select COALESCE(SUM(tables.INDEX_LENGTH + tables.DATA_LENGTH + tables.DATA_FREE),0) as sumTableSpace from information_schema.tables as tables " + whereCase + " limit 2000" // ignore_security_alert
	var num int64 = 0
	sum := &num
	if err := conn.Raw(sumSql).Scan(sum); err != nil {
		return nil, err
	}
	return sum, nil
}

func (self *mysqlImpl) getInformationSchemaTablesInfo(conn db.Conn, req *datasource.DescribeTableSpaceReq, whereCase string, sumSpace *int64) ([]*TableStat, error) {
	orderInfo := self.getInformationSchemaTablesInfoOrderInfo(req.OrderItem, req.OrderRule)
	querySql := DBW_CONSOLE_DEFAULT_HINT + "SELECT tables.TABLE_NAME as Name, tables.TABLE_SCHEMA as DB, tables.ENGINE as Engine, tables.INDEX_LENGTH as IndexLength, " +
		" tables.DATA_LENGTH as DataLength, tables.TABLE_ROWS as TableRows,  " +
		" (tables.DATA_FREE / (tables.INDEX_LENGTH + tables.DATA_LENGTH + tables.DATA_FREE)) as SpaceFragmentationRate,  " +
		" COALESCE(tables.AUTO_INCREMENT, 0) as AutoIdUsedRate, (tables.INDEX_LENGTH + tables.DATA_LENGTH + tables.DATA_FREE) as TableSpace, " +
		" tables.AVG_ROW_LENGTH as AvgRowLength,  (tables.INDEX_LENGTH + tables.DATA_LENGTH + tables.DATA_FREE) / " + strconv.FormatInt(*sumSpace, 10) + " as TableSpaceRatio " +
		" FROM information_schema.tables as tables  " +
		whereCase + " order by " + orderInfo +
		" LIMIT ? OFFSET ? " // ignore_security_alert
	var args []interface{}
	args = append(args, req.Limit, req.Offset)
	var tableInfoList []*TableStat
	if err := conn.Raw(querySql, args...).Scan(&tableInfoList); err != nil {
		return nil, err
	}
	return tableInfoList, nil
}

func (self *mysqlImpl) getInformationSchemaTablesInfoOrderInfo(orderItem string, orderRule string) string {
	// 用lowercase忽略大小写
	switch strings.ToLower(orderItem) {
	case "name":
		if strings.EqualFold(orderRule, "DESC") {
			return " Name DESC "
		} else {
			return " Name ASC "
		}
	case "db":
		if strings.EqualFold(orderRule, "DESC") {
			return " DB DESC "
		} else {
			return " DB ASC "
		}
	case "engine":
		if strings.EqualFold(orderRule, "DESC") {
			return " Engine DESC "
		} else {
			return " Engine ASC "
		}
	case "datalength":
		if strings.EqualFold(orderRule, "DESC") {
			return " DataLength DESC "
		} else {
			return " DataLength ASC "
		}
	case "tablerows":
		if strings.EqualFold(orderRule, "DESC") {
			return " TableRows DESC "
		} else {
			return " TableRows ASC "
		}
	case "spacefragmentationrate":
		if strings.EqualFold(orderRule, "DESC") {
			return " SpaceFragmentationRate DESC "
		} else {
			return " SpaceFragmentationRate ASC "
		}
	case "autoidusedrate":
		if strings.EqualFold(orderRule, "DESC") {
			return " AutoIdUsedRate DESC "
		} else {
			return " AutoIdUsedRate ASC "
		}
	case "tablespace":
		if strings.EqualFold(orderRule, "DESC") {
			return " TableSpace DESC "
		} else {
			return " TableSpace ASC "
		}
	case "avgrowlength":
		if strings.EqualFold(orderRule, "DESC") {
			return " AvgRowLength DESC "
		} else {
			return " AvgRowLength ASC "
		}
	case "tablespaceratio":
		if strings.EqualFold(orderRule, "DESC") {
			return " TableSpaceRatio DESC "
		} else {
			return " TableSpaceRatio ASC "
		}
	case "indexlength":
		if strings.EqualFold(orderRule, "DESC") {
			return " IndexLength DESC "
		} else {
			return " IndexLength ASC "
		}
	}
	return " TableSpace DESC "
}

// ignore error?
func (m *mysqlImpl) IsMyOwnInstance(ctx context.Context, instanceId string, _ shared.DataSourceType) bool {
	_, err := m.describeInstance(ctx, instanceId)
	if err != nil {
		log.Warn(ctx, "describeInstance error is %s", err.Error())
		return !isMySQLInstanceNotFoundError(err)
	}
	return true
}
func (m *mysqlImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	var (
		dbInstanceStatusBlackList map[string]string
		blackList                 []string
		rawBlackList              string
	)
	resp, err := m.describeInstance(ctx, instanceId)
	if err != nil {
		log.Warn(ctx, "describeInstance error is %s", err.Error())
		return err
	}
	cfg := m.cnf.Get(ctx)
	if isConnectedInstance {
		rawBlackList = cfg.DBInstanceStateWithConnectionBlackList
	} else {
		rawBlackList = cfg.DBInstanceStateWithoutConnectionBlackList
	}
	//rawBlackList = "{\"MySQL\":\"Creating,Deleting\"}"
	log.Info(ctx, "rawBlackList is %s", rawBlackList)
	err = json.Unmarshal([]byte(rawBlackList), &dbInstanceStatusBlackList)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		blackList = strings.Split(dbInstanceStatusBlackList[ds.String()], ",")
	}
	currentStatus := resp.BasicInfo.InstanceStatus.String()
	for _, item := range blackList {
		if item == currentStatus {
			log.Warn(ctx, "instance status is %s, not support", currentStatus)
			return consts.ErrorWithParam(model.ErrorCode_InstanceNotInRunningStatus, currentStatus)
		}
	}
	return nil
}

func (self *mysqlImpl) DescribeAutoKillSessionConfig(ctx context.Context, req *datasource.DescribeAutoKillSessionConfigReq) (*datasource.DescribeAutoKillSessionConfigResp, error) {
	describeSQLKillConfigReq := &rdsModel_v2.DescribeSQLKillConfigReq{
		InstanceId: req.InstanceId,
	}
	resp := &rdsModel_v2.DescribeSQLKillConfigResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeSQLKillConfig.String(), describeSQLKillConfigReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds DescribeSQLKillConfig, err=%+v", err)
		return nil, err
	}
	return &datasource.DescribeAutoKillSessionConfigResp{
		SQLKillStatus:  resp.SQLKillStatus,
		MaxExecTime:    resp.MaxExecTime,
		ProtectedUsers: resp.GetProtectedUsers(),
	}, nil
}

func (self *mysqlImpl) ModifyAutoKillSessionConfig(ctx context.Context, req *datasource.ModifyAutoKillSessionConfigReq) (*datasource.ModifyAutoKillSessionConfigResp, error) {
	modifySQLKillConfigReq := &rdsModel_v2.ModifySQLKillConfigReq{
		InstanceId: req.InstanceId,
	}
	if req.MaxExecTime != nil {
		modifySQLKillConfigReq.MaxExecTime = req.MaxExecTime
	}
	if req.SQLKillStatus != nil {
		modifySQLKillConfigReq.EnableSQLKill = req.SQLKillStatus
	}
	if req.ProtectedUsers != nil {
		modifySQLKillConfigReq.ProtectedUsers = req.ProtectedUsers
	}
	resp := &rdsModel_v2.ModifySQLKillConfigResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_ModifySQLKillConfig.String(), modifySQLKillConfigReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds ModifySQLKillConfig, err=%+v", err)
		return nil, err
	}
	return &datasource.ModifyAutoKillSessionConfigResp{}, nil
}

func (self *mysqlImpl) DescribeInstanceVersion(ctx context.Context, req *datasource.DescribeInstanceVersionReq) (*datasource.DescribeInstanceVersionResp, error) {
	describeInstanceVersionReq := &rdsModel.InnerDescribeInstanceVersionReq{
		InstanceId: req.InstanceId,
	}
	resp := &rdsModel.InnerDescribeInstanceVersionResp{}
	err := self.mysql.Get().Call(ctx, rdsModel.Action_InnerDescribeInstanceVersion.String(), describeInstanceVersionReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V1))
	if err != nil {
		log.Warn(ctx, "failed to call Rds InnerDescribeInstanceVersion, err=%+v", err)
		return nil, err
	}
	return &datasource.DescribeInstanceVersionResp{
		Version:                   resp.GetEpicVersionNumber(),
		MysqlComponentEpicVersion: resp.ComponentEpicVersion,
	}, nil
}

func (m *mysqlImpl) DescribeInstanceReplicaDelay(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (int64, error) {
	describeDBInstanceDetailReq := &rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err := m.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return -1, err
	}
	var replicaDelay int64 = 0
	for _, val := range describeDBInstanceDetailResp.Nodes {
		if val.SyncDelay > replicaDelay {
			replicaDelay = val.SyncDelay
		}
	}
	return replicaDelay, nil
}

func (m *mysqlImpl) GetInstanceSlaveAddress(ctx types.Context, req *datasource.GetInstanceSlaveAddressReq) (*datasource.GetInstanceSlaveAddressResp, error) {
	describeDBInstanceDetailReq := &rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err := m.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return nil, err
	}
	resp := &datasource.GetInstanceSlaveAddressResp{Address: []string{}}
	for _, val := range describeDBInstanceDetailResp.Nodes {
		if val.NodeType != rdsModel_v2.NodeType_Primary {
			ip, port, err := m.getNodeEndpoint(ctx, &shared.DataSource{InstanceId: req.InstanceId, NodeId: val.NodeId})
			if err != nil {
				log.Warn(ctx, "getNodeEndpoint, err=%v", err)
				return nil, err
			}
			resp.Address = append(resp.Address, fmt.Sprintf("%s:%s", ip, port))
		}
	}
	return resp, nil
}

func (self *mysqlImpl) ListErrLogs(ctx context.Context, req *datasource.ListErrLogsReq) (*datasource.ListErrLogsResp, error) {
	panic("not implemented")
	//listErrorLogRecordsReq := &rdsModel.ListErrorLogRecordsReq{
	//	InstanceId: req.InstanceId,
	//	StartTime:  int64(req.StartTime),
	//	EndTime:    int64(req.EndTime),
	//	Limit:      req.Limit,
	//	Context:    req.Offset,
	//}
	//if req.LogLevel != nil {
	//	if err := fp.StreamOf(req.LogLevel).Map(func(level model.ErrLogLevel) rdsModel.VerbosityLevel {
	//		switch level {
	//		case model.ErrLogLevel_Note:
	//			return rdsModel.VerbosityLevel_Note
	//		case model.ErrLogLevel_Warning:
	//			return rdsModel.VerbosityLevel_Warning
	//		case model.ErrLogLevel_Error:
	//			return rdsModel.VerbosityLevel_Error
	//		default:
	//			return rdsModel.VerbosityLevel_Warning
	//		}
	//	}).ToSlice(&listErrorLogRecordsReq.VerbosityLevel); err != nil {
	//		return nil, err
	//	}
	//}
	//if req.NodeIds != nil {
	//	listErrorLogRecordsReq.NodeName = req.NodeIds
	//}
	//if req.Keyword != "" {
	//	listErrorLogRecordsReq.Message = utils.StringRef(req.Keyword)
	//}
	//// 需要调用运维面接口
	//resp := &rdsModel.ListErrorLogRecordsResp{}
	//if err := self.mysql.Get().Call(ctx, rdsModel.Action_ListErrorLogRecords.String(), listErrorLogRecordsReq, resp, client.WithTenantID("1")); err != nil {
	//	log.Warn(ctx, "get error logs failed %+v", err)
	//	return nil, err
	//}
	//ret := &datasource.ListErrLogsResp{
	//	Total:   resp.Total,
	//	Context: resp.Context,
	//}
	//if err := fp.StreamOf(resp.Datas).
	//	Map(func(record *rdsModel.ErrorLogRecord) *model.ErrLog {
	//		level, err := model.ErrLogLevelFromString(record.VerbosityLevel.String())
	//		if err != nil {
	//			log.Error(ctx, "rds errLog level definition has changed %+v", err)
	//		}
	//		item := &model.ErrLog{
	//			Content:   record.ErrorInfo,
	//			LogLevel:  level,
	//			Timestamp: convertDateStringToTimestamp(ctx, record.CreateTime), // CreateTime为本地时间
	//		}
	//		return item
	//	}).ToSlice(&ret.ErrLogs); err != nil {
	//	return nil, err
	//}
	//return ret, nil
}

func (self *mysqlImpl) getEndpointDetail(ctx context.Context, instanceId string, port string) *SimplifiedEndpointInfo {
	describeDBInstanceDetailReq := &rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	describeDBInstanceDetailResp := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetail InstanceNotFound, err=%v", err)
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		}
		return nil
	}
	for _, ep := range describeDBInstanceDetailResp.Endpoints {
		if ep.GetEndpointProxyPort() == port {
			return &SimplifiedEndpointInfo{
				EndpointId:    ep.EndpointId,
				EndpointName:  ep.EndpointName,
				ReadWriteMode: ep.GetReadWriteMode().String(),
			}
		}
	}
	return nil
}

func convertDateStringToTimestamp(ctx context.Context, DateTime string) int64 {
	layout := "2006-01-02 15:04:05" // 日期格式
	t, err := time.Parse(layout, DateTime)
	if err != nil {
		log.Warn(ctx, "parse %s string failed:%+v", DateTime, err)
		//解析失败返回当前时间戳
		return time.Now().Unix()
	}
	// 将时间转换为秒级时间戳
	ret := t.Unix()
	return ret
}
func (self *mysqlImpl) ModifyProxyThrottleRule(ctx context.Context, req *datasource.ModifyProxyThrottleRuleReq) (*datasource.ModifyProxyThrottleRuleResp, error) {
	proxyThrottleRuleOperation, _ := rdsModel.ProxyThrottleRuleOperationFromString(req.Action)
	proxyThrottleType, _ := rdsModel.ProxyThrottleTypeFromString(req.ProxyThrottleRule.ThrottleTarget)
	ruleInfo := &rdsModel.ProxyThrottleRule{
		Value: int64(req.ProxyThrottleRule.ThrottleThreshold),
	}
	switch proxyThrottleType {
	case rdsModel.ProxyThrottleType_FingerQPS:
		ruleInfo.Finger = &req.ProxyThrottleRule.ThrottleFingerPrint
	case rdsModel.ProxyThrottleType_PsmDbQPS:
		ruleInfo.PSM = &req.ProxyThrottleRule.ThrottleHost
		ruleInfo.DB = &req.ProxyThrottleRule.ThrottleDB
	case rdsModel.ProxyThrottleType_SqlQPS:
		ruleInfo.Sql = &req.ProxyThrottleRule.ThrottleSqlText
	case rdsModel.ProxyThrottleType_KeywordQPS:
		ruleInfo.Keyword = &req.ProxyThrottleRule.Keywords
	}
	rreq := &rdsModel.InnerModifyProxyThrottleRuleReq{
		InstanceId:                 req.InstanceId,
		EndpointId:                 req.ProxyThrottleRule.EndpointID,
		ProxyThrottleRuleOperation: proxyThrottleRuleOperation,
		ProxyThrottleType:          proxyThrottleType,
		ProxyThrottleRule:          ruleInfo,
	}
	rresp := &rdsModel.InnerModifyProxyThrottleRuleResp{}
	err := self.mysql.Get().Call(ctx, rdsModel.Action_InnerModifyProxyThrottleRule.String(), rreq, rresp, client.WithVersion(consts.RDS_MySQL_Version_V1))
	if err != nil {
		log.Warn(ctx, "failed to call Rds InnerModifyProxyThrottleRule, err=%+v", err)
		return nil, err
	}
	ret := &datasource.ModifyProxyThrottleRuleResp{}
	return ret, nil
}

func (m *mysqlImpl) DescribeDBInstances(ctx context.Context, req *rdsModel_v2.DescribeDBInstancesReq) (*rdsModel_v2.DescribeDBInstancesResp, error) {
	if req.PageSize == nil || req.PageNumber == nil {
		// 如果没这两个值，直接调用接口
		return m.selfDescribeDBInstances(ctx, req)
	}
	pageInfo, err := ds_utils.FormatLowerPageInfo(*req.PageNumber, *req.PageSize)
	if err != nil {
		return nil, err
	}
	result := &rdsModel_v2.DescribeDBInstancesResp{InstancesInfo: []*rdsModel_v2.InstanceInfoObject{}, Instances: []*rdsModel_v2.InstanceInfoObject{}, Total: int32(math.MaxInt32)}

	req.PageSize = utils.Int32Ref(pageInfo.PageSize)
	// 为了防止一次性拉挂RDS，我们这里对传入对PageNum和PageSize 做一次重新处理
	for pageNumber := pageInfo.PageNumLower; pageNumber <= pageInfo.PageNumUpper && (pageNumber-1)*pageInfo.PageSize < result.Total; pageNumber++ {
		req.PageNumber = utils.Int32Ref(pageNumber)
		resp, err := m.selfDescribeDBInstances(ctx, req)
		if err != nil {
			return nil, err
		}
		result.Total = resp.Total
		result.Instances = append(result.Instances, resp.Instances...)
	}
	if result.Total == int32(math.MaxInt32) {
		result.Total = 0
	}
	return result, nil
}

func (m *mysqlImpl) selfDescribeDBInstances(ctx context.Context, req *rdsModel_v2.DescribeDBInstancesReq) (*rdsModel_v2.DescribeDBInstancesResp, error) {
	desResp := &rdsModel_v2.DescribeDBInstancesResp{}
	if err := m.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstances.String(), req, desResp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to get rds instances, err=%v", err)
		return nil, err
	}
	return desResp, nil
}

func (m *mysqlImpl) selfDescribeDBInstancesInner(ctx context.Context, req *rdsModel_v2.InnerDescribeDBInstancesReq) (*rdsModel_v2.InnerDescribeDBInstancesResp, error) {
	desResp := &rdsModel_v2.InnerDescribeDBInstancesResp{}
	if err := m.mysql.Get().Call(ctx, rdsModel_v2.Action_InnerDescribeDBInstances.String(), req, desResp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to get rds instances, err=%v", err)
		return nil, err
	}
	return desResp, nil
}

type NewMetaRDSDataSourceIn struct {
	dig.In
	Conf       config.ConfigProvider
	MetaRDSMgr mgr.Provider `name:"meta_rds"`
}

func (self *mysqlImpl) ModifySQLKillRule(ctx context.Context, req *datasource.ModifySQLKillRuleReq) (*datasource.ModifySQLKillRuleResp, error) {
	// 查询实例当前的sql kill规则
	listSQLKillRuleReq := &rdsModel_v2.DescribeSQLKillConfigV2Req{
		InstanceId: req.InstanceId,
	}
	sqlKillRules := &rdsModel_v2.DescribeSQLKillConfigV2Resp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeSQLKillConfigV2.String(), listSQLKillRuleReq, sqlKillRules, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds DescribeSQLKillConfigV2, err=%+v", err)
		return nil, err
	}
	modifySQLKillRuleReq := &rdsModel_v2.ModifySQLKillConfigV2Req{}
	switch req.Action {
	case model.KillSqlEventType_Add.String():
		killRule := req.KillRule[0]
		sqlType := strings.Split(killRule.SqlType, ",")
		nodeType := strings.Split(killRule.NodeType, ",")
		protectedUsers := strings.Split(req.ProtectedUsers, ",")
		sqlKillRule := &rdsModel_v2.SQLKillInfoObject{
			MaxExecTime: utils.Int64Ref(killRule.MaxExecTime),
			SQLType:     sqlType,
			NodeType:    nodeType,
		}
		addedRule := sqlKillRules.SQLKillInfo
		addedRule = append(addedRule, sqlKillRule)
		modifySQLKillRuleReq = &rdsModel_v2.ModifySQLKillConfigV2Req{
			InstanceId:     req.InstanceId,
			SQLKillInfo:    addedRule,
			EnableSQLKill:  utils.BoolRef(true),
			ProtectedUsers: protectedUsers,
		}
	case model.KillSqlEventType_Delete.String():
		submittedRuleSet, err := self.filterDeletedSqlRule(req, sqlKillRules)
		if err != nil {
			return nil, err
		}
		modifySQLKillRuleReq = &rdsModel_v2.ModifySQLKillConfigV2Req{
			InstanceId:  req.InstanceId,
			SQLKillInfo: submittedRuleSet,
		}
		// SQLKillInfo为空的时候，需要把EnableSQLKill设置为false,为true时，rds会触发告警，但不影响功能使用
		if len(submittedRuleSet) < 1 {
			modifySQLKillRuleReq.EnableSQLKill = utils.BoolRef(false)
			modifySQLKillRuleReq.SQLKillInfo = make([]*rdsModel_v2.SQLKillInfoObject, 1) // rds接口问题，若规则清0，长度需要至少为1
		} else {
			modifySQLKillRuleReq.EnableSQLKill = utils.BoolRef(true)
			modifySQLKillRuleReq.SQLKillInfo = make([]*rdsModel_v2.SQLKillInfoObject, 1) // rds接口问题，若规则清0，长度需要至少为1
		}
	case model.KillSqlEventType_Stop.String():
		submittedRuleSet, err := self.filterDeletedSqlRule(req, sqlKillRules)
		if err != nil {
			return nil, err
		}
		modifySQLKillRuleReq = &rdsModel_v2.ModifySQLKillConfigV2Req{
			InstanceId:  req.InstanceId,
			SQLKillInfo: submittedRuleSet,
		}
		// SQLKillInfo为空的时候，需要把EnableSQLKill设置为false,为true时，rds会触发告警，但不影响功能使用
		if len(submittedRuleSet) < 1 {
			modifySQLKillRuleReq.EnableSQLKill = utils.BoolRef(false)
			modifySQLKillRuleReq.SQLKillInfo = make([]*rdsModel_v2.SQLKillInfoObject, 1) // rds接口问题，若规则清0，长度需要至少为1
		} else {
			modifySQLKillRuleReq.EnableSQLKill = utils.BoolRef(true)
			modifySQLKillRuleReq.SQLKillInfo = make([]*rdsModel_v2.SQLKillInfoObject, 1) // rds接口问题，若规则清0，长度需要至少为1
		}
	default:
		log.Warn(ctx, "Not supported kill sql event type %s", req.Action)
	}
	resp := &rdsModel_v2.ModifySQLKillConfigV2Resp{}
	err = self.mysql.Get().Call(ctx, rdsModel_v2.Action_ModifySQLKillConfigV2.String(), modifySQLKillRuleReq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds ModifySQLKillConfigV2, err=%+v", err)
		return nil, err
	}
	return &datasource.ModifySQLKillRuleResp{}, nil
}

func (self *mysqlImpl) CreateDBInstance(ctx context.Context, req *rdsModel_v2.CreateDBInstanceReq) (*rdsModel_v2.CreateDBInstanceResp, error) {

	resp := &rdsModel_v2.CreateDBInstanceResp{}
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_CreateDBInstance.String(), req, resp, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "CreateDBInstance fail, req is:%v, error:%s", utils.Show(req), err)
		return nil, err
	}
	return resp, nil
}

func (self *mysqlImpl) DescribeDBInstanceDetailForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	describeDBInstanceDetailReq := &rdsModel_v2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetailForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetailForPilot, err=%v", err)
			return "", err
		}
	}

	return utils.Show(describeDBInstanceDetailResp), nil
}

func (self *mysqlImpl) DescribeDBInstanceParametersForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	describeDBInstanceDetailReq := &rdsModel_v2.DescribeDBInstanceParametersReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &rdsModel_v2.DescribeDBInstanceParametersResp{}
	log.Info(ctx, "DescribeDBInstanceParametersForPilot req is :%s", utils.Show(describeDBInstanceDetailReq))
	err := self.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBInstanceParameters.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetailForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetailForPilot, err=%v", err)
			return "", err
		}
	}

	return utils.Show(describeDBInstanceDetailResp), nil
}

func (self *mysqlImpl) filterDeletedSqlRule(req *datasource.ModifySQLKillRuleReq, sqlKillRules *rdsModel_v2.DescribeSQLKillConfigV2Resp) ([]*rdsModel_v2.SQLKillInfoObject, error) {
	var (
		deletedRuleSet []*rdsModel_v2.SQLKillInfoObject
	)
	submittedRuleSet := make([]*rdsModel_v2.SQLKillInfoObject, 0)
	if err := fp.StreamOf(req.KillRule).Map(func(killRule *datasource.KillRuleInfo) *rdsModel_v2.SQLKillInfoObject {
		sqlType := strings.Split(killRule.SqlType, ",")
		nodeType := strings.Split(killRule.NodeType, ",")
		return &rdsModel_v2.SQLKillInfoObject{
			SQLType:     sqlType,
			NodeType:    nodeType,
			MaxExecTime: utils.Int64Ref(killRule.MaxExecTime),
		}
	}).ToSlice(&deletedRuleSet); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	for _, rule := range sqlKillRules.SQLKillInfo {
		var isExisted bool
		hashValue := hashRule(rule)
		for _, r := range deletedRuleSet {
			hv := hashRule(r)
			if hashValue == hv {
				isExisted = true
				break
			}
		}
		// 没有命中删除的规则，则继续保留
		if !isExisted {
			submittedRuleSet = append(submittedRuleSet, rule)
		}
	}
	return submittedRuleSet, nil
}

func NewMetaRDSDataSource(p NewMetaRDSDataSourceIn) NewMySQLDataSourceOut {
	return NewMySQLDataSourceOut{
		Source: retryIfWhiteListNotReady(&metaRDSImpl{
			mysqlImpl: mysqlImpl{
				DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
				cnf:               p.Conf,
				mysql:             p.MetaRDSMgr,
			},
		}),
	}
}

type NewMetaRDSDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

type metaRDSImpl struct {
	mysqlImpl
}

func (mi *metaRDSImpl) Type() shared.DataSourceType {
	return shared.MetaRDS
}

// 计算结构体的哈希值
func hashRule(target interface{}) string {
	// 将结构体转换为 JSON 字符串
	jsonData, err := json.Marshal(target)
	if err != nil {
		return ""
	}
	hash := sha256.New()
	_, err = hash.Write(jsonData)
	if err != nil {
		return ""
	}
	hashBytes := hash.Sum(nil)
	return hex.EncodeToString(hashBytes)
}

func (self *mysqlImpl) DescribeTableSpaceAutoIncr(ctx context.Context, req *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceAutoIncrResp, error) {
	log.Info(ctx, "DescribeTableSpaceAutoIncr-ds %s", utils.Show(req))
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	tableInfoList, err := self.getTablesInfoAutoIncr(ctx, conn, req)
	if err != nil {
		return nil, err
	}
	ret := &shared.DescribeTableSpaceAutoIncrResp{}
	ret.TableStatAutoIncr = CovertTableSpaceInfoAutoIncr(tableInfoList)
	ret.Total = int32(len(tableInfoList))
	return ret, nil
}

func (self *mysqlImpl) getTablesInfoAutoIncr(ctx context.Context, conn db.Conn, req *datasource.DescribeTableSpaceReq) ([]*TableStatAutoIncr, error) {
	querySql := DBW_CONSOLE_DEFAULT_HINT +
		"SELECT " +
		"c.TABLE_SCHEMA as DBName," +
		"c.TABLE_NAME as TableName, " +
		"c.COLUMN_NAME as ColumnName, " +
		"ROUND((t.AUTO_INCREMENT / POW(2, CASE c.DATA_TYPE " +
		"       WHEN 'tinyint' THEN 8 " +
		"       WHEN 'smallint' THEN 16 " +
		"       WHEN 'mediumint' THEN 24 " +
		"       WHEN 'int' THEN 31 " +
		"       WHEN 'bigint' THEN 63 " +
		"       ELSE 0 END) * 100), 2) AS AutoIncrementRatio, " +
		"   t.AUTO_INCREMENT as AutoIncrement, " +
		"   c.DATA_TYPE as DataType, " +
		"   POW(2, CASE c.DATA_TYPE " +
		"       WHEN 'tinyint' THEN 8 " +
		"       WHEN 'smallint' THEN 16 " +
		"       WHEN 'mediumint' THEN 24 " +
		"       WHEN 'int' THEN 31 " +
		"       WHEN 'bigint' THEN 63 " +
		"       ELSE 0 END) - 1 AS 'MaxValue' " +
		"FROM " +
		"   INFORMATION_SCHEMA.COLUMNS c " +
		"JOIN " +
		"   INFORMATION_SCHEMA.TABLES t " +
		"ON " +
		"   c.TABLE_SCHEMA = t.TABLE_SCHEMA " +
		"   AND c.TABLE_NAME = t.TABLE_NAME " +
		"WHERE " +
		"   c.EXTRA LIKE '%auto_increment%' " +
		"   AND t.AUTO_INCREMENT IS NOT NULL " +
		"   AND (t.AUTO_INCREMENT / POW(2, CASE c.DATA_TYPE " +
		"       WHEN 'tinyint' THEN 8 " +
		"       WHEN 'smallint' THEN 16 " +
		"       WHEN 'mediumint' THEN 24 " +
		"       WHEN 'int' THEN 31 " +
		"       WHEN 'bigint' THEN 63 " +
		"       ELSE 0 END) * 100) > 90 " +
		"ORDER BY " +
		"   AutoIncrementRatio DESC" +
		" LIMIT ? OFFSET ? " // ignore_security_alert
	var args []interface{}
	args = append(args, req.Limit, req.Offset)
	var tableInfoList []*TableStatAutoIncr
	log.Info(ctx, "getTablesInfoAutoIncr-querySql %s", utils.Show(querySql))
	if err := conn.Raw(querySql, args...).Scan(&tableInfoList); err != nil {
		return nil, err
	}
	return tableInfoList, nil
}

package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/ds_utils"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	utils2 "code.byted.org/videoarch/cloud_gopkg/utils"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/bytebrain"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/monitor/influxdb"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	shardingModelV1 "code.byted.org/infcs/dbw-mgr/gen/mysql-sharding-mgr/2018-01-01/kitex_gen/model"
	shardingModel "code.byted.org/infcs/dbw-mgr/gen/mysql-sharding-mgr/2022-01-01/kitex_gen/model/v2"
)

const (
	ShardingDialogAnalysis = "select * from information_schema.processlist " // sys.schema_table_lock_waits 授予Normal无法执行
	ShardingProxyDialog    = "veproxy show processlist"
)

var ShardingProxyPortsList = []string{"3679", "3680", "3682"}

type NewMySQLShardingDataSourceIn struct {
	dig.In
	Conf            config.ConfigProvider
	MgrClient       mgr.Provider `name:"sharding"`
	C3ConfProvider  c3.ConfigProvider
	L               location.Location
	MonitorClient   *influxdb.RdsMonitor
	ShuttleSvc      shuttle.PGWShuttleService
	ByteBrainClient bytebrain.ByteBrainService
	IdSvc           idgen.Service
	ActorClient     cli.ActorClient
}

type NewMySQLShardingDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewMySQLShardingDataSource(p NewMySQLShardingDataSourceIn) NewMySQLShardingDataSourceOut {
	return NewMySQLShardingDataSourceOut{
		Source: retryIfWhiteListNotReady(&mysqlShardingImpl{&mysqlImpl{
			DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
			cnf:               p.Conf,
			mysql:             p.MgrClient,
			C3ConfProvider:    p.C3ConfProvider,
			L:                 p.L,
			MonitorClient:     p.MonitorClient,
			ShuttleSvc:        p.ShuttleSvc,
			ByteBrainClient:   p.ByteBrainClient,
			IdSvc:             p.IdSvc,
			ActorClient:       p.ActorClient,
		}}),
	}
}

type mysqlShardingImpl struct {
	*mysqlImpl
}

func (self *mysqlShardingImpl) AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error) {
	return "", nil
}

func (self *mysqlShardingImpl) DescribeDBInstanceSSL(ctx context.Context, req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	return &datasource.DescribeDBInstanceSSLResp{
		SSLEnable: false,
	}, nil
}

func (self *mysqlShardingImpl) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	nds := new(shared.DataSource)
	*nds = *ds
	ip, port, err := self.DescribeInnerConnectionInfo(ctx, nds)
	if err != nil {
		return err
	}
	nds.Address = fmt.Sprintf("%s:%s", ip, port)
	log.Info(ctx, "ip is %s port is %s", ip, port)
	conn, err := self.getConn(ctx, nds)
	if err != nil {
		log.Warn(ctx, "check conn by fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	_, err = conn.Version()
	if err != nil {
		log.Warn(ctx, "get version by %v fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())

	}
	return nil
}

func (self *mysqlShardingImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	ip, port, err := self.DescribeInnerConnectionInfo(ctx, ds)
	if err != nil {
		log.Warn(ctx, "get instance %s address error %s", ds.InstanceId, err)
		return err
	}
	ds.Address = fmt.Sprintf("%s:%s", ip, port)
	return nil
}

func (self *mysqlShardingImpl) DescribeInnerConnectionInfo(ctx context.Context, ds *shared.DataSource) (ip, port string, err error) {
	req := &shardingModel.DescribeDBInstanceDetailReq{
		InstanceId: ds.InstanceId,
	}
	resp := &shardingModel.DescribeDBInstanceDetailResp{}
	if err = self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstanceDetail.String(), req, resp, client.WithTenantID("1")); err != nil {
		log.Warn(ctx, "describe mysql sharding instance %s error %v", ds.InstanceId, err)
		return
	}
	for _, conn := range resp.ConnectionInfo {
		if conn.EndpointType == shardingModel.EndpointType_Cluster {
			for _, addr := range conn.Address {
				if addr.NetworkType == shardingModel.NetworkType_Carma {
					ip = addr.Domain
					port = addr.Port
					break
				}
			}
		}
	}
	if tenant.IsRDSMultiCloudPlatform(ctx, self.cnf) {
		if ds_utils.GetEndpointModeByPSM(ds.Address) == datasource.ReadOnly {
			port, err = self.getReadonlyEndpointProxyPort(ctx, ds.InstanceId)
			if err != nil {
				log.Warn(ctx, "get readonly endpoint port error %s", err)
				return
			}
		}
		if ds_utils.GetEndpointModeByPSM(ds.Address) == datasource.ReadWrite {
			port, err = self.getReadWriteEndpointProxyPort(ctx, ds.InstanceId)
			if err != nil {
				log.Warn(ctx, "get readonly endpoint port error %s", err)
				return
			}
		}
	}
	return
}

func (self *mysqlShardingImpl) getReadonlyEndpointProxyPort(ctx context.Context, instanceId string) (string, error) {
	req := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
		Type:       self.Type(),
	}
	resp, err := self.DescribeDBInstanceDetail(ctx, req)
	if err != nil {
		return "", err
	}
	log.Info(ctx, "describe instances detail %s", utils.Show(resp))
	for _, endpoint := range resp.Endpoints {
		if endpoint.Mode == datasource.ReadOnly {
			return endpoint.ProxyPort, nil
		}
	}
	return "", NoReadonlyEndpoint
}

func (self *mysqlShardingImpl) getReadWriteEndpointProxyPort(ctx context.Context, instanceId string) (string, error) {
	req := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
		Type:       self.Type(),
	}
	resp, err := self.DescribeDBInstanceDetail(ctx, req)
	if err != nil {
		return "", err
	}
	log.Info(ctx, "describe instances detail %s", utils.Show(resp))
	for _, endpoint := range resp.Endpoints {
		if endpoint.Mode == datasource.ReadWrite {
			return endpoint.ProxyPort, nil
		}
	}
	return "", NoReadWriteEndpoint
}

func (self *mysqlShardingImpl) Type() shared.DataSourceType {
	return shared.MySQLSharding
}

func (self *mysqlShardingImpl) DescribeDBInstanceEndpoints(ctx context.Context, req *datasource.DescribeDBInstanceEndpointsReq) (*datasource.DescribeDBInstanceEndpointsResp, error) {
	var endpointList []*datasource.EndpointInfo
	describeDBInstanceDetailReq := &shardingModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	resp := &shardingModel.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, resp, client.WithTenantID("1"))
	if err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return nil, err
	}
	if err = fp.StreamOf(resp.Endpoints).Map(func(i *shardingModel.ConnectionInfoObject) *datasource.EndpointInfo {
		return &datasource.EndpointInfo{
			EndpointName:  i.EndpointName,
			EndpointID:    i.EndpointId,
			EndpointType:  i.EndpointType.String(),
			ReadWriteMode: i.GetReadWriteMode().String(),
		}
	}).ToSlice(&endpointList); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	ret := &datasource.DescribeDBInstanceEndpointsResp{
		Endpoints: endpointList,
	}
	return ret, nil
}

func (self *mysqlShardingImpl) DescribeDBInstanceShardInfos(ctx context.Context, req *datasource.DescribeDBInstanceShardInfosReq) (*datasource.DescribeDBInstanceShardInfosResp, error) {
	var shards []*datasource.ShardInfo
	describeDBInstanceDetailReq := &shardingModel.DescribeDBInstanceDetailReq{
		InstanceId:  req.InstanceId,
		ShardDetail: utils.BoolRef(true),
	}
	resp := &shardingModel.DescribeDBInstanceDetailResp{}
	err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, resp, client.WithTenantID("1"))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetail InstanceNotFound, err=%v", err)
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		}
		return nil, err
	}
	if err = fp.StreamOf(resp.ShardInfo).Map(func(i *shardingModel.ShardInfoObject) *datasource.ShardInfo {
		var (
			nodes       []*model.ShardNodeInfo
			logicShards []*model.DBRangeObject
		)
		for _, node := range i.NodeDetailInfo {
			nodes = append(nodes, &model.ShardNodeInfo{
				NodeId:   node.NodeId,
				NodeType: node.NodeType.String(),
				ZoneId:   node.ZoneId,
			})
		}
		for _, shard := range i.DBRange {
			if req.DBName == "" || req.DBName == shard.DBName {
				logicShards = append(logicShards, &model.DBRangeObject{
					DBName: shard.DBName,
					Range:  shard.Range,
				})
			}
		}
		return &datasource.ShardInfo{
			ShardId:       i.ShardId,
			GroupId:       i.GroupId,
			Nodes:         nodes,
			LogicShardIds: logicShards,
			ComponentType: model.ComponentType_Shards,
		}
	}).ToSlice(&shards); err != nil {
		return nil, err
	}
	ret := &datasource.DescribeDBInstanceShardInfosResp{
		Shards: shards,
	}
	return ret, nil
}

func (self *mysqlShardingImpl) ListInstanceNodes(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {
	query := shardingModelV1.ListInstancePodsReq{
		InstanceId: req.InstanceId,
		ShardId:    req.ShardId,
	}
	resp := &shardingModelV1.ListInstancePodsResp{}
	if err := self.mysql.Get().Call(ctx, shardingModelV1.Action_ListInstancePods.String(), query, resp, client.WithVersion(consts.RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesResp{}
	ret.AccountId = resp.AccountId
	for _, node := range resp.Datas {
		nodeInfo := &model.NodeInfoObject{
			NodeId: node.Name,
		}
		if req.NodeType == nil {
			ret.Nodes = append(ret.Nodes, nodeInfo)
			continue
		}
		switch *req.NodeType {
		case model.RdsNodeType_MySQLServer:
			if node.Component == "MySQL" {
				ret.Nodes = append(ret.Nodes, nodeInfo)
				continue
			}
		case model.RdsNodeType_ShardingProxy:
			if node.Component == "Proxy" {
				ret.Nodes = append(ret.Nodes, nodeInfo)
				continue
			}
		}
	}
	return ret, nil
}

func (self *mysqlShardingImpl) ListInstanceProxyAddress(ctx context.Context, req *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	// 查询proxy地址,shardId为空，获取mysql地址，shardId不为空
	query := &shardingModelV1.ListInstancePodsReq{
		InstanceId: req.InstanceId,
	}
	resp := &shardingModelV1.ListInstancePodsResp{}
	if err := self.mysql.Get().Call(ctx, shardingModelV1.Action_ListInstancePods.String(), query, resp, client.WithVersion(consts.RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "failed to call ListInstancePods, err=%v", err)
		return nil, err
	}
	ret := &datasource.ListInstancePodsResp{
		Total: resp.Total,
	}
	if err := fp.StreamOf(resp.Datas).Map(func(db *shardingModelV1.KubePod) *shared.KubePod {
		var (
			containerList []*shared.KubeContainer
		)
		for _, item := range db.Containers {
			if item.Name == "proxy" {
				containerItem := &shared.KubeContainer{
					Name: item.Name,
					Cpu:  item.Cpu,
					Mem:  item.Mem,
					Port: item.Port,
				}
				containerList = append(containerList, containerItem)
			}
		}
		return &shared.KubePod{
			Name:        db.Name,
			Zone:        db.Zone,
			KubeCluster: db.KubeCluster,
			Region:      db.Region,
			NodeIP:      db.NodeIP,
			PodIP:       db.PodIP,
			NodePool:    db.NodePool,
			Component:   db.Component,
			NodeId:      db.Name,
			Containers:  containerList,
		}
	}).ToSlice(&ret.Data); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *mysqlShardingImpl) ModifyProxyThrottleRule(ctx context.Context, req *datasource.ModifyProxyThrottleRuleReq) (*datasource.ModifyProxyThrottleRuleResp, error) {
	proxyThrottleRuleOperation, _ := shardingModel.ProxyThrottleRuleOperationFromString(req.Action)

	proxyThrottleType, _ := shardingModel.ProxyThrottleTypeFromString(req.ProxyThrottleRule.ThrottleTarget)
	ruleInfo := &shardingModel.ProxyThrottleRule{
		Value: utils.Int64Ref(int64(req.ProxyThrottleRule.ThrottleThreshold)),
	}
	switch proxyThrottleType {
	case shardingModel.ProxyThrottleType_FingerQPS:
		ruleInfo.Finger = &req.ProxyThrottleRule.ThrottleFingerPrint
	case shardingModel.ProxyThrottleType_PsmDbQPS:
		ruleInfo.PSM = &req.ProxyThrottleRule.ThrottleHost
		ruleInfo.DB = &req.ProxyThrottleRule.ThrottleDB
	case shardingModel.ProxyThrottleType_SqlQPS:
		ruleInfo.Sql = &req.ProxyThrottleRule.ThrottleSqlText
	case shardingModel.ProxyThrottleType_KeywordQPS:
		ruleInfo.Keyword = &req.ProxyThrottleRule.Keywords
	case shardingModel.ProxyThrottleType_GroupQPS:
		var groupIds []int64
		for _, val := range strings.Split(req.ProxyThrottleRule.GroupIds, ",") {
			groupId := conv.StrToInt64(val, 0)
			groupIds = append(groupIds, groupId)
		}
		ruleInfo.GroupIds = groupIds
	}
	rreq := &shardingModel.InnerModifyProxyThrottleRuleReq{
		InstanceId:                 req.InstanceId,
		EndpointId:                 req.ProxyThrottleRule.EndpointID,
		ProxyThrottleRuleOperation: proxyThrottleRuleOperation,
		ProxyThrottleType:          proxyThrottleType,
		ProxyThrottleRule:          ruleInfo,
	}
	err := self.mysql.Get().Call(ctx, shardingModel.Action_InnerModifyProxyThrottleRule.String(), rreq, nil, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds InnerModifyProxyThrottleRule, err=%+v", err)
		return nil, err
	}
	ret := &datasource.ModifyProxyThrottleRuleResp{}
	return ret, nil
}

func (self *mysqlShardingImpl) DescribeSQLCCLConfig(ctx context.Context, req *datasource.DescribeSQLCCLConfigReq) (*datasource.DescribeSQLCCLConfigResp, error) {
	// 火山内核限流暂不支持
	return &datasource.DescribeSQLCCLConfigResp{SQLConcurrencyControlStatus: false}, nil
}

func (self *mysqlShardingImpl) ListSQLCCLRules(ctx context.Context, req *datasource.ListSQLCCLRulesReq) (*datasource.ListSQLCCLRulesResp, error) {
	// 火山内核限流暂不支持
	ret := &datasource.ListSQLCCLRulesResp{
		CCLRules: []*datasource.CCLRuleInfo{},
	}
	return ret, nil
}

func (self *mysqlShardingImpl) DescribeSqlFingerPrintOrKeywords(ctx context.Context, req *datasource.DescribeSqlFingerPrintOrKeywordsReq) (*datasource.DescribeSqlFingerPrintOrKeywordsResp, error) {
	sqlThrottleType, _ := shardingModel.SQLThrottleTypeFromString(req.ObjectType)
	rreq := &shardingModel.InnerGetFingerprintAndKeywordReq{
		Sql:             req.SqlText,
		SQLThrottleType: shardingModel.SQLThrottleTypePtr(sqlThrottleType),
	}
	rresp := &shardingModel.InnerGetFingerprintAndKeywordResp{}
	err := self.mysql.Get().Call(ctx, shardingModel.Action_InnerGetFingerprintAndKeyword.String(), rreq, rresp, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Rds InnerGetFingerprintAndKeyword, err=%+v", err)
		return nil, err
	}
	ret := &datasource.DescribeSqlFingerPrintOrKeywordsResp{
		FingerPrint: rresp.GetFingerPrint(),
		Keywords:    rresp.GetKeyword(),
	}
	return ret, nil
}
func (self *mysqlShardingImpl) DescribeSqlType(ctx context.Context, req *datasource.DescribeSqlTypeReq) (*datasource.DescribeSqlTypeResp, error) {
	ret := &datasource.DescribeSqlTypeResp{}
	sqlType, err := datasource.GetRdsSqlType(ctx, req.SqlText)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_SQLReviewParserSqlError)
	}
	ret.SqlType = sqlType
	return ret, nil
}
func (self *mysqlShardingImpl) IsMyOwnInstance(ctx context.Context, instanceId string, _ shared.DataSourceType) bool {
	rreq := &shardingModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	resp := &shardingModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstanceDetail.String(), rreq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "get sharding instance detailed fail %v", err)
		return false
	}
	return true
}
func (self *mysqlShardingImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	var (
		dbInstanceStatusBlackList map[string]string
		blackList                 []string
		rawBlackList              string
	)
	rreq := &shardingModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	resp := &shardingModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstanceDetail.String(), rreq, resp, client.WithVersion(consts.RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "get sharding instance detailed fail %v", err)
		return err
	}
	cfg := self.cnf.Get(ctx)
	if isConnectedInstance {
		rawBlackList = cfg.DBInstanceStateWithConnectionBlackList
	} else {
		rawBlackList = cfg.DBInstanceStateWithoutConnectionBlackList
	}
	err := json.Unmarshal([]byte(rawBlackList), &dbInstanceStatusBlackList)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		blackList = strings.Split(dbInstanceStatusBlackList[ds.String()], ",")
	}
	currentStatus := resp.BasicInfo.InstanceStatus.String()
	for _, item := range blackList {
		if item == currentStatus {
			log.Warn(ctx, "instance status is %s, not support", currentStatus)
			return consts.ErrorWithParam(model.ErrorCode_InstanceNotInRunningStatus, currentStatus)
		}
	}
	return nil
}

type ShardingDBInfo struct {
	DBName string `gorm:"column:dbname"`
	Type   string `gorm:"column:type"`
}

type ShardingTableInfo struct {
	DBName    string `gorm:"column:dbname"`
	TableName string `gorm:"column:table_name"`
	Type      string `gorm:"column:type"`
}

type VProxyTableInfo struct {
	TableName    string `gorm:"column:TableName"`
	ShardingRule string `gorm:"column:ShardingRule"`
}

func (self *mysqlShardingImpl) ListDatabases(ctx context.Context, req *datasource.ListDatabasesReq) (*datasource.ListDatabasesResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
	}
	defer conn.Close()

	ret := &datasource.ListDatabasesResp{}
	var dbList []*ShardingDBInfo
	sql := "select * from byte_drds_meta.byte_shard_db where dbname !='byte_drds_meta' "
	var args []interface{}
	if req.Keyword != "" {
		sql += " and dbname like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if !req.EnableSystemDB {
		sql += " and dbname not in  (?) "
		systemDB := []string{"information_schema", "performance_schema", "mysql", "sys"}
		args = append(args, systemDB)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	if err := conn.Raw(sql, args...).Scan(&dbList); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
	}

	if req.Keyword == "" {
		if err = conn.Raw("select count(1) from byte_drds_meta.byte_shard_db where dbname !='byte_drds_meta' " + DBW_CONSOLE_DEFAULT_HINT).
			Scan(&ret.Total); err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
		}
	} else {
		sqlCount := "select count(1) from byte_drds_meta.byte_shard_db where dbname like ? "
		sqlCount += DBW_CONSOLE_DEFAULT_HINT
		if err = conn.Raw(sqlCount, `%`+req.Keyword+`%`).
			Scan(&ret.Total); err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
		}
	}

	if err = fp.StreamOf(dbList).Map(func(db *ShardingDBInfo) *shared.DatabaseInfo {
		return &shared.DatabaseInfo{
			Name: db.DBName,
		}
	}).ToSlice(&ret.Items); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *mysqlShardingImpl) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (resp *datasource.DescribeDBInstanceDetailResp, err error) {
	describeDBInstanceDetailReq := &shardingModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &shardingModel.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err = self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2),
		client.WithTenantID("1"))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetail InstanceNotFound, err=%v", err)
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		}
		return nil, err
	}
	resp = &datasource.DescribeDBInstanceDetailResp{
		InstanceId:      describeDBInstanceDetailResp.BasicInfo.GetInstanceId(),
		InstanceName:    describeDBInstanceDetailResp.BasicInfo.GetInstanceName(),
		InstanceStatus:  describeDBInstanceDetailResp.BasicInfo.GetInstanceStatus().String(),
		RegionId:        describeDBInstanceDetailResp.BasicInfo.GetRegionId(),
		ZoneId:          describeDBInstanceDetailResp.BasicInfo.GetZoneId(),
		DBEngine:        describeDBInstanceDetailResp.BasicInfo.GetDBEngine().String(),
		DBEngineVersion: describeDBInstanceDetailResp.BasicInfo.GetDBEngineVersion().String(),
		InstanceType:    describeDBInstanceDetailResp.BasicInfo.GetInstanceType().String(),
		VCPU:            describeDBInstanceDetailResp.BasicInfo.VCPU,
		Memory:          describeDBInstanceDetailResp.BasicInfo.Memory,
		ProjectName:     describeDBInstanceDetailResp.BasicInfo.ProjectName,
		StorageSpace:    describeDBInstanceDetailResp.BasicInfo.StorageSpace,
		NodeSpec:        describeDBInstanceDetailResp.BasicInfo.NodeSpec,
	}
	if describeDBInstanceDetailResp.BasicInfo.IsSetPerconaVersion() {
		resp.DBEngineSubVersion = describeDBInstanceDetailResp.BasicInfo.GetPerconaVersion().String()
	}
	fp.StreamOf(describeDBInstanceDetailResp.ConnectionInfo).Map(func(endpoint *shardingModel.ConnectionInfoObject) *datasource.Endpoint {
		var proxyPort, domainAddr, endpointName string
		for _, addr := range endpoint.Address {
			if addr.NetworkType == shardingModel.NetworkType_Carma {
				proxyPort = addr.Port
				domainAddr = addr.Domain
				break
			}
		}
		if endpoint.EndpointType == shardingModel.EndpointType_Primary && endpoint.GetReadWriteMode() == shardingModel.ReadWriteMode_ReadWrite {
			endpointName = "main"
		}
		if endpoint.EndpointType == shardingModel.EndpointType_ReadOnly && endpoint.GetReadWriteMode() == shardingModel.ReadWriteMode_ReadOnly {
			endpointName = "read-only"
		}
		if endpoint.EndpointType == shardingModel.EndpointType_Cluster && endpoint.GetReadWriteMode() == shardingModel.ReadWriteMode_ReadWrite {
			endpointName = "default"
		}
		edp := &datasource.Endpoint{
			Mode:         ShardingEndpointMode(endpoint.ReadWriteMode),
			ProxyPort:    proxyPort,
			Domain:       domainAddr,
			EndpointType: endpoint.EndpointType.String(),
			EndpointName: endpointName,
			EndpointId:   endpoint.EndpointId,
		}
		return edp
	}).ToSlice(&resp.Endpoints)
	return resp, nil
}

func (self *mysqlShardingImpl) DescribeDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq,
) (*datasource.DescribeDialogInfosResp, error) {
	if req.Offset < 0 || req.Limit < 0 {
		return &datasource.DescribeDialogInfosResp{}, nil
	}
	ret := &datasource.DescribeDialogInfosResp{}
	dialogInfos, err := self.getAllDialogInfos(ctx, req)
	if err != nil {
		return nil, err
	}
	ret.DialogDetails = self.filterDialogDetails(ctx, dialogInfos, req.QueryFilter, req.Offset, req.Limit)
	return ret, nil
}

func (self *mysqlShardingImpl) DescribeDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq,
) (ret *datasource.DescribeDialogStatisticsResp, err error) {
	dialogInfos, err := self.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:        req.Source,
		Component:     req.Component,
		QueryFilter:   req.QueryFilter,
		InternalUsers: req.InternalUsers,
	})
	if err != nil {
		return nil, err
	}
	// get aggregated info
	statistics := self.getDialogStatistics(ctx, dialogInfos, req.TopN)

	ret = &datasource.DescribeDialogStatisticsResp{
		DialogStatistics: statistics,
	}

	return ret, nil

}

func (self *mysqlShardingImpl) getAllDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) ([]*datasource.DialogInfo, error) {
	var (
		dialogInfos []*datasource.DialogInfo
		nodeList    []*datasource.NodeAddressObj
	)
	// DB侧会话
	if req.Component == model.Component_DBEngine.String() {
		sqlQuery := datasource.DbwDasHint + ShardingDialogAnalysis
		if len(req.InternalUsers) > 0 {
			sqlQuery += fmt.Sprintf("WHERE USER not in ('%s')", strings.Join(req.InternalUsers, "','"))
		}
		// 直连pod
		maSource := *req.Source
		addressList, err := self.DescribeShardInstanceAddress(ctx, maSource.InstanceId, req.QueryFilter.GetShardId())
		if err != nil {
			return nil, err
		}
		if len(req.QueryFilter.GetNodeIds()) > 0 {
			for _, nodeObj := range addressList {
				for _, nodeId := range req.QueryFilter.GetNodeIds() {
					if nodeId == nodeObj.NodeId {
						nodeList = append(nodeList, nodeObj)
						break
					}
				}
			}
		} else {
			nodeList = addressList
		}
		for _, node := range nodeList {
			var (
				tempDialog []*datasource.DialogInfo
			)
			maSource.Address = fmt.Sprintf("%s:%s", node.IPAddress, node.Port)
			log.Info(ctx, "Current node %s address is %s", node.NodeId, maSource.Address)
			conn, err := self.getConn(ctx, &maSource)
			if err != nil {
				log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
				//conn.Close()
				continue
			}
			if err = conn.Raw(sqlQuery).Scan(&tempDialog); err != nil {
				conn.Close()
				log.Warn(ctx, "nodeId %s Exec sql %s failed %s", node.NodeId, sqlQuery, err)
				continue
			}
			conn.Close()
			// log.Info(ctx, "DescribeDialogInfos nodeId %s dialogInfos: %s", node.NodeId, dialogInfos)
			// fill NULL column
			if err = fp.StreamOf(tempDialog).Foreach(func(d *datasource.DialogInfo) {
				if !d.Info.Valid {
					d.Info.String = "NULL"
					d.SqlTemplate, d.SqlTemplateID = datasource.GetSqlTemplate(ctx, "-")
					d.SqlType = "-"
				} else {
					d.SqlTemplate, d.SqlTemplateID = datasource.GetSqlTemplate(ctx, d.Info.String)
					sqlType, err := datasource.GetRdsSqlType(ctx, d.Info.String)
					if err != nil {
						d.SqlType = "-"
					} else {
						d.SqlType = sqlType
					}
				}
				if !d.BlockingPid.Valid {
					d.BlockingPid.String = "NULL"
				}
				// fill nodeId and nodeType
				d.NodeId = node.NodeId
				d.NodeType = node.NodeType
			}).ToSlice(&tempDialog); err != nil {
				return nil, err
			}
			dialogInfos = append(dialogInfos, tempDialog...)
		}
	} else {
		// 直连proxy
		maSource := *req.Source
		dbInfo, err := self.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{InstanceId: maSource.InstanceId})
		if err != nil {
			return nil, err
		}
		proxyList, err := self.ListInstanceProxyAddress(ctx, &datasource.ListInstancePodsReq{InstanceId: maSource.InstanceId})
		if err != nil {
			return nil, err
		}
		edList := dbInfo.Endpoints
		var proxyDialList []*datasource.ShardingProxyDialog
		sqlQuery := datasource.DbwDasHint + ShardingProxyDialog
		for _, pod := range proxyList.Data {
			for _, port := range ShardingProxyPortsList {
				var (
					tmpDialInfo  []*datasource.ShardingProxyDialog
					endPointId   string
					endPointName string
				)
				// 获取endPointName
				for _, ed := range edList {
					if ed.ProxyPort == port {
						endPointId = ed.EndpointId
						endPointName = ed.EndpointName
						break
					}
				}
				maSource.Address = fmt.Sprintf("%s:%s", pod.PodIP, port)
				log.Info(ctx, "Current proxy %s address is %s", endPointName, maSource.Address)
				conn, err := self.getConn(ctx, &maSource)
				if err != nil {
					log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
					conn.Close()
					continue
				}
				if err = conn.Raw(sqlQuery).Scan(&tmpDialInfo); err != nil {
					conn.Close()
					log.Warn(ctx, "endpoint %s Exec sql %s failed %s", endPointName, sqlQuery, err)
					continue
				}
				conn.Close()
				for _, dialInfo := range tmpDialInfo {
					dialInfo.Port = port
					dialInfo.ProxyId = endPointId
					dialInfo.EndpointName = endPointName
					proxyDialList = append(proxyDialList, dialInfo)
				}
			}
		}
		if err := fp.StreamOf(proxyDialList).Map(func(db *datasource.ShardingProxyDialog) *datasource.DialogInfo {
			item := &datasource.DialogInfo{
				ProcessID: db.ProcessID,
				DB:        db.DB,
				Host:      db.Host,
				Command:   db.Command,
				User:      db.User,
				Time:      db.Time,
				State:     "", // proxy不支持
				NodeId:    db.ProxyId,
				NodeType:  "Proxy",
				Info: sql.NullString{
					String: db.Command,
					Valid:  true,
				},
				BlockingPid: sql.NullString{
					String: "NULL",
					Valid:  false,
				},
				EndpointName: db.EndpointName,
			}
			item.SqlTemplate, item.SqlTemplateID = datasource.GetSqlTemplate(ctx, db.Command)
			return item
		}).ToSlice(&dialogInfos); err != nil {
			return nil, err
		}
		// sharding代理会话过滤掉内部用户
		dialogInfos = self.filterUser(ctx, dialogInfos, req.InternalUsers)
	}
	return dialogInfos, nil
}

func (self *mysqlShardingImpl) filterDialogDetails(ctx context.Context, data []*datasource.DialogInfo, queryFilter *shared.DialogQueryFilter, offset int64, limit int64) *shared.DialogDetails {
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.DialogInfo) bool {
				return strings.ToLower(d.Command) == "sleep"
			}).ToSlice(&tData)
		}
		if pID := queryFilter.ProcessID; pID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.ProcessID, pID)
			}).ToSlice(&tData)
		}
		if user := queryFilter.User; user != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.User, user)
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.Host, host)
			}).ToSlice(&tData)
		}
		if fDB := queryFilter.DB; fDB != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.DB, fDB)
			}).ToSlice(&tData)
		}
		if command := queryFilter.Command; command != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.Command, command)
			}).ToSlice(&tData)
		}
		if state := queryFilter.State; state != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.State, state)
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if nodeType := queryFilter.NodeType; nodeType != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.NodeType, nodeType)
			}).ToSlice(&tData)
		}
		if timeLimit := queryFilter.LowerExecTimeLimit; timeLimit != "" {
			//limitInt, er := strconv.Atoi(queryFilter.LowerExecTimeLimit)
			limitFloat, er := strconv.ParseFloat(queryFilter.LowerExecTimeLimit, 64)
			if er == nil {
				fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
					execTime, _ := strconv.ParseFloat(d.Time, 64)
					return execTime >= limitFloat
				}).ToSlice(&tData)
			}
		}
		if info := queryFilter.Info; info != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.Info.String, info)
			}).ToSlice(&tData)
		}
		if psm := queryFilter.PSM; psm != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				targetPSM := datasource.ExtractRdsPsm(d.Info.String)
				return strings.Contains(targetPSM.Psm, psm)
			}).ToSlice(&tData)
		}
		if sqlTemplate := queryFilter.SqlTemplate; sqlTemplate != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.SqlTemplate, sqlTemplate)
			}).ToSlice(&tData)
		}
		if sqlTemplateId := queryFilter.SqlTemplateID; sqlTemplateId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				return strings.Contains(d.SqlTemplateID, sqlTemplateId)
			}).ToSlice(&tData)
		}
		// 过滤多节点(支持shard)
		if nodeIdList := queryFilter.NodeIds; len(nodeIdList) > 0 {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				var existed bool
				for _, nodeId := range nodeIdList {
					if d.NodeId == nodeId {
						existed = true
						break
					}
				}
				return existed
			}).ToSlice(&tData)
		}
	}
	// 默认按Time倒序
	datasource.SortDialog(tData, shared.DESC, "Time")
	var details []*shared.DialogDetail
	fp.StreamOf(tData[offset:fp.MinInt64(offset+limit, int64(len(tData)))]).
		Map(func(d *datasource.DialogInfo) *shared.DialogDetail {
			psmItem := datasource.ExtractRdsPsm(d.Info.String)
			return &shared.DialogDetail{
				ProcessID:     d.ProcessID,
				User:          d.User,
				Host:          d.Host,
				DB:            d.DB,
				Command:       d.Command,
				Time:          d.Time,
				State:         d.State,
				Info:          d.Info.String,
				BlockingPid:   d.BlockingPid.String,
				NodeId:        d.NodeId,
				NodeType:      d.NodeType,
				PSM:           psmItem.Psm,
				EndpointName:  d.EndpointName,
				EndpointId:    d.EndpointId,
				SqlTemplateID: d.SqlTemplateID,
				SqlTemplate:   d.SqlTemplate,
			}
		}).ToSlice(&details)

	return &shared.DialogDetails{
		Details: details,
		Total:   int32(len(details)),
	}
}
func (self *mysqlShardingImpl) getDialogStatistics(ctx context.Context, data []*datasource.DialogInfo, topN int32) *shared.DialogStatistics {
	userInfo := make(map[string]*datasource.UserAggregatedInfo)
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	dbInfo := make(map[string]*datasource.DBAggregatedInfo)
	psmInfo := make(map[string]*datasource.PSMAggregatedInfo)
	var activeConn, totalConn int32 = 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(data).Foreach(func(d *datasource.DialogInfo) {
		totalConn += 1
		// 提取psm
		psmItem := datasource.ExtractRdsPsm(d.Info.String)
		if _, ok := psmInfo[psmItem.Psm]; !ok {
			psmInfo[psmItem.Psm] = &datasource.PSMAggregatedInfo{PSM: psmItem.Psm}
		}
		psmInfo[psmItem.Psm].TotalConn += 1

		if _, ok := userInfo[d.User]; !ok {
			userInfo[d.User] = &datasource.UserAggregatedInfo{User: d.User}
		}
		userInfo[d.User].TotalConn += 1
		ip := datasource.ExtractIP(d.Host)
		log.Info(ctx, "ExtractIP former: %s, after: %s", d.Host, ip)
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1
		if _, ok := dbInfo[d.DB]; !ok {
			dbInfo[d.DB] = &datasource.DBAggregatedInfo{DB: d.DB}
		}
		dbInfo[d.DB].TotalConn += 1
		if strings.ToLower(d.Command) != "sleep" {
			activeConn += 1
			userInfo[d.User].ActiveConn += 1
			ipInfo[ip].ActiveConn += 1
			dbInfo[d.DB].ActiveConn += 1
			psmInfo[psmItem.Psm].ActiveConn += 1
		}
	}).Run()

	var userList []*shared.UserAggregatedInfo
	var ipList []*shared.IPAggregatedInfo
	var dbList []*shared.DBAggregatedInfo
	var psmList []*shared.PSMAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(userInfo).ZipMap(func(k string, v *datasource.UserAggregatedInfo) *shared.UserAggregatedInfo {
		return &shared.UserAggregatedInfo{
			User:              v.User,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&userList)
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)
	fp.KVStreamOf(dbInfo).ZipMap(func(k string, v *datasource.DBAggregatedInfo) *shared.DBAggregatedInfo {
		return &shared.DBAggregatedInfo{
			DB:                v.DB,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&dbList)
	fp.KVStreamOf(psmInfo).ZipMap(func(k string, v *datasource.PSMAggregatedInfo) *shared.PSMAggregatedInfo {
		return &shared.PSMAggregatedInfo{
			PSM:               v.PSM,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&psmList)
	sort.Slice(userList, func(i, j int) bool {
		if userList[i].TotalConnections > userList[j].TotalConnections {
			return true
		}
		if userList[i].TotalConnections == userList[j].TotalConnections &&
			userList[i].ActiveConnections == userList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(dbList, func(i, j int) bool {
		if dbList[i].TotalConnections > dbList[j].TotalConnections {
			return true
		}
		if dbList[i].TotalConnections == dbList[j].TotalConnections &&
			dbList[i].ActiveConnections == dbList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(psmList, func(i, j int) bool {
		if psmList[i].TotalConnections > psmList[j].TotalConnections {
			return true
		}
		if psmList[i].TotalConnections == psmList[j].TotalConnections &&
			psmList[i].ActiveConnections == psmList[j].ActiveConnections {
			return true
		}
		return false
	})

	return &shared.DialogStatistics{
		DialogOver: &shared.DialogOverview{
			ActiveConnections: activeConn,
			TotalConnections:  totalConn,
		},
		UserAggregatedInfo: userList[:fp.MinInt(int(topN), len(userList))],
		IPAggregatedInfo:   ipList[:fp.MinInt(int(topN), len(ipList))],
		DBAggregatedInfo:   dbList[:fp.MinInt(int(topN), len(dbList))],
		PSMAggregatedInfo:  psmList[:fp.MinInt(int(topN), len(psmList))],
	}
}
func ShardingEndpointMode(enableReadOnly *shardingModel.ReadWriteMode) datasource.EndpointMode {
	if *enableReadOnly == shardingModel.ReadWriteMode_ReadOnly {
		return datasource.ReadOnly
	} else if *enableReadOnly == shardingModel.ReadWriteMode_ReadWrite {
		return datasource.ReadWrite
	}
	return datasource.Default
}
func (self *mysqlShardingImpl) KillProcess(ctx context.Context, req *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	if len(req.ProcessIDs) < 1 {
		return &datasource.KillProcessResp{}, nil
	}
	if req.NodeId == "" {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	var nodeAddr string
	// 直连pod
	maSource := *req.Source
	addressList, err := self.DescribeShardInstanceAddress(ctx, maSource.InstanceId, req.ShardId)
	if err != nil {
		return nil, err
	}
	for _, node := range addressList {
		if node.NodeId == req.NodeId {
			nodeAddr = fmt.Sprintf("%s:%s", node.IPAddress, node.Port)
			break
		}
	}
	if nodeAddr == "" {
		log.Warn(ctx, "Invalid node %s", req.NodeId)
		return nil, fmt.Errorf("nodeId %s can't find address", req.NodeId)
	}
	maSource.Address = nodeAddr
	conn, err := self.getConn(ctx, &maSource)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
		return nil, err
	}
	defer conn.Close()

	ret := &datasource.KillProcessResp{}
	for _, processID := range req.ProcessIDs {
		if err = conn.Exec("KILL ?", processID); err != nil {
			// 不存在的threadId报错不统计
			if !strings.Contains(strings.ToLower(err.Error()), strings.ToLower("unknown thread id")) {
				ret.FailInfoList = append(ret.FailInfoList, &shared.KillFailInfo{
					ProcessId:    processID,
					ErrorMessage: err.Error(),
				})
			}
		}
	}
	return ret, nil
}

func (self *mysqlShardingImpl) ListInstanceLightWeight(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}
	desReq := &shardingModel.DescribeDBInstancesReq{
		InstanceId: utils.StringRef(req.InstanceId),
		PageNumber: utils.Int32Ref(req.PageNumber),
		PageSize:   utils.Int32Ref(req.PageSize),
	}
	if req.InstanceName != "" {
		desReq.InstanceName = utils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		desReq.InstanceId = utils.StringRef(req.InstanceId)
	}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		desReq.CreateTimeStart = utils.StringRef(req.CreateTimeStart)
		desReq.CreateTimeEnd = utils.StringRef(req.CreateTimeEnd)
	}
	// 如果是运维面账号，则不需要传这个值
	if req.TenantId != "0" && req.TenantId != "1" {
		desReq.AccountId = utils.StringRef(req.TenantId)
	}
	desResp := &shardingModel.DescribeDBInstancesResp{}
	if desReq.GetAccountId() != "" {
		if err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstances.String(), desReq, desResp, client.WithVersion(RDS_MySQL_Version_V2), client.WithTenantID(desReq.GetAccountId())); err != nil {
			log.Warn(ctx, "failed to get rds instances, err=%v", err)
			return nil, err
		}
	} else {
		if err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstances.String(), desReq, desResp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
			log.Warn(ctx, "failed to get rds instances, err=%v", err)
			return nil, err
		}
	}
	resp := &datasource.ListInstanceResp{Total: int64(desResp.Total)}
	desRespInfo := desResp.Instances
	fp.StreamOf(desRespInfo).Map(func(inst *shardingModel.InstanceInfoObject) *model.InstanceInfo {
		return &model.InstanceInfo{
			InstanceId:      utils.StringRef(inst.InstanceId),
			InstanceName:    utils.StringRef(inst.InstanceName),
			InstanceStatus:  inst.InstanceStatus.String(),
			DBEngineVersion: inst.GetDBEngineVersion().String(),
			AccessSource:    "云数据库 MySQL Sharding版",
		}
	}).ToSlice(&resp.InstanceList)
	return resp, nil
}
func (self *mysqlShardingImpl) DescribeShardInstanceAddress(ctx context.Context, instanceId string, shardId string) ([]*datasource.NodeAddressObj, error) {
	if shardId == "" {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	var ret []*datasource.NodeAddressObj
	describeShardInstanceDetailReq := &shardingModel.DescribeShardInstanceDetailReq{
		InstanceId: instanceId,
		ShardId:    shardId,
	}
	describeShardInstanceDetailResp := &shardingModel.DescribeShardInstanceDetailResp{}
	err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeShardInstanceDetail.String(), describeShardInstanceDetailReq, describeShardInstanceDetailResp, client.WithTenantID("1"))
	if err != nil {
		log.Warn(ctx, "failed to call DescribeShardInstanceDetail, err=%v", err)
		return nil, err
	}
	if describeShardInstanceDetailResp.GetBasicInfo().DBEngine == shardingModel.DBEngine_Mysql {
		if err = fp.StreamOf(describeShardInstanceDetailResp.Nodes).Map(func(i *shardingModel.NodeDetailInfoObject) *datasource.NodeAddressObj {
			if i.Address.NetworkType == shardingModel.NetworkType_Carma {
				return &datasource.NodeAddressObj{
					NodeId:      i.GetNodeId(),
					NodeType:    i.GetNodeType().String(),
					IPAddress:   i.GetAddress().Domain, // mysql sharding统一返回域名
					Ipv6Address: i.GetAddress().IPv6Address,
					Port:        i.GetAddress().Port,
				}
			}
			return nil
		}).ToSlice(&ret); err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
	} else {
		// vedb需要调用ListInstancePods接口来获取所有节点的ip
		query := &shardingModelV1.ListInstancePodsReq{
			InstanceId: instanceId,
			ShardId:    shardId,
		}
		resp := &shardingModelV1.ListInstancePodsResp{}
		if err := self.mysql.Get().Call(ctx, shardingModelV1.Action_ListInstancePods.String(), query, resp, client.WithVersion(consts.RDS_MySQL_Version_V1)); err != nil {
			log.Warn(ctx, "failed to call ListInstancePods, err=%v", err)
			return nil, err
		}
		for _, pod := range resp.Datas {
			if pod.GetComponent() == "MySQL" {
				var port, adminPort string
				for _, container := range pod.GetContainers() {
					if container.Name == "ndb-dbengine" {
						portList := strings.Split(container.Port, " ")
						port = portList[0]
						adminPort = portList[1]
						break
					}
				}
				ret = append(ret, &datasource.NodeAddressObj{
					NodeId:    pod.Name,
					IPAddress: pod.PodIP,
					Port:      port,
					AdminPort: adminPort,
				})
			}
		}
	}
	log.Info(ctx, "sharding addressList is %s", utils.Show(ret))
	return ret, nil
}
func (self *mysqlShardingImpl) EnsureAccount(ctx context.Context, req *datasource.EnsureAccountReq) error {
	// 1、调用接口获取ds.address
	if err := self.FillDataSource(ctx, req.Source); err != nil {
		log.Warn(ctx, "sharding EnsureAccount error,getDatasourceAddress error:%s", err.Error())
		return err
	}

	// 2、直接连接,如果不能连接,则重新修改密码
	_, err := self.getConn(ctx, req.Source)
	if err != nil {
		if strings.Contains(strings.ToLower(err.Error()), consts.MySQLAccountError) {
			log.Warn(ctx, "sharding get conn error %s,maybe there is no account", err.Error())
			// 创建一个新的账号,然后去连接
			err = self.resetAccount(ctx, req.Source.InstanceId, shared.MySQLSharding)
			if err != nil {
				log.Warn(ctx, "sharding resetAccount err: %v", err)
				return err
			}
		}
	}
	return nil
}

func (self *mysqlShardingImpl) resetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	if !self.checkInstanceIsRunning(ctx, instanceId, dsType) {
		return consts.ErrorOf(model.ErrorCode_NotSupportAction)
	}
	//删除账号
	// 我们直接调用删除接口，确保删除调这个账号后重建
	c3cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	if err := self.DeleteAccount(ctx, &datasource.DeleteAccountReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: c3cfg.DBWAccountName,
	}); err != nil {
		log.Warn(ctx, "sharding EnsureAccount failed to delete account, err=%v", err)
		return err
	}
	//创建账号
	if err := self.CreateAccount(ctx, &datasource.CreateAccountReq{
		DSType:          dsType,
		InstanceId:      instanceId,
		AccountName:     c3cfg.DBWAccountName,
		AccountPassword: handler.GetAccountPassword(c3cfg.DbwAccountPasswordGenKey, instanceId),
		AccountType:     shardingModel.AccountType_DBW.String(),
	}); err != nil {
		log.Warn(ctx, "sharding failed to create account, err=%v", err)
		return err
	}
	return nil
}

func (self *mysqlShardingImpl) checkInstanceIsRunning(ctx context.Context, instanceId string, dsType shared.DataSourceType) bool {
	var retryCount int8
LOOP:
	retryCount += 1
	if retryCount > 3 {
		log.Info(ctx, "Check instance status exceed over 3 times,quit")
		return false
	}
	describeDBInstanceDetailReq := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
		Type:       dsType,
	}
	resp, err := self.DescribeDBInstanceDetail(ctx, describeDBInstanceDetailReq)
	if err != nil {
		return false
	}
	if resp.InstanceStatus != "Running" {
		time.Sleep(5 * time.Second)
		goto LOOP
	}
	return true
}

func (self *mysqlShardingImpl) DeleteAccount(ctx context.Context, req *datasource.DeleteAccountReq) error {
	rreq := &shardingModel.DeleteDBAccountReq{
		InstanceId:  req.InstanceId,
		AccountName: req.AccountName,
	}
	err := self.mysql.Get().Call(ctx, shardingModel.Action_DeleteDBAccount.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2))
	if err == nil {
		return nil
	}
	if strings.Contains(strings.ToLower(err.Error()), "exist") || strings.Contains(strings.ToLower(err.Error()), "未找到该账号") {
		log.Warn(ctx, "delete account, instanceId=%s, account=%s, account is not exists，ignore error", req.InstanceId, req.AccountName)
		return nil
	}
	log.Warn(ctx, "failed to delete account, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
	return err
}

func (self *mysqlShardingImpl) CreateAccount(ctx context.Context, req *datasource.CreateAccountReq) error {
	accountType, err := shardingModel.AccountTypeFromString(req.AccountType)
	if err != nil {
		log.Warn(ctx, "failed to identify account type , account type=%s, err=%v", req.AccountType, err.Error())
		return err
	}
	rreq := &shardingModel.CreateDBAccountReq{
		InstanceId:      req.InstanceId,
		AccountName:     req.AccountName,
		AccountPassword: req.AccountPassword,
		AccountType:     accountType,
	}
	if err = self.mysql.Get().Call(ctx, shardingModel.Action_CreateDBAccount.String(), rreq, nil, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to create account, instanceId=%s, err=%v", req.InstanceId, err.Error())
		if strings.Contains(err.Error(), "InvalidAccountName_Duplicate") {
			return nil
		}
		return err
	}
	return nil
}

// TODO: sharding 持续kill待sharding 3.10.2发布后上线
func (self *mysqlShardingImpl) ModifySQLKillRule(ctx context.Context, req *datasource.ModifySQLKillRuleReq) (*datasource.ModifySQLKillRuleResp, error) {

	switch req.Action {
	case model.KillSqlEventType_Add.String():
		killRule := req.KillRule[0]
		ruleId := fmt.Sprintf("%d", killRule.RuleID)
		sqlType := strings.Split(killRule.SqlType, ",")
		endpointIds := strings.Split(killRule.NodeType, ",")
		for _, ed := range endpointIds {
			modifyGlobalConfigReq := &shardingModel.ModifyProxyKillGlobalConfigReq{
				InstanceId:     req.InstanceId,
				EndpointId:     ed,
				ProtectedUsers: strings.Split(req.ProtectedUsers, ","),
			}
			modifySQLKillRuleReq := &shardingModel.ModifyProxyKillConfigReq{
				InstanceId: req.InstanceId,
				EndpointId: ed,
				//User:                     utils.StringRef(req.ProtectedUsers),  // sharding 保护用户需要调用另外一个接口
				ProxyKillConfigOperation: shardingModel.ProxyKillConfigOperation_Add,
				SqlTypes:                 convertStringSliceToSqlType(sqlType),
				MaxExecTime:              utils.Int32Ref(int32(killRule.MaxExecTime)),
				RuleId:                   utils.StringRef(ruleId),
			}
			if killRule.FingerPrint != "" {
				modifySQLKillRuleReq.Fingers = []string{killRule.FingerPrint}
			}
			if killRule.Host != "" {
				modifySQLKillRuleReq.PSMs = []string{killRule.Host} // 这里的host对应的psm
			}
			if killRule.Keyword != "" {
				modifySQLKillRuleReq.SqlKeywords = []string{killRule.Keyword}
			}
			err := self.mysql.Get().Call(ctx, shardingModel.Action_ModifyProxyKillConfig.String(), modifySQLKillRuleReq, nil, client.WithVersion(consts.RDS_MySQL_Version_V2))
			if err != nil {
				log.Warn(ctx, "failed to call sharding ModifySQLKillConfig, err=%+v", err)
				return nil, err
			}
			err = self.mysql.Get().Call(ctx, shardingModel.Action_ModifyProxyKillGlobalConfig.String(), modifyGlobalConfigReq, nil, client.WithVersion(consts.RDS_MySQL_Version_V2))
			if err != nil {
				log.Warn(ctx, "failed to call sharding ModifyProxyKillGlobalConfig, err=%+v", err)
				return nil, err
			}
		}
	case model.KillSqlEventType_Delete.String():
		for _, rule := range req.KillRule {
			ruleId := fmt.Sprintf("%d", rule.RuleID)
			endpointIds := strings.Split(rule.NodeType, ",")
			for _, ed := range endpointIds {
				modifySQLKillRuleReq := &shardingModel.ModifyProxyKillConfigReq{
					InstanceId:               req.InstanceId,
					EndpointId:               ed,
					ProxyKillConfigOperation: shardingModel.ProxyKillConfigOperation_Delete,
					RuleId:                   utils.StringRef(ruleId),
				}
				err := self.mysql.Get().Call(ctx, shardingModel.Action_ModifyProxyKillConfig.String(), modifySQLKillRuleReq, nil, client.WithVersion(consts.RDS_MySQL_Version_V2))
				if err != nil {
					log.Warn(ctx, "failed to call sharding ModifySQLKillConfig, err=%+v", err)
					return nil, err
				}
			}
		}

	case model.KillSqlEventType_Stop.String():
		for _, rule := range req.KillRule {
			ruleId := fmt.Sprintf("%d", rule.RuleID)
			endpointIds := strings.Split(rule.NodeType, ",")
			for _, ed := range endpointIds {
				modifySQLKillRuleReq := &shardingModel.ModifyProxyKillConfigReq{
					InstanceId:               req.InstanceId,
					EndpointId:               ed,
					ProxyKillConfigOperation: shardingModel.ProxyKillConfigOperation_Delete,
					RuleId:                   utils.StringRef(ruleId),
				}
				err := self.mysql.Get().Call(ctx, shardingModel.Action_ModifyProxyKillConfig.String(), modifySQLKillRuleReq, nil, client.WithVersion(consts.RDS_MySQL_Version_V2))
				if err != nil {
					log.Warn(ctx, "failed to call sharding ModifySQLKillConfig, err=%+v", err)
					return nil, err
				}
			}
		}
	default:
		log.Warn(ctx, "Not supported kill sql event type %s", req.Action)
	}

	return &datasource.ModifySQLKillRuleResp{}, nil
}

func (self *mysqlShardingImpl) DescribeInstanceAddressList(ctx context.Context, req *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	// 获取所有shard
	shardInfos, err := self.DescribeDBInstanceShardInfos(ctx, &datasource.DescribeDBInstanceShardInfosReq{InstanceId: req.InstanceId, Type: req.Type})
	if err != nil {
		return nil, err
	}
	var ret []*datasource.DescribeInstanceAddressResp
	for _, item := range shardInfos.Shards {
		describeShardInstanceDetailReq := &shardingModel.DescribeShardInstanceDetailReq{
			InstanceId: req.InstanceId,
			ShardId:    item.ShardId,
		}
		describeShardInstanceDetailResp := &shardingModel.DescribeShardInstanceDetailResp{}
		err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeShardInstanceDetail.String(), describeShardInstanceDetailReq, describeShardInstanceDetailResp, client.WithTenantID("1"))
		if err != nil {
			log.Warn(ctx, "failed to call DescribeShardInstanceDetail, err=%v", err)
			return nil, err
		}
		if describeShardInstanceDetailResp.Nodes == nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, "get shard address failed")
		}
		for _, node := range describeShardInstanceDetailResp.Nodes {
			temp := &datasource.DescribeInstanceAddressResp{
				NodeId: node.NodeId,
			}
			if node.Address.NetworkType == shardingModel.NetworkType_Carma {
				temp.IP = node.Address.Domain
				port, _ := strconv.ParseInt(node.Address.Port, 10, 64)
				temp.Port = int32(port)
			}
			ret = append(ret, temp)
		}
	}
	return ret, nil
}

func (self *mysqlShardingImpl) GetShardingDbType(ctx context.Context, dbSource *shared.DataSource, dbName string, tableName string) (string, error) {
	conn, err := self.getConn(ctx, dbSource)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", dbSource.Address, err)
		return "", err
	}
	defer conn.Close()

	// var tbList []*ShardingTableInfo
	// sqlStr := "select * from byte_drds_meta.byte_shard_table where dbname = ? and table_name = ? limit 1 "
	var tbList []*VProxyTableInfo
	sqlStr := "veproxy show 'table' info ;"
	if err = conn.Raw(sqlStr).Scan(&tbList); err != nil {
		log.Warn(ctx, "get sharding partition infos error:%s ", err.Error())
		return "", err
	}
	if len(tbList) == 0 {
		return "", nil
	}
	for _, vProxyTable := range tbList {
		if vProxyTable.TableName == tableName {
			return vProxyTable.ShardingRule, nil
		}
	}
	return "", nil
}

func (self *mysqlShardingImpl) ListTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	oldDbName := req.Source.Db
	req.Source.Db = req.DB
	if req.Keyword != "" || req.Limit != 0 || req.Offset != 0 {
		return self.listTablesFromInformationSchema(ctx, req)
	}
	conn, err := self.getConn(ctx, req.Source)
	req.Source.Db = oldDbName
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	// var tbList []*ShardingTableInfo
	// sqlStr := "select * from byte_drds_meta.byte_shard_table where dbname = ? and table_name = ? limit 1 "
	var tbList []*VProxyTableInfo
	sqlStr := " veproxy show 'table' info ;"
	sqlStr += DBW_CONSOLE_DEFAULT_HINT

	if err = conn.Raw(sqlStr).Scan(&tbList); err != nil {
		log.Warn(ctx, "get sharding partition infos error:%s ", err.Error())
		return nil, err
	}
	ret := &datasource.ListTablesResp{}
	var tableItems []string
	for _, tbInfo := range tbList {
		tableItems = append(tableItems, tbInfo.TableName)
	}
	ret.Items = tableItems
	ret.Total = int64(len(tbList))
	return ret, nil
}

func (self *mysqlShardingImpl) listTablesFromInformationSchema(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTablesResp{}
	sql := "select TABLE_NAME, TABLE_COMMENT from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	if err = conn.Raw(sql, args...).Scan(&ret.Tables); err != nil {
		return nil, err
	}
	sql = "select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args = []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	args = []interface{}{req.DB}
	/* get count */
	counterSQL := "/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE' " // ignore_security_alert
	if req.Keyword != "" {
		counterSQL += "and TABLE_NAME like ?"
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		counterSQL += genListTableFilter(req.Filters)
	}
	if err = conn.Raw(counterSQL, args...).Scan(&ret.Total); err != nil {
		return nil, err
	}
	return ret, nil
}
func (self *mysqlShardingImpl) GetPartitionInfos(ctx context.Context, dbSource *shared.DataSource, dbName string) ([]*datasource.DbPartitionInfo, error) {
	conn, err := self.getConn(ctx, dbSource)
	if err != nil {
		log.Warn(ctx, "get sharding conn error:%s", err.Error())
		return nil, err
	}
	defer conn.Close()

	// sqlStr := "select partition_dbname, dbname, shard_id, group_id, created_timestamp, last_modified_timestamp from  byte_drds_meta.byte_shard_db_partition where dbname = ? "
	// sqlStr += DBW_CONSOLE_DEFAULT_HINT
	var partitions []*datasource.DbPartitionInfo
	var vProxyDBInfos []*datasource.VProxyDBInfo
	// 转译DBName，用反引号引，防sql注入
	// 这里不用 ？ 的原因是，veproxy后面那个db只能用`或者不加引号，不能用'和"
	sqlStr := fmt.Sprintf("veproxy show dbpartitions `%s` ", translateDbName(dbName))
	if err = conn.Raw(sqlStr).Scan(&vProxyDBInfos); err != nil {
		log.Warn(ctx, "get sharding partition infos error:%s ", err.Error())
		return nil, err
	}
	for _, vProxyDbInfo := range vProxyDBInfos {
		spaceRe, _ := regexp.Compile(`\s*,\s*`)
		idList := spaceRe.Split(vProxyDbInfo.ShardIds, -1)
		for _, shardId := range idList {
			sId, err := utils2.String2Int(strings.TrimSpace(shardId))
			if err != nil {
				errMsg := fmt.Sprintf("shardIds:%s shardId: %s is not number", vProxyDbInfo.ShardIds, shardId)
				log.Warn(ctx, errMsg)
				return nil, fmt.Errorf(errMsg)
			}
			partition := &datasource.DbPartitionInfo{DbName: dbName, GroupId: vProxyDbInfo.GroupId, ShardId: int32(sId)}
			partitions = append(partitions, partition)
		}
	}
	return partitions, nil
}

func (self *mysqlShardingImpl) DescribeInstanceAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	describeConnectionReq := &shared.DataSource{
		InstanceId: req.InstanceId,
	}
	ip, port, err := self.DescribeInnerConnectionInfo(ctx, describeConnectionReq)
	if err != nil {
		log.Warn(ctx, "get sharding conn error:%s", err)
		return nil, err
	}
	portNum, _ := strconv.ParseInt(port, 10, 64)
	return &datasource.DescribeInstanceAddressResp{
		IP:   ip,
		Port: int32(portNum),
	}, nil
}

func (self *mysqlShardingImpl) ListInstancePods(ctx context.Context, req *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	// 查询proxy地址,shardId为空，获取mysql地址，shardId不为空
	query := &shardingModelV1.ListInstancePodsReq{
		InstanceId: req.InstanceId,
	}
	resp := &shardingModelV1.ListInstancePodsResp{}
	if err := self.mysql.Get().Call(ctx, shardingModelV1.Action_ListInstancePods.String(), query, resp, client.WithVersion(consts.RDS_MySQL_Version_V1)); err != nil {
		log.Warn(ctx, "failed to call ListInstancePods, err=%v", err)
		return nil, err
	}
	ret := &datasource.ListInstancePodsResp{
		Total: resp.Total,
	}
	if err := fp.StreamOf(resp.Datas).Map(func(db *shardingModelV1.KubePod) *shared.KubePod {
		var (
			containerList []*shared.KubeContainer
		)
		for _, item := range db.Containers {
			if item.Name == "proxy" {
				containerItem := &shared.KubeContainer{
					Name: item.Name,
					Cpu:  item.Cpu,
					Mem:  item.Mem,
					Port: item.Port,
				}
				containerList = append(containerList, containerItem)
			}
		}
		return &shared.KubePod{
			Name:        db.Name,
			Zone:        db.Zone,
			KubeCluster: db.KubeCluster,
			Region:      db.Region,
			NodeIP:      db.NodeIP,
			PodIP:       db.PodIP,
			NodePool:    db.NodePool,
			Component:   db.Component,
			NodeId:      db.Name,
			Containers:  containerList,
		}
	}).ToSlice(&ret.Data); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *mysqlShardingImpl) DescribeDBInstanceDetailForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	describeDBInstanceDetailReq := &shardingModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	describeDBInstanceDetailResp := &shardingModel.DescribeDBInstanceDetailResp{}
	log.Info(ctx, "describeDBInstanceDetailReq :%s", describeDBInstanceDetailReq.String())
	err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstanceDetail.String(), describeDBInstanceDetailReq, describeDBInstanceDetailResp, client.WithVersion(consts.RDS_MySQL_Version_V2),
		client.WithTenantID("1"))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetail InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
			return "", err

		}
	}
	return utils.Show(describeDBInstanceDetailResp), nil
}

func (self *mysqlShardingImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}

	// get mapping of spec code to detail
	specReq := &shardingModel.DescribeDBInstanceSpecsReq{}
	specResp := &shardingModel.DescribeDBInstanceSpecsResp{}
	if err := self.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstanceSpecs.String(), specReq, specResp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to get rds instance, err=%v", err)
		return nil, err
	}
	var specMap map[string]*model.InstanceSpec
	err := fp.StreamOf(specResp.GetInstanceSpecsInfo()).
		ToSetBy(func(specInfo *shardingModel.InstanceSpecsInfoObject) (string, *model.InstanceSpec) {
			return specInfo.GetSpecCode(), &model.InstanceSpec{
				CpuNum:   specInfo.GetVCPU(),
				MemInGiB: float64(specInfo.GetMemory()),
			}
		}).To(&specMap)
	if err != nil {
		return nil, err
	}

	// get rds instances
	desReq := &shardingModel.DescribeDBInstancesReq{
		PageNumber: utils.Int32Ref(req.PageNumber),
		PageSize:   utils.Int32Ref(req.PageSize),
	}
	if req.InstanceName != "" {
		desReq.InstanceName = utils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		desReq.InstanceId = utils.StringRef(req.InstanceId)
	}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		desReq.CreateTimeStart = utils.StringRef(req.CreateTimeStart)
		desReq.CreateTimeEnd = utils.StringRef(req.CreateTimeEnd)
	}
	// 如果是运维面账号，则不需要传这个值
	if req.TenantId != "0" && req.TenantId != "1" {
		desReq.AccountId = utils.StringRef(req.TenantId)
	}
	var status shardingModel.InstanceStatus
	if req.InstanceStatus != "" {
		status, err = shardingModel.InstanceStatusFromString(req.InstanceStatus)
		if err != nil {
			log.Warn(ctx, "failed to get InstanceStatus from string %s", req.InstanceStatus)
		}
		desReq.InstanceStatus = &status
	}
	if req.DBEngineVersion != "" {
		version, err := shardingModel.DBEngineVersionFromString(req.DBEngineVersion)
		if err != nil {
			log.Warn(ctx, "failed to get DBEngineVersion from string %s", req.DBEngineVersion)
		}
		desReq.DBEngineVersion = &version
	}
	if req.ZoneId != "" {
		desReq.ZoneId = utils.StringRef(req.ZoneId)
	}
	if len(req.Tags) > 0 {
		tagFilter := make([]*shardingModel.TagFilterObject, 0)
		for _, tag := range req.Tags {
			tagFilter = append(tagFilter, &shardingModel.TagFilterObject{
				Key:   tag.Key,
				Value: utils.StringRef(tag.Value),
			})
		}
		desReq.TagFilters = tagFilter
	}
	if req.ProjectName != "" {
		desReq.ProjectName = utils.StringRef(req.ProjectName)
	}
	desResp, err := self.DescribeDBInstances(ctx, desReq)
	if err != nil {
		return nil, err
	}
	desRespInfo := desResp.Instances
	err = fp.StreamOf(desRespInfo).Reject(func(inst *shardingModel.InstanceInfoObject) bool {
		return inst.InstanceStatus == shardingModel.InstanceStatus_Released
	}).ToSlice(&desRespInfo)
	if err != nil {
		return nil, err
	}
	resp := &datasource.ListInstanceResp{Total: int64(desResp.Total)}
	err = fp.StreamOf(desRespInfo).
		Map(func(inst *shardingModel.InstanceInfoObject) *model.InstanceInfo {
			// get instance private address
			var (
				privateDomain, privatePort string
			)
			targetTags := make([]*model.TagObject, 0)
			fp.StreamOf(inst.GetAddressObject()).Foreach(func(addr *shardingModel.AddressObject) {
				if addr.GetNetworkType() == shardingModel.NetworkType_Private {
					privateDomain = addr.GetDomain()
					privatePort = addr.GetPort()
				}
			}).Run()
			internalAddress := "-"
			if privateDomain != "" && privatePort != "" {
				internalAddress = fmt.Sprintf("%s:%s", privateDomain, privatePort)
			}
			instStatus := inst.InstanceStatus.String()
			hasReadOnlyNodes := false
			if inst.NodeNumber > 2 && inst.InstanceType == shardingModel.InstanceType_DoubleNode {
				hasReadOnlyNodes = true
			}
			if inst.IsSetTags() {
				for _, tag := range inst.GetTags() {
					targetTags = append(targetTags, &model.TagObject{
						Key:   tag.Key,
						Value: *tag.Value,
					})
				}
			}
			accountId := req.TenantId
			if inst.GetAccountId() != "" {
				accountId = inst.GetAccountId()
			}
			subType, _ := model.SubInstanceTypeFromString(inst.GetInstanceType().String())
			return &model.InstanceInfo{
				InstanceId:     utils.StringRef(inst.InstanceId),
				InstanceName:   utils.StringRef(inst.InstanceName),
				InstanceStatus: instStatus,
				Zone:           inst.ZoneId,
				InstanceSpec: &model.InstanceSpec{
					CpuNum:     specMap[inst.GetNodeSpec()].GetCpuNum(),
					MemInGiB:   specMap[inst.GetNodeSpec()].GetMemInGiB(),
					NodeNumber: inst.GetNodeNumber(),
				},
				DBEngineVersion: inst.GetDBEngineVersion().String(),
				InternalAddress: internalAddress,
				AccessSource:    "分布式数据库中间件 ByteScale",
				HasReadOnlyNode: hasReadOnlyNodes,
				ProjectName:     utils.StringRef(inst.ProjectName),
				Tags:            targetTags,
				SubInstanceType: &subType,
				AccountId:       utils.StringRef(accountId),
				CreateTime:      utils.StringRef(inst.GetCreateTime()),
				InstanceType:    model.InstanceType_MySQLSharding,
				LinkType:        model.LinkType_Volc,
			}
		}).ToSlice(&resp.InstanceList)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *mysqlShardingImpl) DescribeDBInstances(ctx context.Context, req *shardingModel.DescribeDBInstancesReq) (*shardingModel.DescribeDBInstancesResp, error) {
	if req.PageSize == nil || req.PageNumber == nil {
		// 如果没这两个值，直接调用接口
		return m.selfDescribeDBInstances(ctx, req)
	}
	pageInfo, err := ds_utils.FormatLowerPageInfo(*req.PageNumber, *req.PageSize)
	if err != nil {
		return nil, err
	}
	result := &shardingModel.DescribeDBInstancesResp{InstancesInfo: []*shardingModel.InstanceInfoObject{}, Instances: []*shardingModel.InstanceInfoObject{}, Total: int32(math.MaxInt32)}

	req.PageSize = utils.Int32Ref(pageInfo.PageSize)
	// 为了防止一次性拉挂RDS，我们这里对传入对PageNum和PageSize 做一次重新处理
	for pageNumber := pageInfo.PageNumLower; pageNumber <= pageInfo.PageNumUpper && (pageNumber-1)*pageInfo.PageSize < result.Total; pageNumber++ {
		req.PageNumber = utils.Int32Ref(pageNumber)
		resp, err := m.selfDescribeDBInstances(ctx, req)
		if err != nil {
			return nil, err
		}
		result.Total = resp.Total
		result.InstancesInfo = append(result.InstancesInfo, resp.InstancesInfo...)
		result.Instances = append(result.Instances, resp.Instances...)
	}
	if result.Total == int32(math.MaxInt32) {
		result.Total = 0
	}
	return result, nil
}

func (m *mysqlShardingImpl) selfDescribeDBInstances(ctx context.Context, req *shardingModel.DescribeDBInstancesReq) (*shardingModel.DescribeDBInstancesResp, error) {
	desResp := &shardingModel.DescribeDBInstancesResp{}
	if err := m.mysql.Get().Call(ctx, shardingModel.Action_DescribeDBInstances.String(), req, desResp, client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
		log.Warn(ctx, "failed to get rds instances, err=%v", err)
		return nil, err
	}
	return desResp, nil
}

func translateDbName(dbName string) string {
	return strings.ReplaceAll(dbName, "`", "``")
}

func convertStringSliceToSqlType(input []string) []shardingModel.SqlType {
	var ret []shardingModel.SqlType
	for _, item := range input {
		res, _ := shardingModel.SqlTypeFromString(item)
		ret = append(ret, res)
	}
	return ret
}

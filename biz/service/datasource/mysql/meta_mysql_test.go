package mysql

import (
	"code.byted.org/infcs/dbw-mgr/biz/config"
	entityInstance "code.byted.org/infcs/dbw-mgr/biz/entity/instance"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	connpoolMock "code.byted.org/infcs/dbw-mgr/biz/test/mocks/conn_pool"
	instMock "code.byted.org/infcs/dbw-mgr/biz/test/mocks/instance"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	metaMysqlModel "code.byted.org/infcs/dbw-mgr/gen/meta-mysql-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"errors"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
	"testing"
)

type MetaMysqlImplTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *MetaMysqlImplTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *MetaMysqlImplTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestMetaMysqlImplTestSuite(t *testing.T) {
	suite.Run(t, new(MetaMysqlImplTestSuite))
}
func mockMetaMysqlImpl() *metaMySQLImpl {
	return &metaMySQLImpl{
		metaMysql:       &mocks.MockProvider{},
		instanceManager: &instMock.MockInstanceManager{},
	}
}

func (suite *MetaMysqlImplTestSuite) Test_metaMySQLKillProcess() {
	metaImpl := mockMetaMysqlImpl()
	mockInstance := instMock.NewMockInstanceManager(suite.ctrl)
	mockInstance.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entityInstance.CtrlInstance{
		ID:                   1,
		InstanceId:           "1",
		InstanceType:         "1",
		InstanceName:         "1",
		Status:               "1",
		TenantId:             "1",
		Region:               "",
		Source:               0,
		DatabaseUser:         "",
		DatabasePassword:     "",
		Address:              "",
		OwnerUid:             "",
		DbaUid:               "",
		WorkflowTemplateId:   0,
		SecurityGroupId:      0,
		ControlMode:          0,
		ApprovalFlowConfigId: 0,
		CreatedAt:            0,
		UpdatedAt:            0,
		DeletedAt:            0,
		Deleted:              0,
		Tags:                 "",
		Extra: map[string]string{
			"MetaMySQLRegionName": "aaa",
		},
	}, nil).AnyTimes()
	metaImpl.instanceManager = mockInstance
	mockey.PatchConvey("test Test_metaMySQLKillProcess", suite.T(), func() {
		baseMock0 := mockey.Mock(log.Log).Return().Build()
		defer baseMock0.UnPatch()
		mockey.Mock((*metaMySQLImpl).IsEnableProxyRWMode).Return(false, nil).Build()
		mockey.Mock((*metaMySQLImpl).GetPods).Return([]*shared.KubePod{
			{
				Component: "MySQL",
				PodIP:     "*******",
				NodeId:    "mysql-xxx-master0",
				Role:      "Primary",
			},
			{
				Component: "MySQL",
				PodIP:     "*******",
				NodeId:    "mysql-xxx-slave0",
				Role:      "Secondary",
			},
		}, nil).Build()
		mock1 := mockey.Mock((*metaMySQLImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mocks.MockConn).Exec).Return(nil).Build()
		defer mock2.UnPatch()
		mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
		defer mock4.UnPatch()
		_, err := metaImpl.KillProcess(context.Background(), &datasource.KillProcessReq{Source: &shared.DataSource{Address: "test", Type: shared.MetaMySQL},
			ProcessIDs: []string{"111", "222"}, NodeId: "mysql-xxx-master0"})
		So(err, ShouldBeNil)
	})
}

func TestMetaMysqlFilterTrxAndLocks(t *testing.T) {
	mockey.PatchConvey("filter by trxId", t, func() {
		v := &metaMySQLImpl{}
		trxAndLocks := []*shared.TrxAndLock{{TrxId: "abcde"}, {TrxId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{TrxId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{TrxId: "abcde"}})
	})
	mockey.PatchConvey("filter by processId", t, func() {
		v := &metaMySQLImpl{}
		trxAndLocks := []*shared.TrxAndLock{{ProcessId: "abcde"}, {ProcessId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{ProcessId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{ProcessId: "abcde"}})
	})
	mockey.PatchConvey("filter by blockTrxId", t, func() {
		v := &metaMySQLImpl{}
		trxAndLocks := []*shared.TrxAndLock{{BlockTrxId: "abcde"}, {BlockTrxId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{BlockTrxId: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{BlockTrxId: "abcde"}})
	})
	mockey.PatchConvey("filter by sqlBlocked", t, func() {
		v := &metaMySQLImpl{}
		trxAndLocks := []*shared.TrxAndLock{{SqlBlocked: "abcde"}, {SqlBlocked: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{SqlBlocked: utils.StringRef("de")}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{SqlBlocked: "abcde"}})
	})
	mockey.PatchConvey("filter by trxExecTime", t, func() {
		v := &metaMySQLImpl{}
		trxAndLocks := []*shared.TrxAndLock{{TrxExecTime: 100}, {TrxExecTime: 1000}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, &model.TrxQueryFilter{TrxExecTime: utils.Int32Ref(100)}, nil)
		So(got, ShouldResemble, []*shared.TrxAndLock{{TrxExecTime: 1000}})
	})
	mockey.PatchConvey("filter by nodeIds", t, func() {
		v := &metaMySQLImpl{}
		trxAndLocks := []*shared.TrxAndLock{{NodeId: "abcde"}, {NodeId: "abcEF"}}
		got := v.filterTrxAndLocks(context.TODO(), trxAndLocks, nil, []string{"abcde"})
		So(got, ShouldResemble, []*shared.TrxAndLock{{NodeId: "abcde"}})
	})
}
func TestMetaMySQLFilterWaitLockDetails(t *testing.T) {
	impl := &metaMySQLImpl{}
	mockey.PatchConvey("filterWaitLockDetails", t, func() {
		mockey.PatchConvey("filter by nodeIds", func() {
			resp := impl.filterWaitLockDetails(context.TODO(), []*datasource.LockCurrentWaitsDetail{
				{
					NodeId:             "1",
					RTrxMysqlThreadId:  "111",
					RTrxId:             "111",
					RTrxOperationState: "xxx",
					RTrxRowsLocked:     "1",
					RTrxRowsModified:   "1",
					RTrxStarted:        "xxx",
					RTrxState:          "xxx",
					RTrxWaitStarted:    "xxx",
					RWaitingQuery:      "xxx",
					RBlockedWaitSecs:   "10",
					BTrxId:             "xxx",
					BBlockingQuery:     "xxx",
					BBlockingWaitSecs:  "10",
					BTrxMysqlThreadId:  "222",
					BTrxOperationState: "xxxx",
					BTrxRowsLocked:     "1",
					BTrxRowsModified:   "1",
					BTrxStarted:        "xxx",
					BTrxState:          "xxx",
					BTrxWaitStarted:    "xxx",
				},
				{NodeId: "2",
					RTrxMysqlThreadId:  "111",
					RTrxId:             "111",
					RTrxOperationState: "xxx",
					RTrxRowsLocked:     "1",
					RTrxRowsModified:   "1",
					RTrxStarted:        "xxx",
					RTrxState:          "xxx",
					RTrxWaitStarted:    "xxx",
					RWaitingQuery:      "xxx",
					RBlockedWaitSecs:   "10",
					BTrxId:             "xxx",
					BBlockingQuery:     "xxx",
					BBlockingWaitSecs:  "10",
					BTrxMysqlThreadId:  "222",
					BTrxOperationState: "xxxx",
					BTrxRowsLocked:     "1",
					BTrxRowsModified:   "1",
					BTrxStarted:        "xxx",
					BTrxState:          "xxx",
					BTrxWaitStarted:    "xxx",
				},
				{NodeId: "3"},
			}, &model.WaitLockQueryFilter{}, []string{"1", "2"})
			So(resp.Total, ShouldEqual, 2)
			So(resp.Result[0].NodeId, ShouldEqual, "1")
			So(resp.Result[1].NodeId, ShouldEqual, "2")
		})
	})
}
func (suite *MetaMysqlImplTestSuite) TestMetaMySQLGetAllWaitLocks() {
	ctx := context.TODO()
	conn := &connpoolMock.MockConn{}
	req := &datasource.DescribeLockCurrentWaitsReq{
		Source: &shared.DataSource{
			Type:       shared.MetaMySQL,
			InstanceId: "mysql-xxx",
			NodeId:     "mysql-xxx-master0",
		},
	}
	impl := mockMetaMysqlImpl()
	mockInstance := instMock.NewMockInstanceManager(suite.ctrl)
	mockInstance.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entityInstance.CtrlInstance{
		ID:                   1,
		InstanceId:           "1",
		InstanceType:         "1",
		InstanceName:         "1",
		Status:               "1",
		TenantId:             "1",
		Region:               "",
		Source:               0,
		DatabaseUser:         "",
		DatabasePassword:     "",
		Address:              "",
		OwnerUid:             "",
		DbaUid:               "",
		WorkflowTemplateId:   0,
		SecurityGroupId:      0,
		ControlMode:          0,
		ApprovalFlowConfigId: 0,
		CreatedAt:            0,
		UpdatedAt:            0,
		DeletedAt:            0,
		Deleted:              0,
		Tags:                 "",
		Extra: map[string]string{
			"MetaMySQLRegionName": "aaa",
		},
	}, nil).AnyTimes()
	impl.instanceManager = mockInstance

	impl.ConnPool = &mocks.MockPool{}
	mockey.PatchConvey("test getAllWaitLocks", suite.T(), func() {
		mockey.Mock((*metaMySQLImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
			Data: []*shared.KubePod{
				{
					Component: "MySQL",
					PodIP:     "*******",
					NodeId:    "mysql-xxx-master0",
					Role:      "Primary",
				},
				{
					Component: "MySQL",
					PodIP:     "*******",
					NodeId:    "mysql-xxx-slave0",
					Role:      "Secondary",
				},
			},
		}, nil).Build()
		mock1 := mockey.Mock((*metaMySQLImpl).getConnV2).Return(conn, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
		defer mock4.UnPatch()
		_, err := impl.getAllWaitLocks(ctx, req)
		So(err, ShouldBeNil)
	})
}
func (suite *MetaMysqlImplTestSuite) TestMetaMySQLGetAllTrxAndLocks() {
	ctx := context.TODO()
	conn := &connpoolMock.MockConn{}
	req := &datasource.DescribeTrxAndLocksReq{
		Source: &shared.DataSource{
			Type:       shared.MetaMySQL,
			InstanceId: "mysql-xxx",
		},
	}
	impl := mockMetaMysqlImpl()
	mockInstance := instMock.NewMockInstanceManager(suite.ctrl)
	mockInstance.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entityInstance.CtrlInstance{
		ID:                   1,
		InstanceId:           "1",
		InstanceType:         "1",
		InstanceName:         "1",
		Status:               "1",
		TenantId:             "1",
		Region:               "",
		Source:               0,
		DatabaseUser:         "",
		DatabasePassword:     "",
		Address:              "",
		OwnerUid:             "",
		DbaUid:               "",
		WorkflowTemplateId:   0,
		SecurityGroupId:      0,
		ControlMode:          0,
		ApprovalFlowConfigId: 0,
		CreatedAt:            0,
		UpdatedAt:            0,
		DeletedAt:            0,
		Deleted:              0,
		Tags:                 "",
		Extra: map[string]string{
			"MetaMySQLRegionName": "aaa",
		},
	}, nil).AnyTimes()
	impl.instanceManager = mockInstance
	impl.ConnPool = &mocks.MockPool{}
	mockey.PatchConvey("test getAllTrxLocks", suite.T(), func() {
		mockey.Mock((*metaMySQLImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
			Data: []*shared.KubePod{
				{
					Component: "MySQL",
					PodIP:     "*******",
					NodeId:    "mysql-xxx-master0",
					Role:      "Primary",
				},
				{
					Component: "MySQL",
					PodIP:     "*******",
					NodeId:    "mysql-xxx-slave0",
					Role:      "Secondary",
				},
			},
		}, nil).Build()
		mock1 := mockey.Mock((*metaMySQLImpl).getConnV2).Return(conn, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockPool).Put).Return().Build()
		defer mock4.UnPatch()
		_, err := impl.getAllTrxLocks(ctx, req)
		So(err, ShouldBeNil)
	})
}
func (suite *MetaMysqlImplTestSuite) Test_MetaMysqlDescribeLock() {
	ctx := context.Background()
	conn := &connpoolMock.MockConn{}
	impl := mockMetaMysqlImpl()
	mockInstance := instMock.NewMockInstanceManager(suite.ctrl)
	mockInstance.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entityInstance.CtrlInstance{
		ID:                   1,
		InstanceId:           "1",
		InstanceType:         "1",
		InstanceName:         "1",
		Status:               "1",
		TenantId:             "1",
		Region:               "",
		Source:               0,
		DatabaseUser:         "",
		DatabasePassword:     "",
		Address:              "",
		OwnerUid:             "",
		DbaUid:               "",
		WorkflowTemplateId:   0,
		SecurityGroupId:      0,
		ControlMode:          0,
		ApprovalFlowConfigId: 0,
		CreatedAt:            0,
		UpdatedAt:            0,
		DeletedAt:            0,
		Deleted:              0,
		Tags:                 "",
		Extra: map[string]string{
			"MetaMySQLRegionName": "aaa",
		},
	}, nil).AnyTimes()
	impl.instanceManager = mockInstance
	mockey.PatchConvey("Test describeLock", suite.T(), func() {
		mockey.PatchConvey("Test get mysql version failed", func() {
			mock1 := mockey.Mock((*metaMySQLImpl).getConnV2).Return(conn, nil).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(errors.New("get mysql version failed")).Build()
			defer mock3.UnPatch()
			_, err := impl.describeLock(ctx, conn)
			So(err, ShouldNotBeNil)
		})
		mockey.PatchConvey("Test get mysql version success", func() {
			mock1 := mockey.Mock((*metaMySQLImpl).getConnV2).Return(conn, nil).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock((*connpoolMock.MockConn).Raw).Return(&mocks.MockResult{}).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
			defer mock3.UnPatch()
			_, err := impl.describeLock(ctx, conn)
			So(err, ShouldBeNil)
		})
	})
}
func (suite *MetaMysqlImplTestSuite) TestMetaIsEnableProxyRWMode() {
	ctx := context.Background()
	impl := mockMetaMysqlImpl()
	mockey.PatchConvey("normal", suite.T(), func() {
		mockey.Mock((*metaMySQLImpl).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
			Endpoints: []*datasource.Endpoint{
				{
					Mode:         datasource.ReadWrite,
					ProxyPort:    "1",
					Domain:       "1",
					EndpointType: metaMysqlModel.EndpointType_Cluster.String(),
					EndpointId:   "1",
				},
			},
		}, nil).Build()
		_, err := impl.IsEnableProxyRWMode(ctx, "mymeta-xx")
		So(err, ShouldBeNil)
	})
}
func (suite *MetaMysqlImplTestSuite) TestMetaCheckInstanceState() {
	ctx := context.Background()
	mockey.PatchConvey("normal", suite.T(), func() {
		cfg := mocks.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&config.Config{
			DBInstanceStateWithoutConnectionBlackList: "{\"MetaMySQL\":\"Creating,Deleting\"}",
			DBInstanceStateWithConnectionBlackList:    "{\"MetaMySQL\":\"Creating,Deleting\"}",
		}).AnyTimes()
		mgrProvider := mocks.NewMockMgrProvider(suite.ctrl)
		mgrClient := mocks.NewMockMgrClient(suite.ctrl)
		mgrClient.EXPECT().Call(gomock.Any(), metaMysqlModel.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, metaMysqlModel.DescribeDBInstanceDetailResp{
			BasicInfo: &metaMysqlModel.BasicInfoObject{
				InstanceStatus: metaMysqlModel.InstanceStatus_Creating,
			},
		}).Return(nil).AnyTimes()
		mgrProvider.EXPECT().Get().Return(mgrClient).AnyTimes()
		mockInstance := instMock.NewMockInstanceManager(suite.ctrl)
		mockInstance.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entityInstance.CtrlInstance{
			ID:                   1,
			InstanceId:           "1",
			InstanceType:         "1",
			InstanceName:         "1",
			Status:               "1",
			TenantId:             "1",
			Region:               "",
			Source:               0,
			DatabaseUser:         "",
			DatabasePassword:     "",
			Address:              "",
			OwnerUid:             "",
			DbaUid:               "",
			WorkflowTemplateId:   0,
			SecurityGroupId:      0,
			ControlMode:          0,
			ApprovalFlowConfigId: 0,
			CreatedAt:            0,
			UpdatedAt:            0,
			DeletedAt:            0,
			Deleted:              0,
			Tags:                 "",
			Extra: map[string]string{
				"MetaMySQLRegionName": "aaa",
			},
		}, nil).AnyTimes()
		h := NewMetaMySQLDataSource(NewMetaMySQLDataSourceIn{
			In:              dig.In{},
			Conf:            cfg,
			MetaMySQLMgr:    mgrProvider,
			InstanceManager: mockInstance,
		})
		got := h.Source.CheckInstanceState(ctx, "mysql-xxx", shared.MetaMySQL, true)
		suite.NotEmpty(got)
	})
}
func Test_DescribeSqlType(t *testing.T) {
	ctx := context.Background()
	mockey.PatchConvey("Test DescribeSqlType", t, func() {
		mockey.PatchConvey("Test GetRdsSqlType failed", func() {
			req := &datasource.DescribeSqlTypeReq{SqlText: "SELECT * FROM "}
			resp, err := (&metaMySQLImpl{}).DescribeSqlType(ctx, req)
			So(resp, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})

		mockey.PatchConvey("Test GetRdsSqlType success", func() {
			expectedSqlType := "SELECT"
			req := &datasource.DescribeSqlTypeReq{SqlText: "SELECT * FROM users;"}
			resp, err := (&metaMySQLImpl{}).DescribeSqlType(ctx, req)
			So(resp.SqlType, ShouldEqual, expectedSqlType)
			So(err, ShouldBeNil)
		})
	})
}

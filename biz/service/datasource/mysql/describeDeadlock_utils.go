package mysql

import (
	"regexp"
	"time"

	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
)

/* for DescribeTrxAndLocks */
type TrxAndLock struct {
	ProcessId          string    `gorm:"column:ProcessId"`
	TrxId              string    `gorm:"column:TrxId"`
	TrxState           string    `gorm:"column:TrxStatus"`
	TrxIsoLevel        string    `gorm:"column:TrxIsoLevel"`
	TrxStartTime       time.Time `gorm:"column:TrxStartTime"`
	TrxWaitStartTime   time.Time `gorm:"column:TrxWaitStartTime"`
	SqlBlocked         string    `gorm:"column:SqlBlocked"`
	TrxTablesLocked    int32     `gorm:"column:TrxTablesLocked"`
	TrxRowsLocked      int32     `gorm:"column:TrxRowsLocked"`
	TrxRowsModified    int32     `gorm:"column:TrxRowsModified"`
	TrxLockStructs     int32     `gorm:"column:TrxLockStructs"`
	TrxRequestedLockId string    `gorm:"column:trx_requested_lock_id"`
	BlockTrxId         string    `gorm:"column:BlockTrxId"`
	TrxExecTime        int32     `gorm:"column:TrxExecTime"`
}

/* for DescribeLock */
type Lock57 struct {
	LockId             string `gorm:"column:lock_id"`
	LockAssociateIndex string `gorm:"column:lock_index"`
	LockAssociateTable string `gorm:"column:lock_table"`
	LockType           string `gorm:"column:lock_type"` //RECORD,TABLE
	LockModel          string `gorm:"column:lock_mode"` //S, X, IS, IX, GAP, AUTO_INC, UNKNOWN
	TrxId              string `gorm:"column:lock_trx_id"`
}

type Lock80 struct {
	LockProperty       string `gorm:"column:LOCK_STATUS"`
	LockId             string `gorm:"column:ENGINE_LOCK_ID"`
	LockAssociateIndex string `gorm:"column:INDEX_NAME"`
	LockAssociateTable string `gorm:"column:OBJECT_NAME"`
	LockType           string `gorm:"column:LOCK_TYPE"` //RECORD,TABLE
	LockModel          string `gorm:"column:LOCK_MODE"` //S, X, IS, IX, GAP, AUTO_INC, UNKNOWN
	LockBegin          string `gorm:"column:OBJECT_INSTANCE_BEGIN"`
	TrxId              string `gorm:"column:ENGINE_TRANSACTION_ID"`
}

/* for DescribeDeadlock */
type StatusInfo struct {
	Status string `gorm:"column:Status"`
}

func ternary(a bool, f1, f2 func() string) string {
	if a {
		return f1()
	}
	return f2()
}

func GetRegResults(s string, regs []string) []string {
	var results []string
	for _, reg := range regs {
		R := regexp.MustCompile(reg)
		res := ternary(R.FindStringSubmatch(s) != nil, func() string { return R.FindStringSubmatch(s)[len(R.FindStringSubmatch(s))-1] }, func() string { return "" })
		results = append(results, res)
	}
	return results
}

func GetSqlType(sql string) string {
	p := parser.New()
	var nodes []ast.StmtNode
	nodes, _, _ = p.Parse(sql, "", "")
	for _, typ := range nodes {
		switch typ.(type) {
		case *ast.AlterDatabaseStmt, *ast.AlterInstanceStmt, *ast.AlterTableStmt, *ast.AlterUserStmt, *ast.AlterViewStmt:
			return "Alter"
		case *ast.ShowStmt:
			return "Show"
		case *ast.SelectStmt:
			return "Select"
		case *ast.UpdateStmt:
			return "Update"
		case *ast.DropStatsStmt, *ast.DropTableStmt, *ast.DropDatabaseStmt, *ast.DropIndexStmt, *ast.DropSequenceStmt, *ast.DropBindingStmt, *ast.DropUserStmt, *ast.DropFunctionStmt, *ast.DropProcedureStmt:
			return "Drop"
		case *ast.DeleteStmt:
			return "Delete"
		case *ast.CreateDatabaseStmt, *ast.CreateIndexStmt, *ast.CreateBindingStmt, *ast.CreateTableStmt, *ast.CreateUserStmt, *ast.CreateViewStmt, *ast.CreateSequenceStmt, *ast.CreateTriggerStmt, *ast.CreateFunctionStmt, *ast.CreateProcedureStmt:
			return "Create"
		default:
			return "other type"
		}
	}
	return "other type"
}

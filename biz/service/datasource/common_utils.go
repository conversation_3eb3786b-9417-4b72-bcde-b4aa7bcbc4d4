package datasource

import (
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"net"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/framework/db"
	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	_ "github.com/pingcap/tidb/types/parser_driver"
)

// 定义默认的 SQL Hint 前缀
const (
	DbwHint    = "/*+ DBW DEFAULT*/"
	DbwDasHint = "/*+ DBW DAS DEFAULT*/"
)

func SortDialog(dialogs []*DialogInfo, sortBy shared.SortBy, orderBy string) {
	switch orderBy {
	case "Time":
		if sortBy == shared.ASC {
			sort.Slice(dialogs, func(i, j int) bool {
				// str转换为seconds e.g:12s->12
				var t0, t1 string
				if !strings.Contains(dialogs[i].Time, "s") {
					t0 = fmt.Sprintf("%ss", dialogs[i].Time)
					t1 = fmt.Sprintf("%ss", dialogs[j].Time)
				} else {
					t0 = dialogs[i].Time
					t1 = dialogs[j].Time
				}
				t00, _ := time.ParseDuration(t0)
				t11, _ := time.ParseDuration(t1)
				return t00 < t11
			})
		} else if sortBy == shared.DESC {
			sort.Slice(dialogs, func(i, j int) bool {
				var t0, t1 string
				if !strings.Contains(dialogs[i].Time, "s") {
					t0 = fmt.Sprintf("%ss", dialogs[i].Time)
					t1 = fmt.Sprintf("%ss", dialogs[j].Time)
				} else {
					t0 = dialogs[i].Time
					t1 = dialogs[j].Time
				}
				t00, _ := time.ParseDuration(t0)
				t11, _ := time.ParseDuration(t1)
				return t00 > t11
			})
		}
	}
}
func ExtractRdsPsm(sql string) *psmItem {
	pos := GetPsmInfo(sql)
	if pos == nil {
		return &psmItem{
			Psm: "-",
		}
	}
	psm := sql[pos[2]:pos[3]]
	ip := sql[pos[4]:pos[5]]
	pid := sql[pos[6]:pos[7]]
	return &psmItem{
		Psm: psm,
		ip:  ip,
		pid: pid,
	}
}

func GetPsmInfo(sql string) []int {
	state := CommentNonState
	pos := make([]int, 8)
	sqlLen := len(sql)
	escape := false
	for i := 0; i < sqlLen; i++ {
		v := sql[i]
		switch state {
		case CommentNonState:
			switch v {
			case '/':
				if i+1 < sqlLen && sql[i+1] == '*' {
					state = Comment
					pos[0] = i
					i = i + 1
				}
			case '\'':
				state = CommentChar1
			case '"':
				state = CommentChar2
			case '`':
				state = CommentChar3
			}
		case CommentChar1:
			switch v {
			case '\\':
				escape = true
			case '\'':
				if !escape {
					state = CommentNonState
				}
				escape = false
			default:
				escape = false
			}
		case CommentChar2:
			switch v {
			case '\\':
				escape = true
			case '"':
				if !escape {
					state = CommentNonState
				}
				escape = false
			default:
				escape = false
			}
		case CommentChar3:
			switch v {
			case '\\':
				escape = true
			case '`':
				if !escape {
					state = CommentNonState
				}
				escape = false
			default:
				escape = false
			}
		case Comment:
			switch v {
			case 'p':
				if i+2 < sqlLen && sql[i+1] == 's' && sql[i+2] == 'm' {
					state = PsmPsm
					i = i + 2
				}
			case '*':
				if i+1 < sqlLen && sql[i+1] == '/' {
					state = CommentNonState
					i = i + 1
				}
			}
		case PsmPsm:
			switch v {
			case '=':
				pos[2] = i + 1
			case ',':
				pos[3] = i
				state = PsmIP
			case '*':
				if i+1 < sqlLen && sql[i+1] == '/' {
					state = CommentNonState
					i = i + 1
				}

			}
		case PsmIP:
			switch v {
			case '=':
				if sql[i-2] == 'i' && sql[i-1] == 'p' {
					pos[4] = i + 1
				}
			case ',':
				if pos[4] > 0 {
					pos[5] = i
					state = PsmPid
				}
			case '*':
				if i+1 < sqlLen && sql[i+1] == '/' {
					state = CommentNonState
					i = i + 1
				}
			}
		case PsmPid:
			switch v {
			case '=':
				pos[6] = i + 1
			case '*':
				if i+1 < sqlLen && sql[i+1] == '/' {
					pos[7] = i
					pos[1] = i + 2
					return pos
				}
			}
		}
	}
	return nil
}

func GetAccountPassword(key string, instanceId string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(instanceId))
	expectedMac := hex.EncodeToString(mac.Sum(nil))
	return "Dbw_" + expectedMac[:26]
}

func ConvertIPV6(ip string) string {
	if strings.Count(ip, ":") > 2 && !strings.HasPrefix(ip, "[") {
		ip = "[" + ip + "]"
	}
	return ip
}

// 提取地址中的IP段
func ExtractIP(addr string) string {
	var ip string
	// 处理带方括号的IPv6地址 (格式: [IPv6]:port)
	if strings.Count(addr, ":") > 2 && addr[0] == '[' {
		if end := strings.Index(addr, "]"); end != -1 {
			ip = addr[:end+1]
			return ip
		} else {
			// 处理带方括号的IPv6地址 (格式: [IPv6])
			return addr
		}
	}
	// 处理不带方扩号的IPv6地址
	if strings.Count(addr, ":") > 2 {
		// 尝试解析纯IPv6地址
		if res := net.ParseIP(addr); res != nil {
			ip = res.String()
			return ip
		}
		// 处理带端口的IPv6 (非标准格式)
		if lastColon := strings.LastIndex(addr, ":"); lastColon != -1 {
			ip = addr[:lastColon]
			return ip
		}
	}
	// 处理IPv4/localhost地址
	if colon := strings.LastIndex(addr, ":"); colon != -1 {
		ip = addr[:colon]
		return ip
	} else {
		return addr
	}
}

// WithHint 自动添加 Hint
func WithHint(sql, hint string) string {
	return hint + sql
}

type TableStat struct {
	Name                   string  `gorm:"Name"`
	DB                     string  `gorm:"DB"`
	Engine                 string  `gorm:"Engine"`
	DataLength             float64 `gorm:"DataLength"`
	IndexLength            float64 `gorm:"IndexLength"`
	TableRows              float64 `gorm:"TableRows"`
	SpaceFragmentationRate float64 `gorm:"SpaceFragmentationRate"`
	AutoIdUsedRate         float64 `gorm:"AutoIdUsedRate"`
	TableSpace             float64 `gorm:"TableSpace"`
	AvgRowLength           float64 `gorm:"AvgRowLength"`
	TableSpaceRatio        float64 `gorm:"TableSpaceRatio"`
}

func GetSumSpace(conn db.Conn, whereCase string) (*int64, error) {
	sumSql := "/*+ DBW SQL CONSOLE DEFAULT*/ select COALESCE(SUM(tables.INDEX_LENGTH + tables.DATA_LENGTH + tables.DATA_FREE),0) as sumTableSpace from information_schema.tables as tables " + whereCase + " limit 2000" // ignore_security_alert
	var num int64 = 0
	sum := &num
	if err := conn.Raw(sumSql).Scan(sum); err != nil {
		return nil, err
	}
	return sum, nil
}

func GetInformationSchemaTablesInfo(ctx context.Context, conn db.Conn, req *DescribeTableSpaceReq, whereCase string, sumSpace *int64) ([]*TableStat, error) {
	orderInfo := getInformationSchemaTablesInfoOrderInfo(req.OrderItem, req.OrderRule)
	querySql := DbwDasHint + "SELECT tables.TABLE_NAME as Name, tables.TABLE_SCHEMA as DB, tables.ENGINE as Engine, tables.INDEX_LENGTH as IndexLength, " +
		" tables.DATA_LENGTH as DataLength, tables.TABLE_ROWS as TableRows,  " +
		" (tables.DATA_FREE / (tables.INDEX_LENGTH + tables.DATA_LENGTH + tables.DATA_FREE)) as SpaceFragmentationRate,  " +
		" COALESCE(tables.AUTO_INCREMENT, 0) as AutoIdUsedRate, (tables.INDEX_LENGTH + tables.DATA_LENGTH + tables.DATA_FREE) as TableSpace, " +
		" tables.AVG_ROW_LENGTH as AvgRowLength,  (tables.INDEX_LENGTH + tables.DATA_LENGTH + tables.DATA_FREE) / " + strconv.FormatInt(*sumSpace, 10) + " as TableSpaceRatio " +
		" FROM information_schema.tables as tables  " +
		whereCase + " order by " + orderInfo +
		" LIMIT ? OFFSET ? " // ignore_security_alert
	var args []interface{}
	args = append(args, req.Limit, req.Offset)
	var tableInfoList []*TableStat
	if err := conn.Raw(querySql, args...).Scan(&tableInfoList); err != nil {
		return nil, err
	}
	log.Info(ctx, "GetInformationSchemaTablesInfo-querySql %s ", querySql)
	return tableInfoList, nil
}

func getInformationSchemaTablesInfoOrderInfo(orderItem string, orderRule string) string {
	// 用lowercase忽略大小写
	switch strings.ToLower(orderItem) {
	case "name":
		if strings.EqualFold(orderRule, "DESC") {
			return " Name DESC "
		} else {
			return " Name ASC "
		}
	case "db":
		if strings.EqualFold(orderRule, "DESC") {
			return " DB DESC "
		} else {
			return " DB ASC "
		}
	case "engine":
		if strings.EqualFold(orderRule, "DESC") {
			return " Engine DESC "
		} else {
			return " Engine ASC "
		}
	case "datalength":
		if strings.EqualFold(orderRule, "DESC") {
			return " DataLength DESC "
		} else {
			return " DataLength ASC "
		}
	case "tablerows":
		if strings.EqualFold(orderRule, "DESC") {
			return " TableRows DESC "
		} else {
			return " TableRows ASC "
		}
	case "spacefragmentationrate":
		if strings.EqualFold(orderRule, "DESC") {
			return " SpaceFragmentationRate DESC "
		} else {
			return " SpaceFragmentationRate ASC "
		}
	case "autoidusedrate":
		if strings.EqualFold(orderRule, "DESC") {
			return " AutoIdUsedRate DESC "
		} else {
			return " AutoIdUsedRate ASC "
		}
	case "tablespace":
		if strings.EqualFold(orderRule, "DESC") {
			return " TableSpace DESC "
		} else {
			return " TableSpace ASC "
		}
	case "avgrowlength":
		if strings.EqualFold(orderRule, "DESC") {
			return " AvgRowLength DESC "
		} else {
			return " AvgRowLength ASC "
		}
	case "tablespaceratio":
		if strings.EqualFold(orderRule, "DESC") {
			return " TableSpaceRatio DESC "
		} else {
			return " TableSpaceRatio ASC "
		}
	case "indexlength":
		if strings.EqualFold(orderRule, "DESC") {
			return " IndexLength DESC "
		} else {
			return " IndexLength ASC "
		}
	}
	return " TableSpace DESC "
}

func CovertTableSpaceInfo(tableStats []*TableStat) []*shared.TableStat {
	var res []*shared.TableStat
	for _, value := range tableStats {
		stat := &shared.TableStat{
			Name:                   value.Name,
			DB:                     value.DB,
			Engine:                 value.Engine,
			DataLength:             value.DataLength,
			IndexLength:            value.IndexLength,
			TableRows:              value.TableRows,
			SpaceFragmentationRate: value.SpaceFragmentationRate,
			AutoIdUsedRate:         value.AutoIdUsedRate,
			TableSpace:             value.TableSpace,
			AvgRowLength:           value.AvgRowLength,
			TableSpaceRatio:        value.TableSpaceRatio,
		}
		res = append(res, stat)
	}
	return res
}

func GetRdsSqlType(ctx context.Context, sqlText string) (string, error) {
	var ret string
	stmt, err := parser.New().ParseOneStmt(sqlText, "", "")
	if err != nil {
		log.Warn(ctx, "parse sql failed, sql: %s, err: %v", sqlText, err)
		return "", err
	}
	//log.Info(ctx, "parse sql success, sql: %s, stmt: %s", sqlText, stmt.Text())
	switch stmt.(type) {
	case *ast.SelectStmt:
		ret = "SELECT"
	case *ast.InsertStmt:
		if stmt.(*ast.InsertStmt).IsReplace {
			ret = "REPLACE"
		} else {
			ret = "INSERT"
		}
	case *ast.UpdateStmt:
		ret = "UPDATE"
	case *ast.DeleteStmt:
		ret = "DELETE"
	case *ast.ShowStmt:
		ret = "SHOW"
	case *ast.AlterDatabaseStmt:
		ret = "ALTER"
	case *ast.AlterTableStmt:
		ret = "ALTER"
	case *ast.AlterUserStmt:
		ret = "ALTER"
	case *ast.AlterInstanceStmt:
		ret = "ALTER"
	case *ast.AnalyzeTableStmt:
		ret = "ANALYZE"
	case *ast.BeginStmt:
		ret = "BEGIN"
	case *ast.DoStmt:
		ret = "DO"
	case *ast.DropDatabaseStmt:
		ret = "DROP"
	case *ast.DropIndexStmt:
		ret = "DROP"
	case *ast.DropTableStmt:
		ret = "DROP"
	case *ast.DropSequenceStmt:
		ret = "DROP"
	case *ast.DropUserStmt:
		ret = "DROP"
	case *ast.DropBindingStmt:
		ret = "DROP"
	case *ast.DeallocateStmt:
		ret = "DEALLOCATE"
	case *ast.ExecuteStmt:
		ret = "EXECUTE"
	case *ast.ExplainStmt:
		if stmt.(*ast.ExplainStmt).Analyze {
			//case *ast.ExplainableStmt:
			ret = "EXPLAINABLE"
		} else {
			ret = "EXPLAIN"
		}
	case *ast.FlushStmt:
		ret = "FLUSH"
	case *ast.FlashBackTableStmt:
		ret = "FLASHBACK"
	case *ast.GrantStmt:
		ret = "GRANT"
	case *ast.GrantRoleStmt:
		ret = "GRANT"
	case *ast.IndexAdviseStmt:
		ret = "INDEX"
	case *ast.KillStmt:
		ret = "KILL"
	case *ast.LoadDataStmt:
		ret = "LOAD"
	case *ast.LoadStatsStmt:
		ret = "LOAD"
	case *ast.LockTablesStmt:
		ret = "LOCK"
	case *ast.PrepareStmt:
		ret = "PreparedStmt"
	case *ast.RenameTableStmt:
		ret = "RENAME"
	case *ast.RecoverTableStmt:
		ret = "RECOVER"
	case *ast.RevokeStmt:
		ret = "REVOKE"
	case *ast.RevokeRoleStmt:
		ret = "REVOKE"
	case *ast.RollbackStmt:
		ret = "ROLLBACK"
	case *ast.SplitRegionStmt:
		ret = "SPLIT"
	case *ast.SetStmt:
		ret = "SET"
	case *ast.ChangeStmt:
		ret = "CHANGE"
	case *ast.SetRoleStmt:
		ret = "SET"
	case *ast.SetDefaultRoleStmt:
		ret = "SET"
	case *ast.TraceStmt:
		ret = "TRACE"
	case *ast.TruncateTableStmt:
		ret = "TRUNCATE"
	case *ast.UnlockTablesStmt:
		ret = "UNLOCK"
	case *ast.UseStmt:
		ret = "USE"
	case *ast.ShutdownStmt:
		ret = "SHUTDOWN"
	default:
		ret = stmt.Text()
	}
	return ret, nil
}

func Fingerprint(Sql string) string {
	return parser.Normalize(Sql)
}
func ToHash(text string) string {
	hash := sha256.New()
	hash.Write([]byte(text))
	// 获取计算后的哈希值
	hashBytes := hash.Sum(nil)
	// 将字节数组转换为十六进制字符串
	return hex.EncodeToString(hashBytes)
}
func GetSqlTemplate(ctx context.Context, Sql string) (string, string) {
	SqlTemplate := Fingerprint(Sql)
	sqlTemplateId := ToHash(SqlTemplate)
	return SqlTemplate, sqlTemplateId
}

var (
	ErrInvalidPageNumber = errors.New("页码必须大于0")
	ErrInvalidPageSize   = errors.New("每页大小必须大于0")
	ErrInvalidOffset     = errors.New("偏移量不能为负数")
)

// PageParams 分页参数结构
type PageParams struct {
	PageNumber int // 当前页码（从1开始）
	PageSize   int // 每页大小
}

// LimitOffset 分页限制结构
type LimitOffset struct {
	Limit  int // 限制数量
	Offset int // 偏移量
}

// ToPageNumberSize 将 LimitOffset 转换为 PageParams
func ToPageNumberSize(limit, offset int32) (pageNumber, pageSize int32) {

	// 计算页码
	pageSize = limit
	if pageSize == 0 {
		pageSize = 10 // 默认每页大小
	}

	pageNumber = 1
	if offset > 0 {
		pageNumber = (offset / pageSize) + 1
	}
	return
}

// ToLimitOffset 将 PageParams 转换为 LimitOffset
func ToLimitOffset(pageNumber, pageSize int32) (limit, offset int32) {
	// 计算偏移量
	offset = (pageNumber - 1) * pageSize
	limit = pageSize
	return
}

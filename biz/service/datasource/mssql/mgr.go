package mssql

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/utils"

	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	mssqlModelV2 "code.byted.org/infcs/dbw-mgr/gen/mssql-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	dsutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

type NewMSSQLDataSourceIn struct {
	dig.In
	Conf           config.ConfigProvider
	MSSQLMgr       mgr.Provider `name:"mssql"`
	C3ConfProvider c3.ConfigProvider
	L              location.Location
	ShuttleSvc     shuttle.PGWShuttleService
	IdSvc          idgen.Service
	ActorClient    cli.ActorClient
}

type NewMSSQLDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewMSSQLDataSource(p NewMSSQLDataSourceIn) NewMSSQLDataSourceOut {
	return NewMSSQLDataSourceOut{
		Source: datasource.RetryIfWhiteListNotReady(&mssqlImpl{
			DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
			cnf:               p.Conf,
			mgr:               p.MSSQLMgr,
			C3ConfProvider:    p.C3ConfProvider,
			L:                 p.L,
			ShuttleSvc:        p.ShuttleSvc,
			IdSvc:             p.IdSvc,
			ActorClient:       p.ActorClient,
		}),
	}
}

type mssqlImpl struct {
	datasource.DataSourceService
	cnf            config.ConfigProvider
	mgr            mgr.Provider
	C3ConfProvider c3.ConfigProvider
	L              location.Location
	ShuttleSvc     shuttle.PGWShuttleService
	IdSvc          idgen.Service
	ActorClient    cli.ActorClient
}

func (m *mssqlImpl) Type() shared.DataSourceType {
	return shared.MSSQL
}

func (m *mssqlImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	if err := m.checkAllowList(ctx, ds.InstanceId); err != nil {
		return err
	}
	connInfo, err := m.GetConnectionInfo(ctx, &GetConnectionInfoReq{
		InstanceId: ds.InstanceId,
		//NodeId: dsutils.StringRef(ds.NodeId)
	})
	if err != nil {
		return err
	}
	ds.Address = fmt.Sprintf("%s:%s", connInfo.IP, connInfo.Port)
	ds.VpcID = connInfo.VPCID
	return nil
}

func (m *mssqlImpl) GetConnectionInfo(ctx context.Context, req *GetConnectionInfoReq) (*GetConnectionInfoResp, error) {
	instanceDetail, err := m.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	})
	if err != nil {
		return nil, err
	}
	res := &GetConnectionInfoResp{}
	res.VPCID = instanceDetail.VPCID
	// TODO mssql not support multi endpoint
	res.Port = instanceDetail.ConnectionsInfos[0].Address[0].Port
	if req.NodeId != nil {
		for _, node := range instanceDetail.NodeInfos {
			if node.NodeId == *req.NodeId {
				res.IP = node.NodeIP
			}
		}
	} else {
		res.IP = instanceDetail.ConnectionsInfos[0].Address[0].IPAddress
		for _, conn := range instanceDetail.ConnectionsInfos {
			for _, address := range conn.Address {
				if address.NetworkType == datasource.Private {
					res.IP = address.IPAddress
				}
			}
		}
	}
	if res.IP == "" || res.Port == "" {
		return nil, errors.New("address error")
	}
	return res, nil
}

func (m *mssqlImpl) DescribeDBInstanceDetail(
	ctx context.Context,
	req *datasource.DescribeDBInstanceDetailReq) (*datasource.DescribeDBInstanceDetailResp, error) {
	realReq := mssqlModelV2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	instanceDetailResp := &mssqlModelV2.DescribeDBInstanceDetailResp{}
	if err := m.mgr.Get().Call(ctx, mssqlModelV2.Action_DescribeDBInstanceDetail.String(), realReq, instanceDetailResp, client.WithVersion(RDS_MSSQL_Version_V2)); err != nil {
		return nil, err
	}
	res := &datasource.DescribeDBInstanceDetailResp{}
	fp.StreamOf(instanceDetailResp.NodeDetailInfo).Map(func(node *mssqlModelV2.NodeDetailInfoObject) *datasource.NodeInfo {
		return &datasource.NodeInfo{
			NodeId: node.NodeId,
			NodeIP: node.NodeIP,
		}
	}).ToSlice(&res.NodeInfos)
	fp.StreamOf(instanceDetailResp.ConnectionInfo).Map(func(conn *mssqlModelV2.ConnectionInfoObject) *datasource.ConnectionInfo {
		connInfo := &datasource.ConnectionInfo{
			EndpointId: conn.EndpointId,
		}
		for _, address := range conn.Address {
			addr := &datasource.AddressObject{
				Domain:    address.Domain,
				IPAddress: address.IPAddress,
				Port:      address.Port,
			}
			connInfo.Address = append(connInfo.Address, addr)
		}
		return connInfo
	}).ToSlice(&res.ConnectionsInfos)
	res.VPCID = instanceDetailResp.BasicInfo.VpcId
	return res, nil
}

func (m *mssqlImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	msReq := &mssqlModelV2.DescribeDBInstancesReq{
		PageNumber: dsutils.Int32Ref(req.PageNumber),
		PageSize:   dsutils.Int32Ref(req.PageSize),
		//InstanceCategory: mssqlModelV2.InstanceCategoryPtr(mssqlModelV2.InstanceCategory_Primary),
	}
	if req.InstanceName != "" {
		msReq.InstanceName = dsutils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		msReq.InstanceId = dsutils.StringRef(req.InstanceId)
	}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		msReq.CreateTimeStart = dsutils.StringRef(req.CreateTimeStart)
		msReq.CreateTimeEnd = dsutils.StringRef(req.CreateTimeEnd)
	}
	if len(req.Tags) > 0 {
		tagFilter := make([]*mssqlModelV2.TagFilterObject, 0)
		for _, tag := range req.Tags {
			tagFilter = append(tagFilter, &mssqlModelV2.TagFilterObject{
				Key:   tag.Key,
				Value: dsutils.StringRef(tag.Value),
			})
		}
		msReq.TagFilters = tagFilter
	}
	var (
		status mssqlModelV2.InstanceStatus
		engine mssqlModelV2.DBEngineVersion
	)
	var err error
	if req.InstanceStatus != "" {
		status, err = mssqlModelV2.InstanceStatusFromString(req.InstanceStatus)
		if err != nil {
			log.Warn(ctx, "failed to get InstanceStatus from string %s", req.InstanceStatus)
		}
		msReq.InstanceStatus = &status
	}
	if req.DBEngineVersion != "" {
		engine, err = mssqlModelV2.DBEngineVersionFromString(req.DBEngineVersion)
		if err != nil {
			log.Warn(ctx, "failed to get DBengine from string %s", req.DBEngineVersion)
		}
		msReq.DBEngineVersion = &engine
	}
	if req.SubInstanceType != "" {
		instanceType, err := mssqlModelV2.InstanceTypeFromString(req.SubInstanceType)
		if err != nil {
			log.Warn(ctx, "SubInstanceType %s is not supported,return default", req.SubInstanceType)
		}
		msReq.InstanceType = &instanceType
	}
	if req.ZoneId != "" {
		msReq.ZoneId = dsutils.StringRef(req.ZoneId)
	}
	msResp := &mssqlModelV2.DescribeDBInstancesResp{}
	// 使用正则表达式提取 CPU 和内存数值
	//re := regexp.MustCompile(`(\d+)c(\d+)g`)

	if err := m.mgr.Get().Call(ctx, mssqlModelV2.Action_DescribeDBInstances.String(), msReq, msResp, client.WithVersion(RDS_MSSQL_Version_V2)); err != nil {
		return nil, err
	}
	res := &datasource.ListInstanceResp{}
	fp.StreamOf(msResp.InstancesInfo).Map(func(ins *mssqlModelV2.InstanceInfoObject) *model.InstanceInfo {
		instanceSpec := &model.InstanceSpec{}
		//matches := re.FindStringSubmatch(ins.NodeSpec)
		//if len(matches) == 3 {
		//	// 将字符串转换为 int64
		//	tempCpuNum, err := strconv.ParseInt(matches[1], 10, 32)
		//	if err == nil {
		//		// 将 int64 转换为 int32
		//		cpuNum := int32(tempCpuNum)
		//		instanceSpec.CpuNum = cpuNum
		//	}
		//	memory, err := strconv.ParseFloat(matches[2], 64)
		//	if err == nil {
		//		instanceSpec.MemInGiB = memory
		//	}
		//
		//}
		var nodes []*model.NodeInfo
		for _, node := range ins.NodeDetailInfo {
			n := &model.NodeInfo{}
			var role model.NodeRole
			switch node.NodeType {
			case mssqlModelV2.NodeType_Primary:
				role = model.NodeRole_Primary
			case mssqlModelV2.NodeType_Secondary:
				role = model.NodeRole_Secondary
			}
			n.NodeId = node.NodeId
			n.NodeRole = &role
			nodes = append(nodes, n)
		}
		instanceSpec.MemInGiB = float64(ins.Memory)
		instanceSpec.CpuNum = ins.VCPU
		instanceSpec.NodeNumber = int32(len(nodes))
		instanceType := ins.GetInstanceType().String()
		subType, _ := model.SubInstanceTypeFromString(instanceType)
		zoneIds := strings.Split(ins.ZoneId, ";")
		targetTags := make([]*model.TagObject, 0)
		if ins.IsSetTags() {
			for _, tag := range ins.GetTags() {
				targetTags = append(targetTags, &model.TagObject{
					Key:   tag.Key,
					Value: *tag.Value,
				})
			}
		}
		accountId := req.TenantId
		if ins.GetAccountId() != "" {
			accountId = ins.GetAccountId()
		}
		return &model.InstanceInfo{
			InstanceId:      dsutils.StringRef(ins.InstanceId),
			InstanceName:    dsutils.StringRef(ins.InstanceName),
			InstanceStatus:  ins.InstanceStatus.String(),
			Nodes:           nodes,
			DBEngineVersion: ins.DBEngineVersion.String(),
			Zone:            zoneIds[0],
			InstanceSpec:    instanceSpec,
			SubInstanceType: &subType,
			ProjectName:     &ins.ProjectName,
			HasReadOnlyNode: ins.ReadOnlyNumber > 0,
			RegionId:        dsutils.StringRef(ins.GetRegionId()),
			Tags:            targetTags,
			AccountId:       dsutils.StringRef(accountId),
			CreateTime:      dsutils.StringRef(ins.GetCreateTime()),
			InstanceType:    model.InstanceType_MSSQL,
			LinkType:        model.LinkType_Volc,
		}
	}).ToSlice(&res.InstanceList)
	res.Total = int64(msResp.Total)
	return res, nil
}

func (m *mssqlImpl) DescribeDBInstanceSSL(
	ctx context.Context,
	req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	return &datasource.DescribeDBInstanceSSLResp{
		SSLEnable: false,
	}, nil
}

func (m *mssqlImpl) IsMyOwnInstance(ctx context.Context, instanceId string, _ shared.DataSourceType) bool {
	rreq := &mssqlModelV2.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	resp := &mssqlModelV2.DescribeDBInstanceDetailResp{}
	if err := m.mgr.Get().Call(ctx, mssqlModelV2.Action_DescribeDBInstanceDetail.String(), rreq, resp, client.WithVersion(RDS_MSSQL_Version_V2)); err != nil {
		log.Warn(ctx, "get mssql instance detailed fail %v", err)
		return false
	}
	return true
}
func (m *mssqlImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	var (
		dbInstanceStatusBlackList map[string]string
		blackList                 []string
		rawBlackList              string
	)
	rreq := &mssqlModelV2.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	resp := &mssqlModelV2.DescribeDBInstanceDetailResp{}
	if err := m.mgr.Get().Call(ctx, mssqlModelV2.Action_DescribeDBInstanceDetail.String(), rreq, resp, client.WithVersion(RDS_MSSQL_Version_V2)); err != nil {
		log.Warn(ctx, "get mssql instance detailed fail %v", err)
		return err
	}
	cfg := m.cnf.Get(ctx)
	if isConnectedInstance {
		rawBlackList = cfg.DBInstanceStateWithConnectionBlackList
	} else {
		rawBlackList = cfg.DBInstanceStateWithoutConnectionBlackList
	}
	err := json.Unmarshal([]byte(rawBlackList), &dbInstanceStatusBlackList)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		blackList = strings.Split(dbInstanceStatusBlackList[ds.String()], ",")
	}
	currentStatus := resp.BasicInfo.InstanceStatus.String()
	for _, item := range blackList {
		if item == currentStatus {
			log.Warn(ctx, "instance status is %s, not support", currentStatus)
			return consts.ErrorWithParam(model.ErrorCode_InstanceNotInRunningStatus, currentStatus)
		}
	}
	return nil
}
func (m *mssqlImpl) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	// mssql 暂时不支持检查连接
	return nil
}

func (m *mssqlImpl) DescribeDBInstanceDetailForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	realReq := mssqlModelV2.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	instanceDetailResp := &mssqlModelV2.DescribeDBInstanceDetailResp{}
	err := m.mgr.Get().Call(ctx, mssqlModelV2.Action_DescribeDBInstanceDetail.String(), realReq, instanceDetailResp, client.WithVersion(RDS_MSSQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetailForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetailForPilot, err=%v", err)
			return "", err
		}
	}

	return utils.Show(instanceDetailResp), nil
}

func (m *mssqlImpl) DescribeDBInstanceParametersForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	realReq := mssqlModelV2.DescribeDBInstanceParametersReq{
		InstanceId: req.InstanceId,
	}
	instanceDetailResp := &mssqlModelV2.DescribeDBInstanceParametersResp{}
	err := m.mgr.Get().Call(ctx, mssqlModelV2.Action_DescribeDBInstanceParameters.String(), realReq, instanceDetailResp, client.WithVersion(RDS_MSSQL_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceParametersForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceParametersForPilot, err=%v", err)
			return "", err
		}
	}

	return utils.Show(instanceDetailResp), nil
}

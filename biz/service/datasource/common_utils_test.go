package datasource

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"context"
	"github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"reflect"
	"testing"
)

func TestSortDialog(t *testing.T) {
	type args struct {
		dialogs []*DialogInfo
		sortBy  shared.SortBy
		orderBy string
	}
	tests := []struct {
		name string
		args args
		want []*DialogInfo
	}{
		{
			name: "test asc",
			args: args{
				sortBy:  shared.ASC,
				orderBy: "Time",
				dialogs: []*DialogInfo{
					{
						Time:      "12s",
						ProcessID: "123",
					},
					{
						Time:      "10s",
						ProcessID: "234",
					},
					{
						Time:      "1s",
						ProcessID: "345",
					},
				},
			},
			want: []*DialogInfo{
				{
					Time:      "1s",
					ProcessID: "345",
				},
				{
					Time:      "10s",
					ProcessID: "234",
				},
				{
					Time:      "12s",
					ProcessID: "123",
				},
			},
		},
		{
			name: "test desc",
			args: args{
				sortBy:  shared.DESC,
				orderBy: "Time",
				dialogs: []*DialogInfo{
					{
						Time:      "12s",
						ProcessID: "123",
					},
					{
						Time:      "10s",
						ProcessID: "2345",
					},
					{
						Time:      "1s",
						ProcessID: "3456",
					},
				},
			},
			want: []*DialogInfo{
				{
					Time:      "12s",
					ProcessID: "123",
				},
				{
					Time:      "10s",
					ProcessID: "2345",
				},
				{
					Time:      "1s",
					ProcessID: "3456",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SortDialog(tt.args.dialogs, tt.args.sortBy, tt.args.orderBy)
			if !reflect.DeepEqual(tt.want, tt.args.dialogs) {
				t.Errorf("orderBy not expected")
			}
		})
	}
}

func TestExtractRdsPsm(t *testing.T) {
	type args struct {
		sql string
	}
	tests := []struct {
		name string
		args args
		want *psmItem
	}{
		{
			name: "test select",
			args: args{
				sql: "SELECT /* psm=cmp.ecom.effect_task, ip=unknown, pid=376 */  * FROM `t_user_task`  WHERE (user_id = '176785064') AND (status in (1,3,4)) AND (task_scene in (5,100)) ORDER BY id desc LIMIT 600",
			},
			want: &psmItem{
				Psm: "cmp.ecom.effect_task",
				ip:  "unknown",
				pid: "376 ",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ExtractRdsPsm(tt.args.sql); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExtractRdsPsm() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_GetSqlTemplate(t *testing.T) {
	ctx := context.Background()
	sql := "SELECT * FROM users WHERE id = 1"
	mockey.PatchConvey("Test GetSqlTemplate with valid SQL", t, func() {
		sqlTemplate, sqlTemplateId := GetSqlTemplate(ctx, sql)
		So(sqlTemplateId, ShouldEqual, "94237023eee8e902d29a0a31c88be20e2ce46e76e052a5eefadfa252cc382247")
		So(sqlTemplate, ShouldEqual, "select * from users where id = ?")
	})
}
func TestGetRdsSqlType(t *testing.T) {
	ctx := context.Background()
	tests := []struct {
		name    string
		sql     string
		want    string
		wantErr bool
	}{
		// 错误情况测试
		{
			name:    "invalid SQL",
			sql:     "SELECT * FROM", // 不完整的SQL
			wantErr: true,
		},
		{
			name:    "empty SQL",
			sql:     "",
			wantErr: true,
		},

		// SELECT 类型测试
		{
			name: "simple select",
			sql:  "SELECT * FROM users",
			want: "SELECT",
		},
		{
			name: "select with join",
			sql:  "SELECT u.id, o.total FROM users u JOIN orders o ON u.id = o.user_id",
			want: "SELECT",
		},

		// INSERT/REPLACE 类型测试
		{
			name: "basic insert",
			sql:  "INSERT INTO users (name) VALUES ('John')",
			want: "INSERT",
		},
		{
			name: "replace statement",
			sql:  "REPLACE INTO users (id, name) VALUES (1, 'John')",
			want: "REPLACE",
		},

		// UPDATE 类型测试
		{
			name: "simple update",
			sql:  "UPDATE users SET name = 'Jane' WHERE id = 1",
			want: "UPDATE",
		},

		// DELETE 类型测试
		{
			name: "delete with condition",
			sql:  "DELETE FROM users WHERE id = 1",
			want: "DELETE",
		},

		// DDL 语句测试
		{
			name: "alter table",
			sql:  "ALTER TABLE users ADD COLUMN email VARCHAR(255)",
			want: "ALTER",
		},
		{
			name: "drop table",
			sql:  "DROP TABLE users",
			want: "DROP",
		},
		{
			name: "truncate table",
			sql:  "TRUNCATE TABLE logs",
			want: "TRUNCATE",
		},

		// 事务控制测试
		{
			name: "begin transaction",
			sql:  "BEGIN",
			want: "BEGIN",
		},
		{
			name: "rollback",
			sql:  "ROLLBACK",
			want: "ROLLBACK",
		},

		// EXPLAIN 特殊分支测试
		{
			name: "explain",
			sql:  "EXPLAIN SELECT * FROM users",
			want: "EXPLAIN",
		},
		{
			name: "explain analyze",
			sql:  "EXPLAIN ANALYZE SELECT * FROM users",
			want: "EXPLAINABLE",
		},

		// 其他类型测试
		{
			name: "show tables",
			sql:  "SHOW TABLES",
			want: "SHOW",
		},
		{
			name: "use database",
			sql:  "USE mydb",
			want: "USE",
		},
		{
			name: "set variable",
			sql:  "SET autocommit = 0",
			want: "SET",
		},
		{
			name: "grant permission",
			sql:  "GRANT SELECT ON db.* TO 'user'@'host'",
			want: "GRANT",
		},
		{
			name: "comment statement",
			sql:  "commit",
			want: "commit",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetRdsSqlType(ctx, tt.sql)

			// 错误处理检查
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRdsSqlType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 结果值检查
			if got != tt.want {
				t.Errorf("GetRdsSqlType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_ExtractIP(t *testing.T) {
	mockey.PatchConvey("Test IPv6 address with brackets and port", t, func() {
		addr := "[2001:0db8:85a3:0000:0000:8a2e:0370:7334]:8080"
		expected := "[2001:0db8:85a3:0000:0000:8a2e:0370:7334]"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})

	mockey.PatchConvey("Test IPv6 address with brackets only", t, func() {
		addr := "[2001:0db8:85a3:0000:0000:8a2e:0370:7334]"
		expected := "[2001:0db8:85a3:0000:0000:8a2e:0370:7334]"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})

	mockey.PatchConvey("Test pure IPv6 address", t, func() {
		addr := "2001:0db8:85a3:0000:0000:8a2e:0370:7334"
		expected := "2001:db8:85a3::8a2e:370:7334"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})

	mockey.PatchConvey("Test IPv6 address with port (non-standard format)", t, func() {
		addr := "2001:0db8:85a3:0000:0000:8a2e:0370:7334:8080"
		expected := "2001:0db8:85a3:0000:0000:8a2e:0370:7334"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})

	mockey.PatchConvey("Test IPv4 address with port", t, func() {
		addr := "***********:8080"
		expected := "***********"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})

	mockey.PatchConvey("Test IPv4 address without port", t, func() {
		addr := "***********"
		expected := "***********"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})

	mockey.PatchConvey("Test localhost address with port", t, func() {
		addr := "localhost:8080"
		expected := "localhost"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})

	mockey.PatchConvey("Test localhost address without port", t, func() {
		addr := "localhost"
		expected := "localhost"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})

	mockey.PatchConvey("Test domain address port", t, func() {
		addr := "n199-045-237.byted.org:10716"
		expected := "n199-045-237.byted.org"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})
	mockey.PatchConvey("Test domain address without port", t, func() {
		addr := "n199-045-237.byted.org"
		expected := "n199-045-237.byted.org"
		actual := ExtractIP(addr)
		So(actual, ShouldEqual, expected)
	})
}

func Test_ToPageNumberSize(t *testing.T) {
	mockey.PatchConvey("Test ToPageNumberSize with default page size", t, func() {
		limit, offset := int32(0), int32(0)
		pageNumber, pageSize := ToPageNumberSize(limit, offset)
		So(pageSize, ShouldEqual, 10)  // Default page size should be 10
		So(pageNumber, ShouldEqual, 1) // Default page number should be 1
	})

	mockey.PatchConvey("Test ToPageNumberSize with custom page size", t, func() {
		limit, offset := int32(20), int32(0)
		pageNumber, pageSize := ToPageNumberSize(limit, offset)
		So(pageSize, ShouldEqual, 20)  // Custom page size should be 20
		So(pageNumber, ShouldEqual, 1) // Default page number should be 1
	})

	mockey.PatchConvey("Test ToPageNumberSize with offset greater than zero", t, func() {
		limit, offset := int32(10), int32(15)
		pageNumber, pageSize := ToPageNumberSize(limit, offset)
		So(pageSize, ShouldEqual, 10)  // Custom page size should be 10
		So(pageNumber, ShouldEqual, 2) // Page number should be calculated as (offset / pageSize) + 1
	})

	mockey.PatchConvey("Test ToPageNumberSize with offset exactly divisible by page size", t, func() {
		limit, offset := int32(10), int32(20)
		pageNumber, pageSize := ToPageNumberSize(limit, offset)
		So(pageSize, ShouldEqual, 10)  // Custom page size should be 10
		So(pageNumber, ShouldEqual, 3) // Page number should be calculated as (offset / pageSize) + 1
	})

	mockey.PatchConvey("Test ToPageNumberSize with offset less than page size", t, func() {
		limit, offset := int32(10), int32(5)
		pageNumber, pageSize := ToPageNumberSize(limit, offset)
		So(pageSize, ShouldEqual, 10)  // Custom page size should be 10
		So(pageNumber, ShouldEqual, 1) // Page number should be 1 since offset is less than page size
	})

	mockey.PatchConvey("Test ToPageNumberSize with negative offset", t, func() {
		limit, offset := int32(10), int32(-5)
		pageNumber, pageSize := ToPageNumberSize(limit, offset)
		So(pageSize, ShouldEqual, 10)  // Custom page size should be 10
		So(pageNumber, ShouldEqual, 1) // Page number should be 1 since offset is negative
	})
}

func Test_ToLimitOffset(t *testing.T) {
	mockey.PatchConvey("Test ToLimitOffset with valid inputs", t, func() {
		mockey.PatchConvey("Test pageNumber is 1 and pageSize is 10", func() {
			pageNumber, pageSize := int32(1), int32(10)
			limit, offset := ToLimitOffset(pageNumber, pageSize)
			So(limit, ShouldEqual, 10)
			So(offset, ShouldEqual, 0)
		})

		mockey.PatchConvey("Test pageNumber is 2 and pageSize is 10", func() {
			pageNumber, pageSize := int32(2), int32(10)
			limit, offset := ToLimitOffset(pageNumber, pageSize)
			So(limit, ShouldEqual, 10)
			So(offset, ShouldEqual, 10)
		})

		mockey.PatchConvey("Test pageNumber is 3 and pageSize is 5", func() {
			pageNumber, pageSize := int32(3), int32(5)
			limit, offset := ToLimitOffset(pageNumber, pageSize)
			So(limit, ShouldEqual, 5)
			So(offset, ShouldEqual, 10)
		})
	})

	mockey.PatchConvey("Test ToLimitOffset with edge cases", t, func() {
		mockey.PatchConvey("Test pageNumber is 0 and pageSize is 10", func() {
			pageNumber, pageSize := int32(0), int32(10)
			limit, offset := ToLimitOffset(pageNumber, pageSize)
			So(limit, ShouldEqual, 10)
			So(offset, ShouldEqual, -10)
		})

		mockey.PatchConvey("Test pageNumber is 1 and pageSize is 0", func() {
			pageNumber, pageSize := int32(1), int32(0)
			limit, offset := ToLimitOffset(pageNumber, pageSize)
			So(limit, ShouldEqual, 0)
			So(offset, ShouldEqual, 0)
		})

		mockey.PatchConvey("Test pageNumber is -1 and pageSize is 10", func() {
			pageNumber, pageSize := int32(-1), int32(10)
			limit, offset := ToLimitOffset(pageNumber, pageSize)
			So(limit, ShouldEqual, 10)
			So(offset, ShouldEqual, -20)
		})

		mockey.PatchConvey("Test pageNumber is 1 and pageSize is -10", func() {
			pageNumber, pageSize := int32(1), int32(-10)
			limit, offset := ToLimitOffset(pageNumber, pageSize)
			So(limit, ShouldEqual, -10)
			So(offset, ShouldEqual, 0)
		})
	})
}

package pg

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/monitor/influxdb"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"strconv"
	"testing"
	"time"
)

func initGetMetricUsageReq() *datasource.GetMetricUsageReq {
	return &datasource.GetMetricUsageReq{
		DSType:     shared.Postgres,
		InstanceId: "instanceId",
		TenantId:   "tenantId",
		StartTime:  time.Now(),
		EndTime:    time.Now().Add(-15 * time.Minute),
	}
}

// 初始化测试所需的 DataPoint
func initDataPoints() []*model.DataPoint {
	var result []*model.DataPoint
	for i := 0; i < 10; i++ {
		point := &model.DataPoint{
			TimeStamp: int32(i),
			Value:     float64(i * 2),
		}
		result = append(result, point)
	}
	return result
}

// Mock MonitorClient
type MockMonitorClient struct{}

func (m *MockMonitorClient) DoQueryFromDbPG(ctx context.Context, cmd string, dbName string) ([]*model.DataPoint, error) {
	return initDataPoints(), nil
}

// 测试 GetAvgCpuUsage 函数
func TestGetAvgCpuUsage(t *testing.T) {
	impl := &postgreImpl{}
	mock := mockey.Mock((*influxdb.RdsMonitor).DoQueryFromDbPG).Return(initDataPoints(), nil).Build()
	defer mock.UnPatch()
	res, err := impl.GetAvgCpuUsage(context.Background(), initGetMetricUsageReq())
	if err != nil {
		t.Fatal("failed", err)
	}
	expectedAvg := 0.0 // 因为 DataPoint 的平均值是 9.0
	assert.Equal(t, expectedAvg, res.Avg)
}

// 测试 GetAvgMemUsage 函数
func TestGetAvgMemUsage(t *testing.T) {
	impl := &postgreImpl{}
	mock := mockey.Mock((*influxdb.RdsMonitor).DoQueryFromDbPG).Return(initDataPoints(), nil).Build()
	defer mock.UnPatch()
	res, err := impl.GetAvgMemUsage(context.Background(), initGetMetricUsageReq())
	if err != nil {
		t.Fatal("failed", err)
	}

	expectedAvg := 0.0
	assert.Equal(t, expectedAvg, res.Avg)
}

// 测试 GetAvgDiskUsage 函数
func TestGetAvgDiskUsage(t *testing.T) {
	impl := &postgreImpl{}
	mock := mockey.Mock((*influxdb.RdsMonitor).DoQueryFromDbPG).Return(initDataPoints(), nil).Build()
	defer mock.UnPatch()
	res, err := impl.GetAvgDiskUsage(context.Background(), initGetMetricUsageReq())
	if err != nil {
		t.Fatal("failed", err)
	}

	expectedAvg := 0.0
	assert.Equal(t, expectedAvg, res.Avg)
}

// 测试 GetAvgConnectionUsage 函数
func TestGetAvgConnectionUsage(t *testing.T) {
	impl := &postgreImpl{}
	mock := mockey.Mock((*influxdb.RdsMonitor).DoQueryFromDbPG).Return(initDataPoints(), nil).Build()
	defer mock.UnPatch()
	res, err := impl.GetAvgConnectionUsage(context.Background(), initGetMetricUsageReq())
	if err != nil {
		t.Fatal("failed", err)
	}

	expectedAvg := 0.0
	assert.Equal(t, expectedAvg, res.Avg)
}

// 测试 GetHealthSummaryCpu 函数
func TestGetHealthSummaryCpu(t *testing.T) {
	// Mock GetAvgMetricsUsage, GetMaxMetricsUsage, GetMinMetricsUsage, GetMetricDetail
	mockAvg := mockey.Mock((*postgreImpl).GetAvgMetricsUsage).Return(&datasource.GetMetricUsageResp{Avg: 50.0}, nil).Build()
	defer mockAvg.UnPatch()

	mockMax := mockey.Mock((*postgreImpl).GetMaxMetricsUsage).Return(&datasource.GetMetricUsageResp{Max: 80.0}, nil).Build()
	defer mockMax.UnPatch()

	mockMin := mockey.Mock((*postgreImpl).GetMinMetricsUsage).Return(&datasource.GetMetricUsageResp{Min: 30.0}, nil).Build()
	defer mockMin.UnPatch()

	mockDetail := mockey.Mock((*postgreImpl).GetMetricDetail).Return(&model.ItemDataResult_{DataPoints: initDataPoints()}, nil).Build()
	defer mockDetail.UnPatch()

	impl := &postgreImpl{}

	res, err := impl.GetHealthSummaryCpu(context.Background(), initGetMetricUsageReq())
	if err != nil {
		t.Fatal("failed", err)
	}

	assert.Equal(t, datasource.CpuUsage, res.Name)
	assert.Equal(t, percent, res.Unit)
	assert.Equal(t, 80.0, res.Max)
	assert.Equal(t, 30.0, res.Min)
	assert.Equal(t, 50.0, res.Avg)
	assert.Equal(t, initDataPoints(), res.DataPoints)
}

// 测试 GetHealthSummaryMem 函数
func TestGetHealthSummaryMem(t *testing.T) {
	mockAvg := mockey.Mock((*postgreImpl).GetAvgMetricsUsage).Return(&datasource.GetMetricUsageResp{Avg: 60.0}, nil).Build()
	defer mockAvg.UnPatch()
	mockMax := mockey.Mock((*postgreImpl).GetMaxMetricsUsage).Return(&datasource.GetMetricUsageResp{Max: 90.0}, nil).Build()
	defer mockMax.UnPatch()

	mockMin := mockey.Mock((*postgreImpl).GetMinMetricsUsage).Return(&datasource.GetMetricUsageResp{Min: 40.0}, nil).Build()
	defer mockMin.UnPatch()

	mockDetail := mockey.Mock((*postgreImpl).GetMetricDetail).Return(&model.ItemDataResult_{DataPoints: initDataPoints()}, nil).Build()
	defer mockDetail.UnPatch()

	impl := &postgreImpl{}

	res, err := impl.GetHealthSummaryMem(context.Background(), initGetMetricUsageReq())
	if err != nil {
		t.Fatal("failed", err)
	}

	assert.Equal(t, datasource.MemUsage, res.Name)
	assert.Equal(t, percent, res.Unit)
	assert.Equal(t, 90.0, res.Max)
	assert.Equal(t, 40.0, res.Min)
	assert.Equal(t, 60.0, res.Avg)
	assert.Equal(t, initDataPoints(), res.DataPoints)
}

// 测试 GetHealthSummaryConnectionUsage 函数
func TestGetHealthSummaryConnectionUsage(t *testing.T) {
	mockAvg := mockey.Mock((*postgreImpl).GetAvgMetricsUsage).Return(&datasource.GetMetricUsageResp{Avg: 70.0}, nil).Build()
	defer mockAvg.UnPatch()

	mockMax := mockey.Mock((*postgreImpl).GetMaxMetricsUsage).Return(&datasource.GetMetricUsageResp{Max: 95.0}, nil).Build()
	defer mockMax.UnPatch()

	mockMin := mockey.Mock((*postgreImpl).GetMinMetricsUsage).Return(&datasource.GetMetricUsageResp{Min: 50.0}, nil).Build()
	defer mockMin.UnPatch()

	mockDetail := mockey.Mock((*postgreImpl).GetMetricDetail).Return(&model.ItemDataResult_{DataPoints: initDataPoints()}, nil).Build()
	defer mockDetail.UnPatch()

	impl := &postgreImpl{}

	res, err := impl.GetHealthSummaryConnectionUsage(context.Background(), initGetMetricUsageReq())
	if err != nil {
		t.Fatal("failed", err)
	}

	assert.Equal(t, noUnit, res.Unit)
	assert.Equal(t, 95.0, res.Max)
	assert.Equal(t, 50.0, res.Min)
	assert.Equal(t, 70.0, res.Avg)
	assert.Equal(t, initDataPoints(), res.DataPoints)
}

// 测试 HealthSummary 函数
func TestHealthSummary(t *testing.T) {
	impl := &postgreImpl{}
	impl.initHealthSummary()

	// Mock HealthSummaryMetric 中的所有函数
	for key, _ := range impl.HealthSummaryMetric {
		mock := mockey.Mock(impl.HealthSummaryMetric[key]).Return(&model.Resource{
			Name: key,
			Max:  80.0,
			Min:  30.0,
			Avg:  50.0,
		}, nil).Build()
		defer mock.UnPatch()
	}

	resources, err := impl.HealthSummary(context.Background(), initGetMetricUsageReq())
	if err != nil {
		t.Fatal("failed", err)
	}

	assert.Equal(t, len(resources), len(impl.HealthSummaryMetric))
	for _, resource := range resources {
		assert.Equal(t, 80.0, resource.Max)
		assert.Equal(t, 30.0, resource.Min)
		assert.Equal(t, 50.0, resource.Avg)
	}
}

// Mock utils.MustStrToInt64
func MustStrToInt64Mock(s string) int64 {
	value, _ := strconv.ParseInt(s, 10, 64)
	return value
}

// TestGetMetricDetail 测试 GetMetricDetail 方法
func TestGetMetricDetail(t *testing.T) {
	impl := &postgreImpl{}

	ctx := context.Background()
	req := &datasource.GetMetricUsageReq{
		InstanceId: "test-instance",
		TenantId:   "test-tenant",
		StartTime:  time.Now(),
		EndTime:    time.Now().Add(15 * time.Minute),
	}
	metricName := "test_metric"
	unit := "test_unit"
	metricType := "current"

	expectedResult := &model.ItemDataResult_{
		DataPoints: []*model.DataPoint{
			{TimeStamp: 1, Value: 100.0},
		},
	}

	// Mock getMySQLMetricPointInfo
	mock := mockey.Mock((*postgreImpl).getMySQLMetricPointInfo).Return(expectedResult, nil).Build()
	defer mock.UnPatch()

	res, err := impl.GetMetricDetail(ctx, req, metricName, unit, metricType)

	assert.Nil(t, err)
	assert.NotNil(t, res)
	// assert.Equal(t, expectedResult, res)
	assert.Equal(t, metricName, res.Description)
	assert.Equal(t, unit, res.Unit)

}

// TestGetMySQLMetricPointInfo 测试 getMySQLMetricPointInfo 方法
func TestGetMySQLMetricPointInfo(t *testing.T) {
	impl := &postgreImpl{}

	ctx := context.Background()
	req := &datasource.GetMetricUsageReq{
		InstanceId: "test-instance",
		TenantId:   "test-tenant",
		StartTime:  time.Now(),
		EndTime:    time.Now().Add(15 * time.Minute),
	}
	tableName := "test_table"
	unit := "test_unit"
	metricType := "current"

	expectedResult := &model.ItemDataResult_{
		DataPoints: []*model.DataPoint{
			{TimeStamp: 1, Value: 100.0},
		},
	}

	// Mock getMySQLMetricDetailInfo
	mock := mockey.Mock((*postgreImpl).getMySQLMetricDetailInfo).Return(expectedResult, nil).Build()
	defer mock.UnPatch()

	res, err := impl.getMySQLMetricPointInfo(ctx, req, tableName, unit, metricType)

	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, expectedResult, res)
}

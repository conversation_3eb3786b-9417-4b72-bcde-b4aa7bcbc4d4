package pg

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"

	"github.com/qjpcpu/fp"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/bytebrain"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/service/infra"
	"code.byted.org/infcs/dbw-mgr/biz/service/monitor/influxdb"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	pgModelv1 "code.byted.org/infcs/dbw-mgr/gen/pg-mgr/2018-01-01/kitex_gen/model"
	pgModel "code.byted.org/infcs/dbw-mgr/gen/pg-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/db"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"code.byted.org/infcs/mgr/kitex_gen/infcs/mgr/framework"
)

const (
	DbwConsoleDefaultHint   = " /*+ DBW SQL CONSOLE DEFAULT*/"
	DbwSoleByteRdsGroupName = `DBW_Sole_Group_Name_For_RDS`
	RdsPgVersionV2          = `2022-01-01`
)

type DatabaseInfo struct {
	DBName           string `gorm:"column:datname"`
	CharacterSetName string `gorm:"column:charset"`
	CollationName    string `gorm:"column:datcollate"`
	CTypeName        string `gorm:"column:datctype"`
	Datconnlimit     string `gorm:"column:datconnlimit"`
	Description      string `gorm:"column:description"`
}

type NewPostgreSQLDataSourceIn struct {
	dig.In
	Conf            config.ConfigProvider
	PgMgr           mgr.Provider `name:"postgres"`
	C3ConfProvider  c3.ConfigProvider
	Loc             location.Location
	ShuttleSvc      shuttle.PGWShuttleService
	InfraMgmt       infra.Service
	ByteBrainClient bytebrain.ByteBrainService
	MonitorClient   *influxdb.RdsMonitor
}

type NewPostgreSQLDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewPostgreSQLDataSource(p NewPostgreSQLDataSourceIn) NewPostgreSQLDataSourceOut {
	return NewPostgreSQLDataSourceOut{
		Source: retryIfWhiteListNotReady(&postgreImpl{
			DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
			cnf:               p.Conf,
			pgmgr:             p.PgMgr,
			C3ConfProvider:    p.C3ConfProvider,
			Loc:               p.Loc,
			InfraMgmt:         p.InfraMgmt,
			ShuttleSvc:        p.ShuttleSvc,
			ByteBrainClient:   p.ByteBrainClient,
			MonitorClient:     p.MonitorClient,
		}),
	}
}

type postgreImpl struct {
	datasource.DataSourceService
	cnf                 config.ConfigProvider
	pgmgr               mgr.Provider
	C3ConfProvider      c3.ConfigProvider
	Loc                 location.Location
	InfraMgmt           infra.Service
	ShuttleSvc          shuttle.PGWShuttleService
	ByteBrainClient     bytebrain.ByteBrainService
	MonitorClient       *influxdb.RdsMonitor
	pgMutex             sync.Mutex
	HealthSummaryMetric map[string]func(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error)
}

func (self *postgreImpl) Type() shared.DataSourceType {
	return shared.Postgres
}

var (
	nodeSpecRe = regexp.MustCompile(`(\d+)c(\d+)g`)
)

func (self *postgreImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}
	rreq := &pgModel.DescribeDBInstancesReq{
		PageNumber: utils.Int32Ref(req.PageNumber),
		PageSize:   utils.Int32Ref(req.PageSize),
	}
	if req.InstanceName != "" {
		rreq.InstanceName = utils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		rreq.InstanceId = utils.StringRef(req.InstanceId)
	}
	if req.DBEngineVersion != "" {
		convertedDBEngine, err := pgModel.DBEngineVersionFromString(req.DBEngineVersion)
		if err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}
		rreq.DBEngineVersion = pgModel.DBEngineVersionPtr(convertedDBEngine)
	}
	if req.InstanceStatus != "" {
		convertedInstanceStatus, err := pgModel.InstanceStatusFromString(req.InstanceStatus)
		if err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}
		rreq.InstanceStatus = pgModel.InstanceStatusPtr(convertedInstanceStatus)
	}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		rreq.CreateTimeStart = utils.StringRef(req.CreateTimeStart)
		rreq.CreateTimeEnd = utils.StringRef(req.CreateTimeEnd)
	}
	// 如果是运维面账号，则不需要传这个值
	if req.TenantId != "0" && req.TenantId != "1" {
		rreq.TenantId = utils.StringRef(req.TenantId)
	}
	if len(req.Tags) > 0 {
		tagFilter := make([]*pgModel.TagFilterObject, 0)
		for _, tag := range req.Tags {
			tagFilter = append(tagFilter, &pgModel.TagFilterObject{
				Key:   tag.Key,
				Value: utils.StringRef(tag.Value),
			})
		}
		rreq.TagFilters = tagFilter
	}
	if req.ProjectName != "" {
		rreq.ProjectName = utils.StringRef(req.ProjectName)
	}
	rresp := &pgModel.DescribeDBInstancesResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstances.String(), rreq, rresp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "get pg instances fail %v", err)
		return nil, err
	}
	resp := &datasource.ListInstanceResp{Total: int64(rresp.Total)}
	err := fp.StreamOf(rresp.Instances).Map(func(i *pgModel.InstanceObject) *model.InstanceInfo {
		internalAddress := "-"
		var (
			vcpu   int32
			memory int32
		)
		for _, endpoint := range i.AddressObject {
			//endpoint类型取默认终端
			if endpoint.NetworkType == pgModel.NetworkType_Private {
				if endpoint.Domain != "" && endpoint.Port != "" {
					internalAddress = fmt.Sprintf("%s:%s", endpoint.Domain, endpoint.Port)
				}
			}
		}
		nodeSpec := strings.Split(i.NodeSpec, ".")
		match := nodeSpecRe.FindStringSubmatch(nodeSpec[len(nodeSpec)-1])
		if len(match) == 3 {
			vcpu = utils.MustStrToInt32(match[1])
			memory = utils.MustStrToInt32(match[2])
		}
		hasReadOnlyNodes := false
		if i.NodeNumber > 2 {
			hasReadOnlyNodes = true
		}
		targetTags := make([]*model.TagObject, 0)
		if i.IsSetTags() {
			for _, tag := range i.GetTags() {
				targetTags = append(targetTags, &model.TagObject{
					Key:   tag.Key,
					Value: *tag.Value,
				})
			}
		}
		accountId := req.TenantId
		if i.GetAccountId() != "" {
			accountId = i.GetAccountId()
		}
		return &model.InstanceInfo{
			InstanceId:     utils.StringRef(i.InstanceId),
			InstanceName:   utils.StringRef(i.InstanceName),
			InstanceStatus: i.InstanceStatus.String(),
			InstanceSpec: &model.InstanceSpec{
				CpuNum:     vcpu,
				MemInGiB:   float64(memory),
				NodeNumber: i.NodeNumber,
			},
			DBEngineVersion: i.DBEngineVersion.String(),
			Zone:            i.ZoneId,
			ProjectName:     i.ProjectName,
			AccessSource:    "云数据库 PostgreSQL 版",
			InternalAddress: internalAddress,
			HasReadOnlyNode: hasReadOnlyNodes,
			RegionId:        utils.StringRef(i.GetRegionId()),
			Tags:            targetTags,
			AccountId:       utils.StringRef(accountId),
			CreateTime:      utils.StringRef(i.GetCreateTime()),
			InstanceType:    model.InstanceType_Postgres,
			LinkType:        model.LinkType_Volc,
		}
	}).ToSlice(&resp.InstanceList)
	// 若instanceList部分元素为空，返回错误信息
	for _, instance := range resp.InstanceList {
		if instance == nil {
			return nil, fmt.Errorf("get pg instance null")
		}
	}
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (self *postgreImpl) ListInstanceLightWeight(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	return self.ListInstance(ctx, req)
}
func (self *postgreImpl) ListInstanceNodes(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {
	rreq := &pgModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	resp := &pgModel.DescribeDBInstanceDetailResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), rreq, resp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "get pg instance detailed fail %v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesResp{}
	fp.StreamOf(resp.Nodes).
		Map(func(node *pgModel.NodeObject) *model.NodeInfoObject {
			nodeType, _ := model.NodeTypeFromString(node.NodeType.String())
			return &model.NodeInfoObject{
				NodeId:   node.NodeId,
				NodeType: nodeType,
				CpuNum:   *node.VCPU,
				MemInGiB: *node.Memory,
				ZoneId:   node.ZoneId,
			}
		}).ToSlice(&ret.Nodes)
	return ret, nil
}

func (self *postgreImpl) IsMyOwnInstance(ctx context.Context, instanceId string, _ shared.DataSourceType) bool {
	rreq := &pgModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	resp := &pgModel.DescribeDBInstanceDetailResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), rreq, resp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "get pg instance detailed fail %v", err)
		return false
	}
	return true
}
func (self *postgreImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	var (
		dbInstanceStatusBlackList map[string]string
		blackList                 []string
		rawBlackList              string
	)
	rreq := &pgModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	resp := &pgModel.DescribeDBInstanceDetailResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), rreq, resp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "get pg instance detailed fail %v", err)
		return err
	}
	cfg := self.cnf.Get(ctx)
	if isConnectedInstance {
		rawBlackList = cfg.DBInstanceStateWithConnectionBlackList
	} else {
		rawBlackList = cfg.DBInstanceStateWithoutConnectionBlackList
	}
	err := json.Unmarshal([]byte(rawBlackList), &dbInstanceStatusBlackList)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		blackList = strings.Split(dbInstanceStatusBlackList[ds.String()], ",")
	}
	currentStatus := resp.BasicInfo.InstanceStatus.String()
	for _, item := range blackList {
		if item == currentStatus {
			log.Warn(ctx, "instance status is %s, not support", currentStatus)
			return consts.ErrorWithParam(model.ErrorCode_InstanceNotInRunningStatus, currentStatus)
		}
	}
	return nil
}

func (self *postgreImpl) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (*datasource.DescribeDBInstanceDetailResp, error) {
	detailResp := &datasource.DescribeDBInstanceDetailResp{
		InstanceId: req.InstanceId,
	}
	rreq := &pgModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	presp := &pgModel.DescribeDBInstanceDetailResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), rreq, presp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Warn(ctx, "DescribeDBInstanceDetail InstanceNotFound of %s, err=%v", req.InstanceId, err)
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetail of %s, err=%v", req.InstanceId, err)
		}
		return nil, err
	}
	if IsPGInstanceDelete(presp.BasicInfo.GetInstanceStatus().String()) {
		log.Warn(ctx, "pg is delete, instance_status:%s", detailResp.InstanceStatus)
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	detailResp.InstanceName = presp.BasicInfo.GetInstanceName()
	detailResp.InstanceStatus = presp.BasicInfo.GetInstanceStatus().String()
	detailResp.RegionId = presp.BasicInfo.GetRegionId()
	detailResp.ZoneId = presp.BasicInfo.ZoneId
	detailResp.DBEngineVersion = presp.BasicInfo.GetDBEngineVersion().String()
	detailResp.VCPU = presp.BasicInfo.VCPU
	detailResp.Memory = presp.BasicInfo.Memory
	detailResp.ProjectName = *presp.BasicInfo.ProjectName
	detailResp.StorageSpace = int64(presp.BasicInfo.GetStorageSpace())
	detailResp.NodeSpec = presp.BasicInfo.NodeSpec
	return detailResp, nil
}

func IsPGInstanceDelete(InstanceStatus string) bool {
	return InstanceStatus == pgModel.InstanceStatus_Deleted.String() ||
		InstanceStatus == pgModel.InstanceStatus_Deleting.String() ||
		InstanceStatus == pgModel.InstanceStatus_CreateError.String()
}

func (self *postgreImpl) DescribeDBInstanceCluster(ctx context.Context, req *datasource.DescribeDBInstanceClusterReq) (*datasource.DescribeDBInstanceClusterResp, error) {
	rreq := &pgModel.DescribeDBInstanceK8SInfoReq{
		InstanceId: req.InstanceId,
	}
	presp := &pgModel.DescribeDBInstanceK8SInfoResp{}
	log.Info(ctx, "%s", libutils.Show(rreq))
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceK8SInfo.String(), rreq, presp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "Action_DescribeDBInstanceK8SInfo fail for %s, %v", req.InstanceId, err)
		return nil, err
	}
	nodePoolToCluster := make(map[string]string)
	// 据说DescribeDBInstanceK8SInfo接口返回的集群信息不准确，所以下面进行替换
	for _, k8sInfo := range presp.Datas {
		nodePoolToCluster[k8sInfo.NodePool] = ""
	}
	nodePoolsResp, err := self.InfraMgmt.ListNodePoolsByPD(ctx, framework.Product_RDS_PostgreSQL.String())
	if err != nil {
		log.Warn(ctx, "ListNodePoolsByPD fail %v", err)
		return nil, err
	}
	for _, pool := range nodePoolsResp.Pool {
		if _, ok := nodePoolToCluster[pool.GetName()]; ok {
			nodePoolToCluster[pool.GetName()] = pool.KubeClusterName
		}
	}
	kubeClusters := make(map[string]string)
	for _, clusterName := range nodePoolToCluster {
		if clusterName == "" {
			log.Error(ctx, "clusterName is nil, InstanceId:%s, DescribeDBInstanceK8SInfoResp:%s, ListNodePoolsResp:%s", req.InstanceId, libutils.Show(presp), libutils.Show(nodePoolsResp))
			return nil, errors.New("clusterName is nil")
		}
		kubeClusters[clusterName] = clusterName
	}
	multiAz := len(kubeClusters) > 1
	return &datasource.DescribeDBInstanceClusterResp{
		MultiAZ:          multiAz,
		AzClusterMap:     kubeClusters,
		NodePool2Cluster: nodePoolToCluster,
	}, nil
}

func (self *postgreImpl) DescribeDBInstanceAuditCollectedPod(ctx context.Context, req *datasource.DescribeDBInstanceAuditCollectedPodReq) (*datasource.DescribeDBInstanceAuditCollectedPodResp, error) {
	panic("pg do not need to get collected pod")
}

func (self *postgreImpl) OpenDBInstanceAuditLog(ctx context.Context, req *datasource.OpenDBInstanceAuditLogReq) (*datasource.OpenDBInstanceAuditLogResp, error) {
	_, err := self.SwitchDBInstanceAudit(ctx, req.InstanceId, true)
	if err != nil {
		return nil, err
	}
	return &datasource.OpenDBInstanceAuditLogResp{}, nil
}

func (self *postgreImpl) CloseDBInstanceAuditLog(ctx context.Context, req *datasource.CloseDBInstanceAuditLogReq) (*datasource.CloseDBInstanceAuditLogResp, error) {
	_, err := self.SwitchDBInstanceAudit(ctx, req.InstanceId, false)
	if err != nil {
		return nil, err
	}
	return &datasource.CloseDBInstanceAuditLogResp{}, nil
}

func (self *postgreImpl) SwitchDBInstanceAudit(ctx context.Context, instanceId string, open bool) (*datasource.OpenDBInstanceAuditLogResp, error) {
	openReq := &pgModel.SwitchDBInstanceAuditReq{
		InstanceId: instanceId,
		Switch:     &open,
	}
	openResp := &pgModel.SwitchDBInstanceAuditResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_SwitchDBInstanceAudit.String(), openReq, openResp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "Action_DescribeDBInstanceK8SInfo fail %v", err)
		return nil, err
	}
	return nil, nil
}

func (self *postgreImpl) DescribeDBProxyConfig(ctx context.Context, req *datasource.DescribeDBProxyConfigReq) (*datasource.DescribeDBProxyConfigResp, error) {
	return &datasource.DescribeDBProxyConfigResp{IsProxyEnable: false}, nil
}

func (self *postgreImpl) AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error) {
	if ds.LinkType != shared.Volc {
		return "", nil
	}
	// 查询pg，获取白名单版本号
	version, err := self.checkNewWhiteList(ctx, ds.InstanceId)
	if err != nil {
		log.Warn(ctx, "postgreImpl: check new white list err: %v", err.Error())
		//return "", err
	}
	// v2白名单逻辑
	if self.isNewWhiteList(ctx, version) {
		allowList := "**********/10"
		//if self.cnf.Get(ctx).EnableRDSInternalConnectSwitch || ds.Address != "" {
		//	allowList = "*********/8,********/8,**********/10"
		//}
		////如果命中了租户ID的白名单才能走shuttle，否则走内网直连方式
		//if !innerUtils.IsTenantEnabledFromCtx(ctx, self.cnf.Get(ctx).RDSShuttleTenantIdList) {
		//	allowList = "*********/8,********/8,**********/10"
		//}
		description := "DBW internal sole white list for rds"
		regionId, err := self.getRegionId(ctx, ds.InstanceId)
		if err != nil {
			log.Warn(ctx, "postgreImpl.getRegionId failed: %s", err.Error())
			return "", err
		}
		log.Info(ctx, "get instance [%s] with region [%s]", ds.InstanceId, regionId)

		// 查询白名单ID（隐藏逻辑：如果查询到了白名单组，但是和MgrPodCIDR中的不一样，这个方法会先做更新，再返回白名单ID）
		wlID, err := self.getWhiteListID(ctx, regionId, ds.InstanceId, false, allowList)
		if err != nil {
			return "", err
		}
		log.Info(ctx, "addWhitelist start, whiteId is %s", wlID)
		if wlID == "" {
			wlID, err = self.CreateAllowListNew(ctx, &pgModel.CreateAllowListReq{
				AllowListName: DbwSoleByteRdsGroupName,
				AllowListType: "IPv4",
				AllowListDesc: utils.StringRef(description),
				AllowList:     allowList,
			})
			if err != nil {
				log.Warn(ctx, "postgreImpl: create allow list failed %s", err.Error())
				return "", err
			}

		}
		instanceWlID, err := self.getWhiteListID(ctx, regionId, ds.InstanceId, true, allowList)
		if err != nil {
			return "", err
		}
		if instanceWlID == "" && wlID != "" {
			err = self.AssociateAllowList(ctx, &pgModel.AssociateAllowListReq{
				AllowListIds: []string{wlID},
				InstanceIds:  []string{ds.InstanceId},
			})
			if err != nil {
				log.Warn(ctx, "associate allow list failed %s", err.Error())
				return "", err
			}
		}
		// v1->v2过渡阶段，等v2创建好后，删除v1的白名单，如果不存在接口也不会报错
		delReq := &pgModelv1.DeleteDBInstanceIPListReq{
			InstanceId: ds.InstanceId,
			GroupName:  `dbw-` + (consts.RDSV1WhiteListNameSuffix),
		}
		log.Info(ctx, "postgreImpl.DeleteDBInstanceIPList req=%s", utils.Show(delReq))
		err = self.pgmgr.Get().Call(ctx, pgModelv1.Action_DeleteDBInstanceIPList.String(), delReq, nil)
		if err != nil {
			log.Warn(ctx, "PG V1 WhiteList Name [%s] DeleteDBInstanceIPList failed, Ingore Err [%v]", `dbw-`+(consts.RDSV1WhiteListNameSuffix), err.Error())
		}
		return wlID, nil
	}

	// v1白名单处理逻辑
	groupName := `dbw-` + (consts.RDSV1WhiteListNameSuffix)
	req := &pgModelv1.CreateDBInstanceIPListReq{
		InstanceId: ds.InstanceId,
		GroupName:  groupName,
		IPList:     []string{"**********/10"},
	}
	log.Info(ctx, "postgreImpl.CreateDBInstanceIPList req=%s", utils.Show(req))
	err = self.pgmgr.Get().Call(ctx, pgModelv1.Action_InnerCreateDBInstanceIPList.String(), req, nil)
	// 忽略白名单已存在错误
	if err != nil && strings.Contains(err.Error(), `InvalidSecurityIPList_Duplicate`) {
		log.Info(ctx, "PG V1 WhiteList Name [%s] Already Exists, Ingore Err [%v]", groupName, err.Error())
		err = nil
	}
	return "", err
}

func (self *postgreImpl) DescribeDBInstanceDetailForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	detailResp := &datasource.DescribeDBInstanceDetailResp{
		InstanceId: req.InstanceId,
	}
	rreq := &pgModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	presp := &pgModel.DescribeDBInstanceDetailResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), rreq, presp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetailForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetailForPilot, err=%v", err)
			return "", err
		}

	}
	if IsPGInstanceDelete(presp.BasicInfo.GetInstanceStatus().String()) {
		log.Info(ctx, "pg is delete, instance_status:%s", detailResp.InstanceStatus)
		return "Instance Not Found", nil
	}

	return utils.Show(presp), nil
}

func (self *postgreImpl) DescribeDBInstanceParametersForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	rreq := &pgModel.DescribeDBInstanceParametersReq{
		InstanceId: req.InstanceId,
	}
	presp := &pgModel.DescribeDBInstanceParametersResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceParameters.String(), rreq, presp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetailForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetailForPilot, err=%v", err)
			return "", err
		}

	}
	return utils.Show(presp), nil
}

func (self *postgreImpl) checkNewWhiteList(ctx context.Context, instanceId string) (*pgModel.AllowListVersion, error) {
	resp, err := self.describeInstance(ctx, instanceId)
	if err != nil {
		return nil, err
	}
	return resp.BasicInfo.AllowListVersion, err
}

func (self *postgreImpl) isNewWhiteList(ctx context.Context, version *pgModel.AllowListVersion) bool {
	versionIsV2orInitial := func(v pgModel.AllowListVersion) bool {
		if v == pgModel.AllowListVersion_v2 ||
			v == pgModel.AllowListVersion_initial {
			return true
		}
		return false
	}

	if version != nil && versionIsV2orInitial(*version) {
		return true
	}
	return false
}

func (self *postgreImpl) getRegionId(ctx context.Context, instanceId string) (string, error) {
	resp, err := self.describeInstance(ctx, instanceId)
	if err != nil {
		log.Warn(ctx, "rdsImpl.getRegionId: %v", err)
		return "", err
	}
	return resp.BasicInfo.RegionId, nil
}

func (self *postgreImpl) getWhiteListID(ctx context.Context, regionId string, instanceId string, withInstanceId bool, allowList string) (string, error) {
	// 根据region获得白名单列表
	req := &pgModel.DescribeAllowListsReq{
		RegionId: regionId,
	}
	if withInstanceId {
		req.InstanceId = utils.StringRef(instanceId)
	}
	resp := &pgModel.DescribeAllowListsResp{}
	err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeAllowLists.String(), req, resp, client.WithVersion(RdsPgVersionV2))
	if err != nil {
		return "", err
	}

	// 从白名单列表中根据group name找到wlid
	var wlID string
	for _, info := range resp.AllowLists {
		if strings.EqualFold(info.AllowListName, DbwSoleByteRdsGroupName) {
			wlID = info.AllowListId
			log.Info(ctx, "postgreImpl.getWhiteListID: get white list id [%s] for groupName [%s], instance [%s]", wlID, DbwSoleByteRdsGroupName, instanceId)
			//如果白名单组内的IP和配置中的不一致，这里会重新再做一次更新
			err = self.ensureAllowList(ctx, instanceId, wlID, allowList)
			if err != nil {
				return "", err
			}
			break
		}
	}
	log.Info(ctx, "postgreImpl.getWhiteListID: find wlID [%s] for groupName [%s], instance [%s] in current allowlists", wlID, DbwSoleByteRdsGroupName, instanceId)
	return wlID, nil
}

func (self *postgreImpl) ensureAllowList(ctx context.Context, instanceId string, wlID string, allowList string) (err error) {
	req := &pgModel.DescribeAllowListDetailReq{
		AllowListId: wlID,
	}
	resp := &pgModel.DescribeAllowListDetailResp{}
	err = self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeAllowListDetail.String(), req, resp, client.WithVersion(RdsPgVersionV2))
	if err != nil {
		log.Warn(ctx, "describe allow list [%s] failed: %s", wlID, err.Error())
		return
	}

	log.Info(ctx, "postgreImpl.ensureAllowList: get white list AllowList is [%s], MgrPodCIDR list is [%s]", resp.AllowList, allowList)
	if !self.containsIPList(resp.AllowList, allowList) {
		rreq := &pgModel.ModifyAllowListReq{
			AllowListName:    DbwSoleByteRdsGroupName,
			AllowListId:      wlID,
			ModifyMode:       pgModel.ModifyMode_Cover,
			AllowList:        utils.StringRef(allowList),
			ApplyInstanceNum: utils.Int32Ref(int32(len(resp.AssociatedInstances))),
		}
		// 如果发现白名单配置值（100段）和v2白名单内的信息不一致，那就更新，但是要忽略更新报错的场景
		log.Info(ctx, "ensureAllowList: modify instance [%s] allowList from [%s] to [%s]", resp.AllowList, allowList)
		err = self.pgmgr.Get().Call(ctx, pgModel.Action_ModifyAllowList.String(), rreq, nil, client.WithVersion(RdsPgVersionV2))
		if err != nil {
			log.Warn(ctx, "modify instance [%s] allowList [%s] with ip [%s] failed: %s", instanceId, wlID, self.cnf.Get(ctx).MgrPodCIDR, err.Error())
			err = nil
			return
		}
	}
	return
}

func (self *postgreImpl) containsIPList(curCIDR, targetCIDR string) bool {
	curCIDRList := strings.Split(curCIDR, ",")
	if len(curCIDRList) < 1 {
		return false
	}
	// maybe not set the param, to alarm
	if targetCIDR == "" {
		log.Error(context.TODO(), "the MgrPodCIDR not set")
	}
	targetCIDRList := strings.Split(targetCIDR, ",")

	return listsEqual(curCIDRList, targetCIDRList)
}

func listsEqual(list1, list2 []string) bool {
	// 如果两个列表长度不同，直接返回 false
	if len(list1) != len(list2) {
		return false
	}

	// 将两个列表中的元素排序
	sort.Strings(list1)
	sort.Strings(list2)

	// 比较排序后的列表是否相等
	for i := 0; i < len(list1); i++ {
		if list1[i] != list2[i] {
			return false
		}
	}

	// 如果所有元素都相等，返回 true
	return true
}

func (self *postgreImpl) AssociateAllowList(ctx context.Context, req *pgModel.AssociateAllowListReq) (err error) {
	log.Info(ctx, "postgreImpl.AssociateAllowList req=%s", utils.Show(req))

	err = self.pgmgr.Get().Call(ctx, pgModel.Action_AssociateAllowList.String(),
		&req,
		nil,
		client.WithVersion(RdsPgVersionV2),
	)

	return err
}

func (self *postgreImpl) CreateAllowListNew(ctx context.Context, req *pgModel.CreateAllowListReq) (id string, err error) {
	log.Info(ctx, "postgreImpl.CreateAllowList req=%s", utils.Show(req))

	originResp := &pgModel.CreateAllowListResp{}
	err = self.pgmgr.Get().Call(ctx, pgModel.Action_CreateAllowList.String(),
		&req,
		originResp,
		client.WithVersion(RdsPgVersionV2),
	)
	if err != nil {
		return "", err
	}

	id = originResp.AllowListId
	log.Info(ctx, "postgreImpl.CreateAllowList: get wlID [%s]", id)
	return id, nil
}

func (self *postgreImpl) describeInstance(ctx context.Context, instanceId string) (*pgModel.DescribeDBInstanceDetailResp, error) {
	rreq := &pgModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}

	rresp := &pgModel.DescribeDBInstanceDetailResp{}
	err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), rreq, rresp, client.WithVersion(consts.Postgres_Version_V2))
	return rresp, err
}

func (self *postgreImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	resp, err := self.describeInstance(ctx, ds.InstanceId)
	if err != nil {
		log.Warn(ctx, "postgreImpl: get PG [%s] describeInstance fail: %v", ds.InstanceId, err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if resp.BasicInfo.VpcID == "" {
		// vpcID应该不会为空，如果为空了，就直接报错返回给用户
		log.Warn(ctx, "postgreImpl: get PG [%s] describeInstance vpcId is nil", ds.InstanceId, err)
		return fmt.Errorf("VpcId is null, please update vpc for your instance")
	}
	if resp.BasicInfo.SubnetId == "" {
		// 子网未升级用户，提示无法登录
		log.Warn(ctx, "postgreImpl: get PG [%s] describeInstance subnetId is nil", ds.InstanceId, err)
		return fmt.Errorf("SubnetId is null, please create subnet for your instance")
	}
	ds.VpcID = resp.BasicInfo.VpcID
	for _, connectionInfo := range resp.Endpoints {
		// 这里需要判断一下是否是默认终端,不是默认终端的，不能要
		if connectionInfo.EndpointType != pgModel.EndpointType_Cluster {
			continue
		}
		for _, connection := range connectionInfo.Address {
			if connection.NetworkType == pgModel.NetworkType_Private {
				ds.Address = fmt.Sprintf("%s:%s", connection.IPAddress, connection.Port)
			}
		}
	}

	if ds.Address == "" {
		log.Warn(ctx, "fill rds instance %s address error", ds.InstanceId)
		return errors.New("get address error")
	}
	return nil
}

func (self *postgreImpl) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	if self.cnf.Get(ctx).PGCheckConnV2 {
		return self.CheckConnV2(ctx, ds)
	}

	if ds.LinkType != shared.Volc {
		return nil
	}

	req := &pgModelv1.ListInstanceNodesReq{
		InstanceId: ds.InstanceId,
	}
	resp := &pgModelv1.ListInstanceNodesResp{}
	err := self.pgmgr.Get().Call(ctx, pgModelv1.Action_ListInstanceNodes.String(), req, resp)
	if err != nil {
		log.Warn(ctx, "get pg %s connection fail %v", ds.InstanceId, err)
		if strings.Contains(strings.ToLower(err.Error()), "is deprecated") {
			return nil
		}
		return err
	}
	var node *pgModelv1.InstanceNodeInfo
	fp.StreamOf(resp.Datas).
		SortBy(func(n1, n2 *pgModelv1.InstanceNodeInfo) bool {
			return n1.NodeRole < n2.NodeRole
		}).
		First().
		To(&node)
	if node == nil {
		log.Info(ctx, "no such instance %s", ds.InstanceId)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, "This instance is not found:"+ds.InstanceId)
	}
	log.Info(ctx, "get rds internal address %s", node.NodeInternalIP)
	nds := new(shared.DataSource)
	*nds = *ds
	nds.Address = node.NodeInternalIP
	ds = nds

	conn, err := self.getConn(ctx, ds)
	if err != nil {
		log.Warn(ctx, "check conn by fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	_, err = conn.Version()
	if err != nil {
		log.Warn(ctx, "get version by %v fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	return nil
}

func (self *postgreImpl) CheckConnV2(ctx context.Context, ds *shared.DataSource) error {
	log.Info(ctx, "begin to CheckConnV2 in postgreImpl %s", ds.GetInstanceId())
	if ds.LinkType != shared.Volc {
		return nil
	}

	req := &pgModel.DescribeDBInstanceDeploymentReq{
		InstanceId: ds.InstanceId,
	}
	resp := &pgModel.DescribeDBInstanceDeploymentResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDeployment.String(), req, resp, client.WithVersion(RdsPgVersionV2), client.WithTenantID("0")); err != nil {
		log.Warn(ctx, "get pg %s connection fail %v", ds.InstanceId, err)
		return err
	}
	log.Info(ctx, "get pg %s connection resp %s", ds.InstanceId, utils.Show(resp))
	var node *pgModel.DeploymentObject
	if err := fp.StreamOf(resp.DeploymentInfo).SortBy(func(n1, n2 *pgModel.DeploymentObject) bool { return n1.NodeType < n2.NodeType }).First().To(&node); err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if node == nil {
		log.Info(ctx, "no such instance %s", ds.InstanceId)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, "This instance is not found:"+ds.InstanceId)
	}
	nds := new(shared.DataSource)
	*nds = *ds
	nds.Address = getAddressFromDeployment(node)
	ds = nds

	conn, err := self.getConn(ctx, ds)
	if err != nil {
		log.Warn(ctx, "check conn by fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	_, err = conn.Version()
	if err != nil {
		log.Warn(ctx, "get version by %v fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	return nil
}

func (self *postgreImpl) KillQuery(ctx context.Context, req *shared.DataSource, connection *shared.ConnectionInfo) error {
	conn, err := self.getConn(ctx, req)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Address, err)
		return err
	}

	defer func(conn db.Conn) {
		_ = conn.Close()
	}(conn)

	log.Info(ctx, "SELECT pg_cancel_backend(%s);", connection.OuterConnectionId)
	return conn.Exec(`SELECT pg_cancel_backend(?); `, connection.OuterConnectionId)
}

func (self *postgreImpl) DescribeDBInstanceSSL(ctx context.Context, req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	return &datasource.DescribeDBInstanceSSLResp{
		InstanceId: req.InstanceId,
		SSLEnable:  false,
	}, nil
}

func (self *postgreImpl) getConn(ctx context.Context, ds *shared.DataSource) (db.Conn, error) {
	log.Info(ctx, "begin to getConn in postgreImpl %s", ds)
	parts := strings.Split(ds.Address, ":")
	num, _ := strconv.ParseUint(parts[1], 10, 0)
	var realDB = "postgres"
	if ds.Db != "" {
		realDB = ds.Db
	}
	opt := &db.Options{
		Host:             parts[0],
		Port:             uint(num),
		DB:               realDB,
		Schema:           "public",
		User:             ds.User,
		Password:         ds.Password,
		Driver:           db.PostgreSQLDriver,
		ParseTimeManualy: false,
	}
	if ds.ConnectTimeoutMs != 0 {
		opt.Timeout = uint(ds.ConnectTimeoutMs)
	}
	if ds.ReadTimeoutMs != 0 {
		opt.ReadTimeout = uint(ds.ReadTimeoutMs)
	}
	if ds.WriteTimeoutMs != 0 {
		opt.WriteTimeout = uint(ds.WriteTimeoutMs)
	}
	return db.NewConn(opt)
}

func (self *postgreImpl) ExplainCommand(ctx context.Context, req *datasource.ExplainCommandReq) (*datasource.ExplainCommandResp, error) {
	// 这里连接数据库执行命令
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	if err != nil {
		return nil, err
	}
	var res []*datasource.ExplainCommandResult
	var plans []interface{}

	command := "EXPLAIN ANALYZE " + req.Command
	if err = conn.Raw(datasource.WithHint(command, datasource.DbwHint)).Scan(&plans); err != nil { // 执行失败
		// 对于explainCommand报错的情况,仅捕获表不存在，如果表存在，则给影响行数直接赋值为0
		log.Warn(ctx, "execute cmd %s err:%s", command, err.Error())
		if !strings.Contains(strings.ToLower(err.Error()), "doesn't exist") {
			res = append(res, &datasource.ExplainCommandResult{
				// 标准形式：(cost=0.14..208821.09 rows=9008784 width=0) (actual time=520.816..1653.159 rows=9 loops=1)
				Rows: "(actual rows=0)",
			})
			return &datasource.ExplainCommandResp{Command: res}, nil
		}
		return nil, err
	}
	for _, plan := range interfaceToStrings(plans, 3000) {
		res = append(res, &datasource.ExplainCommandResult{
			Rows: plan,
		})
	}

	log.Info(ctx, "explain result is %#v", res)
	return &datasource.ExplainCommandResp{Command: res}, nil
}

func (self *postgreImpl) ListDatabases(ctx context.Context, req *datasource.ListDatabasesReq) (*datasource.ListDatabasesResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
	}
	defer conn.Close()

	ret := &datasource.ListDatabasesResp{}
	var dbList []*DatabaseInfo
	sql := "SELECT datname, pg_encoding_to_char (encoding) AS charset, datcollate, datctype, datconnlimit, d.description AS description FROM pg_database LEFT JOIN pg_shdescription d ON d.objoid = oid WHERE datname NOT IN('template0', 'template1')"
	var args []interface{}
	if req.Keyword != "" {
		sql += " and datname like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	log.Info(ctx, "ListDatabases sql %v , args %v", sql, args)
	if err := conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&dbList); err != nil {
		log.Warn(ctx, "ListDatabases sql %v fail, args %v", sql, args)
		return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
	}

	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1)  FROM pg_database LEFT JOIN pg_shdescription d ON d.objoid = oid WHERE datname NOT IN('template0', 'template1') and datname like ?", `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) FROM pg_database LEFT JOIN pg_shdescription d ON d.objoid = oid WHERE datname NOT IN('template0', 'template1')").
			Scan(&ret.Total)
	}

	if err = fp.StreamOf(dbList).Map(func(db *DatabaseInfo) *shared.DatabaseInfo {
		log.Info(ctx, "listDatabase DatabasesInfo: %s", db)
		return &shared.DatabaseInfo{
			Name:             db.DBName,
			CharacterSetName: db.CharacterSetName,
			CollationName:    db.CollationName,
			CType:            db.CTypeName,
			Limit:            db.Datconnlimit,
			Description:      db.Description,
		}
	}).ToSlice(&ret.Items); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *postgreImpl) ListSchema(ctx context.Context, req *datasource.ListSchemaReq) (*datasource.ListSchemaResp, error) {
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListSchemaResp{}
	sql := "SELECT schema_name FROM information_schema.schemata where catalog_name=?"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and schema_name like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	//sql += ` LIMIT ? OFFSET ?`
	sql += DbwConsoleDefaultHint
	//args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.schemata where catalog_name=? and schema_name like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.schemata where catalog_name=?", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *postgreImpl) ListTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTablesResp{}
	if req.Schema == "" {
		return nil, errors.New("no Schema")
	}
	sql := "select TABLE_NAME from information_schema.tables where table_catalog=? and table_schema=? and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args := []interface{}{req.DB, req.Schema}
	if req.Keyword != "" {
		sql += " and table_name like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DbwConsoleDefaultHint
	log.Info(ctx, "ListTables sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where table_catalog=? and table_schema=? and TABLE_TYPE='BASE TABLE' and TABLE_NAME like ?", req.DB, req.Schema, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where table_catalog=? and table_schema=? and TABLE_TYPE='BASE TABLE'", req.DB, req.Schema).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *postgreImpl) ListTablesInfo(ctx context.Context, req *datasource.ListTablesInfoReq) (*datasource.ListTablesInfoResp, error) {
	panic("Not Implement")
}

func (self *postgreImpl) ListSchemaTables(ctx context.Context, req *datasource.ListSchemaTablesReq) (*datasource.ListSchemaTablesResp, error) {
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListSchemaTablesResp{}
	sql := "select TABLE_SCHEMA,TABLE_NAME from information_schema.tables where table_catalog=? and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and table_name like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ? `
	sql += DbwConsoleDefaultHint
	args = append(args, req.Limit, req.Offset)
	log.Info(ctx, "ListSchemaTables sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Tables); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where table_catalog=? and TABLE_TYPE='BASE TABLE' and TABLE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where table_catalog=? and TABLE_TYPE='BASE TABLE'", req.DB).
			Scan(&ret.Total)
	}
	for _, table := range ret.Tables {
		ret.Items = append(ret.Items, table.TableSchema+"."+table.TableName)
	}
	return ret, nil
}

func (self *postgreImpl) ListViews(ctx context.Context, req *datasource.ListViewsReq) (*datasource.ListViewsResp, error) {
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListViewsResp{}
	sql := "select TABLE_NAME from information_schema.TABLES where table_catalog=? and table_schema=? and TABLE_TYPE IN ('VIEW','MATERIALIZED VIEW')"
	args := []interface{}{req.DB, req.Schema}
	if req.Keyword != "" {
		sql += " and table_name like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ? `
	sql += DbwConsoleDefaultHint
	args = append(args, req.Limit, req.Offset)
	log.Info(ctx, "ListViews sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	log.Info(ctx, "ListViews sql res: %v", ret)
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where table_catalog=? and table_schema=? and TABLE_TYPE IN ('VIEW','MATERIALIZED VIEW') and TABLE_NAME like ?", req.DB, req.Schema, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where table_catalog=? and table_schema=? and TABLE_TYPE IN ('VIEW','MATERIALIZED VIEW')", req.DB, req.Schema).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *postgreImpl) ListFunctions(ctx context.Context, req *datasource.ListFunctionsReq) (*datasource.ListFunctionsResp, error) {
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListFunctionsResp{}
	sql := "SELECT p.proname || '(' || pg_get_function_arguments(p.oid) || ')' as function_signature FROM pg_proc p INNER JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = ?"
	args := []interface{}{req.Schema}
	if req.Keyword != "" {
		sql += " and p.proname like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ? `
	sql += DbwConsoleDefaultHint
	args = append(args, req.Limit, req.Offset)
	log.Info(ctx, "ListFunctions sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM pg_proc p INNER JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = ? and p.proname  like ?", req.Schema, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM pg_proc p INNER JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = ?", req.Schema).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *postgreImpl) ListTriggers(ctx context.Context, req *datasource.ListTriggersReq) (*datasource.ListTriggersResp, error) {
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTriggersResp{}
	sql := "SELECT trigger_name FROM information_schema.triggers WHERE event_object_catalog = ? AND trigger_schema = ?"
	args := []interface{}{req.DB, req.Schema}
	if req.Keyword != "" {
		sql += " and trigger_name like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ? `
	sql += DbwConsoleDefaultHint
	args = append(args, req.Limit, req.Offset)
	log.Info(ctx, "ListTriggers sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM information_schema.triggers WHERE event_object_catalog = ? AND trigger_schema = ? and trigger_name like ?", req.DB, req.Schema, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM information_schema.triggers WHERE event_object_catalog = ? AND trigger_schema = ?", req.DB, req.Schema).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *postgreImpl) ListSequence(ctx context.Context, req *datasource.ListSequenceReq) (*datasource.ListSequenceResp, error) {
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListSequenceResp{}
	sql := "SELECT sequence_name FROM information_schema.sequences WHERE sequence_catalog = ? AND sequence_schema = ?"
	args := []interface{}{req.DB, req.Schema}
	if req.Keyword != "" {
		sql += " and sequence_name like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ? `
	sql += DbwConsoleDefaultHint
	args = append(args, req.Limit, req.Offset)
	log.Info(ctx, "ListSequence sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM information_schema.sequences WHERE sequence_catalog = ? AND sequence_schema = ? and sequence_name like ?", req.DB, req.Schema, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM information_schema.sequences WHERE sequence_catalog = ? AND sequence_schema = ?", req.DB, req.Schema).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *postgreImpl) ListPgCollations(ctx context.Context, req *datasource.ListPgCollationsReq) (*datasource.ListPgCollationsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListPgCollationsResp{}
	sql := `SELECT n.nspname || '."' || c.collname || '"' AS collation_name FROM pg_collation c INNER JOIN pg_namespace n ON c.collnamespace = n.oid `
	var args []interface{}
	if req.Keyword != "" {
		sql += " where c.collname like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ? `
	sql += DbwConsoleDefaultHint
	args = append(args, req.Limit, req.Offset)
	log.Info(ctx, "ListPgCollations sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM pg_collation c INNER JOIN pg_namespace n ON c.collnamespace = n.oid where c.collname like ?", `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM pg_collation c INNER JOIN pg_namespace n ON c.collnamespace = n.oid").
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *postgreImpl) ListPgUsers(ctx context.Context, req *datasource.ListPgUsersReq) (*datasource.ListPgUsersResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListPgUsersResp{}
	sql := "SELECT usename FROM pg_user WHERE usename NOT IN ('pg_monitor', 'pg_read_all_settings', 'pg_read_all_stats', 'pg_stat_scan_tables', 'pg_read_server_files', 'pg_write_server_files', 'pg_execute_server_program', 'pg_signal_backend', 'pg_rds_superuser', 'ccp_monitoring', 'postgres', 'primaryuser', 'byte_rds_repl', 'byte_rds_proxy') "
	var args []interface{}
	if req.Keyword != "" {
		sql += " and usename like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ? `
	sql += DbwConsoleDefaultHint
	args = append(args, req.Limit, req.Offset)
	log.Info(ctx, "ListPgUsers sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM pg_user WHERE usename NOT IN ('pg_monitor', 'pg_read_all_settings', 'pg_read_all_stats', 'pg_stat_scan_tables', 'pg_read_server_files', 'pg_write_server_files', 'pg_execute_server_program', 'pg_signal_backend', 'pg_rds_superuser', 'ccp_monitoring', 'postgres', 'primaryuser', 'byte_rds_repl', 'byte_rds_proxy') and usename like ?", `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM pg_user WHERE usename NOT IN ('pg_monitor', 'pg_read_all_settings', 'pg_read_all_stats', 'pg_stat_scan_tables', 'pg_read_server_files', 'pg_write_server_files', 'pg_execute_server_program', 'pg_signal_backend', 'pg_rds_superuser', 'ccp_monitoring', 'postgres', 'primaryuser', 'byte_rds_repl', 'byte_rds_proxy')").
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *postgreImpl) ListTableSpaces(ctx context.Context, req *datasource.ListTableSpacesReq) (*datasource.ListTableSpacesResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTableSpacesResp{}
	sql := "SELECT spcname FROM pg_tablespace"
	var args []interface{}
	if req.Keyword != "" {
		sql += " where sequence_name like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ? `
	sql += DbwConsoleDefaultHint
	args = append(args, req.Limit, req.Offset)
	log.Info(ctx, "ListTableSpaces sql %v , args %v", sql, args)
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM pg_tablespace where sequence_name like ?", `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) FROM pg_tablespace").
			Scan(&ret.Total)
	}
	log.Info(ctx, "DescribeTableSpaces Items ListTableSpaces: %s", ret.Items)
	return ret, nil
}

func (self *postgreImpl) DescribePgTable(ctx context.Context, req *datasource.DescribePgTableReq) (*datasource.DescribePgTableResp, error) {
	ret := &datasource.DescribePgTableResp{}
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	// 查询表格元信息
	var tableMeta DBTableMeta
	sql1 := "SELECT obj_description(c.oid, 'pg_class') AS table_comment,u.usename AS table_owner,c.reloptions AS table_options FROM pg_class c JOIN pg_user u ON u.usesysid = c.relowner WHERE c.relname = ?"
	args1 := []interface{}{req.Table}
	sql1 += DbwConsoleDefaultHint
	log.Info(ctx, "DescribePgTable sql1 %v , args1 %v", sql1, args1)
	if err = conn.Raw(sql1, args1...).Scan(&tableMeta); err != nil {
		return nil, err
	}
	if err != nil {
		log.Warn(ctx, "get tableMeta of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	log.Warn(ctx, "getFillFactor %v and %v", tableMeta, tableMeta.FillRate)
	ret.Option = shared.PgTableInfo_TableOptionInfo{
		Name:     req.Table,
		Owner:    tableMeta.Owner,
		Schema:   req.Schema,
		FillRate: getFillFactor(tableMeta.FillRate),
		Comment:  tableMeta.Comment,
	}

	// 查询table主键
	var priKey string
	sql2 := "SELECT column_name FROM information_schema.constraint_column_usage WHERE constraint_name = (SELECT constraint_name FROM information_schema.table_constraints WHERE table_catalog = ? AND table_schema= ? AND table_name = ? AND constraint_type = 'PRIMARY KEY') AND table_catalog = ? AND table_schema= ? AND table_name = ?"
	args2 := []interface{}{req.DB, req.Schema, req.Table, req.DB, req.Schema, req.Table}
	sql2 += DbwConsoleDefaultHint
	log.Info(ctx, "DescribePgTable sql2 %v , args2 %v", sql2, args2)
	err = conn.Raw(sql2, args2...).Scan(&priKey)
	if err != nil {
		log.Warn(ctx, "get key of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}

	var sche string
	err = conn.Raw(fmt.Sprintf("SET search_path TO '%s'", req.Schema)).Scan(&sche)
	if err != nil {
		log.Warn(ctx, "SET search_path TO %s fail %v", req.Schema, err)
		return nil, err
	}

	// 查询列信息
	var columns []*DBColumn
	sql3 := `SELECT column_name, udt_name, data_type, is_nullable, collation_schema|| '."' || collation_name || '"' AS collation_name, numeric_scale, numeric_precision, character_maximum_length, column_default, col_description(? ::regclass::oid, ordinal_position) AS comment, 'f' AS IsPrimaryKey FROM information_schema.columns WHERE table_catalog = ? AND table_schema = ? AND table_name = ?`
	args3 := []interface{}{"\"" + req.Schema + "\"" + ".\"" + req.Table + "\"", req.DB, req.Schema, req.Table}
	sql3 += DbwConsoleDefaultHint
	log.Info(ctx, "DescribePgTable sql3 %v , args3 %v", sql3, args3)
	err = conn.Raw(sql3, args3...).Scan(&columns)
	if err != nil {
		log.Warn(ctx, "get columns of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	err = fp.StreamOf(columns).Map(func(c *DBColumn) *shared.PgTableInfo_ColumnInfo {
		return &shared.PgTableInfo_ColumnInfo{
			Name:         c.Name,
			Type:         c.Type,
			Array:        isArray(c.Array),
			AllowBeNul:   isAllowBeNul(c.AllowBeNul),
			IsPrimaryKey: isPrimaryKey(priKey, c.Name),
			Comment:      c.Comment,
			DefaultValue: c.DefaultValue,
			Length:       GetLength(c.Precision, c.Length),
			Scale:        c.Scale,
			SortRule:     c.SortRule,
		}
	}).ToSlice(&ret.Columns)
	if err != nil {
		return nil, err
	}

	// 查询索引
	var indexs []*DBIndex
	sql4 := "SELECT i.relname AS index_name, a.attname AS column_name, am.amname AS index_method, obj_description(i.oid, 'pg_class') AS comment, CASE WHEN idx.indisunique THEN 'unique' ELSE 'index' END AS index_type FROM pg_index idx JOIN pg_class t ON t.oid = idx.indrelid JOIN  pg_class i ON i.oid = idx.indexrelid JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(idx.indkey) JOIN pg_am am ON i.relam = am.oid WHERE t.relname =?"
	args4 := []interface{}{req.Table}
	log.Info(ctx, "DescribePgTable sql4 %v , args4 %v", sql4, args4)
	err = conn.Raw(sql4, args4...).Scan(&indexs)
	if err != nil {
		log.Warn(ctx, "get indexs of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}

	err = fp.StreamOf(indexFilter(indexs)).Map(func(d *DBIndex) *shared.PgTableInfo_IndexInfo {
		return &shared.PgTableInfo_IndexInfo{
			Name:        d.Name,
			Columns:     d.Column,
			IndexType:   d.IndexType,
			Comment:     d.Comment,
			IndexMethod: d.IndexMethod,
			Concurrent:  false,
		}
	}).ToSlice(&ret.Indexs)
	if err != nil {
		return nil, err
	}

	//查询外键信息
	var foreignReference []*ForeignReference
	sql5 := "SELECT conname AS foreign_key_name, conrelid::regclass AS table_name, a.attname AS column_name, confrelid::regclass AS referenced_table, af.attname AS referenced_column, confupdtype AS update_action, confdeltype AS delete_action, condeferrable AS is_deferrable, condeferred AS is_deferred, confmatchtype AS match_type, convalidated AS is_validate, obj_description(c.oid, 'pg_constraint') AS constraint_description FROM pg_constraint c JOIN pg_attribute a ON a.attnum = ANY(conkey) AND a.attrelid = conrelid JOIN pg_attribute af ON af.attnum = ANY(confkey) AND af.attrelid = confrelid WHERE conrelid = ?::regclass;"
	args5 := []interface{}{fmt.Sprintf("\"%s\".\"%s\"", req.Schema, req.Table)}
	log.Info(ctx, "DescribePgTable sql4 %v , args4 %v", sql4, args4)
	err = conn.Raw(sql5, args5...).Scan(&foreignReference)
	if err != nil {
		log.Warn(ctx, "get indexs of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	err = fp.StreamOf(ConvertToForeignReferenceForShard(foreignReference)).Map(func(d *ForeignReferenceForShard) *shared.PgTableInfo_ForeignReferenceInfo {
		return &shared.PgTableInfo_ForeignReferenceInfo{
			Name:         d.Name,
			Columns:      d.Columns,
			Delayable:    toBool(d.Delayable),
			Delay:        toBool(d.Delay),
			PerfectMatch: isPerfectMatch(d.PerfectMatch),
			NoVerify:     toBoolNot(d.NoVerify),
			Deleting:     getForeignType(d.Deleting),
			Updating:     getForeignType(d.Updating),
			Comment:      d.Comment,
		}
	}).ToSlice(&ret.ForeignReferenceInfo)
	if err != nil {
		return nil, err
	}

	//查询排他性约束
	var excludeConstraint []*ExcludeConstraint
	sql6 := "SELECT c.conname AS constraint_name, pg_get_constraintdef(c.oid) AS constraint_definition,t.spcname AS tablespace_name,am.amname AS access_method,i.reloptions AS fill_factor,condeferrable AS is_deferrable,condeferred AS is_deferred,d.description AS comment FROM pg_constraint c LEFT JOIN pg_class tbl ON c.conrelid = tbl.oid LEFT JOIN pg_am am ON tbl.relam = am.oid LEFT JOIN pg_index idx ON c.conindid = idx.indexrelid LEFT JOIN pg_class i ON idx.indexrelid = i.oid LEFT JOIN pg_description d ON c.oid = d.objoid  LEFT JOIN pg_tablespace t ON i.reltablespace = t.oid LEFT JOIN information_schema.key_column_usage kcu ON kcu.constraint_name = c.conname  LEFT JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = c.conname  WHERE c.contype = 'x' AND c.conrelid = ?::regclass"
	args6 := []interface{}{fmt.Sprintf("\"%s\".\"%s\"", req.Schema, req.Table)}
	sql6 += DbwConsoleDefaultHint
	log.Info(ctx, "DescribePgTable sql6 %v , args6 %v", sql6, args6)
	err = conn.Raw(sql6, args6...).Scan(&excludeConstraint)
	if err != nil {
		log.Warn(ctx, "get excluedConstraint of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	err = fp.StreamOf(excludeConstraint).Map(func(c *ExcludeConstraint) *shared.PgTableInfo_ExcludeConstraintInfo {
		return &shared.PgTableInfo_ExcludeConstraintInfo{
			Name:         c.Name,
			Definition:   GetDefinition(c.Definition),
			TableSpace:   c.TableSpace,
			AccessMethod: GetAccessMethod(c.Definition, c.AccessMethod),
			FillRate:     getFillFactor(c.FillRate),
			Delayable:    toBool(c.Delayable),
			Delay:        toBool(c.Delay),
			Comment:      c.Comment,
		}
	}).ToSlice(&ret.ExcludeConstraintInfo)
	if err != nil {
		return nil, err
	}

	//查询唯一约束
	var uniqueConstraint []*UniqueConstraint
	sql7 := "SELECT c.conname AS constraint_name,array_agg(DISTINCT kcu.column_name) AS column_name,t.spcname AS tablespace_name,i.reloptions AS fill_factor,bool_or(condeferrable) AS is_deferrable,bool_or(condeferred) AS is_deferred,d.description AS comment FROM pg_constraint c LEFT JOIN pg_index idx ON c.conindid = idx.indexrelid LEFT JOIN pg_class i ON idx.indexrelid = i.oid LEFT JOIN pg_description d ON c.oid = d.objoid LEFT JOIN pg_tablespace t ON i.reltablespace = t.oid LEFT JOIN information_schema.key_column_usage kcu ON kcu.constraint_name = c.conname LEFT JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = c.conname WHERE c.contype = 'u' AND c.conrelid = ?::regclass GROUP BY c.conname, t.spcname, i.reloptions, d.description"
	args7 := []interface{}{fmt.Sprintf("\"%s\".\"%s\"", req.Schema, req.Table)}
	sql7 += DbwConsoleDefaultHint
	log.Info(ctx, "DescribePgTable sql7 %v , args7 %v", sql7, args7)
	err = conn.Raw(sql7, args7...).Scan(&uniqueConstraint)
	if err != nil {
		log.Warn(ctx, "get uniqueConstraint of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	err = fp.StreamOf(uniqueConstraint).Map(func(c *UniqueConstraint) *shared.PgTableInfo_UniqueConstraintInfo {
		return &shared.PgTableInfo_UniqueConstraintInfo{
			Name:       c.Name,
			Field:      c.Field,
			TableSpace: c.TableSpace,
			FillRate:   getFillFactor(c.FillRate),
			Delayable:  toBool(c.Delayable),
			Delay:      toBool(c.Delay),
			Comment:    c.Comment,
		}
	}).ToSlice(&ret.UniqueConstraintInfo)
	if err != nil {
		return nil, err
	}

	//查询检测约束
	var checkConstraint []*CheckConstraint
	sql8 := "SELECT conname AS constraint_name, pg_get_constraintdef(con.oid) AS check_condition, coninhcount > 0 AS is_inherited, convalidated AS is_validated, obj_description(con.oid, 'pg_constraint') AS constraint_comment FROM pg_constraint con WHERE conrelid = ?::regclass AND contype = 'c'"
	args8 := []interface{}{fmt.Sprintf("\"%s\".\"%s\"", req.Schema, req.Table)}
	sql8 += DbwConsoleDefaultHint
	log.Info(ctx, "DescribePgTable sql8 %v , args8 %v", sql8, args8)
	err = conn.Raw(sql8, args8...).Scan(&checkConstraint)
	if err != nil {
		log.Warn(ctx, "get CheckConstraint of  %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	err = fp.StreamOf(checkConstraint).Map(func(c *CheckConstraint) *shared.PgTableInfo_CheckConstraintInfo {
		return &shared.PgTableInfo_CheckConstraintInfo{
			Name:           c.Name,
			Check:          c.Check,
			NonInheritance: toBoolNot(c.NonInheritance),
			NoVerify:       toBoolNot(c.NoVerify),
			Comment:        c.Comment,
		}
	}).ToSlice(&ret.CheckConstraintInfo)
	if err != nil {
		return nil, err
	}
	return ret, err
}

func (self *postgreImpl) GetDiskSize(ctx context.Context, req *datasource.GetDiskSizeReq) (*datasource.GetDiskSizeResp, error) {
	instanceNodesReq := &pgModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	instanceNodesResp := &pgModel.DescribeDBInstanceDetailResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), instanceNodesReq, instanceNodesResp, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "ListInstanceNodes failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	resp := &datasource.GetDiskSizeResp{}

	if instanceNodesResp.BasicInfo != nil {
		resp.DiskDataSize = numericConversion(instanceNodesResp.BasicInfo.StorageDataUse)
		resp.DiskLogSize = numericConversion(instanceNodesResp.BasicInfo.StorageLogUse)
		resp.WalSize = numericConversion(instanceNodesResp.BasicInfo.StorageWALUse)
	}
	return resp, nil
}

func (self *postgreImpl) DescribeTableSpace(ctx context.Context, req *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceResp, error) {
	req.Source.Db = req.Database
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer func(conn db.Conn) { _ = conn.Close() }(conn)
	isEmpty, err := self.isEmpty(conn)
	if err != nil {
		return nil, err
	}
	if isEmpty {
		return &shared.DescribeTableSpaceResp{Total: 0}, nil
	}
	sumSpace, err := self.getSumSpace(conn)
	if err != nil {
		log.Warn(ctx, "get sumSpace fail %v", err)
		return nil, err
	}
	// 因为PG如果只有表没有数据的话，这里sumSpace会变成0，所以为了不出现除以0的情况，我们把这里设置为1
	// 不会影响下面的计算，因为下面一定都是0/1
	if *sumSpace == 0 {
		*sumSpace = 1
	}
	tableInfoList, err := self.getTablesInfo(conn, req, sumSpace)
	if err != nil {
		log.Warn(ctx, "get tableInfoList fail %v", err)
		return nil, err
	}
	total, err := self.getTableTotal(conn)
	if err != nil {
		log.Warn(ctx, "get tableInfoList fail %v", err)
		return nil, err
	}
	ret := &shared.DescribeTableSpaceResp{Total: *total, TableStats: tableInfoList}
	return ret, nil
}

func (self *postgreImpl) DescribeDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) (*datasource.DescribeDialogInfosResp, error) {
	ret := &datasource.DescribeDialogInfosResp{}
	dialogInfos, err := self.getAllDialogInfos(ctx, req)
	if err != nil {
		return nil, err
	}
	// get details info
	ret.DialogDetails = self.filterDialogDetails(ctx, dialogInfos, req.QueryFilter, req.Limit, req.Offset)
	return ret, nil

}
func (self *postgreImpl) DescribeDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq,
) (ret *datasource.DescribeDialogStatisticsResp, err error) {

	dialogInfos, err := self.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:        req.Source,
		Component:     req.Component,
		QueryFilter:   req.QueryFilter,
		InternalUsers: req.InternalUsers,
	})
	if err != nil {
		return nil, err
	}
	// get aggregated info
	statistics := self.getDialogStatistics(ctx, dialogInfos, req.TopN)

	ret = &datasource.DescribeDialogStatisticsResp{
		DialogStatistics: statistics,
	}

	return ret, nil
}
func (self *postgreImpl) DescribeLockCurrentWaits(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) (*datasource.DescribeLockCurrentWaitsResp, error) {
	waitLocks, err := self.getAllWaitLocks(ctx, req)
	if err != nil {
		return nil, err
	}
	// get details info
	ret := self.filterWaitLockDetails(ctx, waitLocks, req.SearchParam, req.NodeIds)
	log.Info(ctx, "lockWaits list is %s", utils.Show(ret))
	return ret, nil
}
func (self *postgreImpl) DescribeTrxAndLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) (*datasource.DescribeTrxAndLocksResp, error) {
	trxAndLockList, err := self.getAllTrxLocks(ctx, req)
	if err != nil {
		return nil, err
	}
	filtedAndLockList := self.filterTrxAndLocks(ctx, trxAndLockList, req.SearchParam, req.NodeIds)
	ret := &datasource.DescribeTrxAndLocksResp{
		Result: &shared.DescribeTrxAndLocksInfo{
			TrxAndLockList: filtedAndLockList,
			Total:          int32(len(filtedAndLockList)),
		},
	}
	return ret, nil
}

func (self *postgreImpl) getAllWaitLocks(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) ([]*datasource.LockCurrentWaitsDetail, error) {
	var (
		waitLockList []*datasource.LockCurrentWaitsDetail
		pods         []*shared.KubePod
		querySql     string
	)
	mp := map[string]string{
		"RTrxStarted":      "blocked_xact_start",
		"RTrxWaitStarted":  "blocked_wait_start",
		"RBlockedWaitSecs": "blocked_wait_seconds",
	}
	// 获取所有pod节点
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "get InstancePods fail: %v", err)
		return nil, err
	}
	// 跳过备节点采集
	for _, pod := range listInstancePodsResp.Data {
		if pod.Role != model.NodeType_Secondary.String() {
			pods = append(pods, pod)
		}
	}
	if len(pods) < 1 {
		log.Warn(ctx, "No pg pod found")
		return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No pg pod found")
	}
	// 查询pg version
	var versionSql = "/*+ DBW DAS DEFAULT*/ show server_version"
	var pgVersion = PgDBVersion{}
	orderByStr := ` order by ` + mp[req.SortParam] + " " + req.Order

	for _, node := range pods {
		var (
			rawPgLocks []*datasource.PgLockCurrentWaitsDetail
			tempLocks  []*datasource.LockCurrentWaitsDetail
		)
		maSource := *req.Source
		maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, node.Containers[0].Port)
		maSource.NodeId = node.NodeId
		conn, err := self.getConn(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			continue
		}
		if err := conn.Raw(versionSql).Scan(&pgVersion); err != nil {
			log.Warn(ctx, "get pg version fail %v", err)
			return nil, err
		}
		log.Info(ctx, "pg version is %v", pgVersion.Version)
		majorVersion := strings.Split(pgVersion.Version, ".")[0]
		// pg 13及以下版本pg_locks不支持waitstart字段
		if strings.Contains(majorVersion, "11") || strings.Contains(majorVersion, "12") || strings.Contains(majorVersion, "13") {
			querySql = "SELECT blocked.datname AS db_name,blocked.usename AS blocked_user,blocked.backend_xid AS blocked_xid," +
				"blocked.xact_start AS blocked_xact_start,blocked.pid AS blocked_pid," +
				"blocked.query AS blocked_query,blocked.state AS blocked_state,l.mode AS lock_mode, l.locktype AS lock_type," +
				"blocking.query AS blocking_query,blocking.pid AS blocking_pid,blocking.backend_xid AS blocking_xid," +
				"blocking.xact_start AS blocking_xact_start,blocking.state AS blocking_state," +
				"ROUND(EXTRACT(EPOCH FROM (now() - blocked.query_start)))::INTEGER AS blocked_wait_seconds FROM pg_stat_activity blocked " +
				"JOIN pg_locks l ON blocked.pid = l.pid AND NOT l.granted " +
				"CROSS JOIN LATERAL unnest(pg_blocking_pids(blocked.pid)) AS blocking_pids JOIN pg_stat_activity blocking " +
				"ON blocking.pid = blocking_pids" + orderByStr + " limit 10000" // ignore_security_alert
		} else {
			querySql = "SELECT blocked.datname AS db_name,blocked.usename AS blocked_user,blocked.backend_xid AS blocked_xid," +
				"blocked.xact_start AS blocked_xact_start,l.waitstart AS blocked_wait_start,blocked.pid AS blocked_pid," +
				"blocked.query AS blocked_query,blocked.state AS blocked_state,l.mode AS lock_mode, l.locktype AS lock_type," +
				"blocking.query AS blocking_query,blocking.pid AS blocking_pid,blocking.backend_xid AS blocking_xid," +
				"blocking.xact_start AS blocking_xact_start,blocking.state AS blocking_state," +
				"ROUND(EXTRACT(EPOCH FROM (now() - blocked.query_start)))::INTEGER AS blocked_wait_seconds FROM pg_stat_activity blocked " +
				"JOIN pg_locks l ON blocked.pid = l.pid AND NOT l.granted " +
				"CROSS JOIN LATERAL unnest(pg_blocking_pids(blocked.pid)) AS blocking_pids JOIN pg_stat_activity blocking " +
				"ON blocking.pid = blocking_pids" + orderByStr + " limit 10000" // ignore_security_alert
		}
		log.Info(ctx, "exec sql is %s", querySql)
		if err = conn.Raw(datasource.WithHint(querySql, datasource.DbwDasHint)).Scan(&rawPgLocks); err != nil {
			log.Warn(ctx, "tempLocks err is %s", err.Error())
			conn.Close()
			continue
		}
		conn.Close()
		log.Info(ctx, "node %s waitLock is %s", node.NodeId, utils.Show(rawPgLocks))
		fp.StreamOf(rawPgLocks).Map(func(d *datasource.PgLockCurrentWaitsDetail) *datasource.LockCurrentWaitsDetail {
			return &datasource.LockCurrentWaitsDetail{
				NodeId:            node.NodeId,
				DbName:            d.DbName,
				RTrxMysqlThreadId: d.BlockedId,
				RTrxId:            d.RTrxId,
				RTrxState:         d.RTrxState,
				RWaitingQuery:     d.RWaitingQuery,
				RTrxStarted:       d.RTrxStarted,
				RTrxWaitStarted:   d.RTrxWaitStarted,
				RBlockedWaitSecs:  d.RBlockedWaitSecs,
				BTrxMysqlThreadId: d.BlockingId,
				BTrxStarted:       d.BTrxStarted,
				BTrxId:            d.BTrxId,
				BTrxState:         d.BTrxState,
				BBlockingQuery:    d.BBlockingQuery,
			}
		}).ToSlice(&tempLocks)
		waitLockList = append(waitLockList, tempLocks...)
	}
	return waitLockList, nil
}
func (self *postgreImpl) filterWaitLockDetails(ctx context.Context, data []*datasource.LockCurrentWaitsDetail, queryFilter *model.WaitLockQueryFilter, nodeIds []string) *datasource.DescribeLockCurrentWaitsResp {
	tData := data
	if len(nodeIds) > 0 {
		fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
			var existed bool
			for _, nodeId := range nodeIds {
				if d.NodeId == nodeId {
					existed = true
					break
				}
			}
			return existed
		}).ToSlice(&tData)
	}
	if queryFilter != nil {
		if blockTrxId := queryFilter.GetRTrxId(); blockTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.RTrxId), strings.ToLower(blockTrxId))
			}).ToSlice(&tData)
		}
		if blockingTrxId := queryFilter.GetBTrxId(); blockingTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.BTrxId), strings.ToLower(blockingTrxId))
			}).ToSlice(&tData)
		}
		if rTrxStatus := queryFilter.GetRTrxState(); rTrxStatus != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.RTrxState), strings.ToLower(rTrxStatus))
			}).ToSlice(&tData)
		}
		if bTrxStatus := queryFilter.GetBTrxState(); bTrxStatus != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.BTrxState), strings.ToLower(bTrxStatus))
			}).ToSlice(&tData)
		}
		if blockedSql := queryFilter.GetRWaitingQuery(); blockedSql != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.RWaitingQuery), strings.ToLower(blockedSql))
			}).ToSlice(&tData)
		}
		if blockingSql := queryFilter.GetBBlockingQuery(); blockingSql != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.LockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.BBlockingQuery), strings.ToLower(blockingSql))
			}).ToSlice(&tData)
		}
	}
	var details []*model.DescribeLockCurrentWaitsDetail
	fp.StreamOf(tData[0:fp.MinInt64(10000, int64(len(tData)))]).
		Map(func(val *datasource.LockCurrentWaitsDetail) *model.DescribeLockCurrentWaitsDetail {
			return &model.DescribeLockCurrentWaitsDetail{
				RTrxMysqlThreadId: val.RTrxMysqlThreadId,
				RTrxId:            val.RTrxId,
				RTrxState:         val.RTrxState,
				RWaitingQuery:     val.RWaitingQuery,
				RTrxStarted:       val.RTrxStarted,
				RTrxWaitStarted:   val.RTrxWaitStarted,
				RBlockedWaitSecs:  val.RBlockedWaitSecs,
				BTrxMysqlThreadId: val.BTrxMysqlThreadId,
				BTrxId:            val.BTrxId,
				BTrxState:         val.BTrxState,
				BBlockingQuery:    val.BBlockingQuery,
				BTrxStarted:       val.BTrxStarted,
				BTrxWaitStarted:   val.BTrxWaitStarted,
				BBlockingWaitSecs: val.BBlockingWaitSecs,
				DbName:            val.DbName,
				NodeId:            val.NodeId,
			}
		}).ToSlice(&details)

	return &datasource.DescribeLockCurrentWaitsResp{
		Result: details,
		Total:  int32(len(details)),
	}
}
func (self *postgreImpl) getAllTrxLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) ([]*shared.TrxAndLock, error) {
	var (
		trxLocksList []*shared.TrxAndLock
		pods         []*shared.KubePod
		querySql     string
	)
	mp := map[string]string{
		"TrxStartTime": "xact_start",
		"TrxExecTime":  "duration_seconds",
	}
	// 获取所有pod节点
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "get InstancePods fail: %v", err)
		return nil, err
	}
	for _, pod := range listInstancePodsResp.Data {
		if pod.Role != model.NodeType_Secondary.String() {
			pods = append(pods, pod)
		}
	}
	if len(pods) < 1 {
		log.Warn(ctx, "No pg pod found")
		return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No pg pod found")
	}
	/* sort */
	orderByStr := ` order by ` + mp[req.SortParam] + " " + req.Order
	querySql = "select pid,backend_xid,wait_event_type,wait_event," +
		"state,xact_start,state_change,EXTRACT(EPOCH FROM (now() - xact_start)) AS duration_seconds,query,pg_blocking_pids(pid) as block_pid from pg_stat_activity " +
		"where  (state = 'active' OR state LIKE 'idle in transaction%') and (backend_xid IS NOT NULL OR backend_xmin IS NOT NULL) and pid != pg_backend_pid() " + orderByStr // ignore_security_alert
	log.Info(ctx, "querySql is %s", querySql)
	for _, node := range pods {
		maSource := *req.Source
		maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, node.Containers[0].Port)
		maSource.NodeId = node.NodeId
		conn, err := self.getConn(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			continue
		}
		/* get TrxAndLocklist */
		var TrxAndLocklist []TrxAndLock
		if err = conn.Raw(datasource.WithHint(querySql, datasource.DbwDasHint)).Scan(&TrxAndLocklist); err != nil {
			log.Warn(ctx, "get trx failed %s", err.Error())
			conn.Close()
			continue
		}
		conn.Close()
		log.Info(ctx, "node %s TrxAndLocklist is %s", node.NodeId, utils.Show(TrxAndLocklist))
		/* add data */
		tempTrxAndLockList := make([]*shared.TrxAndLock, 0)
		for _, TL := range TrxAndLocklist {
			var lockStatus shared.LockStatus
			if TL.WaitEventType == "Lock" {
				lockStatus = shared.LockWait
			} else {
				lockStatus = shared.None
			}
			trxExecTime, _ := strconv.ParseFloat(TL.TrxExecTime, 32)
			tempTrxAndLockList = append(tempTrxAndLockList, &shared.TrxAndLock{
				ProcessId:     TL.ProcessId,
				TrxId:         TL.TrxId,
				TrxStatus:     TL.TrxState,
				TrxStartTime:  TL.TrxStartTime,
				SqlBlocked:    TL.Query,
				LockStatus:    lockStatus,
				TrxExecTime:   int32(trxExecTime),
				TrxChangeTime: TL.TrxChangeTime,
				BlockTrxId:    TL.BlockTrxId,
			})
		}
		fp.StreamOf(tempTrxAndLockList).Foreach(func(d *shared.TrxAndLock) {
			d.NodeId = node.NodeId
		}).ToSlice(&tempTrxAndLockList)
		trxLocksList = append(trxLocksList, tempTrxAndLockList...)
	}
	return trxLocksList, nil
}

func (self *postgreImpl) filterTrxAndLocks(ctx context.Context, data []*shared.TrxAndLock, queryFilter *model.TrxQueryFilter, nodeIds []string) []*shared.TrxAndLock {
	tData := data
	if len(nodeIds) > 0 {
		fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
			var existed bool
			for _, nodeId := range nodeIds {
				if d.NodeId == nodeId {
					existed = true
					break
				}
			}
			return existed
		}).ToSlice(&tData)
	}
	if queryFilter != nil {
		if trxId := queryFilter.GetTrxId(); trxId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.TrxId), strings.ToLower(trxId))
			}).ToSlice(&tData)
		}
		if pId := queryFilter.GetProcessId(); pId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.ProcessId), strings.ToLower(pId))
			}).ToSlice(&tData)
		}
		if trxStatus := queryFilter.GetTrxStatus(); trxStatus != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.TrxStatus), strings.ToLower(trxStatus))
			}).ToSlice(&tData)
		}
		if blockTrxId := queryFilter.GetBlockTrxId(); blockTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.BlockTrxId), strings.ToLower(blockTrxId))
			}).ToSlice(&tData)
		}
		if queryFilter.IsSetLockStatus() {
			lockStatus := queryFilter.GetLockStatus().String()
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.LockStatus.String()), strings.ToLower(lockStatus))
			}).ToSlice(&tData)
		}
		if blockSql := queryFilter.GetSqlBlocked(); blockSql != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.SqlBlocked), strings.ToLower(blockSql))
			}).ToSlice(&tData)
		}
		if queryFilter.IsSetTrxExecTime() {
			trxExecTime := queryFilter.GetTrxExecTime()
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				execTime := d.TrxExecTime
				return execTime > trxExecTime
			}).ToSlice(&tData)
		}
	}
	return tData
}

func (self *postgreImpl) getAllDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) ([]*datasource.PgDialogInfo, error) {
	type NodeInfoObj struct {
		NodeId   string
		NodeType string
		Address  string
	}
	if req.Component == model.Component_DBEngine.String() {
		var (
			dialogInfos            []*datasource.PgDialogInfo
			nodeList, fullAllNodes []*NodeInfoObj
		)
		nodeAddressList, err := self.DescribeInstanceAddressList(ctx, &datasource.DescribeInstanceAddressReq{
			InstanceId: req.Source.InstanceId,
			Type:       shared.Postgres,
			LinkType:   shared.Volc,
		})
		if err != nil {
			return nil, err
		}
		// 获取需要连接的节点地址，默认连接所有节点(除备节点)
		for _, node := range nodeAddressList {
			fullAllNodes = append(fullAllNodes, &NodeInfoObj{
				NodeId:   node.NodeId,
				NodeType: node.NodeType,
				Address:  fmt.Sprintf("%s:%d", node.IP, node.Port),
			})
		}
		if req.QueryFilter != nil {
			if len(req.QueryFilter.GetNodeIds()) > 0 {
				for _, nodeObj := range fullAllNodes {
					for _, nodeId := range req.QueryFilter.GetNodeIds() {
						if nodeId == nodeObj.NodeId {
							nodeList = append(nodeList, nodeObj)
							break
						}
					}
				}
			} else {
				nodeList = fullAllNodes
			}
		} else {
			nodeList = fullAllNodes
		}
		querySql := "SELECT pid,usename,datname,client_addr,client_port, " +
			"ROUND(EXTRACT(EPOCH FROM (now() - query_start))::numeric, 2) AS query_time_seconds,state,query " +
			"FROM pg_stat_activity where client_port is NOT NULL "
		sql := datasource.WithHint(querySql, datasource.DbwDasHint)
		if len(req.InternalUsers) > 0 {
			sql += fmt.Sprintf(" and usename not in ('%s')", strings.Join(req.InternalUsers, "','"))
		}
		sql += " limit 10000"
		for _, nodeInfo := range nodeList {
			var (
				tempDialog []*datasource.PgDialogInfo
			)
			rrSource := req.Source
			log.Info(ctx, "current node is %s,address is %s", nodeInfo.NodeId, nodeInfo.Address)
			rrSource.Address = nodeInfo.Address
			conn, err := self.getConn(ctx, req.Source)
			if err != nil {
				log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
				return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
			}
			defer func(conn db.Conn) { _ = conn.Close() }(conn)
			if err = conn.Raw(sql).Scan(&tempDialog); err != nil {
				return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
			}
			fp.StreamOf(tempDialog).Foreach(func(d *datasource.PgDialogInfo) {
				// fill nodeId and nodeType
				d.NodeId = nodeInfo.NodeId
				d.NodeType = nodeInfo.NodeType
			}).ToSlice(&tempDialog)
			dialogInfos = append(dialogInfos, tempDialog...)
		}
		return dialogInfos, nil
	} else {
		// pg proxy会话暂不支持
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, "not support pg proxy dialogs")
	}

}
func (self *postgreImpl) getDialogStatistics(ctx context.Context, data []*datasource.PgDialogInfo, topN int32) *shared.DialogStatistics {
	userInfo := make(map[string]*datasource.UserAggregatedInfo)
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	dbInfo := make(map[string]*datasource.DBAggregatedInfo)
	var activeConn, totalConn int32 = 0, 0
	// fill NULL column, count activ:：e/total conns, aggregate user/ip/db info
	fp.StreamOf(data).Foreach(func(d *datasource.PgDialogInfo) {
		totalConn++
		if _, ok := userInfo[d.User]; !ok {
			userInfo[d.User] = &datasource.UserAggregatedInfo{User: d.User}
		}
		userInfo[d.User].TotalConn++
		if _, ok := ipInfo[d.Ip]; !ok {
			ipInfo[d.Ip] = &datasource.IPAggregatedInfo{IP: d.Ip}
		}
		ipInfo[d.Ip].TotalConn++
		if _, ok := dbInfo[d.DB]; !ok {
			dbInfo[d.DB] = &datasource.DBAggregatedInfo{DB: d.DB}
		}
		dbInfo[d.DB].TotalConn++
		if strings.ToLower(d.State) != "idle" {
			activeConn++
			userInfo[d.User].ActiveConn++
			ipInfo[d.Ip].ActiveConn++
			dbInfo[d.DB].ActiveConn++
		}
	}).Run()

	var userList []*shared.UserAggregatedInfo
	var ipList []*shared.IPAggregatedInfo
	var dbList []*shared.DBAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(userInfo).ZipMap(func(k string, v *datasource.UserAggregatedInfo) *shared.UserAggregatedInfo {
		return &shared.UserAggregatedInfo{
			User:              v.User,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&userList)
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)
	fp.KVStreamOf(dbInfo).ZipMap(func(k string, v *datasource.DBAggregatedInfo) *shared.DBAggregatedInfo {
		return &shared.DBAggregatedInfo{
			DB:                v.DB,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&dbList)

	sort.Slice(userList, func(i, j int) bool {
		if userList[i].TotalConnections > userList[j].TotalConnections {
			return true
		}
		if userList[i].TotalConnections == userList[j].TotalConnections &&
			userList[i].ActiveConnections == userList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(dbList, func(i, j int) bool {
		if dbList[i].TotalConnections > dbList[j].TotalConnections {
			return true
		}
		if dbList[i].TotalConnections == dbList[j].TotalConnections &&
			dbList[i].ActiveConnections == dbList[j].ActiveConnections {
			return true
		}
		return false
	})

	return &shared.DialogStatistics{
		DialogOver: &shared.DialogOverview{
			ActiveConnections: activeConn,
			TotalConnections:  totalConn,
		},
		UserAggregatedInfo: userList[:fp.MinInt(int(topN), len(userList))],
		IPAggregatedInfo:   ipList[:fp.MinInt(int(topN), len(ipList))],
		DBAggregatedInfo:   dbList[:fp.MinInt(int(topN), len(dbList))],
	}
}
func (self *postgreImpl) filterDialogDetails(ctx context.Context, data []*datasource.PgDialogInfo, queryFilter *shared.DialogQueryFilter, limit int64, offset int64) *shared.DialogDetails {
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.PgDialogInfo) bool {
				return strings.ToLower(d.State) == "idle"
			}).ToSlice(&tData)
		}
		if pID := queryFilter.ProcessID; pID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.PgDialogInfo) bool {
				return strings.Contains(d.ProcessID, pID)
			}).ToSlice(&tData)
		}
		if user := queryFilter.User; user != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.PgDialogInfo) bool {
				return strings.Contains(d.User, user)
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.PgDialogInfo) bool {
				clientAddr := fmt.Sprintf("%s:%s", d.Ip, d.Port)
				return strings.Contains(clientAddr, host)
			}).ToSlice(&tData)
		}
		if fDB := queryFilter.DB; fDB != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.PgDialogInfo) bool {
				return strings.Contains(d.DB, fDB)
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.PgDialogInfo) bool {
				return strings.EqualFold(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if info := queryFilter.Info; info != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.PgDialogInfo) bool {
				return strings.Contains(d.Info, info)
			}).ToSlice(&tData)
		}
		if state := queryFilter.State; state != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.PgDialogInfo) bool {
				return strings.Contains(d.State, state)
			}).ToSlice(&tData)
		}
		if timeLimit := queryFilter.LowerExecTimeLimit; timeLimit != "" {
			limitInt, er := strconv.ParseFloat(queryFilter.LowerExecTimeLimit, 64)
			if er == nil {
				fp.StreamOf(tData).Filter(func(d *datasource.PgDialogInfo) bool {
					queryTime, _ := strconv.ParseFloat(d.QueryTime, 64)
					return queryTime >= limitInt
				}).ToSlice(&tData)
			}
		}
	}
	var details []*shared.DialogDetail
	fp.StreamOf(tData[offset:fp.MinInt64(offset+limit, int64(len(tData)))]).Map(func(d *datasource.PgDialogInfo) *shared.DialogDetail {
		addr := fmt.Sprintf("%s:%s", d.Ip, d.Port)
		return &shared.DialogDetail{
			ProcessID:    d.ProcessID,
			User:         d.User,
			Host:         addr,
			DB:           d.DB,
			Info:         d.Info,
			Time:         d.QueryTime,
			State:        d.State,
			NodeId:       d.NodeId,
			NodeType:     d.NodeType,
			EndpointName: d.EndpointName,
			EndpointId:   d.EndpointId,
		}
	}).ToSlice(&details)

	return &shared.DialogDetails{
		Details: details,
		Total:   int32(len(details)),
	}
}
func (self *postgreImpl) KillProcess(ctx context.Context, req *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	// 需要考虑读写分离场景下 processID
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer func(conn db.Conn) { _ = conn.Close() }(conn)
	ret := &datasource.KillProcessResp{}
	sql := "SELECT pg_terminate_backend(?)"
	for _, processID := range req.ProcessIDs {
		if err = conn.Exec(sql, processID); err != nil {
			ret.FailInfoList = append(ret.FailInfoList, &shared.KillFailInfo{
				ProcessId:    processID,
				ErrorMessage: err.Error(),
			})
		}
	}
	return ret, nil
}

func (self *postgreImpl) getTableTotal(conn db.Conn) (*int32, error) {
	totalSql := "/*+ DBW SQL CONSOLE DEFAULT*/ SELECT count(1) " +
		"FROM pg_stat_user_tables as t " +
		"LEFT JOIN pg_class as c ON c.relname = t.relname " +
		"LEFT JOIN pg_namespace as n ON t.schemaname = n.nspname  " +
		"LEFT JOIN pg_index as i ON c.oid = i.indrelid " +
		"group by t.schemaname, t.relname, t.n_live_tup, t.idx_scan, t.seq_scan, t.last_vacuum, t.last_analyze, t.n_dead_tup"
	var listCount []*int
	if err := conn.Raw(totalSql).Scan(&listCount); err != nil {
		return nil, err
	}
	result := int32(len(listCount))
	return &result, nil
}

func (self *postgreImpl) isEmpty(conn db.Conn) (bool, error) {
	countSql := "/*+ DBW SQL CONSOLE DEFAULT*/ select count(*) from pg_stat_user_tables;"
	var num int64 = 0
	count := &num
	if err := conn.Raw(countSql).Scan(count); err != nil {
		return false, err
	}
	return *count == 0, nil
}

func (self *postgreImpl) ConvertTableSpaceToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp {
	var tableStats []*model.TableStat
	for _, value := range resp.TableStats {
		tableStat := &model.TableStat{
			Name:                 value.Name,
			DB:                   value.DB,
			SchemaName:           &value.SchemaName,
			TableSpace:           &value.TableSpace,
			TableSpaceRatio:      &value.TableSpaceRatio,
			IndexLength:          &value.IndexLength,
			RowLength:            &value.RowLength,
			IdxScan:              &value.IdxScan,
			SeqScan:              &value.SeqScan,
			LastVacuum:           &value.LastVacuum,
			LastAnalyze:          &value.LastAnalyze,
			DeadTupRatio:         &value.DeadTupRatio,
			ColumnStoreIndexSize: &value.ColumnStoreIndexSize,
		}
		tableStats = append(tableStats, tableStat)
	}
	return &model.DescribeTableSpaceResp{
		Total:      resp.Total,
		TableStats: tableStats,
	}
}

func (self *postgreImpl) DescribeTableColumn(ctx context.Context, req *datasource.DescribeTableInfoReq) (*shared.DescribeTableColumnResp, error) {
	req.Source.Db = req.Database
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer func(conn db.Conn) { _ = conn.Close() }(conn)

	fromCase := " FROM information_schema.columns AS c " +
		" WHERE c.table_name = ? AND c.table_schema = ? "

	limitCase := " LIMIT ? OFFSET ? "

	querySql := "SELECT c.column_name as column_name, c.data_type as type, c.column_default as default, c.is_nullable as is_null, " +
		" pg_catalog.col_description((quote_ident(c.table_schema) || '.' || quote_ident(c.table_name))::regclass::oid, c.ordinal_position) AS description,  c.is_identity  as is_identity " +
		fromCase + limitCase

	var columnInfoList []*PostgreColumnStat
	if err := conn.Raw(querySql, req.Table, req.Schema, req.Limit, req.Offset).Scan(&columnInfoList); err != nil {
		log.Warn(ctx, "query error:%s", err.Error())
		return nil, err
	}
	totalSql := "select count(1)  " + fromCase
	var num int32 = 0
	total := &num
	if err := conn.Raw(totalSql, req.Table, req.Schema).Scan(total); err != nil {
		log.Warn(ctx, "query total error:%s", err.Error())
		return nil, err
	}
	columnStat := CovertPostgreColumnStat(columnInfoList)
	return &shared.DescribeTableColumnResp{ColumnStats: columnStat, Total: *total}, nil
}

func (self *postgreImpl) ConvertTableColumnToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableColumnResp) *model.DescribeTableColumnResp {
	var columnStats []*model.ColumnStat
	for _, value := range resp.ColumnStats {
		columnStat := &model.ColumnStat{
			ColumnName: &value.ColumnName,
			Type:       &value.Type,
			Comment:    &value.Description,
			IsNull:     &value.IsNull,
			IsIdentity: &value.IsIdentity,
			Default:    &value.Default,
		}
		columnStats = append(columnStats, columnStat)
	}
	return &model.DescribeTableColumnResp{
		Total:       resp.Total,
		ColumnStats: columnStats,
	}
}

func (self *postgreImpl) DescribeTableIndex(ctx context.Context, req *datasource.DescribeTableInfoReq) (*shared.DescribeTableIndexResp, error) {
	req.Source.Db = req.Database
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer func(conn db.Conn) { _ = conn.Close() }(conn)

	fromCase := " FROM pg_class AS t " +
		" JOIN pg_namespace AS ns ON t.relnamespace = ns.oid " +
		" JOIN pg_index AS ix ON t.oid = ix.indrelid " +
		" JOIN pg_class AS i ON ix.indexrelid = i.oid " +
		" JOIN pg_attribute AS a ON a.attnum = ANY(ix.indkey) AND a.attrelid = t.oid " +
		" JOIN pg_am AS am ON i.relam = am.oid " +
		" LEFT JOIN pg_description AS d ON i.oid = d.objoid" +
		" WHERE t.relname = ? AND ns.nspname = ? GROUP BY i.relname, am.amname, d.description "

	limitCase := " LIMIT ? OFFSET ? "

	querySql := "SELECT i.relname AS index_name, am.amname AS index_type, string_agg(a.attname, ', ') AS columns, d.description AS description " +
		fromCase + limitCase

	var indexInfoList []*PostgreIndexStat
	if err := conn.Raw(querySql, req.Table, req.Schema, req.Limit, req.Offset).Scan(&indexInfoList); err != nil {
		return nil, err
	}

	totalSql := "SELECT i.relname AS index_name, am.amname AS index_type, string_agg(a.attname, ', ') AS columns, d.description AS description " + fromCase
	var sizeList []*PostgreIndexStat
	if err := conn.Raw(totalSql, req.Table, req.Schema).Scan(&sizeList); err != nil {
		return nil, err
	}
	idxStat := CovertPostgreIndexStat(indexInfoList)
	return &shared.DescribeTableIndexResp{IndexStats: idxStat, Total: int32(len(sizeList))}, nil
}

func (self *postgreImpl) ConvertTableIndexToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableIndexResp) *model.DescribeTableIndexResp {
	var indexStats []*model.IndexStat
	for _, value := range resp.IndexStats {
		columnStat := &model.IndexStat{
			IndexName: &value.IndexName,
			Type:      &value.Type,
			Comment:   &value.Description,
			Columns:   &value.Columns,
		}
		indexStats = append(indexStats, columnStat)
	}
	return &model.DescribeTableIndexResp{
		Total:      resp.Total,
		IndexStats: indexStats,
	}
}

func (self *postgreImpl) FormatDescribeStorageCapacityResp(sourceType shared.DataSourceType, diskSize *datasource.GetDiskSizeResp, storageSpace float64) *model.DescribeStorageCapacityResp {
	usedSize := diskSize.DiskLogSize + diskSize.DiskDataSize
	walSize := diagnosis.DecimalPointFloat(diskSize.WalSize)
	resp := &model.DescribeStorageCapacityResp{
		DiskDataSize: diagnosis.DecimalFloat(diskSize.DiskDataSize),
		DiskLogSize:  diagnosis.DecimalFloat(diskSize.DiskLogSize),
		UsedSize:     diagnosis.DecimalFloat(usedSize + *walSize),
		WalSize:      walSize,
		FreeSize:     diagnosis.DecimalPointFloat(storageSpace - usedSize),
		StorageSpace: diagnosis.DecimalPointFloat(storageSpace),
	}
	return resp
}

func (self *postgreImpl) GetDiskAvailableDays(ctx context.Context, req *datasource.GetDiskAvailableDaysReq) (*datasource.GetDiskAvailableDaysResp, error) {
	request := &bytebrain.DiskAvailableDaysReq{
		InstanceId: req.InstanceId,
		AccountId:  req.TenantId,
	}
	days, err := self.ByteBrainClient.DiskAvailableDays(ctx, request)
	if err != nil {
		return nil, err
	}
	log.Info(ctx, "days from byteBrain is %d", days)
	return &datasource.GetDiskAvailableDaysResp{
		AvailableDays: days,
	}, nil
}

// CreateAccount 用户名是固定的dbw_admin
func (self *postgreImpl) CreateAccount(ctx context.Context, req *datasource.CreateAccountReq) error {
	createReq := &pgModel.CreateReadOnlyAccountForDBWReq{
		InstanceId:      req.InstanceId,
		AccountPassword: req.AccountPassword,
	}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_CreateReadOnlyAccountForDBW.String(), createReq, nil, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		if strings.Contains(strings.ToLower(err.Error()), "already exists") {
			return nil
		}
		log.Warn(ctx, "CreateReadOnlyAccountForDBW failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return err
	}
	return nil
}

// DeleteAccount 不支持删除dbw_admin
func (self *postgreImpl) DeleteAccount(ctx context.Context, req *datasource.DeleteAccountReq) error {
	//pg 账号删除接口待优化,5.2.2版本优化
	createReq := &pgModel.DeleteReadOnlyAccountForDBWReq{
		InstanceId: req.InstanceId,
	}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DeleteReadOnlyAccountForDBW.String(), createReq, nil, client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "DeleteAccount failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return err
	}
	return nil
}
func (self *postgreImpl) getSumSpace(conn db.Conn) (*int64, error) {
	sumSql := "/*+ DBW SQL CONSOLE DEFAULT*/ select sum(pg_relation_size((quote_ident(schemaname) || '.' || quote_ident(relname))::regclass)) as total_size from pg_stat_user_tables;"
	var num int64 = 0
	sum := &num
	if err := conn.Raw(sumSql).Scan(sum); err != nil {
		return nil, err
	}
	return sum, nil
}

func (self *postgreImpl) getTablesInfo(conn db.Conn, req *datasource.DescribeTableSpaceReq, sumSpace *int64) ([]*shared.TableStat, error) {
	orderInfo := self.getTablesInfoOrderBy(req.OrderItem, req.OrderRule)
	querySql := fmt.Sprintf("SELECT t.schemaname as schema_name, t.relname as table_name, pg_relation_size((quote_ident(t.schemaname) || '.' || quote_ident(t.relname))::regclass) AS table_size, "+
		"ROUND(pg_relation_size((quote_ident(t.schemaname) || '.' || quote_ident(t.relname))::regclass)::numeric / %s::numeric, 4) AS table_ratio, "+
		"pg_indexes_size((quote_ident(t.schemaname) || '.' || quote_ident(t.relname))::regclass) AS index_size, "+
		"t.n_live_tup as row_length, t.idx_scan as idx_scan, t.seq_scan as seq_scan, "+
		"t.last_vacuum as last_vacuum, t.last_analyze as last_analyze, "+
		"round(t.n_dead_tup * 100 / (t.n_live_tup + t.n_dead_tup + 1),2) AS dead_tup_ratio, "+
		"sum(pg_total_relation_size(i.indexrelid)) AS column_store_index_size "+
		"FROM pg_class as c JOIN pg_namespace as n ON c.relnamespace = n.oid "+
		"JOIN pg_stat_user_tables as t ON (t.schemaname = n.nspname AND c.relname = t.relname) "+
		"left JOIN pg_index as i ON c.oid = i.indrelid "+
		" group by t.schemaname, t.relname, t.n_live_tup, t.idx_scan, t.seq_scan, t.last_vacuum, t.last_analyze, t.n_dead_tup"+
		" order by "+orderInfo+
		" LIMIT ? OFFSET ? ", strconv.FormatInt(*sumSpace, 10)) // ignore_security_alert
	var args []interface{}
	args = append(args, req.Limit, req.Offset)
	var tableInfoList []*PostgreTableStat
	if err := conn.Raw(datasource.WithHint(querySql, datasource.DbwDasHint), args...).Scan(&tableInfoList); err != nil {
		return nil, err
	}
	return CovertPostgreTableStat(tableInfoList, req.Database), nil
}

func (self *postgreImpl) getTablesInfoOrderBy(orderItem string, orderRule string) string {
	// 用lowercase忽略大小写
	item := "table_size"
	switch strings.ToLower(orderItem) {
	case SchemaName:
		item = "schema_name"
	case TableName:
		item = "table_name"
	case TableSize:
		item = "table_size"
	case TableRatio:
		item = "table_ratio"
	case IndexSize:
		item = "index_size"
	case RowLength:
		item = "row_length"
	case IdxScan:
		item = "idx_scan"
	case SeqScan:
		item = "seq_scan"
	case LastVacuum:
		item = "last_vacuum"
	case LastAnalyze:
		item = "last_analyze"
	case DeadTupRatio:
		item = "dead_tup_ratio"
	case ColumnStoreIndexSize:
		item = "column_store_index_size"
	}

	rule := "ASC"
	switch strings.ToLower(orderRule) {
	case ASC:
		rule = "ASC"
	case DESC:
		rule = "DESC"
	}
	return fmt.Sprintf(" %s %s ", item, rule)
}

func (self *postgreImpl) GetDBInnerAddress(ctx context.Context, req *datasource.GetDBInnerAddressReq) (*datasource.GetDBInnerAddressResp, error) {
	if req.Source.LinkType != shared.Volc {
		return nil, errors.New("ticket: datasource LinkType is not shared.Volc")
	}
	rreq := &pgModel.DescribeDBInstanceDetailReq{
		InstanceId: req.Source.InstanceId,
	}

	rresp := &pgModel.DescribeDBInstanceDetailResp{}
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), rreq, rresp,
		client.WithVersion(consts.Postgres_Version_V2)); err != nil {
		log.Warn(ctx, "postgreImpl: describe postgres instance detail error:%v", err)
		return nil, err
	}
	if rresp.BasicInfo.VpcID == "" {
		// vpcID应该不会为空，如果为空了，就直接报错返回给用户
		log.Warn(ctx, "postgreImpl: get PG [%s] describeInstance vpcId is nil", rreq.InstanceId)
		return nil, fmt.Errorf("VpcId is null, please update vpc for your instance")
	}
	if rresp.BasicInfo.SubnetId == "" {
		// 子网未升级用户，提示无法登录
		log.Warn(ctx, "postgreImpl: get PG [%s] describeInstance subnetId is nil", rreq.InstanceId)
		return nil, fmt.Errorf("SubnetId is null, please create subnet for your instance")
	}
	for _, connectionInfo := range rresp.Endpoints {
		// 这里需要判断一下是否是默认终端,不是默认终端的，不能要
		if connectionInfo.EndpointType != pgModel.EndpointType_Cluster {
			continue
		}
		for _, connection := range connectionInfo.Address {
			if connection.NetworkType == pgModel.NetworkType_Private {
				req.Source.Address = fmt.Sprintf("%s:%s", connection.IPAddress, connection.Port)
			}
		}
	}

	if req.Source.Address == "" {
		log.Warn(ctx, "fill rds instance %s address error", rreq.InstanceId)
		return nil, errors.New("get address error")
	}
	return &datasource.GetDBInnerAddressResp{Source: req.Source}, nil
}

func (self *postgreImpl) EnsureAccount(ctx context.Context, req *datasource.EnsureAccountReq) error {
	// 默认连接主节点
	pods, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
		Type:       self.Type(),
	})
	if err != nil {
		return err
	}
	var address string
	if req.Source.NodeId == "" {
		// nodeId为空,则默认连接主节点
		for _, pod := range pods.Data {
			if pod.Role == model.NodeType_Primary.String() {
				for _, container := range pod.Containers {
					if container.Name == "database" {
						address = fmt.Sprintf("%s:%s", pod.PodIP, container.Port)
						req.Source.NodeId = pod.NodeId
						break
					}
				}
			}
		}
	} else {
		for _, pod := range pods.Data {
			if pod.NodeId == req.Source.NodeId {
				for _, container := range pod.Containers {
					if container.Name == "database" {
						address = fmt.Sprintf("%s:%s", pod.PodIP, container.Port)
						break
					}
				}
			}
		}
	}

	req.Source.Address = address
	// 3、直接连接,如果不能连接,则重新修改密码
	_, err = self.getConn(ctx, req.Source)
	if err != nil {
		if strings.Contains(strings.ToLower(err.Error()), "authentication failed") {
			log.Warn(ctx, "pg get conn error %s,maybe there is no account", err.Error())
			// 判断下账号是否存在
			// 创建一个新的账号,然后去连接
			err = self.resetAccount(ctx, req.Source.InstanceId, shared.Postgres)
			if err != nil {
				log.Warn(ctx, "pg resetAccount err: %v", err)
				return err
			}
		}

	}
	return nil
}
func (self *postgreImpl) resetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	//删除账号
	c3Cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	// 我们直接调用删除接口，确保删除调这个账号后重建
	if err := self.DeleteAccount(ctx, &datasource.DeleteAccountReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: c3Cfg.DBWAccountName,
	}); err != nil {
		log.Warn(ctx, "pg EnsureAccount failed to delete account, err=%v", err)
		if !strings.Contains(strings.ToLower(err.Error()), "account name does not exist") {
			return err
		}
	}
	//创建账号
	if err := self.CreateAccount(ctx, &datasource.CreateAccountReq{
		DSType:          dsType,
		InstanceId:      instanceId,
		AccountName:     c3Cfg.DBWAccountName,
		AccountPassword: self.getAccountPassword(c3Cfg.DbwAccountPasswordGenKey, instanceId),
	}); err != nil {
		log.Warn(ctx, "pg failed to create account, err=%v", err)
		return err
	}
	return nil
}

func (self *postgreImpl) getAccountPassword(key string, instanceId string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(instanceId))
	expectedMac := hex.EncodeToString(mac.Sum(nil))
	return "Dbw_" + expectedMac[:26]
}

func (self *postgreImpl) DescribeInstanceAddressList(ctx context.Context, req *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	var (
		pods        []*shared.KubePod
		addressList []*datasource.DescribeInstanceAddressResp
	)
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		Type:       conv.ToSharedType(model.DSType_Postgres),
		LinkType:   shared.Volc,
		InstanceId: req.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "Get instance %s pods failed: %v", req.InstanceId, err)
		return nil, err
	}
	log.Info(ctx, "DescribeInstanceAddressList call ListInstancePods result is %s", utils.Show(listInstancePodsResp))
	// 跳过备节点采集
	for _, pod := range listInstancePodsResp.Data {
		if pod.Role != model.NodeType_Secondary.String() {
			pods = append(pods, pod)
		}
	}
	if len(pods) < 1 {
		log.Warn(ctx, "No pg pod found")
		return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No pg pod found")
	}
	for _, pod := range pods {
		for _, container := range pod.Containers {
			if container.Name == "database" {
				ret := &datasource.DescribeInstanceAddressResp{}
				ret.Port = utils.MustStrToInt32(container.Port)
				ret.IP = pod.PodIP
				ret.NodeId = pod.NodeId
				ret.NodeType = pod.Role
				addressList = append(addressList, ret)
			}
		}
	}
	return addressList, nil
}

func (self *postgreImpl) ListInstancePods(ctx context.Context, req *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	rreq := &pgModel.DescribeDBInstanceDeploymentReq{
		InstanceId: req.InstanceId,
	}
	rresp := &pgModel.DescribeDBInstanceDeploymentResp{}
	log.Info(ctx, "pg instanceId:%s DescribeDBInstanceDeploymentReq:%s", req.InstanceId, rreq.String())
	if err := self.pgmgr.Get().Call(ctx, pgModel.Action_DescribeDBInstanceDeployment.String(), rreq, rresp, client.WithVersion(consts.Postgres_Version_V2), client.WithTenantID("1")); err != nil {
		log.Warn(ctx, "DescribeDBInstanceDeployment failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	ret := &datasource.ListInstancePodsResp{}
	if err := fp.StreamOf(rresp.DeploymentInfo).Map(func(db *pgModel.DeploymentObject) *shared.KubePod {
		var (
			convertedRole string
			containerList []*shared.KubeContainer
			nodeId        string
		)
		switch db.NodeType {
		case pgModel.NodeType_Primary:
			convertedRole = model.NodeType_Primary.String()
		case pgModel.NodeType_Secondary:
			convertedRole = model.NodeType_Secondary.String()
		case pgModel.NodeType_ReadOnly:
			convertedRole = model.NodeType_ReadOnly.String()
		default:
			convertedRole = ""
		}
		for _, item := range db.Containers {
			if item.Name == "database" {
				containerItem := &shared.KubeContainer{
					Name: item.Name,
					Port: fmt.Sprintf("%d", item.Ports["postgres"]),
				}
				containerList = append(containerList, containerItem)
			}
		}
		if _, exists := db.Labels["pg-node"]; exists {
			nodeId = db.Labels["pg-node"]
		} else {
			nodeId = db.Labels["deployment-name"]
		}
		return &shared.KubePod{
			Name:        db.PodName,
			Zone:        db.Zone,
			KubeCluster: db.KubeCluster,
			Region:      db.Region,
			NodeIP:      db.NodeIP,
			PodIP:       db.PodIP,
			Role:        convertedRole,
			NodeId:      nodeId,
			Containers:  containerList,
		}
	}).ToSlice(&ret.Data); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	ret.Total = int32(len(ret.Data))
	log.Info(ctx, "pg listInstancePods is %s", utils.Show(ret))
	return ret, nil
}

func (self *postgreImpl) GetAdvice(ctx context.Context, req *datasource.GetAdviceReq) (*datasource.GetAdviceResp, error) {
	req.Source.Db = req.DB
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	ret := &datasource.GetAdviceResp{
		Explains: []*shared.AdviceInfo_ExplainInfo{},
	}

	// Get EXPLAIN plan
	queryExplain := fmt.Sprintf("%s explain %s", DbwConsoleDefaultHint, req.Sql)

	var planRows []string
	rows, err := conn.Rows(queryExplain) // Fetch the rows for the explain output

	if err != nil {
		log.Warn(ctx, "get explain from DB %s fail, because %v", req.DB, err)
		return nil, err
	}
	defer rows.Close()

	// Collect the explain output
	for rows.Next() {
		var row string
		if err := rows.Scan(&row); err != nil {
			log.Warn(ctx, "failed to scan explain row: %v", err)
			return nil, err
		}
		planRows = append(planRows, row)
	}

	// Map each planRow into AdviceInfo_ExplainInfo struct, assigning the Extra field
	for _, planRow := range planRows {
		explainInfo := &shared.AdviceInfo_ExplainInfo{
			Extra: planRow, // Assigning the row to the Extra field for now
		}
		// Add the explainInfo to the Explains slice
		ret.Explains = append(ret.Explains, explainInfo)
	}

	if err != nil {
		log.Warn(ctx, "get explain from DB %s fail, because %v", req.DB, err)
		return nil, err
	}
	return ret, nil
}

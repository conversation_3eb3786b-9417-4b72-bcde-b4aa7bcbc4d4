package pg

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	pgModel "code.byted.org/infcs/dbw-mgr/gen/pg-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/db"
	. "code.byted.org/luoshiqi/mockito"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type PostgreImplTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *PostgreImplTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *PostgreImplTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestPostgreImplTestSuite(t *testing.T) {
	suite.Run(t, new(PostgreImplTestSuite))
}

func (suite *PostgreImplTestSuite) TestListInstanceNodes() {
	PatchConvey("return correct", suite.T(), func() {
		ctx := context.Background()
		req := &datasource.ListInstanceNodesReq{
			InstanceId: "xxx",
			DSType:     shared.VeDBMySQL,
		}
		rreq := &pgModel.DescribeDBInstanceDetailReq{
			InstanceId: "xxx",
		}
		rresp := &pgModel.DescribeDBInstanceDetailResp{
			Nodes: []*pgModel.NodeObject{
				{
					NodeId:   "xxx-1",
					ZoneId:   "sy-1",
					NodeType: pgModel.NodeType_Primary,
					VCPU:     utils.Int32Ref(1),
					Memory:   utils.Int32Ref(2),
					NodeSpec: "",
				},
			},
		}
		resp := &datasource.ListInstanceNodesResp{
			Nodes: []*model.NodeInfoObject{
				{
					NodeId:   "xxx-1",
					NodeType: model.NodeType_Primary,
					CpuNum:   1,
					MemInGiB: 2,
					ZoneId:   "sy-1",
				},
			},
		}
		mgr := mocks.NewMockMgrProvider(suite.ctrl)
		kc := mocks.NewMockMgrClient(suite.ctrl)
		mgr.EXPECT().Get().Return(kc).AnyTimes()
		kc.EXPECT().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), rreq, gomock.Any(), gomock.Any()).SetArg(3, *rresp).Return(nil).AnyTimes()
		postgre := &postgreImpl{
			pgmgr: mgr,
		}
		r, _ := postgre.ListInstanceNodes(ctx, req)
		convey.So(r, convey.ShouldResemble, resp)
	})
}

//	func (suite *PostgreImplTestSuite) TestListInstance() {
//		PatchConvey("return correct", suite.T(), func() {
//			ctx := context.Background()
//			req := &datasource.ListInstanceReq{
//				Type:            4,
//				LinkType:        shared.Volc,
//				PageNumber:      1,
//				PageSize:        20,
//				InstanceName:    "xxx",
//				InstanceId:      "111",
//				RegionId:        "cn-nanjing-bbit",
//				InstanceStatus:  "Running",
//				DBEngineVersion: pgModel.DBEngineVersion_PostgreSQL_11.String(),
//				ZoneId:          "111",
//				TenantId:        "********",
//			}
//			rreq := &pgModel.DescribeDBInstancesReq{
//				InstanceId: utils.StringRef("xxx"),
//				PageSize:   utils.Int32Ref(10),
//				PageNumber: utils.Int32Ref(1),
//			}
//			rresp := &pgModel.DescribeDBInstancesResp{
//				Total: 1,
//				Instances: []*pgModel.InstanceObject{
//					{
//						InstanceId:      "111",
//						InstanceName:    "xxx",
//						InstanceStatus:  pgModel.InstanceStatus_Running,
//						RegionId:        "cn-nanjing-bbit",
//						ZoneId:          "111",
//						ZoneIds:         []string{"111"},
//						DBEngineVersion: pgModel.DBEngineVersion_PostgreSQL_11,
//						ProjectName:     utils.StringRef("default"),
//						AccountId:       utils.StringRef("********"),
//						CreateTime:      "2023-08-01 12:00:00",
//						AddressObject: []*pgModel.AddressObject{
//							{
//								NetworkType: pgModel.NetworkType_Private,
//								Domain:      "mysql-ha-proxy-agent.default.svc.rds-panel.org",
//								Port:        "3306",
//							},
//						},
//						NodeSpec: "rds.postgres.1c2g",
//						Tags: []*pgModel.TagObject{
//							{
//								Key:   "dbw",
//								Value: utils.StringRef("111"),
//							},
//						},
//					},
//				},
//			}
//			resp := &datasource.ListInstanceResp{
//				Total: 1,
//				InstanceList: []*model.InstanceInfo{
//					{
//						InstanceId:     utils.StringRef("111"),
//						InstanceName:   utils.StringRef("xxx"),
//						InstanceStatus: "Running",
//						Zone:           "111",
//						InstanceSpec: &model.InstanceSpec{
//							CpuNum:     1,
//							MemInGiB:   2,
//							NodeNumber: 0,
//						},
//						DBEngineVersion: pgModel.DBEngineVersion_PostgreSQL_11.String(),
//						InternalAddress: "mysql-ha-proxy-agent.default.svc.rds-panel.org:3306",
//						AccessSource:    "云数据库 PostgreSQL 版",
//						HasReadOnlyNode: false,
//						ProjectName:     utils.StringRef("default"),
//						AccountId:       utils.StringRef("********"),
//						CreateTime:      utils.StringRef("2023-08-01 12:00:00"),
//						InstanceType:    model.InstanceType_Postgres,
//						Tags: []*model.TagObject{
//							{
//								Key:   "dbw",
//								Value: "111",
//							},
//						},
//					},
//				},
//			}
//			mgr := mocks.NewMockMgrProvider(suite.ctrl)
//			kc := mocks.NewMockMgrClient(suite.ctrl)
//			mgr.EXPECT().Get().Return(kc).AnyTimes()
//			kc.EXPECT().Call(ctx, pgModel.Action_DescribeDBInstances.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *rresp).SetArg(2, *rreq).Return(nil).AnyTimes()
//			postgre := &postgreImpl{
//				pgmgr: mgr,
//			}
//			r, _ := postgre.ListInstance(ctx, req)
//			convey.So(r, convey.ShouldResemble, resp)
//		})
//	}
func (suite *PostgreImplTestSuite) TestIsMyOwnInstance() {
	PatchConvey("return true", suite.T(), func() {
		instanceId := "xxx"
		dsType := shared.Postgres
		ctx := context.Background()
		describeDBInstanceDetailReq := &pgModel.DescribeDBInstanceDetailReq{
			InstanceId: instanceId,
		}
		describeDBInstanceDetailResp := &pgModel.DescribeDBInstanceDetailResp{
			Endpoints: []*pgModel.EndpointObject{
				{
					EndpointType: pgModel.EndpointType_Cluster,
					Address: []*pgModel.AddressObject{
						{
							NetworkType: pgModel.NetworkType_Private,
							Domain:      "mysql-ha-proxy-agent.default.svc.rds-panel.org",
							Port:        "3306",
						},
					},
				},
			},
			BasicInfo: &pgModel.BasicInfoObject{
				VCPU:   1,
				Memory: 2,
			},
		}
		mgr := mocks.NewMockMgrProvider(suite.ctrl)
		kc := mocks.NewMockMgrClient(suite.ctrl)
		mgr.EXPECT().Get().Return(kc).AnyTimes()
		kc.EXPECT().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *describeDBInstanceDetailResp).SetArg(2, *describeDBInstanceDetailReq).Return(nil).AnyTimes()
		postgre := &postgreImpl{
			pgmgr: mgr,
		}
		resp := true
		res := postgre.IsMyOwnInstance(ctx, instanceId, dsType)
		convey.So(res, convey.ShouldResemble, resp)
	})
	PatchConvey("return false", suite.T(), func() {
		instanceId := "xxx"
		dsType := shared.Postgres
		ctx := context.Background()
		describeDBInstanceDetailReq := &pgModel.DescribeDBInstanceDetailReq{
			InstanceId: instanceId,
		}
		describeDBInstanceDetailResp := &pgModel.DescribeDBInstanceDetailResp{
			Endpoints: []*pgModel.EndpointObject{
				{
					EndpointType: pgModel.EndpointType_Cluster,
					Address: []*pgModel.AddressObject{
						{
							NetworkType: pgModel.NetworkType_Private,
							Domain:      "mysql-ha-proxy-agent.default.svc.rds-panel.org",
							Port:        "3306",
						},
					},
				},
			},
			BasicInfo: &pgModel.BasicInfoObject{
				VCPU:   1,
				Memory: 2,
			},
		}
		mgr := mocks.NewMockMgrProvider(suite.ctrl)
		kc := mocks.NewMockMgrClient(suite.ctrl)
		mgr.EXPECT().Get().Return(kc).AnyTimes()
		kc.EXPECT().Call(ctx, pgModel.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *describeDBInstanceDetailResp).SetArg(2, *describeDBInstanceDetailReq).Return(errors.New("xxx")).AnyTimes()
		postgre := &postgreImpl{
			pgmgr: mgr,
		}
		resp := false
		res := postgre.IsMyOwnInstance(ctx, instanceId, dsType)
		convey.So(res, convey.ShouldResemble, resp)
	})
}

//func (suite *PostgreImplTestSuite) TestWhiteListV1() {
//	PatchConvey("return false", suite.T(), func() {
//		provider := &mocks.MockProvider{}
//		cnf := &mocks.MockConfigProvider{}
//		ctx := context.Background()
//		impl := postgreImpl{
//			cnf:   cnf,
//			pgmgr: provider,
//		}
//
//		mock1 := mockey.Mock((*postgreImpl).checkNewWhiteList).Return(nil, nil).Build()
//		defer mock1.UnPatch()
//
//		mock2 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
//		defer mock2.UnPatch()
//
//		mgrClient := &mocks.MockMgrClient{}
//		mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
//		defer mock3.UnPatch()
//		mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
//		defer mock4.UnPatch()
//
//		_, err := impl.AddWhiteList(ctx, "string", &shared.DataSource{
//			Address:  "************",
//			Db:       "jq",
//			User:     "dbw",
//			Password: "123",
//		})
//		So(err, ShouldBeNil)
//	})
//
//}
//
//func (suite *PostgreImplTestSuite) TestWhiteListV2() {
//	PatchConvey("return false", suite.T(), func() {
//		provider := &mocks.MockProvider{}
//		ctx := context.Background()
//		impl := postgreImpl{
//			cnf:   &mockConfigProviderImpl{},
//			pgmgr: provider,
//		}
//
//		mock1 := mockey.Mock((*postgreImpl).checkNewWhiteList).Return(nil, nil).Build()
//		defer mock1.UnPatch()
//
//		mock2 := mockey.Mock((*postgreImpl).getRegionId).Return("test1", nil).Build()
//		defer mock2.UnPatch()
//
//		mock3 := mockey.Mock((*postgreImpl).getWhiteListID).Return(nil, nil).Build()
//		defer mock3.UnPatch()
//
//		mock4 := mockey.Mock((*postgreImpl).CreateAllowListNew).Return("wild1", nil).Build()
//		defer mock4.UnPatch()
//
//		mock5 := mockey.Mock((*postgreImpl).AssociateAllowList).Return(nil).Build()
//		defer mock5.UnPatch()
//
//		mock6 := mockey.Mock((*postgreImpl).isNewWhiteList).Return(true).Build()
//		defer mock6.UnPatch()
//
//		mgrClient := &mocks.MockMgrClient{}
//		mock7 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
//		defer mock7.UnPatch()
//		mock8 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
//		defer mock8.UnPatch()
//
//		_, err := impl.AddWhiteList(ctx, "string", &shared.DataSource{
//			InstanceId: "mysql-522e84945a31",
//			Address:    "************",
//			Db:         "jq",
//			User:       "dbw",
//			Password:   "123",
//		})
//		So(err, ShouldBeNil)
//	})
//}

func mockConfigRes() *config.Config {
	return &config.Config{
		MgrPodCIDR: "*********/8,********/8,**********/10",
	}
}

func TestEnsureAllowList(t *testing.T) {
	mgrClient := &mocks.MockMgrClient{}
	mock1 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	defer mock2.UnPatch()

	provider := &mocks.MockProvider{}
	cnf := &mocks.MockConfigProvider{}
	ctx := context.Background()
	impl := postgreImpl{
		cnf:   cnf,
		pgmgr: provider,
	}
	err := impl.ensureAllowList(ctx, "instance1", "wlid1", `**********/10`)
	if err != nil {
		t.Fatal("TestEnsureAllowList1 failed, err is:", err)
	}
}

func TestContainsIPList(t *testing.T) {
	provider := &mocks.MockProvider{}
	cnf := &mocks.MockConfigProvider{}
	impl := postgreImpl{
		cnf:   cnf,
		pgmgr: provider,
	}
	result := impl.containsIPList("*********/8,********/8,**********/10", `**********/10`)
	if result == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}

	result1 := impl.containsIPList("*********/8,11", `**********/10`)
	if result1 == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}

	result2 := impl.containsIPList("**********/10", `**********/10`)
	if result2 == false {
		t.Fatal("TestContainsIPList failed, result is: false")
	}

	result3 := impl.containsIPList("", `**********/10`)
	if result3 == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}

	result4 := impl.containsIPList("**********/10", ``)
	if result4 == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}

	result5 := impl.containsIPList("**********/10   ", `**********/10`)
	if result5 == true {
		t.Fatal("TestContainsIPList failed, result is: true")
	}
}

func TestIsNewWhiteList(t *testing.T) {
	PatchConvey("isNewWhiteList", t, func() {
		PatchConvey("is v2 or initial", func() {
			impl := postgreImpl{}
			for _, ver := range []pgModel.AllowListVersion{pgModel.AllowListVersion_v2, pgModel.AllowListVersion_initial} {
				flag := impl.isNewWhiteList(context.Background(), &ver)
				So(flag, ShouldBeTrue)
			}
		})
		PatchConvey("not v2 or initial", func() {
			impl := postgreImpl{}
			for _, ver := range []pgModel.AllowListVersion{pgModel.AllowListVersion_v1} {
				flag := impl.isNewWhiteList(context.Background(), &ver)
				So(flag, ShouldBeFalse)
			}
			flag := impl.isNewWhiteList(context.Background(), nil)
			So(flag, ShouldBeFalse)
		})
	})
}

func TestFillDataSource(t *testing.T) {
	pg := mockPostgreImpl()
	ds := &shared.DataSource{LinkType: shared.Ecs}
	err := pg.FillDataSource(context.Background(), ds)
	assert.Nil(t, err)

	ds.LinkType = shared.Volc
	mockInstanceResp := &pgModel.DescribeDBInstanceDetailResp{
		BasicInfo: &pgModel.BasicInfoObject{},
		Endpoints: []*pgModel.EndpointObject{{}},
	}

	mock1 := mockey.Mock((*postgreImpl).describeInstance).Return(mockInstanceResp, fmt.Errorf("test")).Build()
	err = pg.FillDataSource(context.Background(), ds)
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*postgreImpl).describeInstance).Return(mockInstanceResp, nil).Build()
	defer mock2.UnPatch()
	err = pg.FillDataSource(context.Background(), ds)
	assert.NotNil(t, err)

	mockInstanceResp.BasicInfo.VpcID = "vpc"
	mockInstanceResp.BasicInfo.SubnetId = "subnet"
	err = pg.FillDataSource(context.Background(), ds)
	assert.NotNil(t, err)
	ds.Address = "127.0.0.1:3306"
	err = pg.FillDataSource(context.Background(), ds)
	assert.Nil(t, err)
}

func TestKillQuery(t *testing.T) {
	pg := mockPostgreImpl()

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	err := pg.KillQuery(context.Background(), &shared.DataSource{}, &shared.ConnectionInfo{})
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockConn).Exec).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	err = pg.KillQuery(context.Background(), &shared.DataSource{}, &shared.ConnectionInfo{})
	assert.Nil(t, err)
}

func TestDescribeDBInstanceSSL(t *testing.T) {
	pg := mockPostgreImpl()
	resp, err := pg.DescribeDBInstanceSSL(context.Background(), &datasource.DescribeDBInstanceSSLReq{InstanceId: "instanceId"})
	assert.Nil(t, err)
	assert.Equal(t, resp.InstanceId, "instanceId")
	assert.Equal(t, resp.SSLEnable, false)
}

func TestListDatabases(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListDatabases(context.Background(), &datasource.ListDatabasesReq{Source: &shared.DataSource{Address: "test"}})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListDatabases(context.Background(), &datasource.ListDatabasesReq{})
	assert.Nil(t, err)
	_, err = pg.ListDatabases(context.Background(), &datasource.ListDatabasesReq{Keyword: "query"})
	assert.Nil(t, err)
}

func TestListSchema(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListSchema(context.Background(), &datasource.ListSchemaReq{Source: &shared.DataSource{Address: "test"}})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListSchema(context.Background(), &datasource.ListSchemaReq{
		Source:  &shared.DataSource{},
		DB:      "",
		Keyword: "",
	})
	assert.Nil(t, err)
	_, err = pg.ListSchema(context.Background(), &datasource.ListSchemaReq{Source: &shared.DataSource{}, Keyword: "query"})
	assert.Nil(t, err)
}

func TestListTables(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListTables(context.Background(), &datasource.ListTablesReq{Source: &shared.DataSource{Address: "test"}, Schema: "sc1"})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListTables(context.Background(), &datasource.ListTablesReq{
		Source:  &shared.DataSource{},
		DB:      "",
		Keyword: "",
		Schema:  "sc1",
		Offset:  0,
		Limit:   0,
		Filters: nil,
	})
	defer mock4.UnPatch()
	_, err = pg.ListTables(context.Background(), &datasource.ListTablesReq{
		Source:  &shared.DataSource{},
		DB:      "",
		Keyword: "query",
		Schema:  "sc1",
		Offset:  0,
		Limit:   0,
		Filters: nil,
	})
	assert.Nil(t, err)
}

func TestListSchemaTables(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListSchemaTables(context.Background(), &datasource.ListSchemaTablesReq{Source: &shared.DataSource{Address: "test"}, Schema: "sc1"})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListSchemaTables(context.Background(), &datasource.ListSchemaTablesReq{Source: &shared.DataSource{}, Schema: "sc1"})
	defer mock4.UnPatch()
	_, err = pg.ListSchemaTables(context.Background(), &datasource.ListSchemaTablesReq{Source: &shared.DataSource{}, Schema: "sc1", Keyword: "query"})
	assert.Nil(t, err)
}

func TestListViews(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListViews(context.Background(), &datasource.ListViewsReq{Source: &shared.DataSource{Address: "test"}, Schema: "sc1"})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListViews(context.Background(), &datasource.ListViewsReq{
		Source:  &shared.DataSource{},
		DB:      "",
		Keyword: "",
		Schema:  "sc1",
		Offset:  0,
		Limit:   0,
	})
	defer mock4.UnPatch()
	_, err = pg.ListViews(context.Background(), &datasource.ListViewsReq{Source: &shared.DataSource{}, Schema: "sc1", Keyword: "query"})
	assert.Nil(t, err)
}

func TestListFunctions(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListFunctions(context.Background(), &datasource.ListFunctionsReq{Source: &shared.DataSource{Address: "test"}, Schema: "sc1"})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListFunctions(context.Background(), &datasource.ListFunctionsReq{Source: &shared.DataSource{}, Schema: "sc1"})
	assert.Nil(t, err)
	_, err = pg.ListFunctions(context.Background(), &datasource.ListFunctionsReq{Source: &shared.DataSource{}, Schema: "sc1", Keyword: "query"})
	assert.Nil(t, err)
}

func TestListTriggers(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListTriggers(context.Background(), &datasource.ListTriggersReq{Source: &shared.DataSource{Address: "test"}, Schema: "sc1"})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListTriggers(context.Background(), &datasource.ListTriggersReq{Source: &shared.DataSource{}, Schema: "sc1"})
	assert.Nil(t, err)
	_, err = pg.ListTriggers(context.Background(), &datasource.ListTriggersReq{Source: &shared.DataSource{}, Schema: "sc1", Keyword: "query"})
	assert.Nil(t, err)
}

func TestListSequence(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListSequence(context.Background(), &datasource.ListSequenceReq{Source: &shared.DataSource{Address: "test"}, Schema: "sc1"})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListSequence(context.Background(), &datasource.ListSequenceReq{Source: &shared.DataSource{}, Schema: "sc1"})
	assert.Nil(t, err)
	_, err = pg.ListSequence(context.Background(), &datasource.ListSequenceReq{Source: &shared.DataSource{}, Schema: "sc1", Keyword: "query"})
	assert.Nil(t, err)
}

func TestListPgCollations(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListPgCollations(context.Background(), &datasource.ListPgCollationsReq{Source: &shared.DataSource{Address: "test"}})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListPgCollations(context.Background(), &datasource.ListPgCollationsReq{Source: &shared.DataSource{}})
	assert.Nil(t, err)
	_, err = pg.ListPgCollations(context.Background(), &datasource.ListPgCollationsReq{Source: &shared.DataSource{}, Keyword: "query"})
	assert.Nil(t, err)
}

func TestListPgUsers(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListPgUsers(context.Background(), &datasource.ListPgUsersReq{Source: &shared.DataSource{Address: "test"}})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListPgUsers(context.Background(), &datasource.ListPgUsersReq{Source: &shared.DataSource{}})
	assert.Nil(t, err)
	_, err = pg.ListPgUsers(context.Background(), &datasource.ListPgUsersReq{Source: &shared.DataSource{}, Keyword: "query"})
	assert.Nil(t, err)
}

func TestListTableSpaces(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.ListTableSpaces(context.Background(), &datasource.ListTableSpacesReq{Source: &shared.DataSource{Address: "test"}})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.ListTableSpaces(context.Background(), &datasource.ListTableSpacesReq{Source: &shared.DataSource{}})
	assert.Nil(t, err)
	_, err = pg.ListTableSpaces(context.Background(), &datasource.ListTableSpacesReq{Source: &shared.DataSource{}, Keyword: "query"})
	assert.Nil(t, err)
}

func TestDescribePgTable(t *testing.T) {
	pg := mockPostgreImpl()

	mock0 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.DescribePgTable(context.Background(), &datasource.DescribePgTableReq{Source: &shared.DataSource{Address: "test"}, DB: "DB1", Schema: "sc1", Table: "table1"})
	assert.NotNil(t, err)
	mock0.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = pg.DescribePgTable(context.Background(), &datasource.DescribePgTableReq{Source: &shared.DataSource{Address: "test"}, DB: "DB1", Schema: "sc1", Table: "table1"})
	assert.Nil(t, err)
}

func TestGetConn(t *testing.T) {
	pg := mockPostgreImpl()

	ds := &shared.DataSource{
		Address:          "127.0.0.1:3306",
		ConnectTimeoutMs: 100,
		ReadTimeoutMs:    100,
		WriteTimeoutMs:   100,
	}
	mock1 := mockey.Mock(db.NewConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()

	_, err := pg.getConn(context.Background(), ds)
	assert.Nil(t, err)
}

func TestDescribeTableSpace(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer baseMock1.UnPatch()

	req := &datasource.DescribeTableSpaceReq{Source: &shared.DataSource{}}
	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, fmt.Errorf("test")).Build()
	_, err := pg.DescribeTableSpace(context.Background(), req)
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	sumSpace := int64(0)
	mock2 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*postgreImpl).getSumSpace).Return(&sumSpace, fmt.Errorf("test")).Build()
	mock8 := mockey.Mock((*postgreImpl).isEmpty).Return(false, nil).Build()
	defer mock8.UnPatch()
	_, err = pg.DescribeTableSpace(context.Background(), req)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*postgreImpl).getSumSpace).Return(&sumSpace, nil).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*postgreImpl).getTablesInfo).Return([]*shared.TableStat{}, fmt.Errorf("test")).Build()
	_, err = pg.DescribeTableSpace(context.Background(), req)
	assert.NotNil(t, err)
	mock5.UnPatch()
	time.Sleep(100 * time.Millisecond)

	total := int32(1)
	mock6 := mockey.Mock((*postgreImpl).getTablesInfo).Return([]*shared.TableStat{}, nil).Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*postgreImpl).getTableTotal).Return(&total, nil).Build()
	defer mock7.UnPatch()
	resp, err := pg.DescribeTableSpace(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, resp.Total, int32(1))
}

func TestConvertTableSpaceToModel(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer baseMock1.UnPatch()

	resp := &shared.DescribeTableSpaceResp{
		Total:      1,
		TableStats: []*shared.TableStat{{Name: "test"}},
	}
	res := pg.ConvertTableSpaceToModel(context.Background(), shared.Postgres, resp)
	assert.Equal(t, res.Total, int32(1))
	assert.Equal(t, len(res.TableStats), 1)
	assert.Equal(t, res.TableStats[0].Name, "test")
}

func TestConvertTableColumnToModel(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer baseMock1.UnPatch()

	resp := &shared.DescribeTableColumnResp{
		Total:       1,
		ColumnStats: []*shared.ColumnStat{{ColumnName: "test"}},
	}
	res := pg.ConvertTableColumnToModel(context.Background(), shared.Postgres, resp)
	assert.Equal(t, res.Total, int32(1))
	assert.Equal(t, len(res.ColumnStats), 1)
	assert.Equal(t, *res.ColumnStats[0].ColumnName, "test")
}

func TestConvertTableIndexToModel(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer baseMock1.UnPatch()

	resp := &shared.DescribeTableIndexResp{
		Total:      1,
		IndexStats: []*shared.IndexStat{{IndexName: "test"}},
	}
	res := pg.ConvertTableIndexToModel(context.Background(), shared.Postgres, resp)
	assert.Equal(t, res.Total, int32(1))
	assert.Equal(t, len(res.IndexStats), 1)
	assert.Equal(t, *res.IndexStats[0].IndexName, "test")
}

func TestFormatDescribeStorageCapacityResp(t *testing.T) {
	pg := mockPostgreImpl()
	res := pg.FormatDescribeStorageCapacityResp(shared.Postgres, &datasource.GetDiskSizeResp{}, 123)
	assert.Equal(t, *res.StorageSpace, float64(123))
}

func TestCheckConn(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer baseMock1.UnPatch()

	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(fmt.Errorf("error")).Build()
	conf := &config.Config{PGCheckConnV2: false}
	mockey.Mock((*mocks.MockConfigProvider).Get).Return(conf).Build()
	pg.CheckConn(context.Background(), &shared.DataSource{
		LinkType: shared.Ecs,
	})
	pg.CheckConn(context.Background(), &shared.DataSource{
		LinkType: shared.Volc,
	})
	mock4.UnPatch()

	mock5 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	pg.CheckConn(context.Background(), &shared.DataSource{
		LinkType: shared.Ecs,
	})
	pg.CheckConn(context.Background(), &shared.DataSource{
		LinkType: shared.Volc,
	})
	defer mock5.UnPatch()
}

func TestCheckConnV2(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer baseMock1.UnPatch()

	mgrClient := &mocks.MockMgrClient{}
	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(fmt.Errorf("error")).Build()
	pg.CheckConnV2(context.Background(), &shared.DataSource{
		LinkType: shared.Ecs,
	})
	pg.CheckConnV2(context.Background(), &shared.DataSource{
		LinkType: shared.Volc,
	})
	mock4.UnPatch()

	mock5 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	pg.CheckConnV2(context.Background(), &shared.DataSource{
		LinkType: shared.Ecs,
	})
	pg.CheckConnV2(context.Background(), &shared.DataSource{
		LinkType: shared.Volc,
	})
	defer mock5.UnPatch()
}

func Test_CheckConnV2(t *testing.T) {
	ctx := context.Background()
	ds := &shared.DataSource{
		InstanceId: "test-instance",
		LinkType:   shared.Volc,
	}
	//containers := []*pgModel.ContainerDescription{
	//	{
	//		Name:  "database",
	//		Ports: map[string]int32{"postgres": 5432},
	//	},
	//}
	//node := &pgModel.DeploymentObject{
	//	DeploymentName:  "pg-test",
	//	NodeType:        0,
	//	PodName:         "pg-test",
	//	LSN:             "pg-test",
	//	Region:          "pg-test",
	//	Zone:            "pg-test",
	//	KubeCluster:     "pg-test",
	//	NodePool:        "pg-test",
	//	NodeIP:          "pg-test",
	//	PodIP:           "pg-test",
	//	PodStatus:       "pg-test",
	//	Labels:          nil,
	//	LatestStartTime: "pg-test",
	//	Containers:      containers,
	//}
	pg := mockPostgreImpl()

	PatchConvey("Test shared.Ecs", t, func() {
		tempDs := &shared.DataSource{
			InstanceId: "test-instance",
			LinkType:   shared.Ecs,
		}
		err := pg.CheckConnV2(ctx, tempDs)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test Call fails", t, func() {
		//Mock((*mocks.MockProvider).Get).Return(&mocks.MockMgrClient{}).Build()
		//Mock((*mocks.MockMgrClient).Call).Return(errors.New("call failed")).Build()
		mgrClient := &mocks.MockMgrClient{}
		mock1 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mocks.MockMgrClient).Call).Return(errors.New("call failed")).Build()
		defer mock2.UnPatch()
		err := pg.CheckConnV2(ctx, ds)
		So(err, ShouldNotBeNil)
	})

	//PatchConvey("Test no deployment info found", t, func() {
	//	mgrClient := &mocks.MockMgrClient{}
	//	mock3 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
	//	defer mock3.UnPatch()
	//	mock4 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
	//	defer mock4.UnPatch()
	//	mockey.Mock(fp.StreamOf).Return(nil).Build()
	//	err := pg.CheckConnV2(ctx, ds)
	//	So(err, ShouldNotBeNil)
	//})

	PatchConvey("Test node found but getConn fails", t, func() {
		mgrClient := &mocks.MockMgrClient{}
		mock1 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock2.UnPatch()
		//mockey.Mock(fp.StreamOf).Return(&pgModel.DeploymentObject{}).Build()
		mock3 := mockey.Mock((*postgreImpl).getConn).Return(nil, errors.New("getConn failed")).Build()
		defer mock3.UnPatch()
		err := pg.CheckConnV2(ctx, ds)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test getConn succeeds but conn.Version fails", t, func() {
		mgrClient := &mocks.MockMgrClient{}
		mock1 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock2.UnPatch()
		//mock3 := mockey.Mock(fp.StreamOf).Return(&pgModel.DeploymentObject{}).Build()
		//defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
		defer mock4.UnPatch()
		mock5 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
		defer mock5.UnPatch()
		mock6 := mockey.Mock((*mocks.MockConn).Version).Return("", errors.New("version failed")).Build()
		defer mock6.UnPatch()
		err := pg.CheckConnV2(ctx, ds)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test all operations succeed", t, func() {
		mgrClient := &mocks.MockMgrClient{}
		mock1 := mockey.Mock((*mocks.MockProvider).Get).Return(mgrClient).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mocks.MockMgrClient).Call).Return(nil).Build()
		defer mock2.UnPatch()
		//mock3 := mockey.Mock(fp.StreamOf).Return(nil).Build()
		//defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
		defer mock4.UnPatch()
		mock5 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
		defer mock5.UnPatch()
		mock6 := mockey.Mock((*mocks.MockConn).Version).Return("13", nil).Build()
		defer mock6.UnPatch()
		err := pg.CheckConnV2(ctx, ds)
		So(err, ShouldNotBeNil)
	})
}

func TestDescribeDialogInfos(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mock6 := mockey.Mock((*postgreImpl).getAllDialogInfos).Return(nil, nil).Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*postgreImpl).filterDialogDetails).Return(nil).Build()
	defer mock7.UnPatch()
	_, err := pg.DescribeDialogInfos(context.Background(), &datasource.DescribeDialogInfosReq{Source: &shared.DataSource{Address: "test"}, Component: "DBEngine", Limit: 10, Offset: 0, InternalUsers: []string{"postgres"}})
	assert.Nil(t, err)
}
func TestDescribeDialogStatistics(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mock6 := mockey.Mock((*postgreImpl).getAllDialogInfos).Return(nil, nil).Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*postgreImpl).getDialogStatistics).Return(nil).Build()
	defer mock7.UnPatch()
	_, err := pg.DescribeDialogInfos(context.Background(), &datasource.DescribeDialogInfosReq{Source: &shared.DataSource{Address: "test"}, Component: "DBEngine", Limit: 10, Offset: 0, InternalUsers: []string{"postgres"}})
	assert.Nil(t, err)
}

func TestFilterDialogDetails(t *testing.T) {
	pg := mockPostgreImpl()
	data := []*datasource.PgDialogInfo{
		{
			ProcessID: "111",
			User:      "test_user",
			Info:      "sleep",
			State:     "idle",
			DB:        "db1",
			Ip:        "********",
			Port:      "5432",
			TrxStart:  "NULL",
		},
		{
			ProcessID: "33",
			User:      "test_user",
			Info:      "sleep",
			State:     "idle",
			DB:        "db1",
			Ip:        "********",
			Port:      "5432",
			TrxStart:  "2024-08-18T22:56:07.510247+08:00",
		},
	}
	queryFilter := &shared.DialogQueryFilter{
		//ShowSleepConnection: "false",
		ProcessID: "111",
		User:      "test_user",
		Host:      "********:5432",
		DB:        "db1",
		Command:   "sleep",
		State:     "idle",
	}
	res := pg.filterDialogDetails(context.Background(), data, queryFilter, 10, 0)
	assert.Equal(t, res.Total, int32(1))
}
func TestGetAllDialogInfos(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mock6 := mockey.Mock((*postgreImpl).DescribeInstanceAddressList).Return([]*datasource.DescribeInstanceAddressResp{
		{
			IP:   "********",
			Port: 5432,
		},
	}, nil).Build()
	defer mock6.UnPatch()
	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
	defer mock2.UnPatch()
	mokc3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
	defer mokc3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*postgreImpl).ListInstanceNodes).Return(&datasource.ListInstanceNodesResp{Nodes: []*model.NodeInfoObject{
		{
			NodeId:   "xxxx",
			NodeType: model.NodeType_Primary,
		},
		{
			NodeId:   "xxxx111",
			NodeType: model.NodeType_Secondary,
		},
	}}, nil).Build()
	defer mock5.UnPatch()
	_, err := pg.getAllDialogInfos(context.Background(), &datasource.DescribeDialogInfosReq{Source: &shared.DataSource{Address: "test"}, Component: "DBEngine", Limit: 10, Offset: 0, InternalUsers: []string{"postgres"}})
	assert.Nil(t, err)
}

func TestGetDialogStatistics(t *testing.T) {
	pg := mockPostgreImpl()
	data := []*datasource.PgDialogInfo{
		{
			ProcessID: "111",
			User:      "test_user",
			Info:      "sleep",
			State:     "idle",
			DB:        "db1",
			Ip:        "********",
			Port:      "5432",
			TrxStart:  "NULL",
		},
		{
			ProcessID: "33",
			User:      "test_user",
			Info:      "sleep",
			State:     "idle",
			DB:        "db1",
			Ip:        "********",
			Port:      "5432",
			TrxStart:  "2024-08-18T22:56:07.510247+08:00",
		},
	}

	res := pg.getDialogStatistics(context.Background(), data, 5)
	assert.NotNil(t, res)
}

func TestKillProcess(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Exec).Return(nil).Build()
	defer mock2.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err := pg.KillProcess(context.Background(), &datasource.KillProcessReq{Source: &shared.DataSource{Address: "test"}, ProcessIDs: []string{"111", "222"}})
	assert.Nil(t, err)
}

func Test_GetAdvice(t *testing.T) {
	pg := mockPostgreImpl()
	mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockConn).Rows).Return(nil, fmt.Errorf("error")).Build()
	defer mock2.UnPatch()
	mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
	defer mock4.UnPatch()
	_, err := pg.GetAdvice(context.Background(), &datasource.GetAdviceReq{Source: &shared.DataSource{Address: "test"}, DB: "test", Sql: "test"})
	assert.NotNil(t, err)

	/*	Sql := "select * from common_types_table t1 inner join common_types_table t2 on t1.id=t2.id   where t1.name='1233'"
		Db := "postgres"
		User := "postgres"
		Password := "yourpassword"
		Address := "localhost:5432"

		sdb := shared.DataSource{Db: Db, User: User, Password: Password, Address: Address}
		req := datasource.GetAdviceReq{Source: &sdb, DB: Db, Sql: Sql}

		postgreImpl := postgreImpl{}
		tableInfo, err := postgreImpl.GetAdvice(context.Background(), &req)
		if err != nil {
			fmt.Println("getTableSchema Error:", err)
		} else {
			fmt.Println("getTableSchema Results:", tableInfo)
		}*/
}

func TestResetAccount(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mock1 := mockey.Mock((*postgreImpl).DeleteAccount).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*postgreImpl).CreateAccount).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockC3ConfigProvider).GetNamespace).Return(&config.C3Config{Application: config.Application{DBWAccountName: "dbw_admin"}}).Build()
	defer mock3.UnPatch()
	err := pg.resetAccount(context.Background(), "pg-xxx", shared.Postgres)
	assert.Nil(t, err)
}

func TestPGGetAllWaitLocks(t *testing.T) {
	ctx := context.TODO()
	req := &datasource.DescribeLockCurrentWaitsReq{
		Source: &shared.DataSource{
			Type:       shared.Postgres,
			InstanceId: "postgre-xxx",
		},
	}
	impl := mockPostgreImpl()
	mockey.PatchConvey("test getAllWaitLocks", t, func() {
		mockey.Mock((*postgreImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
			Data: []*shared.KubePod{
				{
					PodIP:  "*******",
					NodeId: "postgre-xxx-master0",
					Role:   "Primary",
					Containers: []*shared.KubeContainer{
						{
							Name: "database",
							Port: "5432",
						},
					},
				},
			},
		}, nil).Build()
		mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
		defer mock4.UnPatch()
		_, err := impl.getAllWaitLocks(ctx, req)
		So(err, ShouldBeNil)
	})
}
func TestPGGetAllTrxAndLocks(t *testing.T) {
	ctx := context.TODO()
	req := &datasource.DescribeTrxAndLocksReq{
		Source: &shared.DataSource{
			Type:       shared.Postgres,
			InstanceId: "postgre-xxx",
		},
		SortParam: "TrxStartTime",
		Order:     model.Order_Desc.String(),
	}
	impl := mockPostgreImpl()
	mockey.PatchConvey("test getAllTrxLocks", t, func() {
		mockey.Mock((*postgreImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
			Data: []*shared.KubePod{
				{
					PodIP:  "*******",
					NodeId: "postgre-xxx-master0",
					Role:   "Primary",
					Containers: []*shared.KubeContainer{
						{
							Name: "database",
							Port: "5432",
						},
					},
				},
			},
		}, nil).Build()
		mock1 := mockey.Mock((*postgreImpl).getConn).Return(&mocks.MockConn{}, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*mocks.MockConn).Raw).Return(&mocks.MockResult{}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*mocks.MockResult).Scan).Return(nil).Build()
		defer mock3.UnPatch()
		mock4 := mockey.Mock((*mocks.MockConn).Close).Return(nil).Build()
		defer mock4.UnPatch()
		_, err := impl.getAllTrxLocks(ctx, req)
		So(err, ShouldBeNil)
	})
}

func Test_filterWaitLockDetails(t *testing.T) {
	ctx := context.Background()
	impl := mockPostgreImpl()

	PatchConvey("Test filterWaitLockDetails with empty nodeIds and nil queryFilter", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
		}
		resp := impl.filterWaitLockDetails(ctx, data, nil, []string{})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].NodeId, ShouldEqual, "node1")
	})

	PatchConvey("Test filterWaitLockDetails with nodeIds", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
			{NodeId: "node2", RTrxId: "trx3", BTrxId: "trx4", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table2", BBlockingQuery: "UPDATE table2 SET col1 = 'value2'"},
		}
		resp := impl.filterWaitLockDetails(ctx, data, nil, []string{"node1"})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].NodeId, ShouldEqual, "node1")
	})

	PatchConvey("Test filterWaitLockDetails with queryFilter on RTrxId", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
			{NodeId: "node2", RTrxId: "trx3", BTrxId: "trx4", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table2", BBlockingQuery: "UPDATE table2 SET col1 = 'value2'"},
		}
		queryFilter := &model.WaitLockQueryFilter{RTrxId: utils.StringRef("trx1")}
		resp := impl.filterWaitLockDetails(ctx, data, queryFilter, []string{})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].RTrxId, ShouldEqual, "trx1")
	})

	PatchConvey("Test filterWaitLockDetails with queryFilter on BTrxId", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
			{NodeId: "node2", RTrxId: "trx3", BTrxId: "trx4", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table2", BBlockingQuery: "UPDATE table2 SET col1 = 'value2'"},
		}
		queryFilter := &model.WaitLockQueryFilter{BTrxId: utils.StringRef("trx2")}
		resp := impl.filterWaitLockDetails(ctx, data, queryFilter, []string{})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].BTrxId, ShouldEqual, "trx2")
	})

	PatchConvey("Test filterWaitLockDetails with queryFilter on RTrxState", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
			{NodeId: "node2", RTrxId: "trx3", BTrxId: "trx4", RTrxState: "WAITING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table2", BBlockingQuery: "UPDATE table2 SET col1 = 'value2'"},
		}
		queryFilter := &model.WaitLockQueryFilter{RTrxState: utils.StringRef("RUNNING")}
		resp := impl.filterWaitLockDetails(ctx, data, queryFilter, []string{})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].RTrxState, ShouldEqual, "RUNNING")
	})

	PatchConvey("Test filterWaitLockDetails with queryFilter on BTrxState", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
			{NodeId: "node2", RTrxId: "trx3", BTrxId: "trx4", RTrxState: "RUNNING", BTrxState: "WAITING", RWaitingQuery: "SELECT * FROM table2", BBlockingQuery: "UPDATE table2 SET col1 = 'value2'"},
		}
		queryFilter := &model.WaitLockQueryFilter{BTrxState: utils.StringRef("BLOCKING")}
		resp := impl.filterWaitLockDetails(ctx, data, queryFilter, []string{})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].BTrxState, ShouldEqual, "BLOCKING")
	})

	PatchConvey("Test filterWaitLockDetails with queryFilter on RWaitingQuery", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
			{NodeId: "node2", RTrxId: "trx3", BTrxId: "trx4", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table2", BBlockingQuery: "UPDATE table2 SET col1 = 'value2'"},
		}
		queryFilter := &model.WaitLockQueryFilter{RWaitingQuery: utils.StringRef("table1")}
		resp := impl.filterWaitLockDetails(ctx, data, queryFilter, []string{})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].RWaitingQuery, ShouldContainSubstring, "table1")
	})

	PatchConvey("Test filterWaitLockDetails with queryFilter on BBlockingQuery", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
			{NodeId: "node2", RTrxId: "trx3", BTrxId: "trx4", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table2", BBlockingQuery: "UPDATE table2 SET col1 = 'value2'"},
		}
		queryFilter := &model.WaitLockQueryFilter{BBlockingQuery: utils.StringRef("table2")}
		resp := impl.filterWaitLockDetails(ctx, data, queryFilter, []string{})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].BBlockingQuery, ShouldContainSubstring, "table2")
	})

	PatchConvey("Test filterWaitLockDetails with all filters applied", t, func() {
		data := []*datasource.LockCurrentWaitsDetail{
			{NodeId: "node1", RTrxId: "trx1", BTrxId: "trx2", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table1", BBlockingQuery: "UPDATE table1 SET col1 = 'value1'"},
			{NodeId: "node2", RTrxId: "trx3", BTrxId: "trx4", RTrxState: "RUNNING", BTrxState: "BLOCKING", RWaitingQuery: "SELECT * FROM table2", BBlockingQuery: "UPDATE table2 SET col1 = 'value2'"},
		}
		queryFilter := &model.WaitLockQueryFilter{RTrxId: utils.StringRef("trx1"), BTrxId: utils.StringRef("trx2"), RTrxState: utils.StringRef("Running"), BTrxState: utils.StringRef("BLOCKING"), RWaitingQuery: utils.StringRef("table1"), BBlockingQuery: utils.StringRef("table1")}
		resp := impl.filterWaitLockDetails(ctx, data, queryFilter, []string{"node1"})
		So(resp.Total, ShouldEqual, 1)
		So(resp.Result[0].NodeId, ShouldEqual, "node1")
		So(resp.Result[0].RTrxId, ShouldEqual, "trx1")
		So(resp.Result[0].BTrxId, ShouldEqual, "trx2")
		So(resp.Result[0].RTrxState, ShouldEqual, "RUNNING")
		So(resp.Result[0].BTrxState, ShouldEqual, "BLOCKING")
		So(resp.Result[0].RWaitingQuery, ShouldContainSubstring, "table1")
		So(resp.Result[0].BBlockingQuery, ShouldContainSubstring, "table1")
	})
}

func Test_filterTrxAndLocks(t *testing.T) {
	ctx := context.Background()
	impl := mockPostgreImpl()
	mockData := []*shared.TrxAndLock{
		{NodeId: "node1", TrxId: "trx1", ProcessId: "pid1", TrxStatus: "active", BlockTrxId: "blockTrx1", LockStatus: shared.LockWait, SqlBlocked: "SELECT * FROM table1", TrxExecTime: 10},
		{NodeId: "node2", TrxId: "trx2", ProcessId: "pid2", TrxStatus: "inactive", BlockTrxId: "blockTrx2", LockStatus: shared.LockHold, SqlBlocked: "SELECT * FROM table2", TrxExecTime: 5},
	}

	PatchConvey("Test filterTrxAndLocks with empty nodeIds and nil queryFilter", t, func() {
		result := impl.filterTrxAndLocks(ctx, mockData, nil, []string{})
		So(result, ShouldResemble, mockData)
	})

	PatchConvey("Test filterTrxAndLocks with nodeIds", t, func() {
		nodeIds := []string{"node1"}
		result := impl.filterTrxAndLocks(ctx, mockData, nil, nodeIds)
		So(result, ShouldResemble, []*shared.TrxAndLock{mockData[0]})
	})

	PatchConvey("Test filterTrxAndLocks with queryFilter", t, func() {
		queryFilter := &model.TrxQueryFilter{
			TrxId:       utils.StringRef("trx1"),
			ProcessId:   utils.StringRef("pid1"),
			TrxStatus:   utils.StringRef("active"),
			BlockTrxId:  utils.StringRef("blockTrx1"),
			LockStatus:  model.LockstatusPtr(model.Lockstatus_LockWait),
			SqlBlocked:  utils.StringRef("SELECT * FROM table1"),
			TrxExecTime: utils.Int32Ref(5),
		}
		result := impl.filterTrxAndLocks(ctx, mockData, queryFilter, []string{})
		So(result, ShouldResemble, []*shared.TrxAndLock{mockData[0]})
	})

	PatchConvey("Test filterTrxAndLocks with queryFilter and nodeIds", t, func() {
		queryFilter := &model.TrxQueryFilter{
			TrxId:       utils.StringRef("trx1"),
			ProcessId:   utils.StringRef("pid1"),
			TrxStatus:   utils.StringRef("active"),
			BlockTrxId:  utils.StringRef("blockTrx1"),
			LockStatus:  model.LockstatusPtr(model.Lockstatus_LockWait),
			SqlBlocked:  utils.StringRef("SELECT * FROM table1"),
			TrxExecTime: utils.Int32Ref(5),
		}
		nodeIds := []string{"node1"}
		result := impl.filterTrxAndLocks(ctx, mockData, queryFilter, nodeIds)
		So(result, ShouldResemble, []*shared.TrxAndLock{mockData[0]})
	})

	PatchConvey("Test filterTrxAndLocks with queryFilter and no match", t, func() {
		queryFilter := &model.TrxQueryFilter{
			TrxId:       utils.StringRef("trx3"),
			ProcessId:   utils.StringRef("pid3"),
			TrxStatus:   utils.StringRef("pending"),
			BlockTrxId:  utils.StringRef("blockTrx3"),
			LockStatus:  model.LockstatusPtr(model.Lockstatus_LockHold),
			SqlBlocked:  utils.StringRef("SELECT * FROM table3"),
			TrxExecTime: utils.Int32Ref(15),
		}
		result := impl.filterTrxAndLocks(ctx, mockData, queryFilter, []string{})
		So(result, ShouldBeEmpty)
	})

	PatchConvey("Test filterTrxAndLocks with queryFilter and partial match", t, func() {
		queryFilter := &model.TrxQueryFilter{
			TrxId:       utils.StringRef("trx1"),
			ProcessId:   utils.StringRef("pid1"),
			TrxStatus:   utils.StringRef("active"),
			BlockTrxId:  utils.StringRef("blockTrx1"),
			LockStatus:  model.LockstatusPtr(model.Lockstatus_LockWait),
			SqlBlocked:  utils.StringRef("SELECT * FROM table1"),
			TrxExecTime: utils.Int32Ref(5),
		}
		result := impl.filterTrxAndLocks(ctx, mockData, queryFilter, []string{})
		So(result, ShouldResemble, []*shared.TrxAndLock{mockData[0]})
	})
}

func TestPgEnsureAccount(t *testing.T) {
	pg := mockPostgreImpl()
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	mock1 := mockey.Mock((*postgreImpl).ListInstancePods).Return(&datasource.ListInstancePodsResp{
		Data: []*shared.KubePod{
			{
				PodIP:  "*******",
				Role:   "Primary",
				NodeId: "mysql-xxx-0",
				Containers: []*shared.KubeContainer{
					{
						Name: "postgres",
						Port: "3679",
					},
				},
			},
		},

		Total: 1}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*postgreImpl).getConn).Return(nil, fmt.Errorf("authentication failed")).Build()
	defer mock2.UnPatch()
	mock5 := mockey.Mock((*mocks.MockC3ConfigProvider).GetNamespace).Return(&config.C3Config{Application: config.Application{DBWAccountName: "dbw_admin"}}).Build()
	defer mock5.UnPatch()
	mock3 := mockey.Mock((*postgreImpl).DeleteAccount).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*postgreImpl).CreateAccount).Return(nil).Build()
	defer mock4.UnPatch()
	err := pg.EnsureAccount(context.Background(), &datasource.EnsureAccountReq{Source: &shared.DataSource{Address: "test"}})
	assert.Nil(t, err)
}

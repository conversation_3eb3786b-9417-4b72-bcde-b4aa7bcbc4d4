package bytedoc

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	mongoModel "code.byted.org/infcs/dbw-mgr/gen/mongo-mgr/2022-01-01/kitex_gen/infcs/mongodb/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/qjpcpu/fp"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/dig"
)

type NewByteDocDataSourceIn struct {
	dig.In
	Conf              config.ConfigProvider
	DsMgrBuilder      mgr.DsMgrBuilder
	ByteDocHttpClient ByteDocClient
}

type NewByteDocDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewByteDocDataSource(in NewByteDocDataSourceIn) NewByteDocDataSourceOut {
	return NewByteDocDataSourceOut{
		Source: datasource.RetryIfWhiteListNotReady(&bytedocImpl{
			DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
			dsMgrBuilder:      in.DsMgrBuilder,
			conf:              in.Conf,
			byteDocHttpClient: in.ByteDocHttpClient,
		}),
	}
}

type bytedocImpl struct {
	datasource.DataSourceService
	dsMgrBuilder      mgr.DsMgrBuilder
	conf              config.ConfigProvider
	byteDocHttpClient ByteDocClient
}

func (b *bytedocImpl) Type() shared.DataSourceType {
	return shared.ByteDoc
}

func (b *bytedocImpl) AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error) {
	panic("implement me")
}

func (b *bytedocImpl) UpdateWhiteList(ctx context.Context, id string, ds *shared.DataSource, ip []string) error {
	panic("implement me")
}

func (b *bytedocImpl) RemoveWhiteList(ctx context.Context, id string, ds *shared.DataSource, wlID string) error {
	panic("implement me")
}

func (b *bytedocImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	panic("implement me")
}

func (b *bytedocImpl) FillInnerDataSource(ctx context.Context, ds *shared.DataSource) error {
	panic("implement me")
}

func (b *bytedocImpl) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	panic("implement me")
}

func (b *bytedocImpl) CheckDataSource(ctx context.Context, ds *shared.DataSource) error {
	panic("implement me")
}

func (b *bytedocImpl) KillQuery(ctx context.Context, ds *shared.DataSource, conn *shared.ConnectionInfo) error {
	panic("implement me")
}
func (b *bytedocImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	return nil
}
func (b *bytedocImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, "ListInstances panic, err=%v, stack=%s", r, string(debug.Stack()))
		}
	}()
	params := map[string]string{
		"page": utils.Int32ToStr(req.PageNumber),
	}
	if req.InstanceId != "" {
		params["keyword"] = req.InstanceId
	}
	if req.Favor {
		params["favor"] = "true"
		params["username"] = req.UserId
	}
	if req.Owned {
		params["username"] = req.UserId
	}
	if req.InstanceId != "" {
		params["db_name"] = req.InstanceId
	}
	response, err := b.byteDocHttpClient.GET(ctx, DBListAPI, params, nil, req.RegionId, false)
	if err != nil {
		log.Warn(ctx, "Call bytedoc openAPI failed,err %s", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty bytedoc response")
	}
	dbList, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	dbData := &DBList{}
	if err := json.Unmarshal(dbList, dbData); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if dbData.Code != 0 {
		log.Warn(ctx, "Call byteDoc openAPI failed,code %d msg is %s,err is %s", dbData.Code, dbData.Msg, dbData.Error)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Msg)
	}
	ret := &datasource.ListInstanceResp{}
	if err := fp.Stream0Of(dbData.DBs.Results).Map(func(info *Result) *model.InstanceInfo {
		var status string
		// cluster_name 为空的话则表示删除
		if info.ClusterName != "" {
			status = "Running"
		} else {
			status = "Released"
		}
		return &model.InstanceInfo{
			InstanceId:      utils.StringRef(info.DbName),
			InstanceName:    utils.StringRef(info.DbName),
			InstanceStatus:  status,
			RegionId:        utils.StringRef(req.RegionId),
			DBEngineVersion: mongoModel.DBEngineVersion_MongoDB_4_0.String(), // 内场mongo默认为4.0
			PsmDb:           utils.StringRef(info.Psm),
			PsmList:         []string{info.Psm},
			InstanceType:    model.InstanceType_ByteDoc,
			LinkType:        model.LinkType_ByteInner,
		}
	}).ToSlice(&ret.InstanceList); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	ret.Total = int64(dbData.DBs.Total)
	//log.Info(ctx, "ListInstance is %s", utils.Show(ret))
	return ret, nil
}

func (b *bytedocImpl) ListDatabases(ctx context.Context, req *datasource.ListDatabasesReq) (*datasource.ListDatabasesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListInstanceNodes(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListInstancePods(ctx context.Context, req *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (*datasource.DescribeDBInstanceDetailResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDBInstanceEndpoints(ctx context.Context, req *datasource.DescribeDBInstanceEndpointsReq) (*datasource.DescribeDBInstanceEndpointsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDBInstanceShardInfos(ctx context.Context, req *datasource.DescribeDBInstanceShardInfosReq) (*datasource.DescribeDBInstanceShardInfosResp, error) {
	ret := &datasource.DescribeDBInstanceShardInfosResp{}
	topo, err := b.getShardInstanceTopo(ctx, req.DBName, req.RegionId)
	if err != nil {
		return nil, err
	}
	if len(topo) < 1 {
		log.Warn(ctx, "get mongo topo failed")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get mongo topo failed")
	}
	log.Info(ctx, "%s topo is %s", req.InstanceId, utils.Show(topo))
	if err := fp.Stream0Of(topo).Map(func(t *TopoObject) *datasource.ShardInfo {
		var nodeIds []*model.ShardNodeInfo
		item := &datasource.ShardInfo{
			ShardId:       t.ShardId,
			ComponentType: t.Component,
		}
		for _, addr := range t.IpList {
			// shardId为主实例的ip:port
			var nodeId string
			if addr.IPv4 != "" {
				nodeId = fmt.Sprintf("%s:%d", addr.IPv4, addr.Port)
			} else {
				nodeId = fmt.Sprintf("[%s]:%d", addr.IPv6, addr.Port)
			}
			nodeIds = append(nodeIds, &model.ShardNodeInfo{
				NodeType: model.NodeType_Primary.String(), // bytedoc 目前接口无返回准确的role,默认返回Primary
				NodeId:   nodeId,
			})
		}
		item.Nodes = nodeIds
		return item
	}).ToSlice(&ret.Shards); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	return ret, nil
}

func (b *bytedocImpl) DescribeDBInstanceCluster(ctx context.Context, req *datasource.DescribeDBInstanceClusterReq) (*datasource.DescribeDBInstanceClusterResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDBInstanceAuditCollectedPod(ctx context.Context, req *datasource.DescribeDBInstanceAuditCollectedPodReq) (*datasource.DescribeDBInstanceAuditCollectedPodResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) OpenDBInstanceAuditLog(ctx context.Context, req *datasource.OpenDBInstanceAuditLogReq) (*datasource.OpenDBInstanceAuditLogResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) CloseDBInstanceAuditLog(ctx context.Context, req *datasource.CloseDBInstanceAuditLogReq) (*datasource.CloseDBInstanceAuditLogResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) CheckDBInstanceAuditLogStatus(ctx context.Context, req *datasource.CheckDBInstanceAuditLogStatusReq) (*datasource.CheckDBInstanceAuditLogStatusResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDBProxyConfig(ctx context.Context, req *datasource.DescribeDBProxyConfigReq) (*datasource.DescribeDBProxyConfigResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeInstanceFeatures(ctx context.Context, req *datasource.DescribeInstanceFeaturesReq) (*datasource.DescribeInstanceFeaturesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDBInstanceSSL(ctx context.Context, req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeSQLCCLConfig(ctx context.Context, req *datasource.DescribeSQLCCLConfigReq) (*datasource.DescribeSQLCCLConfigResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ModifySQLCCLConfig(ctx context.Context, req *datasource.ModifySQLCCLConfigReq) (*datasource.ModifySQLCCLConfigResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) AddSQLCCLRule(ctx context.Context, req *datasource.AddSQLCCLRuleReq) (*datasource.AddSQLCCLRuleResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ModifyProxyThrottleRule(ctx context.Context, req *datasource.ModifyProxyThrottleRuleReq) (*datasource.ModifyProxyThrottleRuleResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DeleteSQLCCLRule(ctx context.Context, req *datasource.DeleteSQLCCLRuleReq) (*datasource.DeleteSQLCCLRuleResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) FlushSQLCCLRule(ctx context.Context, req *datasource.FlushSQLCCLRuleReq) (*datasource.FlushSQLCCLRuleResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListSQLCCLRules(ctx context.Context, req *datasource.ListSQLCCLRulesReq) (*datasource.ListSQLCCLRulesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeSqlFingerPrintOrKeywords(ctx context.Context, req *datasource.DescribeSqlFingerPrintOrKeywordsReq) (*datasource.DescribeSqlFingerPrintOrKeywordsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeInstanceAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeInstanceAddressList(ctx context.Context, req *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListSchemaTables(ctx context.Context, req *datasource.ListSchemaTablesReq) (*datasource.ListSchemaTablesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListTablesInfo(ctx context.Context, req *datasource.ListTablesInfoReq) (*datasource.ListTablesInfoResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeTable(ctx context.Context, req *datasource.DescribeTableReq) (*datasource.DescribeTableResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeAutoKillSessionConfig(ctx context.Context, req *datasource.DescribeAutoKillSessionConfigReq) (*datasource.DescribeAutoKillSessionConfigResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ModifyAutoKillSessionConfig(ctx context.Context, req *datasource.ModifyAutoKillSessionConfigReq) (*datasource.ModifyAutoKillSessionConfigResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeInstanceVersion(ctx context.Context, req *datasource.DescribeInstanceVersionReq) (*datasource.DescribeInstanceVersionResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListErrLogs(ctx context.Context, req *datasource.ListErrLogsReq) (*datasource.ListErrLogsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribePgTable(ctx context.Context, req *datasource.DescribePgTableReq) (*datasource.DescribePgTableResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeInstanceReplicaDelay(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (int64, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListViews(ctx context.Context, req *datasource.ListViewsReq) (*datasource.ListViewsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeView(ctx context.Context, req *datasource.DescribeViewReq) (*datasource.DescribeViewResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeFunction(ctx context.Context, req *datasource.DescribeFunctionReq) (*datasource.DescribeFunctionResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeProcedure(ctx context.Context, req *datasource.DescribeProcedureReq) (*datasource.DescribeProcedureResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListFunctions(ctx context.Context, req *datasource.ListFunctionsReq) (*datasource.ListFunctionsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListProcedures(ctx context.Context, req *datasource.ListProceduresReq) (*datasource.ListProceduresResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListTriggers(ctx context.Context, req *datasource.ListTriggersReq) (*datasource.ListTriggersResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeTLSConnectionInfo(ctx context.Context, req *datasource.DescribeTLSConnectionInfoReq) (*datasource.DescribeTLSConnectionInfoResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListKeyNumbers(ctx context.Context, req *datasource.ListKeyNumbersReq) (*datasource.ListKeyNumbersResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListKeys(ctx context.Context, req *datasource.ListKeysReq) (*datasource.ListKeysResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetKey(ctx context.Context, req *datasource.GetKeyReq) (*datasource.GetKeyResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListKeyMembers(ctx context.Context, req *datasource.ListKeyMembersReq) (*datasource.ListKeyMembersResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListAlterKVsCommands(ctx context.Context, req *datasource.ListAlterKVsCommandsReq) (*datasource.ListAlterKVsCommandsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) OpenTunnel(ctx context.Context, ds *shared.DataSource, TunnelID string) error {
	panic("implement me")
}

func (b *bytedocImpl) DescribeTrigger(ctx context.Context, req *datasource.DescribeTriggerReq) (*datasource.DescribeTriggerResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeEvent(ctx context.Context, req *datasource.DescribeEventReq) (*datasource.DescribeEventResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListEvents(ctx context.Context, req *datasource.ListEventsReq) (*datasource.ListEventsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) CreateAccount(ctx context.Context, req *datasource.CreateAccountReq) error {
	panic("implement me")
}

func (b *bytedocImpl) CheckPrivilege(ctx context.Context, instanceId, dbName, accountName, priv string, dsType shared.DataSourceType) (bool, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeAccounts(ctx context.Context, req *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeAccounts2(ctx context.Context, req *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp2, error) {
	panic("implement me")
}

func (b *bytedocImpl) CreateAccountAndGrant(ctx context.Context, instanceId, accountName, password, dbName, priv string, dsType shared.DataSourceType) error {
	panic("implement me")
}

func (b *bytedocImpl) ModifyAccountPrivilege(ctx context.Context, req *datasource.ModifyAccountPrivilegeReq) error {
	panic("implement me")
}

func (b *bytedocImpl) GrantAccountPrivilege(ctx context.Context, req *datasource.GrantAccountPrivilegeReq) error {
	panic("implement me")
}

func (b *bytedocImpl) DeleteAccount(ctx context.Context, req *datasource.DeleteAccountReq) error {
	panic("implement me")
}

func (b *bytedocImpl) ListDatabasesWithAccount(ctx context.Context, req *datasource.ListDatabasesWithAccountReq) (*datasource.ListDatabasesWithAccountResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetAdvice(ctx context.Context, req *datasource.GetAdviceReq) (*datasource.GetAdviceResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListCharsets(ctx context.Context, req *datasource.ListCharsetsReq) (*datasource.ListCharsetsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListCollations(ctx context.Context, req *datasource.ListCollationsReq) (*datasource.ListCollationsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListSchema(ctx context.Context, req *datasource.ListSchemaReq) (*datasource.ListSchemaResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListSequence(ctx context.Context, req *datasource.ListSequenceReq) (*datasource.ListSequenceResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListPgCollations(ctx context.Context, req *datasource.ListPgCollationsReq) (*datasource.ListPgCollationsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListPgUsers(ctx context.Context, req *datasource.ListPgUsersReq) (*datasource.ListPgUsersResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListTableSpaces(ctx context.Context, req *datasource.ListTableSpacesReq) (*datasource.ListTableSpacesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDialogDetails(ctx context.Context, req *datasource.DescribeDialogDetailsReq) (*datasource.DescribeDialogDetailsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeEngineStatus(ctx context.Context, req *datasource.DescribeEngineStatusReq) (*datasource.DescribeEngineStatusResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) KillProcess(ctx context.Context, req *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	if len(req.ProcessIDs) < 1 {
		return &datasource.KillProcessResp{}, nil
	}
	var (
		clusterName  string
		failInfoList []*shared.KillFailInfo
	)
	// 获取engine ip: port信息
	topo, err := b.getShardInstanceTopo(ctx, req.Source.InstanceId, req.Source.Region)
	if err != nil {
		return nil, err
	}
	if len(topo) < 1 {
		log.Warn(ctx, "get mongo topo failed")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get mongo topo failed")
	}
	// 获取clusterName
	for _, inst := range topo {
		if inst.Component == model.ComponentType_ConfigServers {
			clusterName = inst.ShardId
			break
		}
	}
	if clusterName == "" {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "bytedoc clusterName is null")
	}
	cnf := b.conf.Get(ctx)
	dbwPwd := getAccountPassword(cnf.C3Config.DbwAccountPasswordGenKey, clusterName)
	req.Source.Password = dbwPwd
	req.Source.User = cnf.C3Config.DBWAccountName
	req.Source.Address = req.NodeId
	conn, err := b.getMongoConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "mongoImpl: connect to datasource %s fail %v", req.Source.Address, err)
		return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close(ctx)
	for _, processId := range req.ProcessIDs {
		opid, _ := strconv.ParseInt(processId, 10, 64)
		if err := conn.KillOp(ctx, "admin", opid); err != nil {
			failInfoList = append(failInfoList, &shared.KillFailInfo{
				ProcessId:    processId,
				ErrorMessage: err.Error()})
		}
	}
	return &datasource.KillProcessResp{FailInfoList: failInfoList}, nil
}

func (b *bytedocImpl) DescribeTrxAndLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) (*datasource.DescribeTrxAndLocksResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDeadlock(ctx context.Context, req *datasource.DescribeDeadlockReq) (*datasource.DescribeDeadlockResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeDeadlockDetect(ctx context.Context, req *datasource.DescribeDeadlockDetectReq) (*datasource.DescribeDeadlockDetectResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeCurrentConn(ctx context.Context, req *datasource.DescribeCurrentConnsReq) (*datasource.DescribeCurrentConnsResp, error) {
	ret := &datasource.DescribeCurrentConnsResp{}
	dialogInfos, err := b.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:      req.Source,
		QueryFilter: req.QueryFilter,
	}, true)
	if err != nil {
		return nil, err
	}
	// get details info
	// 从会话中提取连接信息
	ret.ConnDetails = b.filterConnDetails(ctx, dialogInfos, req)
	ret.ConnStatistics = b.getConnStatistics(ctx, dialogInfos, 5)
	return ret, nil
}
func (b *bytedocImpl) DescribeDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) (*datasource.DescribeDialogInfosResp, error) {
	ret := &datasource.DescribeDialogInfosResp{}
	dialogInfos, err := b.getAllDialogInfos(ctx, req, false)
	if err != nil {
		return nil, err
	}
	// get details info
	ret.DialogDetails = b.filterDialogDetails(ctx, dialogInfos, req)
	return ret, nil
}

func (b *bytedocImpl) DescribeDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq) (*datasource.DescribeDialogStatisticsResp, error) {
	dialogInfos, err := b.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:        req.Source,
		QueryFilter:   req.QueryFilter,
		InternalUsers: req.InternalUsers,
	}, false)
	if err != nil {
		return nil, err
	}
	// get aggregated info
	statistics := b.getDialogStatistics(ctx, dialogInfos, req.TopN)
	ret := &datasource.DescribeDialogStatisticsResp{
		DialogStatistics: statistics,
	}

	return ret, nil
}

func (b *bytedocImpl) DescribeTableSpace(ctx context.Context, req *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ConvertTableSpaceToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp {
	panic("implement me")
}

func (b *bytedocImpl) DescribeTableColumn(ctx context.Context, req *datasource.DescribeTableInfoReq) (*shared.DescribeTableColumnResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ConvertTableColumnToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableColumnResp) *model.DescribeTableColumnResp {
	panic("implement me")
}

func (b *bytedocImpl) DescribeTableIndex(ctx context.Context, req *datasource.DescribeTableInfoReq) (*shared.DescribeTableIndexResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ConvertTableIndexToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableIndexResp) *model.DescribeTableIndexResp {
	panic("implement me")
}

func (b *bytedocImpl) FormatDescribeStorageCapacityResp(sourceType shared.DataSourceType, diskSize *datasource.GetDiskSizeResp, storageSpace float64) *model.DescribeStorageCapacityResp {
	panic("implement me")
}

func (b *bytedocImpl) ExecuteCCL(ctx context.Context, req *datasource.ExecuteCCLReq) (*datasource.ExecuteCCLResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) CCLShow(ctx context.Context, req *datasource.CCLShowReq) (*datasource.CCLShowResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetCpuMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetAvgCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMinCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetCpuUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMemMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetAvgMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMinMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMemUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetAvgDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMinDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetDiskAvailableDays(ctx context.Context, req *datasource.GetDiskAvailableDaysReq) (*datasource.GetDiskAvailableDaysResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetDiskFutureSize(ctx context.Context, req *datasource.GetDiskFutureSizeReq) (*datasource.GetDiskFutureSizeResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetDiskMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetDiskUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetAvgQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMinQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetAvgTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMinTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetAvgConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMinConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetConnectedRatioMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetSessionMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetAvgSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMinSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetLatestDiskUsage(ctx context.Context, req *datasource.GetLatestDiskUsageReq) (float64, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInspectionCpuMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInspectionMemMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInspectionDiskMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInspectionQpsMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInspectionTpsMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInspectionConnectedMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInspectionConnRatioMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *bytedocImpl) HealthSummary(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.Resource, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListCollections(ctx context.Context, req *datasource.ListCollectionsReq) (*datasource.ListCollectionsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListIndexs(ctx context.Context, req *datasource.ListIndexesReq) (*datasource.ListIndexesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListMongoDBs(ctx context.Context, req *datasource.ListMongoDBsReq) (*datasource.ListMongoDBsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) CreateFreeLockCorrectOrder(ctx context.Context, c *datasource.CreateFreeLockCorrectOrderReq) (*datasource.CreateFreeLockCorrectOrderResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) CreateFreeLockCorrectOrderDryRun(ctx context.Context, c *datasource.CreateFreeLockCorrectOrderReq) (*datasource.CreateFreeLockCorrectOrderDryRunResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeFreeLockCorrectOrders(ctx context.Context, req *datasource.DescribeFreeLockCorrectOrdersReq) (*datasource.DescribeFreeLockCorrectOrdersResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) StopFreeLockCorrectOrders(ctx context.Context, req *datasource.StopFreeLockCorrectOrdersReq) error {
	panic("implement me")
}

func (b *bytedocImpl) PreCheckFreeLockCorrectOrders(ctx context.Context, req *datasource.PreCheckFreeLockCorrectOrdersReq) (*datasource.PreCheckFreeLockCorrectOrdersResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetDBInnerAddress(ctx context.Context, req *datasource.GetDBInnerAddressReq) (*datasource.GetDBInnerAddressResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ExecuteDQL(ctx context.Context, req *datasource.ExecuteDQLReq) (*datasource.ExecuteDQLResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ExplainCommand(ctx context.Context, req *datasource.ExplainCommandReq) (*datasource.ExplainCommandResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) IsMyOwnInstance(ctx context.Context, instanceId string, source shared.DataSourceType) bool {
	return true
}

func (b *bytedocImpl) GetTableIndexInfo(ctx context.Context, req *datasource.GetTableIndexInfoReq) (*datasource.GetTableInfoIndexResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetTableIndexValue(ctx context.Context, req *datasource.GetIndexValueReq) (*datasource.GetIndexValueResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxConnections(ctx context.Context, req *datasource.GetMaxConnectionsReq) (int, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetLatestUsedConnection(ctx context.Context, req *datasource.GetMetricUsageReq) (int, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetLatestActiveConnection(ctx context.Context, req *datasource.GetMetricUsageReq) (int, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetCurrentBandwidth(ctx context.Context, req *datasource.GetCurrentBandwidthReq) (*datasource.InstanceBandwidthInfo, error) {
	panic("implement me")
}

func (b *bytedocImpl) BandwidthScale(ctx context.Context, req *datasource.BandwidthScaleReq) error {
	panic("implement me")
}

func (b *bytedocImpl) GetMinBandwidth(ctx context.Context, req *datasource.GetMinMaxBandwidthReq) (int, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetMaxBandwidth(ctx context.Context, req *datasource.GetMinMaxBandwidthReq) (int, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetDiskSize(ctx context.Context, req *datasource.GetDiskSizeReq) (*datasource.GetDiskSizeResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetCurrentMetricData(ctx context.Context, req *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetPreSecondMetricData(ctx context.Context, req *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeFullSQLLogConfig(ctx context.Context, req *datasource.DescribeFullSQLLogConfigReq) (*datasource.DescribeFullSQLLogConfigResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ModifyFullSQLLogConfig(ctx context.Context, req *datasource.ModifyFullSQLLogConfigReq) (*datasource.ModifyFullSQLLogConfigResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeInstanceVariables(ctx context.Context, req *datasource.DescribeInstanceVariablesReq) (*datasource.DescribeInstanceVariablesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribePrimaryKeyRange(ctx context.Context, req *datasource.DescribePrimaryKeyRangeReq) (*datasource.DescribePrimaryKeyRangeResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeSQLAdvisorTableMeta(ctx context.Context, req *datasource.DescribeSQLAdvisorTableMetaReq) (*datasource.DescribeSQLAdvisorTableMetaResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) DescribeSampleData(ctx context.Context, req *datasource.DescribeSampleDataReq) (*datasource.DescribeSampleDataResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) EnsureAccount(ctx context.Context, req *datasource.EnsureAccountReq) error {
	log.Info(ctx, "Start to check account")
	var clusterName, ipAddr string
	topo, err := b.getShardInstanceTopo(ctx, req.Source.InstanceId, req.Source.Region)
	if err != nil {
		return err
	}
	//任选一个实例测试账号存在性
	if len(topo) < 1 {
		log.Warn(ctx, "get bytedoc topo failed")
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "get bytedoc topo failed")
	}
	if len(topo[0].IpList) < 1 {
		log.Info(ctx, "bytedoc ip list is Null")
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "bytedoc ip list is Null")
	}
	// 获取clusterName
	for _, inst := range topo {
		if inst.Component == model.ComponentType_ConfigServers {
			clusterName = inst.ShardId
			break
		}
	}
	// 检查DB侧dbw账号是否存在
	if req.Source.Address != "" {
		ipAddr = req.Source.Address
	} else {
		addr := topo[0].IpList[0]
		if addr.IPv4 != "" {
			ip := addr.IPv4
			ipAddr = fmt.Sprintf("%s:%d", ip, addr.Port)
		} else {
			ip := addr.IPv6
			ipAddr = fmt.Sprintf("[%s]:%d", ip, addr.Port)
		}
	}
	cnf := b.conf.Get(ctx)
	dbwPwd := getAccountPassword(cnf.C3Config.DbwAccountPasswordGenKey, clusterName)
	rrSource := req.Source
	rrSource.Address = ipAddr
	rrSource.Password = dbwPwd
	log.Info(ctx, "mongo ds is %s", utils.Show(rrSource))
	conn, err := b.getMongoConn(ctx, rrSource)
	if err != nil {
		if strings.Contains(err.Error(), "AuthenticationFailed") ||
			strings.Contains(err.Error(), "Authentication failed") {
			log.Info(ctx, "instance %s clusterName is %s", rrSource.InstanceId, clusterName)
			cnf := b.conf.Get(ctx)
			if err := b.deleteDBWAccount(ctx, &datasource.DeleteAccountReq{
				InstanceId: clusterName,
				RegionId:   req.Source.Region,
			}); err != nil {
				return err
			}
			if err := b.createDBWAccount(ctx, &datasource.CreateAccountReq{
				InstanceId:      clusterName,
				AccountPassword: getAccountPassword(cnf.C3Config.DbwAccountPasswordGenKey, clusterName),
				AccountName:     cnf.C3Config.DBWAccountName,
				RegionId:        req.Source.Region,
			}); err != nil {
				return err
			}
		} else {
			log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
			return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
		}
	}
	defer conn.Close(ctx)
	// 账号不存在 reset account
	if err = conn.Ping(ctx); err != nil {
		log.Warn(ctx, "ping %s fail %v", rrSource.Address, err)
		if strings.Contains(err.Error(), "AuthenticationFailed") ||
			strings.Contains(err.Error(), "Authentication failed") {
			log.Info(ctx, "instance %s clusterName is %s", rrSource.InstanceId, clusterName)
			cnf := b.conf.Get(ctx)
			if err := b.deleteDBWAccount(ctx, &datasource.DeleteAccountReq{
				InstanceId: clusterName,
				RegionId:   req.Source.Region,
			}); err != nil {
				return err
			}
			if err := b.createDBWAccount(ctx, &datasource.CreateAccountReq{
				InstanceId:      clusterName,
				AccountPassword: getAccountPassword(cnf.C3Config.DbwAccountPasswordGenKey, clusterName),
				AccountName:     cnf.C3Config.DBWAccountName,
				RegionId:        req.Source.Region,
			}); err != nil {
				return err
			}
		} else {
			log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
			return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
		}
	}
	return nil
}

func (b *bytedocImpl) checkAccountExistInDbEngine(ctx context.Context, req *datasource.EnsureAccountReq) (bool, error) {
	topo, err := b.getShardInstanceTopo(ctx, req.Source.InstanceId, req.Source.Region)
	if err != nil {
		return false, err
	}
	//任选一个实例测试账号存在性
	if len(topo) < 1 {
		log.Warn(ctx, "get bytedoc topo failed")
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, "get bytedoc topo failed")
	}
	if len(topo[0].IpList) < 1 {
		log.Warn(ctx, "bytedoc ip list is Null")
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, "bytedoc ip list is Null")
	}
	addr := topo[0].IpList[0]
	rrSource := req.Source
	var ipAddr string
	if addr.IPv4 != "" {
		ipAddr = addr.IPv4
		rrSource.Address = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
	} else {
		ipAddr = addr.IPv6
		rrSource.Address = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
	}
	log.Info(ctx, "mongo addr is %s", rrSource.Address)
	conn, err := b.getMongoConn(ctx, rrSource)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
		return false, err
	}
	if err = conn.Ping(ctx); err != nil {
		log.Warn(ctx, "ping %s fail %v", rrSource.Address, err)
		return false, err
	}
	defer conn.Close(ctx)
	return true, nil
}
func (b *bytedocImpl) GetDatasourceAddress(ctx context.Context, ds *shared.DataSource) error {
	panic("implement me")
}

func (b *bytedocImpl) GetDBServiceTreeMountInfo(ctx context.Context, req *datasource.GetDBServiceTreeMountInfoReq) (*datasource.GetDBServiceTreeMountInfoResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetDBInstanceInfo(ctx context.Context, req *datasource.GetDBInstanceInfoReq) (*datasource.GetDBInstanceInfoResp, error) {
	return &datasource.GetDBInstanceInfoResp{
		DBInfo: &model.DbInstanceInfo{
			InstanceId:      req.InstanceId,
			InstanceName:    req.InstanceId,
			DBEngineVersion: "4.0",
			IsSharding:      true,
		},
	}, nil

}

func (b *bytedocImpl) InstanceIsExist(ctx context.Context, req *datasource.InstanceIsExistReq) (bool, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInstanceTopo(ctx context.Context, req *datasource.GetInstanceTopoReq) ([]*model.InnerRdsInstance, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInstanceProxyTopo(ctx context.Context, req *datasource.GetInstanceTopoReq) ([]*model.InnerRdsInstance, error) {
	panic("implement me")
}

func (b *bytedocImpl) CreateLogDownloadTask(ctx context.Context, req *datasource.CreateLogDownloadTaskReq) error {
	panic("implement me")
}

func (b *bytedocImpl) GetLogDownloadList(ctx context.Context, req *datasource.GetLogDownloadListReq) (*datasource.GetLogDownloadListResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) GetInstancePrimaryNodeId(ctx context.Context, req *datasource.GetInstancePrimaryNodeIdReq) (*datasource.GetInstancePrimaryNodeIdResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ListSQLKillRules(ctx context.Context, req *datasource.ListSQLKillRulesReq) (*datasource.ListSQLKillRulesResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) ModifySQLKillRule(ctx context.Context, req *datasource.ModifySQLKillRuleReq) (*datasource.ModifySQLKillRuleResp, error) {
	panic("implement me")
}

func (b *bytedocImpl) getAllDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq, isAll bool) ([]*datasource.MongoDialogInfo, error) {
	type NodeInfoObj struct {
		NodeId   string
		NodeType string
	}
	var (
		dialogInfos            []*datasource.MongoDialogInfo
		nodeList, fullAllNodes []*NodeInfoObj
		clusterName            string
	)
	// DB侧会话
	// 获取engine ip: port信息
	topo, err := b.getShardInstanceTopo(ctx, req.Source.InstanceId, req.Source.Region)
	if err != nil {
		return nil, err
	}
	if len(topo) < 1 {
		log.Warn(ctx, "get mongo topo failed")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get mongo topo failed")
	}
	// 获取clusterName
	for _, inst := range topo {
		if inst.Component == model.ComponentType_ConfigServers {
			clusterName = inst.ShardId
			break
		}
	}
	if clusterName == "" {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "mongo clusterName is null")
	}
	cnf := b.conf.Get(ctx)
	dbwPwd := getAccountPassword(cnf.C3Config.DbwAccountPasswordGenKey, clusterName)
	req.Source.Password = dbwPwd
	req.Source.User = cnf.C3Config.DBWAccountName
	for _, instInfo := range topo {
		if instInfo.Component == model.ComponentType_Mongos {
			continue
		}
		for _, addr := range instInfo.IpList {
			var (
				ipAddr string
				nodeId string
			)
			if addr.IPv4 != "" {
				ipAddr = addr.IPv4
				nodeId = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
			} else {
				ipAddr = addr.IPv6
				nodeId = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
			}
			fullAllNodes = append(fullAllNodes, &NodeInfoObj{
				NodeId:   nodeId,
				NodeType: addr.Role,
			})
			break
		}
	}
	if len(req.QueryFilter.GetNodeIds()) > 0 {
		for _, nodeObj := range fullAllNodes {
			for _, nodeId := range req.QueryFilter.GetNodeIds() {
				if nodeId == nodeObj.NodeId {
					nodeList = append(nodeList, &NodeInfoObj{
						NodeId:   nodeObj.NodeId,
						NodeType: nodeObj.NodeType,
					})
					break
				}
			}
		}
	} else {
		nodeList = fullAllNodes
	}
	for _, nodeAddr := range nodeList {
		var (
			tempDialog []*datasource.MongoDialogInfo
		)
		rrSource := req.Source
		rrSource.Address = nodeAddr.NodeId
		conn, err := b.getMongoConn(ctx, rrSource)
		if err != nil {
			log.Warn(ctx, "mongoImpl: connect to datasource %s fail %v", rrSource.Address, err)
			// 授权问题，重新初始化账号
			if strings.Contains(err.Error(), "AuthenticationFailed") {
				if err := b.EnsureAccount(ctx, &datasource.EnsureAccountReq{
					Source: req.Source,
				}); err != nil {
					log.Warn(ctx, "mongoImpl: connect to datasource %s fail %v", rrSource.Address, err)
					return nil, err
				}

			}
			if conn != nil {
				_ = conn.Close(ctx)
			}
			return nil, err
		}
		var res []*datasource.RawMongoDialogInfo
		if isAll {
			res, err = conn.CurrentOpAll(ctx)
			if err != nil {
				log.Warn(ctx, "mongo currentOp failed %v", err)
				return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
			}
		} else {
			res, err = conn.CurrentOp(ctx)
			if err != nil {
				log.Warn(ctx, "mongo currentOp failed %v", err)
				return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
			}
		}
		_ = conn.Close(ctx)
		fp.StreamOf(res).Map(func(info *datasource.RawMongoDialogInfo) *datasource.MongoDialogInfo {
			var execSql, pId string
			if info.Info == nil {
				execSql = "-"
			} else {
				dMap := make(map[string]interface{})
				for _, elem := range info.Info.(primitive.D) {
					dMap[elem.Key] = elem.Value
				}
				jsonData, err := json.Marshal(dMap)
				if err != nil {
					execSql = "-"
				} else {
					execSql = string(jsonData)
				}
			}
			if _, ok := info.ProcessID.(string); ok {
				pId = info.ProcessID.(string)
			} else {
				pId = fmt.Sprintf("%d", info.ProcessID)
			}
			return &datasource.MongoDialogInfo{
				ProcessID:    pId,
				Host:         info.Client, // mongo client
				Desc:         info.Desc,
				Time:         fmt.Sprintf("%d", info.Time),
				ConnectionId: fmt.Sprintf("%d", info.ConnectionId),
				Namespace:    info.Namespace,
				PlanSummary:  info.PlanSummary,
				NodeType:     nodeAddr.NodeType,
				NodeId:       rrSource.Address,
				Client:       info.Client, // mongo client
				State:        fmt.Sprintf("%v", info.State),
				Info:         execSql,
				Command:      info.Command,
			}
		}).ToSlice(&tempDialog)
		dialogInfos = append(dialogInfos, tempDialog...)
	}
	log.Info(ctx, "dialogInfos %s ", utils.Show(dialogInfos))
	return dialogInfos, nil
}

func (b *bytedocImpl) getDialogStatistics(ctx context.Context, data []*datasource.MongoDialogInfo, topN int32) *shared.DialogStatistics {
	tData := data
	fp.StreamOf(tData).Reject(func(dialog *datasource.MongoDialogInfo) bool {
		return dialog.Namespace == ""
	}).ToSlice(&tData)
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	nsInfo := make(map[string]*datasource.NsAggregatedInfo)
	var activeConn, totalConn, longTransactionCount, queryCountWithoutIndex, longestSecsRunning int32 = 0, 0, 0, 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(tData).Foreach(func(d *datasource.MongoDialogInfo) {
		totalConn += 1
		//ip := d.Client // 统计client
		addrList := strings.Split(d.Client, ":") // 统计client
		ip := strings.Join(addrList[:len(addrList)-1], ":")
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1

		if _, ok := nsInfo[d.Namespace]; !ok {
			nsInfo[d.Namespace] = &datasource.NsAggregatedInfo{Namespace: d.Namespace}
		}
		nsInfo[d.Namespace].TotalConn += 1

		if strings.ToLower(d.State) == "true" {
			activeConn += 1
			ipInfo[ip].ActiveConn += 1
			nsInfo[d.Namespace].ActiveConn += 1
		}
		// 提取执行时长超过3s的会话
		queryTimeInt, _ := strconv.ParseInt(d.Time, 10, 64)
		if queryTimeInt > 3 {
			longTransactionCount += 1
		}
		// 获取执行时长最长的会话
		if queryTimeInt > int64(longestSecsRunning) {
			longestSecsRunning = int32(queryTimeInt)
		}
		// 提取没有索引的查询(mongo)
		if strings.Contains(d.PlanSummary, "COLLSCAN") {
			queryCountWithoutIndex += 1
		}

	}).Run()
	var ipList []*shared.IPAggregatedInfo
	var nsList []*shared.NsAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)
	fp.KVStreamOf(nsInfo).ZipMap(func(k string, v *datasource.NsAggregatedInfo) *shared.NsAggregatedInfo {
		return &shared.NsAggregatedInfo{
			Namespace:         v.Namespace,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&nsList)

	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(nsList, func(i, j int) bool {
		if nsList[i].TotalConnections > nsList[j].TotalConnections {
			return true
		}
		if nsList[i].TotalConnections == nsList[j].TotalConnections &&
			nsList[i].ActiveConnections == nsList[j].ActiveConnections {
			return true
		}
		return false
	})
	return &shared.DialogStatistics{
		DialogOver: &shared.DialogOverview{
			ActiveConnections:      activeConn,
			TotalConnections:       totalConn,
			LongestSecsRunning:     longestSecsRunning,
			QueryCountWithoutIndex: queryCountWithoutIndex,
			LongTransactionCount:   longTransactionCount,
		},
		IPAggregatedInfo: ipList[:fp.MinInt(int(topN), len(ipList))],
		NsAggregatedInfo: nsList[:fp.MinInt(int(topN), len(nsList))],
	}
}
func (b *bytedocImpl) getConnStatistics(ctx context.Context, data []*datasource.MongoDialogInfo, topN int32) *shared.ConnStatistics {
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	var activeConn, totalConn int32 = 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(data).Foreach(func(d *datasource.MongoDialogInfo) {
		totalConn += 1
		addrList := strings.Split(d.Host, ":") // 统计client
		ip := strings.Join(addrList[:len(addrList)-1], ":")
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1
		if strings.ToLower(d.State) == "true" {
			activeConn += 1
			ipInfo[ip].ActiveConn += 1
		}
	}).Run()
	var ipList []*shared.IPAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)

	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	return &shared.ConnStatistics{
		ConnOverview: &shared.ConnOverview{
			ActiveConnections: activeConn,
			TotalConnections:  totalConn,
		},
		IPAggregatedInfo: ipList[:fp.MinInt(int(topN), len(ipList))],
	}
}
func (b *bytedocImpl) filterDialogDetails(ctx context.Context, data []*datasource.MongoDialogInfo, req *datasource.DescribeDialogInfosReq) *shared.DialogDetails {
	queryFilter := req.QueryFilter
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.MongoDialogInfo) bool {
				return strings.ToLower(d.State) == "false"
			}).ToSlice(&tData)
		}
		if pID := queryFilter.ProcessID; pID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.ProcessID, pID)
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.Host, host)
			}).ToSlice(&tData)
		}
		if command := queryFilter.Command; command != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Command), strings.ToLower(command))
			}).ToSlice(&tData)
		}
		if timeLimit := queryFilter.LowerExecTimeLimit; timeLimit != "" {
			//limitInt, er := strconv.Atoi(queryFilter.LowerExecTimeLimit)
			limitFloat, er := strconv.ParseFloat(queryFilter.LowerExecTimeLimit, 64)
			if er == nil {
				fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
					execTime, _ := strconv.ParseFloat(d.Time, 64)
					return execTime >= limitFloat
				}).ToSlice(&tData)
			}
		}
		if info := queryFilter.Info; info != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Info), strings.ToLower(info))
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if desc := queryFilter.Desc; desc != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Desc), strings.ToLower(desc))
			}).ToSlice(&tData)
		}
		if planSummary := queryFilter.PlanSummary; planSummary != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.PlanSummary), strings.ToLower(planSummary))
			}).ToSlice(&tData)
		}
		if ns := queryFilter.Namespace; ns != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Namespace), strings.ToLower(ns))
			}).ToSlice(&tData)
		}

		//if nodeType := queryFilter.NodeType; nodeType != "" {
		//	switch nodeType {
		//	case model.NodeType_Primary.String():
		//		nodeType = "master"
		//	case model.NodeType_Secondary.String():
		//		nodeType = "slave"
		//	default:
		//		nodeType = "master"
		//	}
		//	fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
		//		if strings.Contains(d.NodeType, nodeType) {
		//			return true
		//		}
		//		return false
		//	}).ToSlice(&tData)
		//}
		// 过滤多节点(支持shard)
		if nodeIdList := queryFilter.NodeIds; len(nodeIdList) > 0 {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				var existed bool
				for _, nodeId := range nodeIdList {
					if d.NodeId == nodeId {
						existed = true
						break
					}
				}
				return existed
			}).ToSlice(&tData)
		}
	}
	// 默认按Time倒序
	//datasource.SortMongoDialog(tData, shared.DESC, "Time")
	var details []*shared.DialogDetail
	fp.StreamOf(tData).Map(func(info *datasource.MongoDialogInfo) *shared.DialogDetail {
		return &shared.DialogDetail{
			ProcessID:   info.ProcessID,
			Command:     info.Command,
			Info:        info.Info,
			Time:        info.Time,
			Host:        info.Host,
			PlanSummary: info.PlanSummary,
			NodeId:      info.NodeId,
			NodeType:    info.NodeType,
			State:       info.State,
			Desc:        info.Desc,
			Namespace:   info.Namespace,
		}
	}).ToSlice(&details)
	return &shared.DialogDetails{
		Details: details,
		Total:   int32(len(details)),
	}
}
func (b *bytedocImpl) filterConnDetails(ctx context.Context, data []*datasource.MongoDialogInfo, req *datasource.DescribeCurrentConnsReq) *shared.ConnDetails {
	queryFilter := req.QueryFilter
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.MongoDialogInfo) bool {
				return strings.ToLower(d.State) == "false"
			}).ToSlice(&tData)
		}
		if connID := queryFilter.ConnId; connID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.ConnectionId, connID)
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.Host, host)
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if desc := queryFilter.Desc; desc != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Desc), strings.ToLower(desc))
			}).ToSlice(&tData)
		}
		if state := queryFilter.State; state != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.State), strings.ToLower(state))
			}).ToSlice(&tData)
		}
		// 过滤多节点(支持shard)
		if nodeIdList := queryFilter.NodeIds; len(nodeIdList) > 0 {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				var existed bool
				for _, nodeId := range nodeIdList {
					if d.NodeId == nodeId {
						existed = true
						break
					}
				}
				return existed
			}).ToSlice(&tData)
		}
	}
	var details []*shared.ConnDetail
	fp.StreamOf(tData).Map(func(info *datasource.MongoDialogInfo) *shared.ConnDetail {
		return &shared.ConnDetail{
			ConnID: info.ConnectionId,
			Client: info.Host,
			State:  info.State,
			Desc:   info.Desc,
		}
	}).ToSlice(&details)
	return &shared.ConnDetails{
		Details: details,
		Total:   int32(len(details)),
	}
}

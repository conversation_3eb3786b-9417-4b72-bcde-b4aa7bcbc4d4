package dair

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/ds_utils"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"context"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"math"

	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	dairModel "code.byted.org/infcs/dbw-mgr/gen/dair-mgr/ai-db-api/kitex_gen/modelv1"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

const (
	DAIR_Version_V1 = `2025-10-01`
)

type NewDAIRDataSourceIn struct {
	dig.In
	DAIRMgr mgr.Provider `name:"dair"`
}

type NewDAIRDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewDAIRDataSource(p NewDAIRDataSourceIn) NewDAIRDataSourceOut {
	return NewDAIRDataSourceOut{
		Source: retryIfWhiteListNotReady(&dairImpl{
			DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
			dair:              p.DAIRMgr,
		}),
	}
}

type dairImpl struct {
	datasource.DataSourceService
	dair mgr.Provider
}

func (m *dairImpl) Type() shared.DataSourceType {
	return shared.DAIR
}

func (m *dairImpl) DescribeDBInstances(ctx context.Context, req *dairModel.DescribeInstancesReq) (*dairModel.DescribeInstancesResp, error) {
	log.Info(ctx, "dairImpl DescribeDBInstances req: %v", utils.Show(req))
	if req.Offset == nil || req.Limit == nil {
		// 如果没这两个值，直接调用接口
		return m.selfDescribeDBInstances(ctx, req)
	}
	pageNum, pageSize := datasource.ToPageNumberSize(*req.Limit, *req.Offset)
	log.Info(ctx, "dairImpl DescribeDBInstances pageNum: %v, pageSize: %v", pageNum, pageSize)
	pageInfo, err := ds_utils.FormatLowerPageInfo(pageNum, pageSize)
	if err != nil {
		return nil, err
	}
	result := &dairModel.DescribeInstancesResp{Instances: []*dairModel.Instance{}, Total: int32(math.MaxInt32)}
	log.Info(ctx, "dairImpl DescribeDBInstances pageInfo: %v", utils.Show(pageInfo))
	req.Limit = utils.Int32Ref(pageInfo.PageSize)
	// 为了防止一次性拉挂RDS，我们这里对传入对PageNum和PageSize 做一次重新处理
	for pageNumber := pageInfo.PageNumLower; pageNumber <= pageInfo.PageNumUpper && (pageNumber-1)*pageInfo.PageSize < result.Total; pageNumber++ {
		req.Limit = utils.Int32Ref(pageInfo.PageSize)
		resp, err := m.selfDescribeDBInstances(ctx, req)
		if err != nil {
			return nil, err
		}
		result.Total = resp.Total
		result.Instances = append(result.Instances, resp.Instances...)
	}
	if result.Total == int32(math.MaxInt32) {
		result.Total = 0
	}
	return result, nil
}

func (self *dairImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	log.Info(ctx, "dairImpl ListInstance req: %v", utils.Show(req))
	if req.LinkType != shared.Volc {
		return nil, nil
	}
	limit, offset := datasource.ToLimitOffset(req.PageNumber, req.PageSize)
	desReq := &dairModel.DescribeInstancesReq{
		Offset: utils.Int32Ref(offset),
		Limit:  utils.Int32Ref(limit),
	}
	log.Info(ctx, "dairImpl ListInstance limit: %v, offset: %v", limit, offset)
	if req.InstanceName != "" {
		desReq.Search = utils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		desReq.Search = utils.StringRef(req.InstanceId)
	}
	var err error
	desResp, err := self.DescribeDBInstances(ctx, desReq)
	if err != nil {
		return nil, err
	}
	desRespInfo := desResp.Instances
	resp := &datasource.ListInstanceResp{Total: int64(desResp.Total)}
	fp.StreamOf(desRespInfo).
		Map(func(inst *dairModel.Instance) *model.InstanceInfo {
			targetTags := make([]*model.TagObject, 0)
			instStatus := inst.InstanceStatus.String()
			if inst.InstanceTags != nil {
				for _, tag := range inst.InstanceTags {
					targetTags = append(targetTags, &model.TagObject{
						Key:   tag.Key,
						Value: *tag.Value,
					})
				}
			}
			accountId := req.TenantId
			if inst.GetAccountId() != "" {
				accountId = inst.GetAccountId()
			}
			var zone string
			if len(inst.GetZoneIds()) > 0 {
				zone = inst.GetZoneIds()[0]
			}
			// 如果inst.GetNodeSpec不在specMap中，那么就不设置instanceSpec
			return &model.InstanceInfo{
				InstanceId:      utils.StringRef(inst.InstanceId),
				InstanceName:    utils.StringRef(inst.InstanceName),
				InstanceStatus:  instStatus,
				Zone:            zone,
				DBEngineVersion: inst.GetDBEngineVersion().String(),
				AccessSource:    "DAIR",
				ProjectName:     utils.StringRef(inst.GetProjectId()),
				Tags:            targetTags,
				AccountId:       utils.StringRef(accountId),
				CreateTime:      utils.StringRef(inst.GetCreateTime()),
				InstanceType:    model.InstanceType_DAIR,
				LinkType:        model.LinkType_Volc,
			}
		}).ToSlice(&resp.InstanceList)
	log.Info(ctx, "dairImpl ListInstance resp len: %v", len(resp.InstanceList))
	return resp, nil
}

func (m *dairImpl) selfDescribeDBInstances(ctx context.Context, req *dairModel.DescribeInstancesReq) (*dairModel.DescribeInstancesResp, error) {
	log.Info(ctx, "dairImpl selfDescribeDBInstances req: %v", utils.Show(req))
	desResp := &dairModel.DescribeInstancesResp{}
	if err := m.dair.Get().Call(ctx, dairModel.Action_DescribeInstances.String(), req, desResp, client.WithVersion(DAIR_Version_V1)); err != nil {
		log.Warn(ctx, "failed to get rds instances, err=%v", err)
		return nil, err
	}
	return desResp, nil
}

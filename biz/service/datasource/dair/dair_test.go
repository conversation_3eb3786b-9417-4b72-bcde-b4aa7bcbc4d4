package dair

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	dairModel "code.byted.org/infcs/dbw-mgr/gen/dair-mgr/ai-db-api/kitex_gen/modelv1"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"errors"
	"math"
	"testing"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
)

func getDairImpl() *dairImpl {
	provider := &mocks.MockProvider{}
	return &dairImpl{dair: provider}
}

func Test_DescribeDBInstances(t *testing.T) {
	ctx := context.Background()
	req := &dairModel.DescribeInstancesReq{}
	m := getDairImpl()
	resp := &dairModel.DescribeInstancesResp{
		Total: 1,
		Instances: []*dairModel.Instance{
			{
				InstanceId: "instance-123",
			},
		},
	}

	PatchConvey("Test DescribeDBInstances with nil Offset and Limit", t, func() {
		req.Offset = nil
		req.Limit = nil
		expectedResp := &dairModel.DescribeInstancesResp{Instances: []*dairModel.Instance{}, Total: 0}
		Mock(GetMethod(m, "selfDescribeDBInstances")).Return(expectedResp, nil).Build()
		resp, err := m.DescribeDBInstances(ctx, req)
		So(resp, ShouldEqual, expectedResp)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test DescribeDBInstances with valid Offset and Limit", t, func() {
		req.Offset = utils.Int32Ref(0)
		req.Limit = utils.Int32Ref(10)
		expectedResp := &dairModel.DescribeInstancesResp{Instances: []*dairModel.Instance{}, Total: 100}
		Mock(GetMethod(m, "selfDescribeDBInstances")).Return(expectedResp, nil).Build()
		resp, err := m.DescribeDBInstances(ctx, req)
		So(resp.Total, ShouldEqual, 100)
		So(len(resp.Instances), ShouldEqual, 0)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test DescribeDBInstances with FormatLowerPageInfo error", t, func() {
		req.Offset = utils.Int32Ref(0)
		req.Limit = utils.Int32Ref(10)
		Mock((*dairImpl).selfDescribeDBInstances).Return(resp, nil).Build()
		resp, err := m.DescribeDBInstances(ctx, req)
		So(resp, ShouldNotBeNil)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test DescribeDBInstances with selfDescribeDBInstances error", t, func() {
		req.Offset = utils.Int32Ref(0)
		req.Limit = utils.Int32Ref(10)
		Mock(GetMethod(m, "selfDescribeDBInstances")).Return(nil, errors.New("self describe db instances error")).Build()
		resp, err := m.DescribeDBInstances(ctx, req)
		So(resp, ShouldBeNil)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test DescribeDBInstances with multiple pages", t, func() {
		req.Offset = utils.Int32Ref(0)
		req.Limit = utils.Int32Ref(10)
		firstPageResp := &dairModel.DescribeInstancesResp{Instances: []*dairModel.Instance{{InstanceId: "1"}}, Total: 20}
		mock1 := Mock(GetMethod(m, "selfDescribeDBInstances")).Return(firstPageResp, nil).Build()
		defer mock1.UnPatch()
		resp, err := m.DescribeDBInstances(ctx, req)
		So(resp.Total, ShouldEqual, 20)
		So(len(resp.Instances), ShouldEqual, 1)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test DescribeDBInstances with total reset", t, func() {
		req.Offset = utils.Int32Ref(0)
		req.Limit = utils.Int32Ref(10)
		expectedResp := &dairModel.DescribeInstancesResp{Instances: []*dairModel.Instance{}, Total: int32(math.MaxInt32)}
		Mock(GetMethod(m, "selfDescribeDBInstances")).Return(expectedResp, nil).Build()
		resp, err := m.DescribeDBInstances(ctx, req)
		So(resp.Total, ShouldEqual, 0)
		So(len(resp.Instances), ShouldEqual, 0)
		So(err, ShouldBeNil)
	})
}

func Test_ListInstance(t *testing.T) {
	ctx := context.Background()
	req := &datasource.ListInstanceReq{
		LinkType:     shared.Volc,
		PageNumber:   1,
		PageSize:     10,
		InstanceName: "test-instance",
		InstanceId:   "test-instance-id",
		TenantId:     "test-tenant-id",
	}
	m := getDairImpl()

	PatchConvey("Test ListInstance with non-Volc LinkType", t, func() {
		req.LinkType = shared.ByteCloud
		resp, err := m.ListInstance(ctx, req)
		So(resp, ShouldBeNil)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test ListInstance with Volc LinkType", t, func() {
		req.LinkType = shared.Volc
		PatchConvey("Test DescribeDBInstances failed", func() {
			Mock((*dairImpl).DescribeDBInstances).Return(nil, errors.New("describe db instances failed")).Build()
			resp, err := (&dairImpl{}).ListInstance(ctx, req)
			So(resp, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})
		PatchConvey("Test DescribeDBInstances success", func() {
			desResp := &dairModel.DescribeInstancesResp{
				Total: 1,
				Instances: []*dairModel.Instance{
					{
						InstanceId:     "test-instance-id",
						InstanceName:   "test-instance",
						InstanceStatus: dairModel.InstanceStatus_Running,
						ZoneIds:        []string{"test-zone"},
						ProjectId:      "test-project-id",
						InstanceTags: []*dairModel.Tag{
							{
								Key:   "test-key",
								Value: utils.StringRef("test-value"),
							},
						},
						AccountId:  "test-account-id",
						CreateTime: "2023-01-01T00:00:00Z",
					},
				},
			}
			Mock((*dairImpl).DescribeDBInstances).Return(desResp, nil).Build()
			resp, err := m.ListInstance(ctx, req)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 1)
			So(len(resp.InstanceList), ShouldEqual, 1)
			So(resp.InstanceList[0].InstanceId, ShouldEqual, utils.StringRef("test-instance-id"))
			So(resp.InstanceList[0].InstanceName, ShouldEqual, utils.StringRef("test-instance"))
			So(resp.InstanceList[0].InstanceStatus, ShouldEqual, "Running")
			So(resp.InstanceList[0].Zone, ShouldEqual, "test-zone")
			So(resp.InstanceList[0].ProjectName, ShouldEqual, utils.StringRef("test-project-id"))
			So(resp.InstanceList[0].Tags[0].Key, ShouldEqual, "test-key")
			So(resp.InstanceList[0].Tags[0].Value, ShouldEqual, "test-value")
			So(resp.InstanceList[0].AccountId, ShouldEqual, utils.StringRef("test-account-id"))
			So(resp.InstanceList[0].CreateTime, ShouldEqual, utils.StringRef("2023-01-01T00:00:00Z"))
			So(err, ShouldBeNil)
		})
	})
}

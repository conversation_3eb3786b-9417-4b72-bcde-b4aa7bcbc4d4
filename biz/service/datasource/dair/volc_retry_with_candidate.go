package dair

import (
	"context"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
)

func retryIfWhiteListNotReady(orig datasource.DataSourceService) datasource.DataSourceService {
	wrapper := datasource.NewDataSourceServiceDecorator(orig)

	whiteListNotReady := func(err error) bool {
		return err != nil && strings.Contains(err.Error(), `client ip is not in whitelist`)
	}

	wrapper.DecorateListDatabases(func(next datasource.ListDatabasesFunc) datasource.ListDatabasesFunc {
		return func(ctx context.Context, req *datasource.ListDatabasesReq) (ret *datasource.ListDatabasesResp, err error) {
			for i := 0; i < 3; i++ {
				if ret, err = next(ctx, req); err != nil && whiteListNotReady(err) {
					time.Sleep(time.Second)
				} else {
					break
				}
			}
			return
		}
	})

	/* retry delete white ip list */
	wrapper.DecorateRemoveWhiteList(func(next datasource.RemoveWhiteListFunc) datasource.RemoveWhiteListFunc {
		return func(ctx context.Context, g string, ds *shared.DataSource, wlID string) (err error) {
			for i := 0; i < 3; i++ {
				if err = next(ctx, g, ds, wlID); err != nil {
					time.Sleep(time.Second)
				} else {
					break
				}
			}
			return
		}
	})
	return wrapper.Export()
}

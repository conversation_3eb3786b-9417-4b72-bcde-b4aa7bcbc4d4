package datasource

import (
	"database/sql"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
)

type CommentState int

const (
	CommentNonState CommentState = iota
	CommentChar1
	CommentChar2
	CommentChar3
	Comment
	PsmPsm
	PsmIP
	PsmPid
)

const (
	SyntaxCn                 = "语法检查"
	PermissionCn             = "权限检查"
	ExplainCn                = "Explain检查"
	SecurityRuleCn           = "安全规则检查"
	OnlineDDLDryCn           = "OnlineDDL 条件检查"
	OnlineDDLOriginalTableCn = "表信息检查"
	DtsTaskCn                = "DTS任务检查"
	UniqueKeyCn              = "唯一键检查"
)

type ListInstanceReq struct {
	Type                  shared.DataSourceType
	LinkType              shared.LinkType
	PageNumber            int32
	PageSize              int32
	InstanceName          string
	InstanceId            string
	RegionId              string
	InstanceStatus        string
	DbwStatus             model.DbwStatus
	DBEngineVersion       string
	ZoneId                string
	TenantId              string
	CreateTimeStart       string
	CreateTimeEnd         string
	SubInstanceType       string // 如查询mongo实例列表的时候需要配置(分片集、副本级)
	Tags                  []*model.TagObject
	ProjectName           string
	OwndInstancePrivilege bool
	SecurityGroupId       string
	ControlMode           int
	Query                 string
	UserId                string // 字节云使用
	Favor                 bool   // 字节云使用
	Owned                 bool   // 字节云使用
}

type ListInstanceResp struct {
	Total        int64
	InstanceList []*model.InstanceInfo
}

type ListDatabasesReq struct {
	Source         *shared.DataSource
	Offset, Limit  int64
	Keyword        string
	EnableSystemDB bool
}

type ListDatabasesResp struct {
	Total int64
	Items []*shared.DatabaseInfo
}

type ListInstancePodsReq struct {
	Type       shared.DataSourceType
	LinkType   shared.LinkType
	InstanceId string
}
type ListInstancePodsResp struct {
	Total int32
	Data  []*shared.KubePod
}

type DescribeInstanceAddressReq struct {
	Type       shared.DataSourceType
	LinkType   shared.LinkType
	InstanceId string
	NodeType   string
	RegionId   string
}

type DescribeInstanceAddressResp struct {
	NodeId    string
	IP        string
	Port      int32
	NodeType  string
	Component string
}

type ListTablesReq struct {
	Source              *shared.DataSource
	DB, Keyword, Schema string
	Offset, Limit       int64
	Filters             []string
}

type ListTablesResp struct {
	Total  int64
	Items  []string
	Tables []*Table
}

type Table struct {
	Name    string `gorm:"column:TABLE_NAME"`
	Schema  string `gorm:"column:TABLE_SCHEMA"`
	Comment string `gorm:"column:TABLE_COMMENT"`
}

type ListSchemaTablesReq struct {
	Source              *shared.DataSource
	DB, Keyword, Schema string
	Offset, Limit       int64
	Filters             []string
}

type ListSchemaTablesResp struct {
	Total  int64
	Items  []string
	Tables []*SchemaTable
}

type SchemaTable struct {
	TableName   string `gorm:"column:table_name;type:varchar(128);NOT NULL"`
	TableSchema string `gorm:"column:table_schema;type:varchar(128);NOT NULL"`
}

type DescribeTableReq struct {
	Source    *shared.DataSource
	DB, Table string
}

type DescribeTableResp struct {
	Comment     string
	Columns     []*shared.TableInfo_ColumnInfo
	Indexs      []*shared.TableInfo_IndexInfo
	ForeignKeys []*shared.TableInfo_ForeignKeyInfo
	Option      shared.TableInfo_TableOptionInfo
	Definition  string
	HTAPOption  *shared.TableInfo_HTAPOptionInfo
}

type ListTablesInfoReq struct {
	Source   *shared.DataSource
	Database string
}

type ListTablesInfoResp struct {
	Tables []*TableInfo
}

type TableInfo struct {
	Name        string
	Columns     []*shared.TableInfo_ColumnInfo
	Indexes     []*shared.TableInfo_IndexInfo
	ForeignKeys []*shared.TableInfo_ForeignKeyInfo
	Option      *shared.TableInfo_TableOptionInfo
	Definition  string
	Comment     string
}

type DescribePgTableReq struct {
	Source            *shared.DataSource
	DB, Schema, Table string
}

type DescribePgTableResp struct {
	Option                shared.PgTableInfo_TableOptionInfo
	Columns               []*shared.PgTableInfo_ColumnInfo
	Indexs                []*shared.PgTableInfo_IndexInfo
	ForeignReferenceInfo  []*shared.PgTableInfo_ForeignReferenceInfo
	ExcludeConstraintInfo []*shared.PgTableInfo_ExcludeConstraintInfo
	UniqueConstraintInfo  []*shared.PgTableInfo_UniqueConstraintInfo
	CheckConstraintInfo   []*shared.PgTableInfo_CheckConstraintInfo
	//InheritsInfo          []*shared.PgTableInfo_InheritsInfo
	//SimilarityInfo        []*shared.PgTableInfo_SimilarityInfo
}

type ListViewsReq struct {
	Source              *shared.DataSource
	DB, Keyword, Schema string
	Offset, Limit       int64
}

type ListViewsResp struct {
	Total int64
	Items []string
}

type DescribeViewReq struct {
	Source   *shared.DataSource
	DB, View string
}

type DescribeViewResp struct {
	ViewInfo *shared.ViewInfo
}

type DescribeFunctionReq struct {
	Source       *shared.DataSource
	DB, Function string
}

type DescribeFunctionResp struct {
	*shared.FunctionInfo
}

type DescribeProcedureReq struct {
	Source        *shared.DataSource
	DB, Procedure string
}

type DescribeProcedureResp struct {
	*shared.ProcedureInfo
}

type ListFunctionsReq struct {
	Source              *shared.DataSource
	DB, Keyword, Schema string
	Offset, Limit       int64
}

type ListFunctionsResp struct {
	Total int64
	Items []string
}

type Function struct {
	Name   string
	Schema string
}

type ListProceduresReq struct {
	Source        *shared.DataSource
	DB, Keyword   string
	Offset, Limit int64
}

type ListProceduresResp struct {
	Total int64
	Items []string
}

type ListTriggersReq struct {
	Source              *shared.DataSource
	DB, Keyword, Schema string
	Offset, Limit       int64
}

type ListTriggersResp struct {
	Total int64
	Items []string
}

type DescribeTLSConnectionInfoReq struct {
	RegionId   string
	InstanceId string
	Type       shared.DataSourceType
}

type DescribeTLSConnectionInfoResp struct {
	Endpoint string
	TopicID  string
}

type DescribeEventReq struct {
	Source   *shared.DataSource
	DB, Name string
}

type DescribeEventResp struct {
	*shared.EventInfo
}

type ListEventsReq struct {
	Source        *shared.DataSource
	DB, Keyword   string
	Offset, Limit int64
}

type ListEventsResp struct {
	Total int64
	Items []string
}

type ChangeDBReq struct {
	Source *shared.DataSource
	DB     string
}

type ChangeDBResp struct {
	DB string
}

type DescribeTriggerReq struct {
	Source      *shared.DataSource
	DB, Trigger string
}

type DescribeTriggerResp struct {
	TriggerInfo *shared.TriggerInfo
}

type CreateAccountReq struct {
	DSType          shared.DataSourceType
	InstanceId      string
	AccountName     string
	AccountPassword string
	AccountType     string
	RegionId        string
}

type ResetAccountReq struct {
	DSType          shared.DataSourceType
	InstanceId      string
	AccountName     string
	AccountPassword string
}

type DescribeAccountsReq struct {
	DSType      shared.DataSourceType
	InstanceId  string
	AccountName string
	PageSize    int32
	PageNumber  int32
	// for mongo
	AuthDB string
}

// FIXME will deprecate
type DescribeAccountResp struct {
	Total        int32
	AccountsInfo []*rdsModel_v2.AccountsInfoObject
}

type DescribeAccountResp2 struct {
	Total        int32
	AccountsInfo []*AccountInfo
}

type AccountInfo struct {
	AccountName string
	AccountType string
	Privileges  []*AccountPrivilege
	AuthDB      string
}

type AccountPrivilege struct {
	DBName    string
	Privilege string
}

type ModifyAccountPrivilegeReq struct {
	DSType                      shared.DataSourceType
	InstanceId                  string
	AccountName                 string
	ModifyAccountPrivilegesInfo []*ModifyAccountPrivilegeInfo
}

type ModifyAccountPrivilegeInfo struct {
	DBName          string
	ActionType      string
	Privilege       string
	PrivilegeCustom *string
}
type GrantAccountPrivilegeReq struct {
	DSType              shared.DataSourceType
	InstanceId          string
	AccountName         string
	DBName              string
	AccountPrivilege    string
	AccountPrivilegeStr string
	AccessIP            string
	TableNames          string
}

type DeleteAccountReq struct {
	DSType      shared.DataSourceType
	InstanceId  string
	AccountName string
	RegionId    string
}

type ListDatabasesWithAccountReq struct {
	DSType     shared.DataSourceType
	InstanceId string
	DBName     string
	PageSize   int32
	PageNumber int32
}

type ListDatabasesWithAccountResp struct {
	Total   int32
	DBInfos []*rdsModel.DBInfo
}

type GetAdviceReq struct {
	Source *shared.DataSource
	DB     string
	Sql    string
}

type GetAdviceResp struct {
	Explains []*shared.AdviceInfo_ExplainInfo
}

type ListCharsetsReq struct {
	Source        *shared.DataSource
	Keyword       string
	Offset, Limit int64
}

type ListCharsetsResp struct {
	Total int64
	Items []string
}

type ListCollationsReq struct {
	Source        *shared.DataSource
	Keyword       string
	Offset, Limit int64
}

type ListCollationsResp struct {
	Total int64
	Items []string
}

type DescribeDialogInfosReq struct {
	Source        *shared.DataSource
	Offset, Limit int64
	QueryFilter   *shared.DialogQueryFilter
	InternalUsers []string // 内部账户，不暴露给用户
	InternalIPs   []string // 内部ip，不暴露给用户
	Component     string   // 实例内部单元类型:Proxy DBEngine
}

type DescribeDialogInfosResp struct {
	DialogStatistics *shared.DialogStatistics
	DialogDetails    *shared.DialogDetails
}
type DescribeCurrentConnsReq struct {
	Source      *shared.DataSource
	QueryFilter *shared.DialogQueryFilter
	Component   string // Proxy DBEngine
}

type DescribeCurrentConnsResp struct {
	ConnStatistics *shared.ConnStatistics
	ConnDetails    *shared.ConnDetails
}

type DescribeDialogDetailsReq struct {
	Source        *shared.DataSource
	Offset, Limit int64
	QueryFilter   *shared.DialogQueryFilter
	InternalUsers []string // 内部账户，不暴漏给用户
	InternalIPs   []string // 内部ip，不暴漏给用户
}

type DescribeDialogDetailsResp struct {
	DialogDetails []*shared.DialogDetail
	Total         int32
}

type DescribeDialogStatisticsReq struct {
	Source        *shared.DataSource
	InternalUsers []string // 内部账户，不暴漏给用户
	InternalIPs   []string // 内部ip，不暴露给用户
	Component     string   // 实例内部单元类型:Proxy DBEngine
	QueryFilter   *shared.DialogQueryFilter
	TopN          int32
}

type DescribeDialogStatisticsResp struct {
	DialogStatistics *shared.DialogStatistics
}

type DescribeEngineStatusReq struct {
	Source *shared.DataSource
}

type DescribeEngineStatusResp struct {
	EngineStatus *shared.EngineStatus
}

type KillProcessReq struct {
	Source     *shared.DataSource
	ProcessIDs []string
	NodeId     string
	ShardId    string
	Region     string
}

type KillProcessResp struct {
	FailInfoList []*shared.KillFailInfo
}

/* trx and lock*/
type DescribeTrxAndLocksReq struct {
	Source                    *shared.DataSource
	TrxStatus, LockStatus     string
	TrxExecTime               int32
	SortParam, Order, ShardId string
	NodeIds                   []string
	SearchParam               *model.TrxQueryFilter
}

type DescribeTrxAndLocksResp struct {
	Result *shared.DescribeTrxAndLocksInfo
}

type DescribeLockCurrentWaitsReq struct {
	Source                    *shared.DataSource
	SortParam, Order, ShardId string
	NodeIds                   []string
	SearchParam               *model.WaitLockQueryFilter
}

type DescribeLockCurrentWaitsResp struct {
	Result []*model.DescribeLockCurrentWaitsDetail
	Total  int32
}

type DescribeDeadlockReq struct {
	Source               *shared.DataSource
	InstanceId, TenantId string
	NodeId               []string
}

type DescribeDeadlockResp struct {
	*shared.DescribeDeadlockInfo
}

type DescribeDeadlockDetectReq struct {
	Source *shared.DataSource
}

type DescribeDeadlockDetectResp struct {
	*shared.DescribeDeadlockDetectInfo
}

type GetMetricUsageReq struct {
	DSType                         shared.DataSourceType
	InstanceId, TenantId, RegionId string
	PrimaryNodeId                  string
	NodeIds                        []string
	NodeId                         *string
	DiagType                       *model.DiagType
	DBName                         *string
	Intervals                      int32
	StartTime, EndTime             time.Time
}

type GetMonitorByMetricReq struct {
	DSType                         shared.DataSourceType
	InstanceId, TenantId, RegionId string
	Interval                       int32
	NodeIds                        []string
	StartTime, EndTime             time.Time
}

type GetInstanceDetailReq struct {
	DSType     shared.DataSourceType
	InstanceId string
}

type GetInstanceDetailResp struct {
	BasicInfo *BasicInfoObject
}

type BasicInfoObject struct {
	NodeSpec        string
	DBEngineVersion string
}

type GetMaxConnectionsReq struct {
	DSType     shared.DataSourceType
	InstanceId string
}

type GetMetricUsageResp struct {
	Max, Min, Avg float64
	Unit          string
	Name          string
}

type GetDiskAvailableDaysReq struct {
	DSType               shared.DataSourceType
	InstanceId, TenantId string
}

type GetDiskFutureSizeReq struct {
	DSType               shared.DataSourceType
	InstanceId, TenantId string
}

type GetLatestDiskUsageReq struct {
	DSType     shared.DataSourceType
	InstanceId string
}

type GetDiskAvailableDaysResp struct {
	AvailableDays int
}

type GetDiskFutureSizeResp struct {
	FutureSize float64
}

type ListCollectionsReq struct {
	Source        *shared.DataSource
	DB, Keyword   string
	Offset, Limit int32
}

type ListCollectionsResp struct {
	Total int32
	Items []string
}

type IndexInfo struct {
	Ns                 string                 `bson:"ns,omitempty"`
	Name               string                 `bson:"name,omitempty"`
	Background         bool                   `bson:"background,omitempty"`
	Unique             bool                   `bson:"unique,omitempty"`
	Sparse             bool                   `bson:"sparse,omitempty"`
	ExpireAfterSeconds int64                  `bson:"expireAfterSeconds,omitempty"`
	Key                map[string]interface{} `bson:"key,omitempty"`
}

type ListIndexesReq struct {
	Source                *shared.DataSource
	DB, Col, Keyword, Idx string
	Offset, Limit         int32
}

type ListIndexesResp struct {
	Total   int32
	Items   []string
	IdxInfo *IndexInfo
}

type ListMongoDBsReq struct {
	Source        *shared.DataSource
	Keyword       string
	Offset, Limit int32
}

type ListMongoDBsResp struct {
	*shared.MongoDBsInfo
}

type ListInstanceNodesReq struct {
	DSType     shared.DataSourceType
	InstanceId string
	ShardId    string
	NodeType   *model.RdsNodeType
	RegionId   string
}

type ListInstanceNodesResp struct {
	Nodes     []*model.NodeInfoObject
	AccountId string
}

type ListInstanceNodesOriResp struct {
	NodesInfo []*InstanceNodeInfo
}

type InstanceNodeInfo struct {
	NodeID     string
	PodName    string
	Role       string
	Zone       string
	NodeStatus string
}

type DescribeDBProxyConfigReq struct {
	InstanceId string
	Type       shared.DataSourceType
}
type DescribeDBProxyConfigResp struct {
	IsProxyEnable bool
}

type DescribeSQLCCLConfigReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type DescribeSQLCCLConfigResp struct {
	SQLConcurrencyControlStatus bool
}

type ModifySQLCCLConfigReq struct {
	InstanceId                  string
	Type                        shared.DataSourceType
	EnableSqlConcurrencyControl bool
}
type ModifySQLCCLConfigResp struct {
}

type CCLRuleInfo struct {
	RuleID           int64
	UserID           string
	HostName         string
	SchemaName       string
	TableName        string
	PartitionName    string // veDB 分片名
	Keywords         string
	SqlType          string
	State            string
	Ordered          string
	MaxQueueSize     int64
	WaitTimeout      int64
	ConcurrencyCount int32
	Rejected         int64
}
type VeDBCCLRuleItem struct {
	RuleID           int64  `gorm:"column:ID"`
	HostName         string `gorm:"column:HOST"`
	UserName         string `gorm:"column:USER"`
	SchemaName       string `gorm:"column:SCHEMA"`
	TableName        string `gorm:"column:TABLE"`
	PartitionName    string `gorm:"column:PARTITION"` // veDB 分片名
	Keywords         string `gorm:"column:KEYWORDS"`
	SqlType          string `gorm:"column:TYPE"`
	State            string `gorm:"column:STATE"`
	Ordered          string `gorm:"column:ORDERED"`
	WaitTimeout      int64  `gorm:"column:WAIT_TIMEOUT"`
	ConcurrencyCount int32  `gorm:"column:CONCURRENCY_COUNT"`
	Rejected         int64  `gorm:"column:REJECTED"`
	Matched          int64  `gorm:"column:MATCHED"`
}
type KillRuleInfo struct {
	RuleID         int64  `json:"rule_id"`
	User           string `json:"user"`
	ProtectedUsers string `json:"protected_users"`
	Host           string `json:"host"`
	DB             string `json:"db"`
	MaxExecTime    int64  `json:"max_exec_time"`
	NodeType       string `json:"node_type"`
	SqlType        string `json:"sql_type"`
	Keyword        string `json:"keyword"`
	FingerPrint    string `json:"finger_print"`
	SqlText        string `json:"sql_text"`
}

type ProxyThrottleRuleInfo struct {
	RuleID              int64
	UserID              string
	Keywords            string
	SqlType             string
	State               string
	ThrottleType        string
	ThrottleTarget      string
	EndpointID          string
	EndpointType        string
	ThrottleDB          string
	ThrottleHost        string
	ThrottleSqlText     string
	ThrottleFingerPrint string
	ThrottleThreshold   int32
	Rejected            int64
	GroupIds            string
	Duration            int64
	ThrottleObjId       string
}

type AddSQLCCLRuleReq struct {
	InstanceId string
	Type       shared.DataSourceType
	CCLRule    *CCLRuleInfo
}

type AddSQLCCLRuleResp struct {
}

type ModifyProxyThrottleRuleReq struct {
	InstanceId        string
	Type              shared.DataSourceType
	ProxyThrottleRule *ProxyThrottleRuleInfo
	Action            string
	RegionId          string
}

type ModifyProxyThrottleRuleResp struct {
}

type DeleteSQLCCLRuleReq struct {
	InstanceId string
	Type       shared.DataSourceType
	CCLRule    *CCLRuleInfo
}

type DeleteSQLCCLRuleResp struct {
}

type FlushSQLCCLRuleReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type FlushSQLCCLRuleResp struct {
}

type ListSQLCCLRulesReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type ListSQLCCLRulesResp struct {
	CCLRules []*CCLRuleInfo
}

type DescribeSqlFingerPrintOrKeywordsReq struct {
	Type       shared.DataSourceType
	LinkType   model.LinkType
	SqlText    string
	ObjectType string // Keyword or Fingerprint
}

type DescribeSqlFingerPrintOrKeywordsResp struct {
	FingerPrint string
	Keywords    string
}
type DescribeSqlTypeReq struct {
	Type    shared.DataSourceType
	SqlText string
}

type DescribeSqlTypeResp struct {
	SqlType string
}
type ModifySqlConcurrencyControlRuleReq struct {
	Type      shared.DataSourceType
	LinkType  model.LinkType
	Threshold int64
}

type ModifySqlConcurrencyControlRuleResp struct {
}

type DescribeDBInstanceDetailReq struct {
	InstanceId string
	Type       shared.DataSourceType
	RegionId   string
}

type GetInstanceSlaveAddressReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type GetInstanceSlaveAddressResp struct {
	Address []string
}

type DescribeDBInstanceDetailResp struct {
	InstanceId         string
	InstanceName       string
	InstanceStatus     string
	RegionId           string
	ZoneId             string
	DBEngine           string
	DBEngineVersion    string
	InstanceType       string
	VCPU               int32
	Memory             int32
	StorageSpace       int64
	ProjectName        string
	VPCID              string
	NodeInfos          []*NodeInfo
	ConnectionsInfos   []*ConnectionInfo
	Endpoints          []*Endpoint
	DBEngineSubVersion string
	Shared             bool
	NodeSpec           string
	NoAuthMode         *model.NoAuthMode
	AllowListInfo      AllowListInfo
	SpecFamily         string
	ChargeType         string // 计算计费类型
	NodeNumber         int32
	MasterInstanceId   string
	ImportanceLevel    string
	SensitiveLevel     int32
	StorageType        string
}

type NodeInfo struct {
	NodeId    string
	RegionId  string
	ZoneId    string
	NodeIP    string
	Endpoints []*Endpoint
}
type EndpointMode int

type DescribeDBInstanceSpecReq struct {
	InstanceType shared.DataSourceType
}

type DescribeDBInstanceSpecResp struct {
	Total     int32
	NodeSpecs []*NodeSpecObject
}

type NodeSpecObject struct {
	NodeSpec          string
	VCPU              int32
	Memory            int32
	Connection        int32
	SpecFamily        string
	PrePaidMinStorage int32
	PrePaidMaxStorage int32
	MaxIops           int32
}

func (e EndpointMode) String() string {
	switch e {
	case ReadOnly:
		return "ReadOnly"
	case ReadWrite:
		return "ReadWrite"
	case Default:
		return "Default"
	default:
		return ""
	}
}

const (
	ReadWrite EndpointMode = iota
	ReadOnly
	Default
)

type Endpoint struct {
	Mode                     EndpointMode
	ProxyPort                string
	Domain                   string
	EndpointType             string
	EndpointId               string
	EndpointName             string
	EnableReadWriteSplitting string
}

type ConnectionInfo struct {
	EndpointId string
	Address    []*AddressObject
}

type NodeAddressObj struct {
	NodeId      string
	NodeType    string
	IPAddress   string
	Ipv6Address string
	Port        string
	AdminPort   string
}
type AddressObject struct {
	NetworkType   NetworkType
	Domain        string
	IPAddress     string
	Port          string
	SubnetId      string
	EipId         string
	DNSVisibility bool
}

type NetworkType int64

const (
	Private NetworkType = iota
	Public
	Inner
	Ingress
	Carma
	PublicZone
	Invalid
)

type AllowListInfo struct {
	AllowListVersion *string
}

type DescribeDBInstanceClusterReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type DescribeDBInstanceClusterResp struct {
	MultiAZ          bool
	AzClusterMap     map[string]string
	NodePool2Cluster map[string]string
}

type DescribeDBInstanceAuditCollectedPodReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type DescribeDBInstanceAuditCollectedPodResp struct {
	Port       []string
	CpuRequest string
}
type OpenDBInstanceAuditLogReq struct {
	InstanceId        string
	Type              shared.DataSourceType
	AuditLogTypes     []string
	AuditLogComponent *string
}
type OpenDBInstanceAuditLogResp struct {
}

type CloseDBInstanceAuditLogReq struct {
	InstanceId string
	Type       shared.DataSourceType
}
type CloseDBInstanceAuditLogResp struct {
}

type CheckDBInstanceAuditLogStatusReq struct {
	InstanceId string
	Type       shared.DataSourceType
}
type CheckDBInstanceAuditLogStatusResp struct {
	Can    bool
	Enable *bool
}

type DescribeTableSpaceReq struct {
	Source     *shared.DataSource
	Product    int64
	InstanceId string
	RegionId   string
	Limit      int32
	Offset     int32
	OrderItem  string
	OrderRule  string
	Database   string
	TableName  string
	TableType  string
}

type DescribeTableInfoReq struct {
	Source     *shared.DataSource
	InstanceId string
	Database   string
	Schema     string
	Table      string
	Limit      int32
	Offset     int32
}

type DescribeDBInstanceSSLReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type DescribeDBInstanceSSLResp struct {
	InstanceId    string
	SSLEnable     bool
	IsValid       bool
	SSLExpireTime string
}

type CreateFreeLockCorrectOrderReq struct {
	InstanceId string
	Type       shared.DataSourceType
	TableName  string
	DBName     string
	ExecSQL    string
	Comment    string
	DryRun     bool

	OrderConfig *OrderConfig
	GhostConfig *GhostConfig
}

// OrderConfig 这里是一些差异类的工单配置
type OrderConfig struct {
	//sharding config
	AccountName           string
	ScheduleTime          string
	ExecuteNow            bool
	IsCreateShardingTable bool
	ShardingKeyName       string
	ShardingKeyType       string
}

type GhostConfig struct {
	// ghost config
	KillLongTxn          *bool
	RenameDisallowWindow *string
	RplDelayCheckRule    *string
}

type CreateFreeLockCorrectOrderResp struct {
	OrderId string
}

type CreateFreeLockCorrectOrderDryRunReq struct {
	InstanceId   string
	InstanceType shared.DataSourceType
	TableName    string
	DBName       string
	ExecSQL      string
	UserName     *string
	Password     *string
	Comment      string
}

type CreateFreeLockCorrectOrderDryRunResp struct {
	DryRunSuccess bool
	ErrorCode     string
	Reason        string
}

type DescribeFreeLockCorrectOrdersReq struct {
	InstanceId      string
	Type            shared.DataSourceType
	SqlTaskId       string
	OrderId         string
	OrderStatus     *model.SqlTaskStatus
	CreateTimeStart int32
	CreateTimeEnd   int32
	PageNumber      int32
	PageSize        int32
}

type StopFreeLockCorrectOrdersReq struct {
	InstanceType shared.DataSourceType
	InstanceId   string
	SqlTaskId    string
	OrderId      string
}

type PreCheckFreeLockCorrectOrdersReq struct {
	InstanceType shared.DataSourceType
	InstanceId   string
	DBName       string
	ExecSQL      string
}

type PreCheckFreeLockCorrectOrdersResp struct {
	DryRunSuccess bool
	ErrorCode     string
	Reason        string
}

type DescribeFreeLockCorrectOrdersResp struct {
	Total int32
	Datas []*model.SqlTask
}

type GetDBInnerAddressReq struct {
	Source *shared.DataSource
}

type GetDBInnerAddressResp struct {
	Source *shared.DataSource
}

type ExecuteDQLReq struct {
	Source  *shared.DataSource
	Command string
	Columns []string
}

type ExecuteDQLResp struct {
	ColumnName  []string
	ColumnValue []string
}

type ExecuteDMLAndGetAffectedRowsReq struct {
	Source  *shared.DataSource
	Command string
}

//type ExecuteDMLAndGetAffectedRowsResp struct {
//	rows int64
//}

type ExplainCommandReq struct {
	Source  *shared.DataSource
	Command string
}

type ExplainCommandResp struct {
	Command      []*ExplainCommandResult
	MssqlCommand []*ExplainMssqlCommandResult
	PgCommand    []*ExplainPgCommandResult
}

type ExplainCommandResult struct {
	Id           int32  `gorm:"column:id;type:int;NOT NULL"`
	SelectType   string `gorm:"column:select_type;type:varchar(64);NOT NULL"`
	Table        string `gorm:"column:table;type:varchar(64);NOT NULL"`
	Partitions   string `gorm:"column:partitions;type:varchar(64);NOT NULL"`
	Type         string `gorm:"column:type;type:varchar(64);NOT NULL"`
	PossibleKeys string `gorm:"column:possible_keys;type:varchar(64);NOT NULL"`
	Key          string `gorm:"column:key;type:varchar(64);NOT NULL"`
	KeyLen       string `gorm:"column:key_len;type:varchar(64);NOT NULL"`
	Ref          string `gorm:"column:ref;type:varchar(64);NOT NULL"`
	Rows         string `gorm:"column:rows;type:varchar(64);NOT NULL"`
	Filtered     string `gorm:"column:filtered;type:varchar(64);NOT NULL"`
	Extra        string `gorm:"column:Extra;type:varchar(64);NOT NULL"`
}

type ExplainMssqlCommandResult struct {
	StmtText           string  `gorm:"column:StmtText;type:varchar(256);NOT NULL"`
	StmtId             int     `gorm:"column:StmtId;type:int;NOT NULL"`
	NodeId             int     `gorm:"column:NodeId;type:int;NOT NULL"`
	Parent             int     `gorm:"column:Parent;type:int;NOT NULL"`
	PhysicalOp         string  `gorm:"column:PhysicalOp;type:varchar(64);NOT NULL"`
	LogicalOp          string  `gorm:"column:LogicalOp;type:varchar(64);NOT NULL"`
	Argument           string  `gorm:"column:Argument;type:varchar(64);NOT NULL"`
	DefinedValues      string  `gorm:"column:DefinedValues;type:varchar(64);NOT NULL"`
	EstimateRows       int     `gorm:"column:EstimateRows;type:int;NOT NULL"`
	EstimateIO         float64 `gorm:"column:EstimateIO;type:int;NOT NULL"`
	EstimateCPU        float64 `gorm:"column:EstimateCPU;type:int;NOT NULL"`
	AvgRowSize         int     `gorm:"column:AvgRowSize;type:int;NOT NULL"`
	TotalSubtreeCost   float64 `gorm:"column:TotalSubtreeCost;type:int;NOT NULL"`
	OutputList         string  `gorm:"column:OutputList;type:varchar(64);NOT NULL"`
	Warnings           string  `gorm:"column:Warnings;type:varchar(64);NOT NULL"`
	Type               string  `gorm:"column:Type;type:varchar(64);NOT NULL"`
	Parallel           string  `gorm:"column:Parallel;type:varchar(64);NOT NULL"`
	EstimateExecutions int     `gorm:"column:EstimateExecutions;type:int;NOT NULL"`
}

type ExplainPgCommandResult struct {
}

type ExecuteCCLReq struct {
	Type     shared.DataSourceType
	LinkType shared.LinkType
	Source   *shared.DataSource
	Commend  string
}

type ExecuteCCLResp struct {
	Result string
}

type CCLShowReq struct {
	Type     shared.DataSourceType
	LinkType shared.LinkType
	Source   *shared.DataSource
	Commend  string
}

type CCLShowResp struct {
	Result []*shared.CCLRuleInfo
}
type TopicItem struct {
	MatchedRegex string `json:"matched_regex"`
	TopicID      string `json:"topic_id"`
	Enabled      bool   `json:"enabled"`
}
type SlowLogTopicV2 struct {
	InstanceType string       `json:"instance_type"`
	TopicList    []*TopicItem `json:"topic_list"`
}

type LogTopic struct {
	InstanceType string `json:"instance_type"`
	TopicID      string `json:"topic_id"`
}

type TLSSlowLogTopicV2 struct {
	Topics []*SlowLogTopicV2 `json:"topics"`
}

type TLSSlowLogTopic struct {
	Topics []*LogTopic `json:"topics"`
}

type TLSErrLogTopic struct {
	Topics []*SlowLogTopicV2 `json:"topics"`
}

type TLSDeadlockTopic struct {
	Topics []*LogTopic `json:"topics"`
}

type GetCurrentBandwidthReq struct {
	DSType shared.DataSourceType
	Rule   *entity.AutoScaleRule
}

type InstanceBandwidthInfo struct {
	InstanceId       string
	InstanceSpec     int64 // 到MiB
	IsUnShared       bool  // redis 用
	CurrentBandwidth int
	Status           string
	InstanceType     string // (redis)集群类型
	ArchType         string // (redis)架构类型 标准/集群
	InstanceClass    string // (redis)架构类型 单节点/主备
}

type BandwidthScaleReq struct {
	DSType        shared.DataSourceType
	Rule          *entity.AutoScaleRule
	BandwidthInfo *InstanceBandwidthInfo
	NextBandwidth int
}

type GetMinMaxBandwidthReq struct {
	DSType        shared.DataSourceType
	TenantId      string
	BandwidthInfo *InstanceBandwidthInfo
}

type SecurityRulesCheckReq struct {
	InstanceType     shared.DataSourceType
	InstanceId       string
	TenantId         string
	Source           string
	ParsedSql        string
	SqlExecutionType model.SqlExecutionType
	DataSource       *shared.DataSource
	RuleKey          string
	RuleFunc         string
}

type GetDiskSizeReq struct {
	InstanceType shared.DataSourceType
	InstanceId   string
}

type GetDiskSizeResp struct {
	DiskLogSize       float64
	DiskDataSize      float64
	WalSize           float64 // PG
	DiskBinLogSize    float64 // VeDB, MySQL
	DiskErrorLogSize  float64 // MySQL
	DiskSlowLogSize   float64 // MySQL
	OtherLogSize      float64
	RedoLogSize       float64 // VeDB
	UndoLogSize       float64 // VeDB
	SysUsedStorage    float64 // VeDB
	TmpUsedStorage    float64 // VeDB
	UsedStorage       float64 //  VeDB
	RelayLogSize      float64 // MySQL
	AuditLogSize      float64 // MySQL
	UserDataUsageSize float64 // MySQL
	OtherUsageSize    float64 // MySQL
	IsNew             int32
}

type GetMetricDatapointsReq struct {
	InstanceId  string
	RegionId    string
	NodeId      string
	Type        shared.DataSourceType
	Measurement string
	MetricName  string
	MultiValue  string
	StartTime   time.Time
	EndTime     time.Time
	Period      int32
	MonitorType string
	Value       string
}

type GetMetricDatapointsResp struct {
	DataPoints []*model.DataPoint
}

type ListErrLogsReq struct {
	InstanceId string
	Type       shared.DataSourceType
	Offset     string
	Limit      int32
	StartTime  int32
	EndTime    int32
	LogLevel   []model.ErrLogLevel
	NodeIds    []string
	Keyword    string
}

type ListErrLogsResp struct {
	Total   int32
	Context string
	ErrLogs []*model.ErrLog
}

type DescribeAutoKillSessionConfigReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type DescribeAutoKillSessionConfigResp struct {
	SQLKillStatus  bool
	MaxExecTime    int64
	ProtectedUsers []string
}

type ModifyAutoKillSessionConfigReq struct {
	InstanceId     string
	Type           shared.DataSourceType
	SQLKillStatus  *bool
	MaxExecTime    *int64
	ProtectedUsers []string
}

type ModifyAutoKillSessionConfigResp struct {
}

type DescribeInstanceVersionReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type DescribeInstanceVersionResp struct {
	Version                   string
	MysqlComponentEpicVersion []*rdsModel.ComponentEpicVersion
}

type MySQLProxyDialog struct {
	ProcessID string `json:"Id"`
	User      string `json:"User"`
	Host      string `json:"Host"`
	DB        string `json:"DB"`
	Command   string `json:"Command"` // command 即DB侧的Info
	Time      string `json:"Time"`
	KeepConn  string `json:"Keep Conn"`
	LoginTime string `json:"LoginTime"`
	Info      string `json:"Info"`
	ProxyId   string `json:"ProxyId"`
	Port      string `json:"Port"`
}

type VeDBProxyDialog struct {
	ProcessID string `gorm:"column:Id"`
	User      string `gorm:"column:User"`
	Host      string `gorm:"column:Host"`
	DB        string `gorm:"column:DB"`
	Command   string `gorm:"column:Command"`
	Time      string `gorm:"column:Time"`
	PSM       string `gorm:"column:Last PSM"`
	KeepConn  string `gorm:"column:Keep Conn"`
	LoginTime string `gorm:"column:LoginTime"`
	Info      string `json:"Info"`
	ProxyId   string `json:"ProxyId"`
	ProxyName string `json:"ProxyName"`
	Port      string `json:"Port"`
}
type ShardingProxyDialog struct {
	ProcessID    string `gorm:"column:Id"`
	User         string `gorm:"column:User"`
	Host         string `gorm:"column:Host"`
	DB           string `gorm:"column:DB"`
	Command      string `gorm:"column:Command"`
	Time         string `gorm:"column:Time"`
	PSM          string `gorm:"column:Last PSM"`
	KeepConn     string `gorm:"column:Keep Conn"`
	LoginTime    string `gorm:"column:Login time"`
	Info         string `json:"Info"`
	ProxyId      string `json:"ProxyId"`
	Port         string `json:"Port"`
	EndpointName string `json:"EndpointName"`
}

type ListSchemaReq struct {
	Source      *shared.DataSource
	DB, Keyword string
	//Offset, Limit int64
}

type ListSchemaResp struct {
	Total int64
	Items []string
}

type ListSequenceReq struct {
	Source              *shared.DataSource
	DB, Keyword, Schema string
	Offset, Limit       int64
}

type ListSequenceResp struct {
	Total int64
	Items []string
}

type ListPgCollationsReq struct {
	Source        *shared.DataSource
	Keyword       string
	Offset, Limit int64
}

type ListPgCollationsResp struct {
	Total int64
	Items []string
}

type ListPgUsersReq struct {
	Source        *shared.DataSource
	Keyword       string
	Offset, Limit int64
}

type ListPgUsersResp struct {
	Total int64
	Items []string
}

type ListTableSpacesReq struct {
	Source        *shared.DataSource
	Keyword       string
	Offset, Limit int64
}

type ListTableSpacesResp struct {
	Total int64
	Items []string
}

type PageSearchInfo struct {
	PageSize     int32
	PageNumLower int32
	PageNumUpper int32
}

// GetTableIndexInfoReq 获取表信息
type GetTableIndexInfoReq struct {
	TableName string
	Source    *shared.DataSource
	Command   string
}

type GetTableInfoIndexResp struct {
	TableIndexInfo []*TableIndexInfo
}

type TableIndexInfo struct {
	TableName    string `gorm:"column:TABLE_NAME"`
	IndexName    string `gorm:"column:INDEX_NAME"`
	Nullable     string `gorm:"column:NULLABLE"`
	SeqInIndex   int    `gorm:"column:SEQ_IN_INDEX"`
	IndexType    string `gorm:"column:INDEX_TYPE"`
	ColumnName   string `gorm:"column:COLUMN_NAME"`
	SubPart      string `gorm:"column:SUB_PART"`
	IndexComment string `gorm:"column:INDEX_COMMENT"`
}

// GetIndexValueReq 获取表信息
type GetIndexValueReq struct {
	TableName string
	Source    *shared.DataSource
	Command   string
	Columns   []string
}

type GetIndexValueResp struct {
	TableIndexValue []*TableIndexValue
}

type TableIndexValue struct {
	IndexName  string
	IndexValue string
}

type DescribeInstanceFeaturesReq struct {
	InstanceId string
	Type       shared.DataSourceType
}
type DescribeInstanceFeaturesResp struct {
	Features []model.InstanceFeatureType
}

type DescribeFullSQLLogConfigReq struct {
	InstanceID string

	InstanceType shared.DataSourceType

	RegionId string
	DBName   string
}
type DescribeFullSQLLogConfigResp struct {
	SQLCollectorStatus SQLCollectorStatus
	TLSDomain          string
	TLSProjectId       string
	TLSTopic           string

	BmqCluster string
	BmqTopic   string
}

type SQLCollectorStatus int

const (
	SQLCollectorStatus_Disabled SQLCollectorStatus = 0
	SQLCollectorStatus_Enable   SQLCollectorStatus = 1
)

type ModifyFullSQLLogConfigReq struct {
	InstanceID         string
	DryRun             bool
	SQLCollectorStatus SQLCollectorStatus

	RegionId   string
	DBName     string
	BmqCluster string
	BmqTopic   string

	InstanceType shared.DataSourceType
}

type ModifyFullSQLLogConfigResp struct {
}

// SQLAdvisor
type DescribeInstanceVariablesReq struct {
	Source     *shared.DataSource
	InstanceID string
	TenantID   string
	Region     string
	Variables  []string
}

type DescribeInstanceVariablesResp struct {
	Variables map[string]string
}

type DescribePrimaryKeyRangeReq struct {
	Source     *shared.DataSource
	InstanceID string
	TenantID   string
	Region     string
	DBName     string
	TableName  string
	Columns    []string
}

type DescribePrimaryKeyRangeResp struct {
	PrimaryKeyInfo *PrimaryKeyInfo
}
type PrimaryKeyInfo struct {
	MinNum []*PrimaryKeyValue
	MaxNum []*PrimaryKeyValue
}
type PrimaryKeyValue struct {
	ColumnName string
	Value      string
}

type DescribeSQLAdvisorTableMetaReq struct {
	Source     *shared.DataSource
	InstanceID string
	TenantID   string
	Region     string
	DBName     string
	TableList  []string
}

type DescribeSQLAdvisorTableMetaResp struct {
	Success bool
	Data    []*SQLAdvisorTableMetaData
}

type SQLAdvisorTableMetaData struct {
	Name                 string
	TableInfo            *SQLAdvisorTableInfo
	ColumnInfo           []*SQLAdvisorColumnInfo
	StatisticsInfo       []*SQLAdvisorStatisticsInfo
	InnodbTableStatsInfo *SQLAdvisorInnodbTableStatsInfo
	CreateTableInfo      *SQLAdvisorCreateTableInfo
}

type SQLAdvisorTableInfo struct {
	TableCatalog   string `gorm:"column:TABLE_CATALOG;type:varchar(64);NOT NULL"`
	TableSchema    string `gorm:"column:TABLE_SCHEMA;type:varchar(64);NOT NULL"`
	TableName      string `gorm:"column:TABLE_NAME;type:varchar(64);NOT NULL"`
	TableType      string `gorm:"column:TABLE_TYPE;type:varchar(64);NOT NULL"`
	Engine         string `gorm:"column:ENGINE;type:varchar(64);NOT NULL"`
	Version        string `gorm:"column:VERSION;type:varchar(64);NOT NULL"`
	RowFormat      string `gorm:"column:ROW_FORMAT;type:varchar(64);NOT NULL"`
	TableRows      string `gorm:"column:TABLE_ROWS;type:varchar(64);NOT NULL"`
	AvgRowLength   string `gorm:"column:AVG_ROW_LENGTH;type:varchar(64);NOT NULL"`
	DataLength     string `gorm:"column:DATA_LENGTH;type:varchar(64);NOT NULL"`
	MaxDataLength  string `gorm:"column:MAX_DATA_LENGTH;type:varchar(64);NOT NULL"`
	IndexLength    string `gorm:"column:INDEX_LENGTH;type:varchar(64);NOT NULL"`
	DataFree       string `gorm:"column:DATA_FREE;type:varchar(64);NOT NULL"`
	AutoIncrement  string `gorm:"column:AUTO_INCREMENT;type:varchar(64);NOT NULL"`
	CreateTime     string `gorm:"column:CREATE_TIME;type:varchar(64);NOT NULL"`
	UpdateTime     string `gorm:"column:UPDATE_TIME;type:varchar(64);NOT NULL"`
	CheckTime      string `gorm:"column:CHECK_TIME;type:varchar(64);NOT NULL"`
	TableCollation string `gorm:"column:TABLE_COLLATION;type:varchar(64);NOT NULL"`
	CheckSum       string `gorm:"column:CHECK_SUM;type:varchar(64);NOT NULL"`
	CreateOptions  string `gorm:"column:CREATE_OPTIONS;type:varchar(64);NOT NULL"`
	TableComment   string `gorm:"column:TABLE_COMMENT;type:varchar(64);NOT NULL"`
}

type SQLAdvisorColumnInfo struct {
	TableCatalog           string `gorm:"column:TABLE_CATALOG;type:varchar(64);NOT NULL"`
	TableSchema            string `gorm:"column:TABLE_SCHEMA;type:varchar(64);NOT NULL"`
	TableName              string `gorm:"column:TABLE_NAME;type:varchar(64);NOT NULL"`
	ColumnName             string `gorm:"column:COLUMN_NAME;type:varchar(64);NOT NULL"`
	OrdinalPosition        string `gorm:"column:ORDINAL_POSITION;type:varchar(64);NOT NULL"`
	ColumnDefault          string `gorm:"column:COLUMN_DEFAULT;type:varchar(64);NOT NULL"`
	IsNullable             string `gorm:"column:IS_NULLABLE;type:varchar(64);NOT NULL"`
	DataType               string `gorm:"column:DATA_TYPE;type:varchar(64);NOT NULL"`
	CharacterMaximumLength string `gorm:"column:CHARACTER_MAXIMUM_LENGTH;type:varchar(64);NOT NULL"`
	CharacterOctetLength   string `gorm:"column:CHARACTER_OCTET_LENGTH;type:varchar(64);NOT NULL"`
	NumericPrecision       string `gorm:"column:NUMERIC_PRECISION;type:varchar(64);NOT NULL"`
	CharacterSetName       string `gorm:"column:CHARACTER_SET_NAME;type:varchar(64);NOT NULL"`
	CollationName          string `gorm:"column:COLLATION_NAME;type:varchar(64);NOT NULL"`
	ColumnType             string `gorm:"column:COLUMN_TYPE;type:varchar(64);NOT NULL"`
	ColumnKey              string `gorm:"column:COLUMN_KEY;type:varchar(64);NOT NULL"`
	Extra                  string `gorm:"column:EXTRA;type:varchar(64);NOT NULL"`
	Privileges             string `gorm:"column:PRIVILEGES;type:varchar(64);NOT NULL"`
	ColumnComment          string `gorm:"column:COLUMN_COMMENT;type:varchar(64);NOT NULL"`
	GenerationExpression   string `gorm:"column:GENERATION_EXPRESSION;type:varchar(64);NOT NULL"`
	SrsID                  string `gorm:"column:SRS_ID;type:varchar(64);NOT NULL"`
}

type SQLAdvisorStatisticsInfo struct {
	TableCatalog string `gorm:"column:TABLE_CATALOG;type:varchar(64);NOT NULL"`
	TableSchema  string `gorm:"column:TABLE_SCHEMA;type:varchar(64);NOT NULL"`
	TableName    string `gorm:"column:TABLE_NAME;type:varchar(64);NOT NULL"`
	NonUnique    string `gorm:"column:NON_UNIQUE;type:varchar(64);NOT NULL"`
	IndexSchema  string `gorm:"column:INDEX_SCHEMA;type:varchar(64);NOT NULL"`
	IndexName    string `gorm:"column:INDEX_NAME;type:varchar(64);NOT NULL"`
	SeqInIndex   string `gorm:"column:SEQ_IN_INDEX;type:varchar(64);NOT NULL"`
	ColumnName   string `gorm:"column:COLUMN_NAME;type:varchar(64);NOT NULL"`
	Collation    string `gorm:"column:COLLATION;type:varchar(64);NOT NULL"`
	Cardinality  string `gorm:"column:CARDINALITY;type:varchar(64);NOT NULL"`
	SubPart      string `gorm:"column:SUB_PART;type:varchar(64);NOT NULL"`
	Packed       string `gorm:"column:PACKED;type:varchar(64);NOT NULL"`
	Nullable     string `gorm:"column:NULLABLE;type:varchar(64);NOT NULL"`
	IndexType    string `gorm:"column:INDEX_TYPE;type:varchar(64);NOT NULL"`
	Comment      string `gorm:"column:COMMENT;type:varchar(64);NOT NULL"`
	IndexComment string `gorm:"column:INDEX_COMMENT;type:varchar(64);NOT NULL"`
	IsVisible    string `gorm:"column:IS_VISIBLE;type:varchar(64);NOT NULL"`
	Expression   string `gorm:"column:EXPRESSION;type:varchar(64);NOT NULL"`
}

type SQLAdvisorInnodbTableStatsInfo struct {
	DatabaseName         string `gorm:"column:database_name;type:varchar(64);NOT NULL"`
	TableName            string `gorm:"column:table_name;type:varchar(64);NOT NULL"`
	LastUpdate           string `gorm:"column:last_update;type:varchar(64);NOT NULL"`
	NRows                string `gorm:"column:n_rows;type:varchar(64);NOT NULL"`
	ClusteredIndexSize   string `gorm:"column:clustered_index_size;type:varchar(64);NOT NULL"`
	SumOfOtherIndexSizes string `gorm:"column:sum_of_other_index_sizes;type:varchar(64);NOT NULL"`
}

type SQLAdvisorCreateTableInfo struct {
	Table       string `gorm:"column:Table;type:varchar(64);NOT NULL"`
	CreateTable string `gorm:"column:Create Table;type:varchar(20000);NOT NULL"`
}

type DescribeSampleDataReq struct {
	Source     *shared.DataSource
	InstanceID string
	Region     string
	DbName     string
	TableName  string
	PrimaryKey []string
	Columns    []string
	MinNum     []*PrimaryKeyValue
	MaxNum     []*PrimaryKeyValue
	OrderBy    model.SQLAdvisorOrderBy
	Limit      int32
}

type DescribeSampleDataResp struct {
	Total   int32
	Records []map[string]string
}

type EnsureAccountReq struct {
	Source *shared.DataSource
}

type GetInstancePrimaryNodeIdReq struct {
	Type       shared.DataSourceType
	InstanceID string
}

type GetInstancePrimaryNodeIdResp struct {
	PrimaryNodeID string
}

type EndpointInfo struct {
	EndpointName  string
	EndpointID    string
	EndpointPort  string
	EndpointType  string
	ReadWriteMode string
	NodeID        []string
}

type ShardInfo struct {
	ShardId       string
	GroupId       int32
	LogicShardIds []*model.DBRangeObject
	Nodes         []*model.ShardNodeInfo
	ComponentType model.ComponentType
}

type DescribeDBInstanceShardInfosReq struct {
	InstanceId string
	Type       shared.DataSourceType
	DBName     string
	RegionId   string
}

type DescribeDBInstanceEndpointsReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type DescribeDBInstanceEndpointsResp struct {
	Endpoints []*EndpointInfo
}

type DescribeDBInstanceShardInfosResp struct {
	Shards []*ShardInfo
}

type GetDBServiceTreeMountInfoReq struct {
	InstanceId string
	RegionId   string
	Type       shared.DataSourceType
}

type GetDBServiceTreeMountInfoResp struct {
	DBSTMountInfos []*model.ServiceTreeInfo
}

type GetDBInstanceInfoReq struct {
	InstanceId string
	RegionId   string
	Type       shared.DataSourceType
}

type GetDBInstanceInfoResp struct {
	DBInfo *model.DbInstanceInfo
}

type InstanceIsExistReq struct {
	DBName   string
	RegionId string
	Type     shared.DataSourceType
}

type GetInstanceTopoReq struct {
	DBName   string
	RegionId string
	Type     shared.DataSourceType
}

type ModifySQLKillRuleReq struct {
	InstanceId     string
	Type           shared.DataSourceType
	KillRule       []*KillRuleInfo
	Action         string
	ProtectedUsers string
}

type ModifySQLKillRuleResp struct {
}

type ListSQLKillRulesReq struct {
	InstanceId string
	Type       shared.DataSourceType
}

type ListSQLKillRulesResp struct {
}

type CheckDBWAccountReq struct {
	DSType      shared.DataSourceType
	InstanceId  string
	AccountName string
}

type CheckDBWAccountResp struct {
	AccountPrivileges string
}

type PgDialogInfo struct {
	ProcessID    string `gorm:"column:pid"`
	User         string `gorm:"column:usename"`
	Ip           string `gorm:"column:client_addr"`
	Port         string `gorm:"column:client_port"`
	DB           string `gorm:"column:datname"`
	Info         string `gorm:"column:query"`
	QueryStart   string `gorm:"column:query_start"`
	TrxStart     string `gorm:"column:xact_start"`
	State        string `gorm:"column:state"`
	NodeType     string `gorm:"column:NodeType"`
	NodeId       string `gorm:"column:NodeId"`
	EndpointId   string `gorm:"column:EndpointId"`
	EndpointName string `gorm:"column:EndpointName"`
	QueryTime    string `gorm:"column:query_time_seconds"`
}

type DialogInfo struct {
	ProcessID     string         `gorm:"column:ID"`
	User          string         `gorm:"column:USER"`
	Host          string         `gorm:"column:HOST"`
	DB            string         `gorm:"column:DB"`
	Command       string         `gorm:"column:COMMAND"`
	Time          string         `gorm:"column:TIME"`
	State         string         `gorm:"column:STATE"`
	Info          sql.NullString `gorm:"column:INFO"`
	BlockingPid   sql.NullString `gorm:"column:BLOCKINGPID"`
	NodeType      string         `gorm:"column:NodeType"`
	NodeId        string         `gorm:"column:NodeId"`
	EndpointId    string         `gorm:"column:EndpointId"`
	EndpointName  string         `gorm:"column:EndpointName"`
	PSM           string         `gorm:"column:PSM"`
	SqlTemplate   string         `gorm:"column:SqlTemplate"`
	SqlTemplateID string         `gorm:"column:SqlTemplateID"`
	SqlType       string         `gorm:"column:sql_type"`
}

type DbPartitionInfo struct {
	DbName  string `gorm:"column:dbname"`
	ShardId int32  `gorm:"column:shard_id"`
	GroupId int32  `gorm:"column:group_id"`
}

type VProxyDBInfo struct {
	DB       string `gorm:"column:DB"`
	ShardIds string `gorm:"column:ShardIDs"`
	GroupId  int32  `gorm:"column:GroupID"`
}

type CalculateSpecAfterScaleReq struct {
	Type         shared.DataSourceType
	InstanceId   string
	CurrentSpec  string
	CurrentCpu   int32
	CurrentMem   int32
	ScalingLimit string // 扩缩容限制
	Metrics      []*AutoScaleMetricName
	SpecFamily   string
	ScalingType  model.AutoScaleAction
}

type CalculateSpecAfterScaleResp struct {
	NewSpec       string // 扩容后的规格
	NewCpu        int32
	NewMem        int32
	NewSpecFamily string
}

type AutoScaleMetricName struct {
	MetricName  string
	MetricValue float32
}

type ModifyDBInstanceSpecReq struct {
	Type       shared.DataSourceType
	InstanceId string
	NodeNumber int32  // 扩容后的节点数量
	NodeSpec   string // 扩容后的规格
	NodeType   string // 扩容后的节点类型
	ZoneId     string // 可用区ID
}
type ModifyDBInstanceSpecResp struct {
	NewSpec string // 扩容后的规格
}

type GetManagedAccountAndPwdResp struct {
	AccountName     string
	AccountPassword string
}

// FIXME
type LockCurrentWaitsDetail struct {
	DbName             string `gorm:"column:db_name"`
	RTrxMysqlThreadId  string `gorm:"column:r_trx_mysql_thread_id"`
	RTrxId             string `gorm:"column:r_trx_id"`
	RTrxState          string `gorm:"column:r_trx_state"`
	RWaitingQuery      string `gorm:"column:r_waiting_query"`
	RTrxStarted        string `gorm:"column:r_trx_started"`
	RTrxWaitStarted    string `gorm:"column:r_trx_wait_started"`
	RBlockedWaitSecs   string `gorm:"column:r_blocked_wait_secs"`
	RTrxRowsModified   string `gorm:"column:r_trx_rows_modified"`
	RTrxRowsLocked     string `gorm:"column:r_trx_rows_locked"`
	RTrxOperationState string `gorm:"column:r_trx_operation_state"`

	BTrxMysqlThreadId  string `gorm:"column:b_trx_mysql_thread_id"`
	BTrxId             string `gorm:"column:b_trx_id"`
	BTrxState          string `gorm:"column:b_trx_state"`
	BBlockingQuery     string `gorm:"column:b_blocking_query"`
	BTrxStarted        string `gorm:"column:b_trx_started"`
	BTrxWaitStarted    string `gorm:"column:b_trx_wait_started"`
	BBlockingWaitSecs  string `gorm:"column:b_blocking_wait_secs"`
	BTrxRowsModified   string `gorm:"column:b_trx_rows_modified"`
	BTrxRowsLocked     string `gorm:"column:b_trx_rows_locked"`
	BTrxOperationState string `gorm:"column:b_trx_operation_state"`
	NodeId             string `gorm:"column:NodeId"`
}
type RawMongoDialogInfo struct {
	ProcessID    interface{} `bson:"opid"`
	Host         string      `bson:"host"`
	Desc         string      `bson:"desc"`
	ConnectionId int32       `bson:"connectionId"`
	Command      string      `bson:"op"`
	Time         int64       `bson:"secs_running"`
	State        bool        `bson:"active"`
	Client       string      `bson:"client"`
	ClientV2     string      `bson:"client_s"` // mongos 返回
	Info         interface{} `bson:"command"`
	Namespace    string      `bson:"ns"`
	PlanSummary  string      `bson:"planSummary"`
	NodeId       string      `bson:"node_id"`
	NodeType     string      `bson:"node_type"`
}

type MongoDialogInfo struct {
	ProcessID    string
	Host         string
	Desc         string
	ConnectionId string
	Command      string
	Time         string
	State        string
	Client       string
	Info         string
	Namespace    string
	PlanSummary  string
	NodeId       string
	NodeType     string
}

type psmItem struct {
	Psm string
	//db  string
	ip  string
	pid string
}
type UserAggregatedInfo struct {
	User       string `gorm:"column:user"`
	ActiveConn int32  `gorm:"column:active_conn"`
	TotalConn  int32  `gorm:"column:total_conn"`
}

type DBAggregatedInfo struct {
	DB         string `gorm:"column:db"`
	ActiveConn int32  `gorm:"column:active_conn"`
	TotalConn  int32  `gorm:"column:total_conn"`
}

type IPAggregatedInfo struct {
	IP         string `gorm:"column:psm"`
	ActiveConn int32  `gorm:"column:active_conn"`
	TotalConn  int32  `gorm:"column:total_conn"`
}

type PSMAggregatedInfo struct {
	PSM        string `gorm:"column:ip"`
	ActiveConn int32  `gorm:"column:active_conn"`
	TotalConn  int32  `gorm:"column:total_conn"`
}

type NsAggregatedInfo struct {
	Namespace  string `gorm:"column:ns"`
	ActiveConn int32  `gorm:"column:active_conn"`
	TotalConn  int32  `gorm:"column:total_conn"`
}

type ValidateDryRunReq struct {
	Type    shared.DataSourceType
	Source  *shared.DataSource
	SqlList []*shared.SqlInfo
}

type ValidateOriginalTableReq struct {
	Type    shared.DataSourceType
	Source  *shared.DataSource
	SqlList []*shared.SqlInfo
}

type ValidateExplainTableReq struct {
	Type    shared.DataSourceType
	Source  *shared.DataSource
	SqlList []*shared.SqlInfo
}

type ValidateUniqIndexReq struct {
	Type     shared.DataSourceType
	Source   *shared.DataSource
	IdxInfos []*shared.PreCheckIndexInfo
}

type IsTableExistsReq struct {
	Type      shared.DataSourceType
	Source    *shared.DataSource
	TableName string
}

type ValidateDropIndexReq struct {
	TableName string
	IndexName string
}

type ExecuteReq struct {
	Type       shared.DataSourceType
	Source     *shared.DataSource
	ExecuteSql string
}

type GetCurrentMaxConnectionsReq struct {
	Type   shared.DataSourceType
	Source *shared.DataSource
}

const (
	ValidateSuccess = 0
	ValidateError   = 1
	ValidateWarn    = 2
)

type PgLockCurrentWaitsDetail struct {
	DbName           string `gorm:"column:db_name"`
	BlockedUser      string `gorm:"column:blocked_user"`
	BlockedId        string `gorm:"column:blocked_pid"`
	RTrxId           string `gorm:"column:blocked_xid"`
	RTrxState        string `gorm:"column:blocked_state"`
	RWaitingQuery    string `gorm:"column:blocked_query"`
	RTrxStarted      string `gorm:"column:blocked_xact_start"`
	RTrxWaitStarted  string `gorm:"column:blocked_wait_start"`
	RBlockedWaitSecs string `gorm:"column:blocked_wait_seconds"`
	LockMode         string `gorm:"column:lock_mode"`
	LockType         string `gorm:"column:lock_type"`
	//LockTable           string `gorm:"column:lock_table"`

	BlockingId     string `gorm:"column:blocking_pid"`
	BTrxId         string `gorm:"column:blocking_xid"`
	BTrxState      string `gorm:"column:blocking_state"`
	BBlockingQuery string `gorm:"column:blocking_query"`
	BTrxStarted    string `gorm:"column:blocking_xact_start"`
	NodeId         string `gorm:"column:NodeId"`
}

type DescribeDBAutoScalingConfigReq struct {
	InstanceType shared.DataSourceType
	InstanceId   string
}

type DescribeDBAutoScalingConfigResp struct {
	AutoLocalSpecConfig   *model.AutoLocalSpecScalingConfig
	ManualLocalSpecConfig *model.ManualLocalSpecScalingConfig
	LocalSpecScalingLimit *model.LocalSpecScalingLimit
}

type ModifyDBAutoScalingConfigReq struct {
	InstanceType               shared.DataSourceType
	InstanceId                 string
	AutoLocalSpecScalingConfig *model.AutoLocalSpecScalingConfig
}

type ModifyDBAutoScalingConfigResp struct {
}

type DescribeDBAutoScaleEventsReq struct {
	InstanceType         shared.DataSourceType
	InstanceId           string
	Metric               AutoScaleMetricName
	AutoScaleSearchParam *model.AutoScaleSearchParam
	PageNumber           int32
	PageSize             int32
	SortBy               model.SortBy
}

type DescribeDBAutoScaleEventsResp struct {
	DiskAutoScaleEvents []*model.DiskAutoScaleEventItem
	Total               int32
}

type ModifyDBLocalSpecManuallyReq struct {
	InstanceId              string
	InstanceType            shared.DataSourceType
	NodeInfos               []*LocalSpecNodeInfo
	ManualEffectiveTimeHour *int32
}

type ModifyDBLocalSpecManuallyResp struct {
}

type LocalSpecNodeInfo struct {
	NodeId   string
	IncrVCPU int32
}

type LockWait80Version struct {
	BlockTrxId       string `gorm:"column:BLOCKING_ENGINE_TRANSACTION_ID"`
	RequestingTrxId  string `gorm:"column:REQUESTING_ENGINE_TRANSACTION_ID"`
	RequestingLockId string `gorm:"column:REQUESTING_ENGINE_LOCK_ID"`
}

type LockWait57Version struct {
	BlockTrxId       string `gorm:"column:blocking_trx_id"`
	RequestingTrxId  string `gorm:"column:requesting_trx_id"`
	RequestingLockId string `gorm:"column:requested_lock_id"`
}

type CurrentTrxDetail struct {
	TrxMysqlThreadId  string `gorm:"column:trx_mysql_thread_id"`
	TrxId             string `gorm:"column:trx_id"`
	TrxState          string `gorm:"column:trx_state"`
	QuerySql          string `gorm:"column:QuerySql"`
	TrxStarted        string `gorm:"column:trx_started"`
	TrxWaitStarted    string `gorm:"column:trx_wait_started"`
	WaitSeconds       string `gorm:"column:WaitSeconds"`
	TrxRowsModified   string `gorm:"column:trx_rows_modified"`
	TrxRowsLocked     string `gorm:"column:trx_rows_locked"`
	TrxOperationState string `gorm:"column:trx_operation_state"`
	TrxRequestLockId  string `gorm:"column:trx_requested_lock_id"`
}

func ReplacePort(address, newPort string) string {
	// 查找最后一个冒号的位置（端口分隔符）
	lastColon := strings.LastIndex(address, ":")
	if lastColon == -1 {
		// 如果没有冒号，直接附加新端口
		return address + ":" + newPort
	}
	// 处理ipv6地址[2001:0db8:85a3:0000:0000:8a2e:0370:7334]
	if strings.HasSuffix(address, "]") {
		return address + ":" + newPort
	}
	// 替换端口部分
	return address[:lastColon+1] + newPort
}

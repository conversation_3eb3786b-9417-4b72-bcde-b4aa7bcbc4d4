package datasource

import (
	"context"
	"fmt"

	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"

	"errors"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"go.uber.org/dig"
)

type DataSourceService interface {
	Type() shared.DataSourceType
	AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error)
	UpdateWhiteList(ctx context.Context, id string, ds *shared.DataSource, ip []string) error
	RemoveWhiteList(ctx context.Context, id string, ds *shared.DataSource, wlID string) error
	FillDataSource(ctx context.Context, ds *shared.DataSource) error
	FillInnerDataSource(ctx context.Context, ds *shared.DataSource) error
	CheckConn(ctx context.Context, ds *shared.DataSource) error
	CheckDataSource(ctx context.Context, ds *shared.DataSource) error
	KillQuery(ctx context.Context, ds *shared.DataSource, conn *shared.ConnectionInfo) error
	ListInstance(ctx context.Context, req *ListInstanceReq) (*ListInstanceResp, error)
	ListInstanceLightWeight(ctx context.Context, req *ListInstanceReq) (*ListInstanceResp, error)
	ListDatabases(ctx context.Context, req *ListDatabasesReq) (*ListDatabasesResp, error)
	ListInstanceNodes(ctx context.Context, req *ListInstanceNodesReq) (*ListInstanceNodesResp, error)
	ListInstanceNodesOri(ctx context.Context, req *ListInstanceNodesReq) (*ListInstanceNodesOriResp, error)
	ListInstancePods(ctx context.Context, req *ListInstancePodsReq) (*ListInstancePodsResp, error)
	DescribeDBInstanceDetail(ctx context.Context, req *DescribeDBInstanceDetailReq) (*DescribeDBInstanceDetailResp, error)
	DescribeDBInstanceSpec(ctx context.Context, req *DescribeDBInstanceSpecReq) (*DescribeDBInstanceSpecResp, error)
	DescribeDBInstanceEndpoints(ctx context.Context, req *DescribeDBInstanceEndpointsReq) (*DescribeDBInstanceEndpointsResp, error)
	DescribeDBInstanceShardInfos(ctx context.Context, req *DescribeDBInstanceShardInfosReq) (*DescribeDBInstanceShardInfosResp, error)
	DescribeDBInstanceCluster(ctx context.Context, req *DescribeDBInstanceClusterReq) (*DescribeDBInstanceClusterResp, error)
	DescribeDBInstanceAuditCollectedPod(ctx context.Context, req *DescribeDBInstanceAuditCollectedPodReq) (*DescribeDBInstanceAuditCollectedPodResp, error)
	OpenDBInstanceAuditLog(ctx context.Context, req *OpenDBInstanceAuditLogReq) (*OpenDBInstanceAuditLogResp, error)
	CloseDBInstanceAuditLog(ctx context.Context, req *CloseDBInstanceAuditLogReq) (*CloseDBInstanceAuditLogResp, error)
	CheckDBInstanceAuditLogStatus(ctx context.Context, req *CheckDBInstanceAuditLogStatusReq) (*CheckDBInstanceAuditLogStatusResp, error)
	DescribeDBProxyConfig(ctx context.Context, req *DescribeDBProxyConfigReq) (*DescribeDBProxyConfigResp, error)
	DescribeInstanceFeatures(ctx context.Context, req *DescribeInstanceFeaturesReq) (*DescribeInstanceFeaturesResp, error)
	DescribeDBInstanceSSL(ctx context.Context, req *DescribeDBInstanceSSLReq) (*DescribeDBInstanceSSLResp, error)
	DescribeSQLCCLConfig(ctx context.Context, req *DescribeSQLCCLConfigReq) (*DescribeSQLCCLConfigResp, error)
	ModifySQLCCLConfig(ctx context.Context, req *ModifySQLCCLConfigReq) (*ModifySQLCCLConfigResp, error)
	AddSQLCCLRule(ctx context.Context, req *AddSQLCCLRuleReq) (*AddSQLCCLRuleResp, error)
	ModifyProxyThrottleRule(ctx context.Context, req *ModifyProxyThrottleRuleReq) (*ModifyProxyThrottleRuleResp, error)
	DeleteSQLCCLRule(ctx context.Context, req *DeleteSQLCCLRuleReq) (*DeleteSQLCCLRuleResp, error)
	FlushSQLCCLRule(ctx context.Context, req *FlushSQLCCLRuleReq) (*FlushSQLCCLRuleResp, error)
	ListSQLCCLRules(ctx context.Context, req *ListSQLCCLRulesReq) (*ListSQLCCLRulesResp, error)
	DescribeSqlFingerPrintOrKeywords(ctx context.Context, req *DescribeSqlFingerPrintOrKeywordsReq) (*DescribeSqlFingerPrintOrKeywordsResp, error)
	DescribeSqlType(ctx context.Context, req *DescribeSqlTypeReq) (*DescribeSqlTypeResp, error)
	DescribeInstanceAddress(ctx context.Context, req *DescribeInstanceAddressReq) (*DescribeInstanceAddressResp, error)
	DescribeInstanceProxyAddress(ctx context.Context, req *DescribeInstanceAddressReq) (*DescribeInstanceAddressResp, error)
	DescribeInstanceAddressList(ctx context.Context, req *DescribeInstanceAddressReq) ([]*DescribeInstanceAddressResp, error)
	ListTables(ctx context.Context, req *ListTablesReq) (*ListTablesResp, error)
	ListAllTables(ctx context.Context, req *ListTablesReq) (*ListTablesResp, error)
	ListSchemaTables(ctx context.Context, req *ListSchemaTablesReq) (*ListSchemaTablesResp, error)
	ListTablesInfo(ctx context.Context, req *ListTablesInfoReq) (*ListTablesInfoResp, error)
	DescribeTable(ctx context.Context, req *DescribeTableReq) (*DescribeTableResp, error)
	DescribeAutoKillSessionConfig(ctx context.Context, req *DescribeAutoKillSessionConfigReq) (*DescribeAutoKillSessionConfigResp, error)
	ModifyAutoKillSessionConfig(ctx context.Context, req *ModifyAutoKillSessionConfigReq) (*ModifyAutoKillSessionConfigResp, error)
	DescribeInstanceVersion(ctx context.Context, req *DescribeInstanceVersionReq) (*DescribeInstanceVersionResp, error)
	ListErrLogs(ctx context.Context, req *ListErrLogsReq) (*ListErrLogsResp, error)
	DescribePgTable(ctx context.Context, req *DescribePgTableReq) (*DescribePgTableResp, error)
	DescribeInstanceReplicaDelay(ctx context.Context, req *DescribeDBInstanceDetailReq) (int64, error)

	ListViews(ctx context.Context, req *ListViewsReq) (*ListViewsResp, error)
	DescribeView(ctx context.Context, req *DescribeViewReq) (*DescribeViewResp, error)
	DescribeFunction(ctx context.Context, req *DescribeFunctionReq) (*DescribeFunctionResp, error)
	DescribeProcedure(ctx context.Context, req *DescribeProcedureReq) (*DescribeProcedureResp, error)

	ListFunctions(ctx context.Context, req *ListFunctionsReq) (*ListFunctionsResp, error)
	ListProcedures(ctx context.Context, req *ListProceduresReq) (*ListProceduresResp, error)
	ListTriggers(ctx context.Context, req *ListTriggersReq) (*ListTriggersResp, error)

	DescribeTLSConnectionInfo(ctx context.Context, req *DescribeTLSConnectionInfoReq) (*DescribeTLSConnectionInfoResp, error)

	ListKeyNumbers(ctx context.Context, req *ListKeyNumbersReq) (*ListKeyNumbersResp, error)
	ListKeys(ctx context.Context, req *ListKeysReq) (*ListKeysResp, error)
	GetKey(ctx context.Context, req *GetKeyReq) (*GetKeyResp, error)
	ListKeyMembers(ctx context.Context, req *ListKeyMembersReq) (*ListKeyMembersResp, error)
	ListAlterKVsCommands(ctx context.Context, req *ListAlterKVsCommandsReq) (*ListAlterKVsCommandsResp, error)

	DescribeBigKeys(ctx context.Context, req *DescribeBigKeysReq) (*DescribeBigKeysResp, error)
	DescribeHotKeys(ctx context.Context, req *DescribeHotKeysReq) (*DescribeHotKeysResp, error)

	OpenTunnel(ctx context.Context, ds *shared.DataSource, TunnelID string) error

	DescribeTrigger(ctx context.Context, req *DescribeTriggerReq) (*DescribeTriggerResp, error)

	DescribeEvent(ctx context.Context, req *DescribeEventReq) (*DescribeEventResp, error)
	ListEvents(ctx context.Context, req *ListEventsReq) (*ListEventsResp, error)

	CreateAccount(ctx context.Context, req *CreateAccountReq) error
	CheckPrivilege(ctx context.Context, instanceId, dbName, accountName, priv string, dsType shared.DataSourceType) (bool, error)
	// FIXME deprecate `DescribeAccountResp` and rename `DescribeAccountResp2` to `DescribeAccountResp`
	DescribeAccounts(ctx context.Context, req *DescribeAccountsReq) (*DescribeAccountResp, error)
	DescribeAccounts2(ctx context.Context, req *DescribeAccountsReq) (*DescribeAccountResp2, error)
	CreateAccountAndGrant(ctx context.Context, instanceId, accountName, password, dbName, priv string, dsType shared.DataSourceType) error
	ModifyAccountPrivilege(ctx context.Context, req *ModifyAccountPrivilegeReq) error
	GrantAccountPrivilege(ctx context.Context, req *GrantAccountPrivilegeReq) error
	DeleteAccount(ctx context.Context, req *DeleteAccountReq) error
	ListDatabasesWithAccount(ctx context.Context, req *ListDatabasesWithAccountReq) (*ListDatabasesWithAccountResp, error)
	GetAdvice(ctx context.Context, req *GetAdviceReq) (*GetAdviceResp, error)

	ListCharsets(ctx context.Context, req *ListCharsetsReq) (*ListCharsetsResp, error)
	ListCollations(ctx context.Context, req *ListCollationsReq) (*ListCollationsResp, error)

	ListSchema(ctx context.Context, req *ListSchemaReq) (*ListSchemaResp, error)
	ListSequence(ctx context.Context, req *ListSequenceReq) (*ListSequenceResp, error)
	ListPgCollations(ctx context.Context, req *ListPgCollationsReq) (*ListPgCollationsResp, error)
	ListPgUsers(ctx context.Context, req *ListPgUsersReq) (*ListPgUsersResp, error)
	ListTableSpaces(ctx context.Context, req *ListTableSpacesReq) (*ListTableSpacesResp, error)

	DescribeDialogDetails(ctx context.Context, req *DescribeDialogDetailsReq) (*DescribeDialogDetailsResp, error)
	DescribeDialogStatistics(ctx context.Context, req *DescribeDialogStatisticsReq) (*DescribeDialogStatisticsResp, error)
	DescribeEngineStatus(ctx context.Context, req *DescribeEngineStatusReq) (*DescribeEngineStatusResp, error)
	KillProcess(ctx context.Context, req *KillProcessReq) (*KillProcessResp, error)
	DescribeTrxAndLocks(ctx context.Context, req *DescribeTrxAndLocksReq) (*DescribeTrxAndLocksResp, error)
	DescribeLockCurrentWaits(ctx context.Context, req *DescribeLockCurrentWaitsReq) (*DescribeLockCurrentWaitsResp, error)
	DescribeDeadlock(ctx context.Context, req *DescribeDeadlockReq) (*DescribeDeadlockResp, error)
	DescribeDeadlockDetect(ctx context.Context, req *DescribeDeadlockDetectReq) (*DescribeDeadlockDetectResp, error)
	DescribeDialogInfos(ctx context.Context, req *DescribeDialogInfosReq) (*DescribeDialogInfosResp, error)
	DescribeCurrentConn(ctx context.Context, req *DescribeCurrentConnsReq) (*DescribeCurrentConnsResp, error)
	DescribeTableSpace(ctx context.Context, req *DescribeTableSpaceReq) (*shared.DescribeTableSpaceResp, error)
	DescribeTableSpaceAutoIncr(ctx context.Context, req *DescribeTableSpaceReq) (*shared.DescribeTableSpaceAutoIncrResp, error)
	ConvertTableSpaceToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp
	DescribeTableColumn(ctx context.Context, req *DescribeTableInfoReq) (*shared.DescribeTableColumnResp, error)
	ConvertTableColumnToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableColumnResp) *model.DescribeTableColumnResp
	DescribeTableIndex(ctx context.Context, req *DescribeTableInfoReq) (*shared.DescribeTableIndexResp, error)
	ConvertTableIndexToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableIndexResp) *model.DescribeTableIndexResp
	FormatDescribeStorageCapacityResp(sourceType shared.DataSourceType, diskSize *GetDiskSizeResp, storageSpace float64) *model.DescribeStorageCapacityResp

	ExecuteCCL(ctx context.Context, req *ExecuteCCLReq) (*ExecuteCCLResp, error)
	CCLShow(ctx context.Context, req *CCLShowReq) (*CCLShowResp, error)

	GetAvgSlowQueries(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetDataPointCountSlowQueries(ctx context.Context, req *GetMetricUsageReq) (int, error)
	GetCpuMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) // CPU
	GetAvgCpuUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMinCpuUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMaxCpuUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetCpuUsageMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	DiagRootCauseALL(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagRootCauseResp, error)
	DiagRootCauseYoYQoQ(ctx context.Context, req *GetMetricUsageReq) (map[string]float64, error)
	DiagRootCauseDiskMetrics(ctx context.Context, req *GetMetricUsageReq) (map[string]string, error)

	GetMemMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) // Mem
	GetAvgMemUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMinMemUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMaxMemUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMemUsageMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)

	GetAvgDiskUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMaxDiskUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMinDiskUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetDiskAvailableDays(ctx context.Context, req *GetDiskAvailableDaysReq) (*GetDiskAvailableDaysResp, error)
	GetDiskFutureSize(ctx context.Context, req *GetDiskFutureSizeReq) (*GetDiskFutureSizeResp, error)
	GetDiskMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error)
	GetDiskUsageMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)

	GetAvgQpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMaxQpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMinQpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetQpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetAvgTpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMaxTpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMinTpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetTpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetAvgConnectionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMaxConnectionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMinConnectionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetConnectedRatioMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetSessionMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) // session
	GetAvgSessionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMaxSessionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetMinSessionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error)
	GetLatestDiskUsage(ctx context.Context, req *GetLatestDiskUsageReq) (float64, error)

	GetInspectionCpuMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionMemMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionDiskMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionQpsMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionTpsMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionConnectedMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionConnRatioMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionBpHitMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionOutputMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)
	GetInspectionInputMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error)

	HealthSummary(ctx context.Context, req *GetMetricUsageReq) ([]*model.Resource, error)
	GetMonitorByMetric(ctx context.Context, req *GetMonitorByMetricReq) ([]*model.MetricResource, error)

	//DiagRootCause(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagRootCauseResp, error)
	ListCollections(ctx context.Context, req *ListCollectionsReq) (*ListCollectionsResp, error)
	ListIndexs(ctx context.Context, req *ListIndexesReq) (*ListIndexesResp, error)
	ListMongoDBs(ctx context.Context, req *ListMongoDBsReq) (*ListMongoDBsResp, error)
	CreateFreeLockCorrectOrder(ctx context.Context, c *CreateFreeLockCorrectOrderReq) (*CreateFreeLockCorrectOrderResp, error)
	CreateFreeLockCorrectOrderDryRun(ctx context.Context, c *CreateFreeLockCorrectOrderReq) (*CreateFreeLockCorrectOrderDryRunResp, error)
	DescribeFreeLockCorrectOrders(ctx context.Context, req *DescribeFreeLockCorrectOrdersReq) (*DescribeFreeLockCorrectOrdersResp, error)
	StopFreeLockCorrectOrders(ctx context.Context, req *StopFreeLockCorrectOrdersReq) error
	PreCheckFreeLockCorrectOrders(ctx context.Context, req *PreCheckFreeLockCorrectOrdersReq) (*PreCheckFreeLockCorrectOrdersResp, error)

	GetDBInnerAddress(ctx context.Context, req *GetDBInnerAddressReq) (*GetDBInnerAddressResp, error)

	ExecuteDQL(ctx context.Context, req *ExecuteDQLReq) (*ExecuteDQLResp, error) // 执行无锁DML工单的查询语句
	ExecuteDMLAndGetAffectedRows(ctx context.Context, req *ExecuteDMLAndGetAffectedRowsReq) (int64, error)

	GetPartitionInfos(ctx context.Context, dbSource *shared.DataSource, dbName string) ([]*DbPartitionInfo, error)
	GetShardingDbType(ctx context.Context, dbSource *shared.DataSource, dbName string, tableName string) (string, error)
	ExplainCommand(ctx context.Context, req *ExplainCommandReq) (*ExplainCommandResp, error)
	IsMyOwnInstance(ctx context.Context, instanceId string, source shared.DataSourceType) bool
	CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error
	GetTableIndexInfo(ctx context.Context, req *GetTableIndexInfoReq) (*GetTableInfoIndexResp, error)
	GetTableIndexValue(ctx context.Context, req *GetIndexValueReq) (*GetIndexValueResp, error)

	GetMaxConnections(ctx context.Context, req *GetMaxConnectionsReq) (int, error)

	GetCurrentBandwidth(ctx context.Context, req *GetCurrentBandwidthReq) (*InstanceBandwidthInfo, error)
	BandwidthScale(ctx context.Context, req *BandwidthScaleReq) error
	GetMinBandwidth(ctx context.Context, req *GetMinMaxBandwidthReq) (int, error)
	GetMaxBandwidth(ctx context.Context, req *GetMinMaxBandwidthReq) (int, error)
	GetDiskSize(ctx context.Context, req *GetDiskSizeReq) (*GetDiskSizeResp, error)
	GetUsedSize(ctx context.Context, req *GetDiskSizeReq) (int64, error)

	GetCurrentMetricData(ctx context.Context, req *GetMetricDatapointsReq) (*GetMetricDatapointsResp, error)
	GetPreSecondMetricData(ctx context.Context, req *GetMetricDatapointsReq) (*GetMetricDatapointsResp, error)
	GetPreSecondMetricDataByInstance(ctx context.Context, req *GetMetricDatapointsReq) (*GetMetricDatapointsResp, error)

	DescribeFullSQLLogConfig(ctx context.Context, req *DescribeFullSQLLogConfigReq) (*DescribeFullSQLLogConfigResp, error)
	ModifyFullSQLLogConfig(ctx context.Context, req *ModifyFullSQLLogConfigReq) (*ModifyFullSQLLogConfigResp, error)

	DescribeInstanceVariables(ctx context.Context, req *DescribeInstanceVariablesReq) (*DescribeInstanceVariablesResp, error)
	DescribePrimaryKeyRange(ctx context.Context, req *DescribePrimaryKeyRangeReq) (*DescribePrimaryKeyRangeResp, error)
	DescribeSQLAdvisorTableMeta(ctx context.Context, req *DescribeSQLAdvisorTableMetaReq) (*DescribeSQLAdvisorTableMetaResp, error)
	DescribeSampleData(ctx context.Context, req *DescribeSampleDataReq) (*DescribeSampleDataResp, error)

	EnsureAccount(ctx context.Context, req *EnsureAccountReq) error
	GetDatasourceAddress(ctx context.Context, ds *shared.DataSource) error
	GetDBServiceTreeMountInfo(ctx context.Context, req *GetDBServiceTreeMountInfoReq) (*GetDBServiceTreeMountInfoResp, error)
	GetDBInstanceInfo(ctx context.Context, req *GetDBInstanceInfoReq) (*GetDBInstanceInfoResp, error)
	InstanceIsExist(ctx context.Context, req *InstanceIsExistReq) (bool, error)
	GetInstanceTopo(ctx context.Context, req *GetInstanceTopoReq) ([]*model.InnerRdsInstance, error)
	GetInstanceProxyTopo(ctx context.Context, req *GetInstanceTopoReq) ([]*model.InnerRdsInstance, error)
	CreateLogDownloadTask(ctx context.Context, req *CreateLogDownloadTaskReq) error
	GetLogDownloadList(ctx context.Context, req *GetLogDownloadListReq) (*GetLogDownloadListResp, error)

	GetInstancePrimaryNodeId(ctx context.Context, req *GetInstancePrimaryNodeIdReq) (*GetInstancePrimaryNodeIdResp, error)

	ListSQLKillRules(ctx context.Context, req *ListSQLKillRulesReq) (*ListSQLKillRulesResp, error)
	ModifySQLKillRule(ctx context.Context, req *ModifySQLKillRuleReq) (*ModifySQLKillRuleResp, error)
	GetManagedAccountAndPwd(ctx context.Context, req *shared.DataSource) (*GetManagedAccountAndPwdResp, error)

	// 扩缩容
	CalculateSpecAfterScale(ctx context.Context, req *CalculateSpecAfterScaleReq) (*CalculateSpecAfterScaleResp, error)
	ModifyDBInstanceSpec(ctx context.Context, req *ModifyDBInstanceSpecReq) (*ModifyDBInstanceSpecResp, error)
	// RDS CPU扩缩容
	DescribeDBAutoScalingConfig(ctx context.Context, req *DescribeDBAutoScalingConfigReq) (*DescribeDBAutoScalingConfigResp, error)
	ModifyDBAutoScalingConfig(ctx context.Context, req *ModifyDBAutoScalingConfigReq) (*ModifyDBAutoScalingConfigResp, error)
	DescribeDBAutoScaleEvents(ctx context.Context, req *DescribeDBAutoScaleEventsReq) (*DescribeDBAutoScaleEventsResp, error)
	ModifyDBLocalSpecManually(ctx context.Context, req *ModifyDBLocalSpecManuallyReq) (*ModifyDBLocalSpecManuallyResp, error)

	ValidateDryRun(ctx context.Context, req *ValidateDryRunReq) *shared.ValidateResponse
	ValidateOriginalTable(ctx context.Context, req *ValidateOriginalTableReq) *shared.ValidateResponse
	ValidateUniqIndex(ctx context.Context, req *ValidateUniqIndexReq) *shared.ValidateResponse
	IsTableExists(ctx context.Context, req *IsTableExistsReq) (bool, error)
	ExecuteSql(ctx context.Context, req *ExecuteReq) error
	GetCurrentMaxConnections(ctx context.Context, req *GetCurrentMaxConnectionsReq) (int, error)

	GetInstanceSlaveAddress(ctx types.Context, req *GetInstanceSlaveAddressReq) (*GetInstanceSlaveAddressResp, error)
	// DescribeDBInstanceDetailForPilot DBCopilot使用，查询Detail的所有数据，然后以JSON结构返回出去
	DescribeDBInstanceDetailForPilot(ctx context.Context, req *DescribeDBInstanceDetailReq) (string, error)
	// DescribeDBInstanceParametersForPilot DBCopilot使用，查询指定实例的参数信息，然后以JSON结构返回出去
	DescribeDBInstanceParametersForPilot(ctx context.Context, req *DescribeDBInstanceDetailReq) (string, error)

	// 获取show create table的结果
	GetCreateTableInfo(ctx context.Context, ds *shared.DataSource, tableName string) (string, error)

	CheckAccountPrivilege(ctx context.Context, req *CheckDBWAccountReq) (bool, error)
	ResetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error
	GrantReplicationPrivilege(ctx context.Context, ds *shared.DataSource, accountName string) error

	DescribeInstancePodAddress(ctx context.Context, req *DescribeInstanceAddressReq) (*DescribeInstanceAddressResp, error)
}

type NewRootDataSourceIn struct {
	dig.In
	Sources []DataSourceService `group:"datasources"`
}

func NewRootDataSource(p NewRootDataSourceIn) DataSourceService {
	source := make(map[shared.DataSourceType]DataSourceService)
	for _, impl := range p.Sources {
		log.Info(context.Background(), "data source %v registered", impl.Type())
		source[impl.Type()] = impl
	}
	return &rootDataSource{sources: source}
}

type rootDataSource struct {
	sources map[shared.DataSourceType]DataSourceService
}

func (root *rootDataSource) DescribeBigKeys(ctx context.Context, req *DescribeBigKeysReq) (*DescribeBigKeysResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeBigKeys(ctx, req)
	}
	return nil, nil
}

func (root *rootDataSource) DescribeHotKeys(ctx context.Context, req *DescribeHotKeysReq) (*DescribeHotKeysResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeHotKeys(ctx, req)
	}
	return nil, nil
}

func (root *rootDataSource) DescribeDBInstanceParametersForPilot(ctx context.Context, req *DescribeDBInstanceDetailReq) (string, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBInstanceParametersForPilot(ctx, req)
	}
	return "", nil
}

func (root *rootDataSource) DescribeDBInstanceDetailForPilot(ctx context.Context, req *DescribeDBInstanceDetailReq) (string, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBInstanceDetailForPilot(ctx, req)
	}
	return "", nil
}

func (root *rootDataSource) InstanceIsExist(ctx context.Context, req *InstanceIsExistReq) (bool, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.InstanceIsExist(ctx, req)
	}
	return false, errors.New("Unknown type to call InstanceIsExist.")
}

func (root *rootDataSource) GetInstanceTopo(ctx context.Context, req *GetInstanceTopoReq) ([]*model.InnerRdsInstance, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetInstanceTopo(ctx, req)
	}
	return []*model.InnerRdsInstance{}, errors.New("Unknown type to call GetInstanceTopo.")
}

func (root *rootDataSource) GetInstanceProxyTopo(ctx context.Context, req *GetInstanceTopoReq) ([]*model.InnerRdsInstance, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetInstanceProxyTopo(ctx, req)
	}
	return []*model.InnerRdsInstance{}, errors.New("Unknown type to call GetInstanceTopo.")
}

func (root *rootDataSource) CreateLogDownloadTask(ctx context.Context, req *CreateLogDownloadTaskReq) error {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.CreateLogDownloadTask(ctx, req)
	}
	return errors.New("Unknown type to call CreateLogDownloadTask.")
}
func (root *rootDataSource) GetLogDownloadList(ctx context.Context, req *GetLogDownloadListReq) (*GetLogDownloadListResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetLogDownloadList(ctx, req)
	}
	return &GetLogDownloadListResp{}, errors.New("Unknown type to call GetLogDownloadList.")
}

func (root *rootDataSource) DescribeFullSQLLogConfig(ctx context.Context, req *DescribeFullSQLLogConfigReq) (*DescribeFullSQLLogConfigResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.DescribeFullSQLLogConfig(ctx, req)
	}
	return &DescribeFullSQLLogConfigResp{}, nil
}

func (root *rootDataSource) ModifyFullSQLLogConfig(ctx context.Context, req *ModifyFullSQLLogConfigReq) (*ModifyFullSQLLogConfigResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.ModifyFullSQLLogConfig(ctx, req)
	}
	return &ModifyFullSQLLogConfigResp{}, nil
}

func (root *rootDataSource) CCLShow(ctx context.Context, req *CCLShowReq) (*CCLShowResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.CCLShow(ctx, req)
	}
	return &CCLShowResp{}, nil
}

func (root *rootDataSource) ExecuteCCL(ctx context.Context, req *ExecuteCCLReq) (*ExecuteCCLResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ExecuteCCL(ctx, req)
	}
	return &ExecuteCCLResp{}, nil
}

func (root *rootDataSource) DescribeDBProxyConfig(ctx context.Context, req *DescribeDBProxyConfigReq) (*DescribeDBProxyConfigResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBProxyConfig(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) DescribeDBInstanceDetail(ctx context.Context, req *DescribeDBInstanceDetailReq) (*DescribeDBInstanceDetailResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBInstanceDetail(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) DescribeDBInstanceSpec(ctx context.Context, req *DescribeDBInstanceSpecReq) (*DescribeDBInstanceSpecResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.DescribeDBInstanceSpec(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.InstanceType.String())
	return nil, nil
}

func (root *rootDataSource) DescribeDBInstanceCluster(ctx context.Context, req *DescribeDBInstanceClusterReq) (*DescribeDBInstanceClusterResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBInstanceCluster(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) DescribeDBInstanceAuditCollectedPod(ctx context.Context, req *DescribeDBInstanceAuditCollectedPodReq) (*DescribeDBInstanceAuditCollectedPodResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBInstanceAuditCollectedPod(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) OpenDBInstanceAuditLog(ctx context.Context, req *OpenDBInstanceAuditLogReq) (*OpenDBInstanceAuditLogResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.OpenDBInstanceAuditLog(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) CloseDBInstanceAuditLog(ctx context.Context, req *CloseDBInstanceAuditLogReq) (*CloseDBInstanceAuditLogResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.CloseDBInstanceAuditLog(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) CheckDBInstanceAuditLogStatus(ctx context.Context, req *CheckDBInstanceAuditLogStatusReq) (*CheckDBInstanceAuditLogStatusResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.CheckDBInstanceAuditLogStatus(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) DescribeDBInstanceSSL(ctx context.Context, req *DescribeDBInstanceSSLReq) (*DescribeDBInstanceSSLResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBInstanceSSL(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) CreateFreeLockCorrectOrder(ctx context.Context, req *CreateFreeLockCorrectOrderReq) (*CreateFreeLockCorrectOrderResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.CreateFreeLockCorrectOrder(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) CreateFreeLockCorrectOrderDryRun(ctx context.Context, req *CreateFreeLockCorrectOrderReq) (*CreateFreeLockCorrectOrderDryRunResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.CreateFreeLockCorrectOrderDryRun(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) DescribeFreeLockCorrectOrders(ctx context.Context, req *DescribeFreeLockCorrectOrdersReq) (*DescribeFreeLockCorrectOrdersResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeFreeLockCorrectOrders(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) StopFreeLockCorrectOrders(ctx context.Context, req *StopFreeLockCorrectOrdersReq) error {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.StopFreeLockCorrectOrders(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", shared.MySQL.String())
	return nil
}

func (root *rootDataSource) PreCheckFreeLockCorrectOrders(ctx context.Context, req *PreCheckFreeLockCorrectOrdersReq) (*PreCheckFreeLockCorrectOrdersResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.PreCheckFreeLockCorrectOrders(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", shared.MySQL.String())
	return nil, nil
}

func (root *rootDataSource) DescribeTLSConnectionInfo(ctx context.Context, req *DescribeTLSConnectionInfoReq) (*DescribeTLSConnectionInfoResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeTLSConnectionInfo(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) Type() shared.DataSourceType {
	return shared.DataSourceType(-1)
}

func (root *rootDataSource) AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error) {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.AddWhiteList(ctx, id, ds)
	}
	log.Warn(ctx, "can't find implement for ds type %v", ds.Type.String())
	return "", nil
}

func (root *rootDataSource) UpdateWhiteList(ctx context.Context, id string, ds *shared.DataSource, ip []string) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.UpdateWhiteList(ctx, id, ds, ip)
	}
	return nil
}
func (root *rootDataSource) RemoveWhiteList(ctx context.Context, id string, ds *shared.DataSource, wlID string) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.RemoveWhiteList(ctx, id, ds, wlID)
	}
	return nil
}

func (root *rootDataSource) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.FillDataSource(ctx, ds)
	}
	return nil
}

func (root *rootDataSource) FillInnerDataSource(ctx context.Context, ds *shared.DataSource) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.FillInnerDataSource(ctx, ds)
	}
	return nil
}

func (root *rootDataSource) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.CheckConn(ctx, ds)
	}
	return nil
}

func (root *rootDataSource) ListInstance(ctx context.Context, ds *ListInstanceReq) (*ListInstanceResp, error) {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.ListInstance(ctx, ds)
	}
	log.Warn(ctx, "not found datasource impl for type %v", ds.Type.String())
	//panic("not found datasource impl for type " + ds.Type.String() + "")
	return &ListInstanceResp{}, nil
}
func (root *rootDataSource) ListInstanceLightWeight(ctx context.Context, ds *ListInstanceReq) (*ListInstanceResp, error) {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.ListInstanceLightWeight(ctx, ds)
	}
	return &ListInstanceResp{}, nil
}

func (root *rootDataSource) ListDatabases(ctx context.Context, req *ListDatabasesReq) (*ListDatabasesResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListDatabases(ctx, req)
	}
	return &ListDatabasesResp{}, nil
}

func (root *rootDataSource) ListTables(ctx context.Context, req *ListTablesReq) (*ListTablesResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListTables(ctx, req)
	}
	return &ListTablesResp{}, nil
}
func (root *rootDataSource) ListAllTables(ctx context.Context, req *ListTablesReq) (*ListTablesResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListAllTables(ctx, req)
	}
	return &ListTablesResp{}, nil
}

func (root *rootDataSource) ListTablesInfo(ctx context.Context, req *ListTablesInfoReq) (*ListTablesInfoResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListTablesInfo(ctx, req)
	}
	return &ListTablesInfoResp{}, nil
}

func (root *rootDataSource) DescribeTable(ctx context.Context, req *DescribeTableReq) (*DescribeTableResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeTable(ctx, req)
	}
	return &DescribeTableResp{}, nil
}

func (root *rootDataSource) DescribePgTable(ctx context.Context, req *DescribePgTableReq) (*DescribePgTableResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribePgTable(ctx, req)
	}
	return &DescribePgTableResp{}, nil
}

func (root *rootDataSource) ListViews(ctx context.Context, req *ListViewsReq) (*ListViewsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListViews(ctx, req)
	}
	return &ListViewsResp{}, nil
}

func (root *rootDataSource) DescribeView(ctx context.Context, req *DescribeViewReq) (*DescribeViewResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeView(ctx, req)
	}
	return &DescribeViewResp{}, nil
}

func (root *rootDataSource) DescribeFunction(ctx context.Context, req *DescribeFunctionReq) (*DescribeFunctionResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeFunction(ctx, req)
	}
	return &DescribeFunctionResp{}, nil
}

func (root *rootDataSource) DescribeProcedure(ctx context.Context, req *DescribeProcedureReq) (*DescribeProcedureResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeProcedure(ctx, req)
	}
	return &DescribeProcedureResp{}, nil
}

func (root *rootDataSource) ListFunctions(ctx context.Context, req *ListFunctionsReq) (*ListFunctionsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListFunctions(ctx, req)
	}
	return &ListFunctionsResp{}, nil
}

func (root *rootDataSource) ListProcedures(ctx context.Context, req *ListProceduresReq) (*ListProceduresResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListProcedures(ctx, req)
	}
	return &ListProceduresResp{}, nil
}

func (root *rootDataSource) ListTriggers(ctx context.Context, req *ListTriggersReq) (*ListTriggersResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListTriggers(ctx, req)
	}
	return &ListTriggersResp{}, nil
}

func (root *rootDataSource) DescribeTrigger(ctx context.Context, req *DescribeTriggerReq) (*DescribeTriggerResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeTrigger(ctx, req)
	}
	return &DescribeTriggerResp{}, nil
}

func (root *rootDataSource) KillQuery(ctx context.Context, req *shared.DataSource, c *shared.ConnectionInfo) error {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.KillQuery(ctx, req, c)
	}
	return nil
}

func (root *rootDataSource) ListKeyNumbers(ctx context.Context, req *ListKeyNumbersReq) (*ListKeyNumbersResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListKeyNumbers(ctx, req)
	}
	return &ListKeyNumbersResp{}, nil
}

func (root *rootDataSource) ListKeys(ctx context.Context, req *ListKeysReq) (*ListKeysResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListKeys(ctx, req)
	}
	return &ListKeysResp{}, nil
}

func (root *rootDataSource) GetKey(ctx context.Context, req *GetKeyReq) (*GetKeyResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.GetKey(ctx, req)
	}
	return nil, nil
}
func (root *rootDataSource) ListKeyMembers(ctx context.Context, req *ListKeyMembersReq) (*ListKeyMembersResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListKeyMembers(ctx, req)
	}
	return &ListKeyMembersResp{}, nil
}

func (root *rootDataSource) ListAlterKVsCommands(ctx context.Context, req *ListAlterKVsCommandsReq) (*ListAlterKVsCommandsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListAlterKVsCommands(ctx, req)
	}
	return &ListAlterKVsCommandsResp{}, nil
}

func (root *rootDataSource) OpenTunnel(ctx context.Context, ds *shared.DataSource, TunnelID string) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.OpenTunnel(ctx, ds, TunnelID)
	}
	return nil
}

func (root *rootDataSource) DescribeEvent(ctx context.Context, req *DescribeEventReq) (*DescribeEventResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeEvent(ctx, req)
	}
	return &DescribeEventResp{}, nil
}

func (root *rootDataSource) ListEvents(ctx context.Context, req *ListEventsReq) (*ListEventsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListEvents(ctx, req)
	}
	return &ListEventsResp{}, nil
}

func (root *rootDataSource) CreateAccount(ctx context.Context, req *CreateAccountReq) error {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.CreateAccount(ctx, req)
	}
	return nil
}
func (root *rootDataSource) DescribeAccounts(ctx context.Context, req *DescribeAccountsReq) (*DescribeAccountResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.DescribeAccounts(ctx, req)
	}
	return &DescribeAccountResp{}, nil
}

func (root *rootDataSource) DescribeAccounts2(ctx context.Context, req *DescribeAccountsReq) (*DescribeAccountResp2, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.DescribeAccounts2(ctx, req)
	}
	panic("Not implement error")
}

func (root *rootDataSource) CreateAccountAndGrant(ctx context.Context, instanceId, accountName, password, dbName, priv string, dsType shared.DataSourceType) error {
	if impl, ok := root.sources[dsType]; ok {
		return impl.CreateAccountAndGrant(ctx, instanceId, accountName, password, dbName, priv, dsType)
	}
	panic("Not implement error")
}
func (root *rootDataSource) CheckPrivilege(ctx context.Context, instanceId, dbName, accountName, priv string, dsType shared.DataSourceType) (bool, error) {
	if impl, ok := root.sources[dsType]; ok {
		return impl.CheckPrivilege(ctx, instanceId, dbName, accountName, priv, dsType)
	}
	panic("Not implement error")
}

func (root *rootDataSource) ModifyAccountPrivilege(ctx context.Context, req *ModifyAccountPrivilegeReq) error {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.ModifyAccountPrivilege(ctx, req)
	}
	return nil
}

func (root *rootDataSource) GrantAccountPrivilege(ctx context.Context, req *GrantAccountPrivilegeReq) error {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GrantAccountPrivilege(ctx, req)
	}
	return nil
}

func (root *rootDataSource) DeleteAccount(ctx context.Context, req *DeleteAccountReq) error {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.DeleteAccount(ctx, req)
	}
	return nil
}

func (root *rootDataSource) ListDatabasesWithAccount(ctx context.Context, req *ListDatabasesWithAccountReq) (*ListDatabasesWithAccountResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.ListDatabasesWithAccount(ctx, req)
	}
	return &ListDatabasesWithAccountResp{}, nil
}

func (root *rootDataSource) CheckDataSource(ctx context.Context, ds *shared.DataSource) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.CheckDataSource(ctx, ds)
	}
	return nil
}

func (root *rootDataSource) GetAdvice(ctx context.Context, req *GetAdviceReq) (*GetAdviceResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.GetAdvice(ctx, req)
	}
	return &GetAdviceResp{}, nil
}

func (root *rootDataSource) ListCharsets(ctx context.Context, req *ListCharsetsReq) (*ListCharsetsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListCharsets(ctx, req)
	}
	return &ListCharsetsResp{}, nil
}
func (root *rootDataSource) ListCollations(ctx context.Context, req *ListCollationsReq) (*ListCollationsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListCollations(ctx, req)
	}
	return &ListCollationsResp{}, nil
}

func (root *rootDataSource) DescribeDialogDetails(ctx context.Context, req *DescribeDialogDetailsReq) (*DescribeDialogDetailsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeDialogDetails(ctx, req)
	}
	return &DescribeDialogDetailsResp{}, nil
}

func (root *rootDataSource) DescribeDialogStatistics(ctx context.Context, req *DescribeDialogStatisticsReq) (*DescribeDialogStatisticsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeDialogStatistics(ctx, req)
	}
	return &DescribeDialogStatisticsResp{}, nil
}

func (root *rootDataSource) ListSchema(ctx context.Context, req *ListSchemaReq) (*ListSchemaResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListSchema(ctx, req)
	}
	return &ListSchemaResp{}, nil
}

func (root *rootDataSource) ListSequence(ctx context.Context, req *ListSequenceReq) (*ListSequenceResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListSequence(ctx, req)
	}
	return &ListSequenceResp{}, nil
}

func (root *rootDataSource) ListPgCollations(ctx context.Context, req *ListPgCollationsReq) (*ListPgCollationsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListPgCollations(ctx, req)
	}
	return &ListPgCollationsResp{}, nil
}

func (root *rootDataSource) ListPgUsers(ctx context.Context, req *ListPgUsersReq) (*ListPgUsersResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListPgUsers(ctx, req)
	}
	return &ListPgUsersResp{}, nil
}

func (root *rootDataSource) ListTableSpaces(ctx context.Context, req *ListTableSpacesReq) (*ListTableSpacesResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListTableSpaces(ctx, req)
	}
	return &ListTableSpacesResp{}, nil
}

func (root *rootDataSource) DescribeEngineStatus(ctx context.Context, req *DescribeEngineStatusReq) (*DescribeEngineStatusResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeEngineStatus(ctx, req)
	}
	return &DescribeEngineStatusResp{}, nil
}

func (root *rootDataSource) KillProcess(ctx context.Context, req *KillProcessReq) (*KillProcessResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.KillProcess(ctx, req)
	}
	return &KillProcessResp{}, nil
}

func (root *rootDataSource) DescribeTrxAndLocks(ctx context.Context, req *DescribeTrxAndLocksReq) (*DescribeTrxAndLocksResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeTrxAndLocks(ctx, req)
	}
	return &DescribeTrxAndLocksResp{}, nil
}

func (root *rootDataSource) DescribeLockCurrentWaits(ctx context.Context, req *DescribeLockCurrentWaitsReq) (*DescribeLockCurrentWaitsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeLockCurrentWaits(ctx, req)
	}
	return &DescribeLockCurrentWaitsResp{}, nil
}

func (root *rootDataSource) DescribeDeadlock(ctx context.Context, req *DescribeDeadlockReq) (*DescribeDeadlockResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeDeadlock(ctx, req)
	}
	return &DescribeDeadlockResp{}, nil
}

func (root *rootDataSource) DescribeTableSpaceAutoIncr(ctx context.Context, req *DescribeTableSpaceReq) (*shared.DescribeTableSpaceAutoIncrResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeTableSpaceAutoIncr(ctx, req)
	}
	return &shared.DescribeTableSpaceAutoIncrResp{}, nil
}

func (root *rootDataSource) DescribeDeadlockDetect(ctx context.Context, req *DescribeDeadlockDetectReq) (*DescribeDeadlockDetectResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeDeadlockDetect(ctx, req)
	}
	return &DescribeDeadlockDetectResp{}, nil
}

func (root *rootDataSource) DescribeDialogInfos(ctx context.Context, req *DescribeDialogInfosReq) (*DescribeDialogInfosResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeDialogInfos(ctx, req)
	}
	return &DescribeDialogInfosResp{}, nil
}
func (root *rootDataSource) DescribeCurrentConn(ctx context.Context, req *DescribeCurrentConnsReq) (*DescribeCurrentConnsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeCurrentConn(ctx, req)
	}
	return &DescribeCurrentConnsResp{}, nil
}
func (root *rootDataSource) DescribeTableSpace(ctx context.Context, req *DescribeTableSpaceReq) (*shared.DescribeTableSpaceResp, error) {
	log.Info(ctx, "DescribeTableSpace-enter1-req %s", utils.Show(req))
	log.Info(ctx, "DescribeTableSpace root %s", root.sources)
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeTableSpace(ctx, req)
	}
	return &shared.DescribeTableSpaceResp{}, nil
}

func (root *rootDataSource) ConvertTableSpaceToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp {
	if impl, ok := root.sources[sourceType]; ok {
		return impl.ConvertTableSpaceToModel(ctx, sourceType, resp)
	}
	return &model.DescribeTableSpaceResp{}
}

func (root *rootDataSource) DescribeTableColumn(ctx context.Context, req *DescribeTableInfoReq) (*shared.DescribeTableColumnResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeTableColumn(ctx, req)
	}
	return &shared.DescribeTableColumnResp{}, nil
}

func (root *rootDataSource) ConvertTableColumnToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableColumnResp) *model.DescribeTableColumnResp {
	if impl, ok := root.sources[sourceType]; ok {
		return impl.ConvertTableColumnToModel(ctx, sourceType, resp)
	}
	return &model.DescribeTableColumnResp{}
}

func (root *rootDataSource) DescribeTableIndex(ctx context.Context, req *DescribeTableInfoReq) (*shared.DescribeTableIndexResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeTableIndex(ctx, req)
	}
	return &shared.DescribeTableIndexResp{}, nil
}

func (root *rootDataSource) ConvertTableIndexToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableIndexResp) *model.DescribeTableIndexResp {
	if impl, ok := root.sources[sourceType]; ok {
		return impl.ConvertTableIndexToModel(ctx, sourceType, resp)
	}
	return &model.DescribeTableIndexResp{}
}

func (root *rootDataSource) FormatDescribeStorageCapacityResp(sourceType shared.DataSourceType, diskSize *GetDiskSizeResp, storageSpace float64) *model.DescribeStorageCapacityResp {
	if impl, ok := root.sources[sourceType]; ok {
		return impl.FormatDescribeStorageCapacityResp(sourceType, diskSize, storageSpace)
	}
	return &model.DescribeStorageCapacityResp{}
}

func (root *rootDataSource) GetAvgCpuUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetAvgCpuUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetAvgSlowQueries(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetAvgSlowQueries(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetDataPointCountSlowQueries(ctx context.Context, req *GetMetricUsageReq) (int, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetDataPointCountSlowQueries(ctx, req)
	}
	return 0, nil
}

func (root *rootDataSource) GetMinCpuUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMinCpuUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMaxCpuUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxCpuUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetCpuUsageMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetCpuUsageMetricDetail(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) DiagRootCauseALL(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagRootCauseResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.DiagRootCauseALL(ctx, req)
	}
	return &model.DescribeDiagRootCauseResp{}, nil
}

func (root *rootDataSource) DiagRootCauseYoYQoQ(ctx context.Context, req *GetMetricUsageReq) (map[string]float64, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.DiagRootCauseYoYQoQ(ctx, req)
	}
	return map[string]float64{}, nil
}

func (root *rootDataSource) DiagRootCauseDiskMetrics(ctx context.Context, req *GetMetricUsageReq) (map[string]string, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.DiagRootCauseDiskMetrics(ctx, req)
	}
	return map[string]string{}, nil
}

func (root *rootDataSource) GetAvgMemUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetAvgMemUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMinMemUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMinMemUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMaxMemUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxMemUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMemUsageMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMemUsageMetricDetail(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetDiskUsageMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetDiskUsageMetricDetail(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetConnectedRatioMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetConnectedRatioMetricDetail(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetCpuMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetCpuMetricDetail(ctx, req)
	}
	return &model.DescribeDiagItemDetailResp{}, nil
}

func (root *rootDataSource) GetInspectionCpuMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionCpuMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetInspectionMemMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionMemMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetInspectionDiskMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionDiskMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetInspectionQpsMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionQpsMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetInspectionTpsMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionTpsMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetInspectionConnectedMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionConnectedMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetInspectionConnRatioMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionConnRatioMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}
func (root *rootDataSource) GetInspectionOutputMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionOutputMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetInspectionInputMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionInputMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetInspectionBpHitMetric(ctx context.Context, req *GetMetricUsageReq) (*model.ItemDataResult_, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetInspectionBpHitMetric(ctx, req)
	}
	return &model.ItemDataResult_{}, nil
}

func (root *rootDataSource) GetAvgDiskUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetAvgDiskUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMaxDiskUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxDiskUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMinDiskUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMinDiskUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMaxConnections(ctx context.Context, req *GetMaxConnectionsReq) (int, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxConnections(ctx, req)
	}
	return 0, nil
}

func (root *rootDataSource) GetDiskAvailableDays(ctx context.Context, req *GetDiskAvailableDaysReq) (*GetDiskAvailableDaysResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetDiskAvailableDays(ctx, req)
	}
	return &GetDiskAvailableDaysResp{}, nil
}

func (root *rootDataSource) GetDiskFutureSize(ctx context.Context, req *GetDiskFutureSizeReq) (*GetDiskFutureSizeResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetDiskFutureSize(ctx, req)
	}
	return &GetDiskFutureSizeResp{}, nil
}

func (root *rootDataSource) GetMemMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMemMetricDetail(ctx, req)
	}
	return &model.DescribeDiagItemDetailResp{}, nil
}

func (root *rootDataSource) GetDiskMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetDiskMetricDetail(ctx, req)
	}
	return &model.DescribeDiagItemDetailResp{}, nil
}

func (root *rootDataSource) GetSessionMetricDetail(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetSessionMetricDetail(ctx, req)
	}
	return &model.DescribeDiagItemDetailResp{}, nil
}

func (root *rootDataSource) GetAvgQpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetAvgQpsUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMaxQpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxQpsUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMinQpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMinQpsUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetAvgTpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetAvgTpsUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMaxTpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxTpsUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMinTpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMinTpsUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetTpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetTpsUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetQpsUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetQpsUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetAvgConnectionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetAvgConnectionUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMaxConnectionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxConnectionUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMinConnectionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMinConnectionUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetAvgSessionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetAvgSessionUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMaxSessionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxSessionUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetMinSessionUsage(ctx context.Context, req *GetMetricUsageReq) (*GetMetricUsageResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMinSessionUsage(ctx, req)
	}
	return &GetMetricUsageResp{}, nil
}

func (root *rootDataSource) GetLatestDiskUsage(ctx context.Context, req *GetLatestDiskUsageReq) (float64, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetLatestDiskUsage(ctx, req)
	}
	return 0, nil
}

func (root *rootDataSource) HealthSummary(ctx context.Context, req *GetMetricUsageReq) ([]*model.Resource, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.HealthSummary(ctx, req)
	}
	return []*model.Resource{}, nil
}

func (root *rootDataSource) GetMonitorByMetric(ctx context.Context, req *GetMonitorByMetricReq) ([]*model.MetricResource, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMonitorByMetric(ctx, req)
	}
	return []*model.MetricResource{}, nil
}

/*func (root *rootDataSource) DiagRootCause(ctx context.Context, req *GetMetricUsageReq) (*model.DescribeDiagRootCauseResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.DiagRootCause(ctx, req)
	}
	return &model.DescribeDiagRootCauseResp{}, nil
}*/

func (root *rootDataSource) ListInstanceNodes(ctx context.Context, req *ListInstanceNodesReq) (*ListInstanceNodesResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.ListInstanceNodes(ctx, req)
	}
	return &ListInstanceNodesResp{}, nil
}

func (root *rootDataSource) ListInstanceNodesOri(ctx context.Context, req *ListInstanceNodesReq) (*ListInstanceNodesOriResp, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.ListInstanceNodesOri(ctx, req)
	}
	return &ListInstanceNodesOriResp{}, nil
}

func (root *rootDataSource) ListCollections(ctx context.Context, req *ListCollectionsReq) (*ListCollectionsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListCollections(ctx, req)
	}
	return &ListCollectionsResp{}, nil
}

func (root *rootDataSource) ListIndexs(ctx context.Context, req *ListIndexesReq) (*ListIndexesResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListIndexs(ctx, req)
	}
	return &ListIndexesResp{}, nil
}

func (root *rootDataSource) ListMongoDBs(ctx context.Context, req *ListMongoDBsReq) (*ListMongoDBsResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListMongoDBs(ctx, req)
	}
	return &ListMongoDBsResp{}, nil
}

func (root *rootDataSource) GetDBInnerAddress(ctx context.Context, req *GetDBInnerAddressReq) (*GetDBInnerAddressResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.GetDBInnerAddress(ctx, req)
	}
	return &GetDBInnerAddressResp{}, nil
}

func (root *rootDataSource) ExecuteDQL(ctx context.Context, req *ExecuteDQLReq) (*ExecuteDQLResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ExecuteDQL(ctx, req)
	}
	return &ExecuteDQLResp{}, nil
}

func (root *rootDataSource) ExecuteDMLAndGetAffectedRows(ctx context.Context, req *ExecuteDMLAndGetAffectedRowsReq) (int64, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ExecuteDMLAndGetAffectedRows(ctx, req)
	}
	return 0, nil
}

func (root *rootDataSource) GetPartitionInfos(ctx context.Context, dbSource *shared.DataSource, dbName string) ([]*DbPartitionInfo, error) {
	if impl, ok := root.sources[dbSource.Type]; ok {
		return impl.GetPartitionInfos(ctx, dbSource, dbName)
	}
	return []*DbPartitionInfo{}, nil
}

func (root *rootDataSource) GetShardingDbType(ctx context.Context, dbSource *shared.DataSource, dbName string, tableName string) (string, error) {
	if impl, ok := root.sources[dbSource.Type]; ok {
		return impl.GetShardingDbType(ctx, dbSource, dbName, tableName)
	}
	return "", nil
}

func (root *rootDataSource) ExplainCommand(ctx context.Context, req *ExplainCommandReq) (*ExplainCommandResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ExplainCommand(ctx, req)
	}
	return &ExplainCommandResp{}, nil
}

func (root *rootDataSource) ListInstancePods(ctx context.Context, req *ListInstancePodsReq) (*ListInstancePodsResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ListInstancePods(ctx, req)
	}
	return &ListInstancePodsResp{}, nil
}

func (root *rootDataSource) DescribeInstanceAddress(ctx context.Context, req *DescribeInstanceAddressReq) (*DescribeInstanceAddressResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeInstanceAddress(ctx, req)
	}
	return &DescribeInstanceAddressResp{}, nil
}

func (root *rootDataSource) DescribeInstanceProxyAddress(ctx context.Context, req *DescribeInstanceAddressReq) (*DescribeInstanceAddressResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeInstanceProxyAddress(ctx, req)
	}
	return &DescribeInstanceAddressResp{}, nil
}

func (root *rootDataSource) DescribeInstanceAddressList(ctx context.Context, req *DescribeInstanceAddressReq) ([]*DescribeInstanceAddressResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeInstanceAddressList(ctx, req)
	}
	return []*DescribeInstanceAddressResp{}, nil
}

func (root *rootDataSource) DescribeSQLCCLConfig(ctx context.Context, req *DescribeSQLCCLConfigReq) (*DescribeSQLCCLConfigResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeSQLCCLConfig(ctx, req)
	}
	return &DescribeSQLCCLConfigResp{}, nil
}

func (root *rootDataSource) ModifySQLCCLConfig(ctx context.Context, req *ModifySQLCCLConfigReq) (*ModifySQLCCLConfigResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ModifySQLCCLConfig(ctx, req)
	}
	return &ModifySQLCCLConfigResp{}, nil
}

func (root *rootDataSource) AddSQLCCLRule(ctx context.Context, req *AddSQLCCLRuleReq) (*AddSQLCCLRuleResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.AddSQLCCLRule(ctx, req)
	}
	return &AddSQLCCLRuleResp{}, nil
}

func (root *rootDataSource) DeleteSQLCCLRule(ctx context.Context, req *DeleteSQLCCLRuleReq) (*DeleteSQLCCLRuleResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DeleteSQLCCLRule(ctx, req)
	}
	return &DeleteSQLCCLRuleResp{}, nil
}

func (root *rootDataSource) FlushSQLCCLRule(ctx context.Context, req *FlushSQLCCLRuleReq) (*FlushSQLCCLRuleResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.FlushSQLCCLRule(ctx, req)
	}
	return &FlushSQLCCLRuleResp{}, nil
}

func (root *rootDataSource) ListSQLCCLRules(ctx context.Context, req *ListSQLCCLRulesReq) (*ListSQLCCLRulesResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ListSQLCCLRules(ctx, req)
	}
	return &ListSQLCCLRulesResp{}, nil
}
func (root *rootDataSource) DescribeSqlFingerPrintOrKeywords(ctx context.Context, req *DescribeSqlFingerPrintOrKeywordsReq) (*DescribeSqlFingerPrintOrKeywordsResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeSqlFingerPrintOrKeywords(ctx, req)
	}
	return &DescribeSqlFingerPrintOrKeywordsResp{}, nil
}

func (root *rootDataSource) ModifyProxyThrottleRule(ctx context.Context, req *ModifyProxyThrottleRuleReq) (*ModifyProxyThrottleRuleResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ModifyProxyThrottleRule(ctx, req)
	}
	return &ModifyProxyThrottleRuleResp{}, nil
}

func (root *rootDataSource) IsMyOwnInstance(ctx context.Context, instanceId string, dsType shared.DataSourceType) bool {
	if impl, ok := root.sources[dsType]; ok {
		return impl.IsMyOwnInstance(ctx, instanceId, dsType)
	}
	return false
}

func (root *rootDataSource) GetTableIndexInfo(ctx context.Context, req *GetTableIndexInfoReq) (*GetTableInfoIndexResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.GetTableIndexInfo(ctx, req)
	}
	return &GetTableInfoIndexResp{}, nil
}

func (root *rootDataSource) GetTableIndexValue(ctx context.Context, req *GetIndexValueReq) (*GetIndexValueResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.GetTableIndexValue(ctx, req)
	}
	return &GetIndexValueResp{}, nil
}

func (root *rootDataSource) GetCurrentBandwidth(ctx context.Context, req *GetCurrentBandwidthReq) (*InstanceBandwidthInfo, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetCurrentBandwidth(ctx, req)
	}
	return &InstanceBandwidthInfo{}, nil
}

func (root *rootDataSource) BandwidthScale(ctx context.Context, req *BandwidthScaleReq) error {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.BandwidthScale(ctx, req)
	}
	return nil
}

func (root *rootDataSource) GetMinBandwidth(ctx context.Context, req *GetMinMaxBandwidthReq) (int, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMinBandwidth(ctx, req)
	}
	return 0, nil
}

func (root *rootDataSource) GetMaxBandwidth(ctx context.Context, req *GetMinMaxBandwidthReq) (int, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.GetMaxBandwidth(ctx, req)
	}
	return 0, nil
}

func (root *rootDataSource) ListErrLogs(ctx context.Context, req *ListErrLogsReq) (*ListErrLogsResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ListErrLogs(ctx, req)
	}
	return &ListErrLogsResp{}, nil
}

func (root *rootDataSource) DescribeAutoKillSessionConfig(ctx context.Context, req *DescribeAutoKillSessionConfigReq) (*DescribeAutoKillSessionConfigResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeAutoKillSessionConfig(ctx, req)
	}
	return &DescribeAutoKillSessionConfigResp{}, nil
}

func (root *rootDataSource) ModifyAutoKillSessionConfig(ctx context.Context, req *ModifyAutoKillSessionConfigReq) (*ModifyAutoKillSessionConfigResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ModifyAutoKillSessionConfig(ctx, req)
	}
	return &ModifyAutoKillSessionConfigResp{}, nil
}

func (root *rootDataSource) DescribeInstanceVersion(ctx context.Context, req *DescribeInstanceVersionReq) (*DescribeInstanceVersionResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeInstanceVersion(ctx, req)
	}
	return &DescribeInstanceVersionResp{}, nil
}

func (root *rootDataSource) GetDiskSize(ctx context.Context, req *GetDiskSizeReq) (*GetDiskSizeResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.GetDiskSize(ctx, req)
	}
	return &GetDiskSizeResp{}, nil
}

func (root *rootDataSource) GetUsedSize(ctx context.Context, req *GetDiskSizeReq) (int64, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.GetUsedSize(ctx, req)
	}
	return 0, nil
}

func (root *rootDataSource) DescribeDBInstanceEndpoints(ctx context.Context, req *DescribeDBInstanceEndpointsReq) (*DescribeDBInstanceEndpointsResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBInstanceEndpoints(ctx, req)
	}
	return &DescribeDBInstanceEndpointsResp{}, nil
}

func (root *rootDataSource) DescribeDBInstanceShardInfos(ctx context.Context, req *DescribeDBInstanceShardInfosReq) (*DescribeDBInstanceShardInfosResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeDBInstanceShardInfos(ctx, req)
	}
	return &DescribeDBInstanceShardInfosResp{}, nil
}

func (root *rootDataSource) DescribeInstanceReplicaDelay(ctx context.Context, req *DescribeDBInstanceDetailReq) (int64, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeInstanceReplicaDelay(ctx, req)
	}
	return -1, nil
}

func (root *rootDataSource) GetCurrentMetricData(ctx context.Context, req *GetMetricDatapointsReq) (*GetMetricDatapointsResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetCurrentMetricData(ctx, req)
	}
	return nil, nil
}

func (root *rootDataSource) GetPreSecondMetricData(ctx context.Context, req *GetMetricDatapointsReq) (*GetMetricDatapointsResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetPreSecondMetricData(ctx, req)
	}
	return nil, nil
}

func (root *rootDataSource) GetPreSecondMetricDataByInstance(ctx context.Context, req *GetMetricDatapointsReq) (*GetMetricDatapointsResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetPreSecondMetricDataByInstance(ctx, req)
	}
	return nil, nil
}

func (root *rootDataSource) DescribeInstanceFeatures(ctx context.Context, req *DescribeInstanceFeaturesReq) (*DescribeInstanceFeaturesResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeInstanceFeatures(ctx, req)
	}
	log.Warn(ctx, "can't find implement for ds type %v", req.Type.String())
	return nil, nil
}

func (root *rootDataSource) DescribeInstanceVariables(ctx context.Context, req *DescribeInstanceVariablesReq) (
	*DescribeInstanceVariablesResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeInstanceVariables(ctx, req)
	}
	return &DescribeInstanceVariablesResp{}, nil
}

func (root *rootDataSource) DescribePrimaryKeyRange(ctx context.Context, req *DescribePrimaryKeyRangeReq) (
	*DescribePrimaryKeyRangeResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribePrimaryKeyRange(ctx, req)
	}
	return &DescribePrimaryKeyRangeResp{}, nil
}

func (root *rootDataSource) DescribeSQLAdvisorTableMeta(ctx context.Context, req *DescribeSQLAdvisorTableMetaReq) (
	*DescribeSQLAdvisorTableMetaResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeSQLAdvisorTableMeta(ctx, req)
	}
	return &DescribeSQLAdvisorTableMetaResp{}, nil
}

func (root *rootDataSource) DescribeSampleData(ctx context.Context, req *DescribeSampleDataReq) (
	*DescribeSampleDataResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.DescribeSampleData(ctx, req)
	}
	return &DescribeSampleDataResp{}, nil
}

func (root *rootDataSource) EnsureAccount(ctx context.Context, req *EnsureAccountReq) error {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.EnsureAccount(ctx, req)
	}
	return nil
}

func (root *rootDataSource) GetDatasourceAddress(ctx context.Context, ds *shared.DataSource) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.GetDatasourceAddress(ctx, ds)
	}
	return nil
}

func (root *rootDataSource) GetDBServiceTreeMountInfo(ctx context.Context, req *GetDBServiceTreeMountInfoReq) (*GetDBServiceTreeMountInfoResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetDBServiceTreeMountInfo(ctx, req)
	}
	return &GetDBServiceTreeMountInfoResp{}, nil
}

func (root *rootDataSource) ModifySQLKillRule(ctx context.Context, req *ModifySQLKillRuleReq) (*ModifySQLKillRuleResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ModifySQLKillRule(ctx, req)
	}
	return &ModifySQLKillRuleResp{}, nil
}

func (root *rootDataSource) ListSQLKillRules(ctx context.Context, req *ListSQLKillRulesReq) (*ListSQLKillRulesResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ListSQLKillRules(ctx, req)
	}
	return &ListSQLKillRulesResp{}, nil
}

func (root *rootDataSource) ListSchemaTables(ctx context.Context, req *ListSchemaTablesReq) (*ListSchemaTablesResp, error) {
	if impl, ok := root.sources[req.Source.Type]; ok {
		return impl.ListSchemaTables(ctx, req)
	}
	return &ListSchemaTablesResp{}, nil
}

func (root *rootDataSource) GetDBInstanceInfo(ctx context.Context, req *GetDBInstanceInfoReq) (*GetDBInstanceInfoResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetDBInstanceInfo(ctx, req)
	}
	return &GetDBInstanceInfoResp{}, nil
}

func (root *rootDataSource) GetInstancePrimaryNodeId(ctx context.Context, req *GetInstancePrimaryNodeIdReq) (*GetInstancePrimaryNodeIdResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetInstancePrimaryNodeId(ctx, req)
	}
	return &GetInstancePrimaryNodeIdResp{}, nil
}

func (root *rootDataSource) GetManagedAccountAndPwd(ctx context.Context, req *shared.DataSource) (*GetManagedAccountAndPwdResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetManagedAccountAndPwd(ctx, req)
	}
	return &GetManagedAccountAndPwdResp{}, nil
}

func (root *rootDataSource) CalculateSpecAfterScale(ctx context.Context, req *CalculateSpecAfterScaleReq) (*CalculateSpecAfterScaleResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.CalculateSpecAfterScale(ctx, req)
	}
	return &CalculateSpecAfterScaleResp{}, nil
}

func (root *rootDataSource) ModifyDBInstanceSpec(ctx context.Context, req *ModifyDBInstanceSpecReq) (*ModifyDBInstanceSpecResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ModifyDBInstanceSpec(ctx, req)
	}
	return &ModifyDBInstanceSpecResp{}, nil
}

func (root *rootDataSource) ValidateDryRun(ctx context.Context, req *ValidateDryRunReq) *shared.ValidateResponse {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ValidateDryRun(ctx, req)
	}
	return &shared.ValidateResponse{}
}

func (root *rootDataSource) ValidateOriginalTable(ctx context.Context, req *ValidateOriginalTableReq) *shared.ValidateResponse {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ValidateOriginalTable(ctx, req)
	}
	return &shared.ValidateResponse{}
}

func (root *rootDataSource) ValidateUniqIndex(ctx context.Context, req *ValidateUniqIndexReq) *shared.ValidateResponse {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ValidateUniqIndex(ctx, req)
	}
	return &shared.ValidateResponse{}
}

func (root *rootDataSource) IsTableExists(ctx context.Context, req *IsTableExistsReq) (bool, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.IsTableExists(ctx, req)
	}
	return false, nil
}

func (root *rootDataSource) ExecuteSql(ctx context.Context, req *ExecuteReq) error {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.ExecuteSql(ctx, req)
	}
	return nil
}

func (root *rootDataSource) GetCurrentMaxConnections(ctx context.Context, req *GetCurrentMaxConnectionsReq) (int, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetCurrentMaxConnections(ctx, req)
	}
	return 0, fmt.Errorf("not implement")
}

func (root *rootDataSource) GetInstanceSlaveAddress(ctx types.Context, req *GetInstanceSlaveAddressReq) (*GetInstanceSlaveAddressResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.GetInstanceSlaveAddress(ctx, req)
	}
	return &GetInstanceSlaveAddressResp{}, nil
}

func (root *rootDataSource) GetCreateTableInfo(ctx context.Context, ds *shared.DataSource, tableName string) (string, error) {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.GetCreateTableInfo(ctx, ds, tableName)
	}
	return "", nil
}

func (root *rootDataSource) CheckAccountPrivilege(ctx context.Context, req *CheckDBWAccountReq) (bool, error) {
	if impl, ok := root.sources[req.DSType]; ok {
		return impl.CheckAccountPrivilege(ctx, req)
	}
	return false, nil
}

func (root *rootDataSource) ResetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	if impl, ok := root.sources[dsType]; ok {
		return impl.ResetAccount(ctx, instanceId, dsType)
	}
	return nil
}

func (root *rootDataSource) GrantReplicationPrivilege(ctx context.Context, ds *shared.DataSource, accountName string) error {
	if impl, ok := root.sources[ds.Type]; ok {
		return impl.GrantReplicationPrivilege(ctx, ds, accountName)
	}
	return nil
}

func (root *rootDataSource) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	if impl, ok := root.sources[ds]; ok {
		return impl.CheckInstanceState(ctx, instanceId, ds, isConnectedInstance)
	}
	return nil
}
func (root *rootDataSource) DescribeSqlType(ctx context.Context, req *DescribeSqlTypeReq) (*DescribeSqlTypeResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeSqlType(ctx, req)
	}
	return &DescribeSqlTypeResp{}, nil
}

func (root *rootDataSource) DescribeInstancePodAddress(ctx context.Context, req *DescribeInstanceAddressReq) (*DescribeInstanceAddressResp, error) {
	if impl, ok := root.sources[req.Type]; ok {
		return impl.DescribeInstancePodAddress(ctx, req)
	}
	return nil, nil
}

func (root *rootDataSource) DescribeDBAutoScalingConfig(ctx context.Context, req *DescribeDBAutoScalingConfigReq) (*DescribeDBAutoScalingConfigResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.DescribeDBAutoScalingConfig(ctx, req)
	}
	return &DescribeDBAutoScalingConfigResp{}, nil
}

func (root *rootDataSource) ModifyDBAutoScalingConfig(ctx context.Context, req *ModifyDBAutoScalingConfigReq) (*ModifyDBAutoScalingConfigResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.ModifyDBAutoScalingConfig(ctx, req)
	}
	return &ModifyDBAutoScalingConfigResp{}, nil
}

func (root *rootDataSource) DescribeDBAutoScaleEvents(ctx context.Context, req *DescribeDBAutoScaleEventsReq) (*DescribeDBAutoScaleEventsResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.DescribeDBAutoScaleEvents(ctx, req)
	}
	return &DescribeDBAutoScaleEventsResp{}, nil
}
func (root *rootDataSource) ModifyDBLocalSpecManually(ctx context.Context, req *ModifyDBLocalSpecManuallyReq) (*ModifyDBLocalSpecManuallyResp, error) {
	if impl, ok := root.sources[req.InstanceType]; ok {
		return impl.ModifyDBLocalSpecManually(ctx, req)
	}
	return &ModifyDBLocalSpecManuallyResp{}, nil
}

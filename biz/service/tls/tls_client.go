package tls

import (
	"bytes"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/shopspring/decimal"

	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/qjpcpu/fp"
	sdk "github.com/volcengine/volc-sdk-golang/service/tls"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
)

type client struct {
	connection *ConnectionInfo
	realClient sdk.Client
	conf       config.ConfigProvider
}

func NewTLSClient(connectionInfo *ConnectionInfo, conf config.ConfigProvider) Client {
	c := &client{
		connection: connectionInfo,
		realClient: sdk.NewClient(
			connectionInfo.Endpoint,
			connectionInfo.AccessKeyID,
			connectionInfo.AccessKeySecret,
			"",
			connectionInfo.Region,
		),
		conf: conf,
	}
	c.realClient.SetTimeout(time.Second * 25)
	return c
}

func (c *client) DescribeSlogLogTimeSeriesStats(ctx context.Context, req *shared.DescribeSlowLogTimeSeriesStatsReq) (*shared.DescribeSlowLogTimeSeriesStatsResp, error) {
	var (
		query            string
		limitStr         string
		slowLogCountList []*shared.SlowLogCount
		whereConditions  string
	)

	query = fmt.Sprintf("* | ")
	interval := req.Interval
	left := req.StartTime / interval * interval
	right := req.EndTime / interval * interval
	if req.EndTime%interval != 0 {
		right = (req.EndTime/interval + 1) * interval
	}

	if req.SearchParam != nil && req.SearchParam.SQLTemplate != "" {
		query = fmt.Sprintf(`"%s" | `, req.SearchParam.SQLTemplate)
	}

	if req.Limit == 0 {
		diff := right - left
		totalCount := int(math.Ceil(float64(diff) / float64(interval)))
		slowLogCountList = make([]*shared.SlowLogCount, totalCount)
		maxLimit := c.conf.Get(ctx).MaxSlowLogDownloadLimits
		limitStr = fmt.Sprintf("limit %d", maxLimit) //默认返回10000条，修复聚合点数限制为100的问题
	} else {
		topN := req.Limit
		slowLogCountList = make([]*shared.SlowLogCount, topN)
		limitStr = fmt.Sprintf("limit %d", req.Limit)
	}

	groupCondition := fmt.Sprintf(offsetKeyTemplate, left, interval)
	groupField := groupCondition + " AS TimestampIndex" // 兼容TLS 2.0架构
	switch req.DataSourceType {
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		whereConditions = fmt.Sprintf("where (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
	case shared.VeDBMySQL:
		whereConditions = fmt.Sprintf("where %s='%s' ", instanceKey, req.InstanceId)
	case shared.Postgres:
		whereConditions = fmt.Sprintf("where %s='%s' ", instanceKey, req.InstanceId)
	default:
		whereConditions = fmt.Sprintf("where ")
	}
	// 规避TLS bug
	whereConditions += fmt.Sprintf(" and %s ", whereConditionsMust)
	whereConditions += fmt.Sprintf(" and %s ", filterSqlParseFailed)
	// 添加SqlTemplateID
	if req.SearchParam != nil && req.SearchParam.SQLTemplateID != "" {
		whereConditions += fmt.Sprintf(" and %s='%s' ", SQLTemplateID, req.SearchParam.SQLTemplateID)
	}
	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		whereConditions += fmt.Sprintf(" and %s ", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		whereConditions += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	query = fmt.Sprintf("%s select %s,%s %s group by %s",
		query, groupField, countField, whereConditions, groupCondition)

	if req.Limit != 0 {
		// topN 倒序排序
		query += fmt.Sprintf(" order by %s DESC ", countKey)
	}
	if limitStr != "" {
		query = fmt.Sprintf("%s %s", query, limitStr)
	}
	searchLogsRequest := &sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		Query:     query,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
	log.Info(ctx, "DescribeSlogLogTimeSeriesStats call TLS SearchLogs request is %s", searchLogsRequest)
	searchLogsResponse, err := c.realClient.SearchLogsV2(searchLogsRequest)
	if err != nil {
		log.Warn(ctx, "DescribeSlogLogTimeSeriesStats: search log from topic %s error: %v, SQL: %s", searchLogsRequest.TopicID, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	schema := searchLogsResponse.AnalysisResult.Schema
	if len(schema) != 2 {
		err = fmt.Errorf("the length of result schema expect 2, got %d", len(schema))
		log.Warn(ctx, "DescribeSlogLogTimeSeriesStats: search log from topic %s error: %v, SQL: %s", searchLogsRequest.TopicID, err, query)
		return nil, err
	}

	// topN给的是整点的时间 topN分支
	if req.Limit != 0 {
		var realLength int
		for _, data := range searchLogsResponse.AnalysisResult.Data {
			slowLogCount := new(shared.SlowLogCount)
			if offsetJson, ok := data[offsetKey]; ok {
				offset, _ := parseInt64(offsetJson)
				slowLogCount.Timestamp = left + interval*offset
			}

			if countJson, ok := data[countKey]; ok {
				count, _ := parseInt64(countJson)
				slowLogCount.Count = count
			}
			slowLogCountList[realLength] = slowLogCount
			realLength++
		}

		slowLogCountList = slowLogCountList[:realLength]
		return &shared.DescribeSlowLogTimeSeriesStatsResp{
			SlowLogCountStats: slowLogCountList,
		}, nil
	}

	for i := 0; i < len(slowLogCountList); i++ {
		if i == 0 {
			slowLogCountList[i] = &shared.SlowLogCount{
				Timestamp: req.StartTime,
				Count:     0,
			}
			continue
		}
		slowLogCountList[i] = &shared.SlowLogCount{
			Timestamp: left + int64(i)*interval,
			Count:     0,
		}
	}
	for _, data := range searchLogsResponse.AnalysisResult.Data {
		var index int64
		if offsetJson, ok := data[offsetKey]; ok {
			offset, _ := parseInt64(offsetJson)
			index = offset
		}
		// 修复 index out of range panic
		if index < 0 || index >= int64(len(slowLogCountList)) {
			continue
		}
		slowLogCount := slowLogCountList[index]
		if countJson, ok := data[countKey]; ok {
			count, _ := parseInt64(countJson)
			slowLogCount.Count = count
		}
	}

	return &shared.DescribeSlowLogTimeSeriesStatsResp{
		SlowLogCountStats: slowLogCountList,
	}, nil
}

func (c *client) DescribeAggregateSlowLogs(ctx context.Context, req *shared.DescribeAggregateSlowLogsReq) (*shared.DescribeAggregateSlowLogsResp, error) {
	query, err := GenAggrSlowLogQuery(ctx, req, c.getInternalUsers(ctx, req.DataSourceType.String()))
	if err != nil {
		return nil, err
	}
	searchLogsRequest := &sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		Query:     query,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
	log.Info(ctx, "DescribeAggregateSlowLogs call TLS SearchLogs request is %s", searchLogsRequest)
	resp, err := c.realClient.SearchLogsV2(searchLogsRequest)
	if err != nil {
		log.Warn(ctx, "DescribeAggregateSlowLogs: search log from topic %s error: %v, SQL: %s", searchLogsRequest.TopicID, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	aggregateSlowLogList := make([]*shared.AggregateSlowLog, len(resp.AnalysisResult.Data))
	totalLockTime := 0.0
	totalQueryTime := 0.0
	totalRowsExamined := 0.0
	totalRowsSent := 0.0
	totalExecuteCount := 0.0

	for i := 0; i < len(resp.AnalysisResult.Data); i++ {
		aggregateSlowLog := new(shared.AggregateSlowLog)
		aggregateSlowLog.LockTimeStats = new(shared.StatisticResult)
		aggregateSlowLog.QueryTimeStats = new(shared.StatisticResult)
		aggregateSlowLog.RowsExaminedStats = new(shared.StatisticResult)
		aggregateSlowLog.RowsSentStats = new(shared.StatisticResult)

		if sqlTemplate, ok := resp.AnalysisResult.Data[i][sqlTemplateKey]; ok {
			if sqlTemplate != nil {
				aggregateSlowLog.SQLTemplate = sqlTemplate.(string)
			} else {
				aggregateSlowLog.SQLTemplate = ""
			}
		}
		if sqlTemplateID, ok := resp.AnalysisResult.Data[i][SQLTemplateID]; ok {
			if sqlTemplateID != nil {
				aggregateSlowLog.SQLTemplateID = sqlTemplateID.(string)
			} else {
				aggregateSlowLog.SQLTemplateID = ""
			}
		}

		if db, ok := resp.AnalysisResult.Data[i][dbKey]; ok {
			if db != nil {
				aggregateSlowLog.DB = db.(string)
			} else {
				aggregateSlowLog.DB = ""
			}
		}

		if user, ok := resp.AnalysisResult.Data[i][userKey]; ok {
			if user != nil {
				aggregateSlowLog.User = user.(string)
			} else {
				aggregateSlowLog.User = ""
			}

		}

		if sourceIP, ok := resp.AnalysisResult.Data[i][sourceIPKey]; ok {
			if sourceIP != nil {
				aggregateSlowLog.SourceIP = sourceIP.(string)
			} else {
				aggregateSlowLog.SourceIP = ""
			}
		}

		if sqlType, ok := resp.AnalysisResult.Data[i][SQLType]; ok {
			if sqlType != nil {
				aggregateSlowLog.SQLType = sqlType.(string)
			} else {
				aggregateSlowLog.SQLType = "-"
			}
		}

		if count, ok := resp.AnalysisResult.Data[i][countKey]; ok {
			aggregateSlowLog.ExecuteCount, _ = parseInt64(count)
			totalExecuteCount += float64(aggregateSlowLog.ExecuteCount)
		}

		// lockTime stats
		if sumLockTime, ok := resp.AnalysisResult.Data[i][sumLockTimeKey]; ok {
			aggregateSlowLog.LockTimeStats.Total, _ = parseFloat64(sumLockTime)
			totalLockTime += aggregateSlowLog.LockTimeStats.Total
		}
		if minLockTime, ok := resp.AnalysisResult.Data[i][minLockTimeKey]; ok {
			aggregateSlowLog.LockTimeStats.Min, _ = parseFloat64(minLockTime)
		}
		if maxLockTime, ok := resp.AnalysisResult.Data[i][maxLockTimeKey]; ok {
			aggregateSlowLog.LockTimeStats.Max, _ = parseFloat64(maxLockTime)
		}
		if avgLockTime, ok := resp.AnalysisResult.Data[i][averageLockTimeKey]; ok {
			aggregateSlowLog.LockTimeStats.Average, _ = parseFloat64(avgLockTime)
		}

		// queryTime stats
		if sumQueryTime, ok := resp.AnalysisResult.Data[i][sumQueryTimeKey]; ok {
			aggregateSlowLog.QueryTimeStats.Total, _ = parseFloat64(sumQueryTime)
			totalQueryTime += aggregateSlowLog.QueryTimeStats.Total
		}
		if minQueryTime, ok := resp.AnalysisResult.Data[i][minQueryTimeKey]; ok {
			aggregateSlowLog.QueryTimeStats.Min, _ = parseFloat64(minQueryTime)
		}
		if maxQueryTime, ok := resp.AnalysisResult.Data[i][maxQueryTimeKey]; ok {
			aggregateSlowLog.QueryTimeStats.Max, _ = parseFloat64(maxQueryTime)
		}
		if avgQueryTime, ok := resp.AnalysisResult.Data[i][averageQueryTimeKey]; ok {
			aggregateSlowLog.QueryTimeStats.Average, _ = parseFloat64(avgQueryTime)
		}

		// rowsSent stats
		if sumRowsSent, ok := resp.AnalysisResult.Data[i][sumRowsSentKey]; ok {
			aggregateSlowLog.RowsSentStats.Total, _ = parseFloat64(sumRowsSent)
			totalRowsSent += aggregateSlowLog.RowsSentStats.Total
		}
		if minRowsSent, ok := resp.AnalysisResult.Data[i][minRowsSentKey]; ok {
			aggregateSlowLog.RowsSentStats.Min, _ = parseFloat64(minRowsSent)
		}
		if maxRowsSent, ok := resp.AnalysisResult.Data[i][maxRowsSentKey]; ok {
			aggregateSlowLog.RowsSentStats.Max, _ = parseFloat64(maxRowsSent)
		}
		if averageRowsSent, ok := resp.AnalysisResult.Data[i][averageRowsSentKey]; ok {
			aggregateSlowLog.RowsSentStats.Average, _ = parseFloat64(averageRowsSent)
		}

		// rowsExamined stats
		if sumRowsExamined, ok := resp.AnalysisResult.Data[i][sumRowsExaminedKey]; ok {
			aggregateSlowLog.RowsExaminedStats.Total, _ = parseFloat64(sumRowsExamined)
			totalRowsExamined += aggregateSlowLog.RowsExaminedStats.Total
		}
		if minRowsExamined, ok := resp.AnalysisResult.Data[i][minRowsExaminedKey]; ok {
			aggregateSlowLog.RowsExaminedStats.Min, _ = parseFloat64(minRowsExamined)
		}
		if maxRowsExamined, ok := resp.AnalysisResult.Data[i][maxRowsExaminedKey]; ok {
			aggregateSlowLog.RowsExaminedStats.Max, _ = parseFloat64(maxRowsExamined)
		}
		if averageRowsExamined, ok := resp.AnalysisResult.Data[i][averageRowsExaminedKey]; ok {
			aggregateSlowLog.RowsExaminedStats.Average, _ = parseFloat64(averageRowsExamined)
		}

		if maxTimestamp, ok := resp.AnalysisResult.Data[i][maxTimestampKey]; ok {
			maxTimestampStr, _ := parseString(maxTimestamp)
			d, _ := decimal.NewFromString(maxTimestampStr)
			aggregateSlowLog.LastAppearTime = d.IntPart()
		}

		if minTimestamp, ok := resp.AnalysisResult.Data[i][minTimestampKey]; ok {
			minTimestampStr, _ := parseString(minTimestamp)
			d, _ := decimal.NewFromString(minTimestampStr)
			aggregateSlowLog.FirstAppearTime = d.IntPart()
		}

		if field, ok := resp.AnalysisResult.Data[i][PSMKey]; ok {
			if field != nil {
				aggregateSlowLog.PSM = field.(string)
			} else {
				aggregateSlowLog.PSM = "-"
			}
		}
		if field, ok := resp.AnalysisResult.Data[i][SqlTableKey]; ok {
			if field != nil {
				aggregateSlowLog.Table = field.(string)
			} else {
				aggregateSlowLog.Table = "-"
			}
		}
		if field, ok := resp.AnalysisResult.Data[i][SqlMethodKey]; ok {
			if field != nil {
				aggregateSlowLog.SQLType = field.(string)
			} else {
				aggregateSlowLog.SQLType = "-"
			}
		}
		aggregateSlowLogList[i] = aggregateSlowLog
	}

	// TODO 方差、中位数、p95等指标需要等TLS就绪
	for i := 0; i < len(aggregateSlowLogList); i++ {
		// 计算百分比
		if totalExecuteCount != 0.0 {
			ratio := float64(aggregateSlowLogList[i].ExecuteCount) / totalExecuteCount * 100
			aggregateSlowLogList[i].ExecuteCountRatio = decimalFn(ratio)
		}

		if totalQueryTime != 0.0 {
			ratio := aggregateSlowLogList[i].QueryTimeStats.Total / totalQueryTime * 100
			aggregateSlowLogList[i].QueryTimeRatio = decimalFn(ratio)
		}

		if totalLockTime != 0.0 {
			ratio := aggregateSlowLogList[i].LockTimeStats.Total / totalLockTime * 100
			aggregateSlowLogList[i].LockTimeRatio = decimalFn(ratio)
		}

		if totalRowsSent != 0.0 {
			ratio := aggregateSlowLogList[i].RowsSentStats.Total / totalRowsSent * 100
			aggregateSlowLogList[i].RowsSentRatio = decimalFn(ratio)
		}

		if totalRowsExamined != 0.0 {
			ratio := aggregateSlowLogList[i].RowsExaminedStats.Total / totalRowsExamined * 100
			aggregateSlowLogList[i].RowsExaminedRatio = decimalFn(ratio)
		}
	}

	//默认按照总耗时降序排序 TODO 排序等待TLS就绪可以用语句排序
	var sortFn = func(aggregateSlowLogs []*shared.AggregateSlowLog, sortBy shared.SortBy, orderBy shared.OrderByForAggregateSlowLog) {
		switch orderBy {
		case shared.ExecuteCount:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].ExecuteCount < aggregateSlowLogs[j].ExecuteCount
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].ExecuteCount > aggregateSlowLogs[j].ExecuteCount
				})
			}
		case shared.TotalQueryTime:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].QueryTimeStats.Total < aggregateSlowLogs[j].QueryTimeStats.Total
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].QueryTimeStats.Total > aggregateSlowLogs[j].QueryTimeStats.Total
				})
			}
		case shared.MaxQueryTime:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].QueryTimeStats.Max < aggregateSlowLogs[j].QueryTimeStats.Max
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].QueryTimeStats.Max > aggregateSlowLogs[j].QueryTimeStats.Max
				})
			}
		case shared.MaxLockTime:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].LockTimeStats.Max < aggregateSlowLogs[j].LockTimeStats.Max
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].LockTimeStats.Max > aggregateSlowLogs[j].LockTimeStats.Max
				})
			}
		case shared.MaxRowsSent:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].RowsSentStats.Max < aggregateSlowLogs[j].RowsSentStats.Max
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].RowsSentStats.Max > aggregateSlowLogs[j].RowsSentStats.Max
				})
			}
		case shared.MaxRowsExamined:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].RowsExaminedStats.Max < aggregateSlowLogs[j].RowsExaminedStats.Max
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].RowsExaminedStats.Max > aggregateSlowLogs[j].RowsExaminedStats.Max
				})
			}
		case shared.AverageQueryTime:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].QueryTimeStats.Average < aggregateSlowLogs[j].QueryTimeStats.Average
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].QueryTimeStats.Average > aggregateSlowLogs[j].QueryTimeStats.Average
				})
			}
		case shared.AverageLockTime:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].LockTimeStats.Average < aggregateSlowLogs[j].LockTimeStats.Average
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].LockTimeStats.Average > aggregateSlowLogs[j].LockTimeStats.Average
				})
			}
		case shared.AverageRowsSent:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].RowsSentStats.Average < aggregateSlowLogs[j].RowsSentStats.Average
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].RowsSentStats.Average > aggregateSlowLogs[j].RowsSentStats.Average
				})
			}
		case shared.AverageRowsExamined:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].RowsExaminedStats.Average < aggregateSlowLogs[j].RowsExaminedStats.Average
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].RowsExaminedStats.Average > aggregateSlowLogs[j].RowsExaminedStats.Average
				})
			}

		case shared.SQLTemplate:
			if sortBy == shared.ASC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].SQLTemplate < aggregateSlowLogs[j].SQLTemplate
				})
			} else if sortBy == shared.DESC {
				sort.Slice(aggregateSlowLogs, func(i, j int) bool {
					return aggregateSlowLogs[i].SQLTemplate > aggregateSlowLogs[j].SQLTemplate
				})
			}
		}
	}

	sortFn(aggregateSlowLogList, req.SortBy, req.OrderBy)

	for i := 0; i < len(aggregateSlowLogList); i++ {
		aggregateSlowLogList[i].PTAnalysisResult = fetchPTAnalysisResult(ctx, aggregateSlowLogList[i])
	}
	return &shared.DescribeAggregateSlowLogsResp{
		AggregateSlowLogs: aggregateSlowLogList,
		Total:             int32(len(aggregateSlowLogList)),
	}, nil

}

func GenAggrSlowLogQuery(ctx context.Context, req *shared.DescribeAggregateSlowLogsReq, internalUsers []string) (string, error) {
	var (
		query           string
		groupCondition  string
		groupFields     string
		hasWhere        bool
		whereConditions string
		fields          []string
	)
	dataSourceType := req.DataSourceType

	searchParam := req.SearchParam
	// 分组字段
	switch dataSourceType {
	case shared.Postgres:
		fields = []string{sqlTemplateKey, SQLType, dbKey, userKey, sourceIPKey, SQLTemplateID}
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		fields = []string{sqlTemplateKey, dbKey, userKey, sourceIPKey, SQLTemplateID, SqlTableKey, PSMKey, SqlMethodKey}
	case shared.VeDBMySQL:
		fields = []string{sqlTemplateKey, dbKey, userKey, sourceIPKey, SQLTemplateID, SqlTableKey, PSMKey, SqlMethodKey}
	default:
		log.Warn(ctx, "unknown instance type")
		return "", consts.ErrorOf(model.ErrorCode_InstanceTypeNotSupport)
	}
	for _, group := range searchParam.GroupIgnored {
		switch group {
		case shared.User:
			fields = rmFields(fields, userKey)
		case shared.SourceIP:
			fields = rmFields(fields, sourceIPKey)
		case shared.PSMGroup:
			fields = rmFields(fields, PSMKey)
		case shared.TableGroup:
			fields = rmFields(fields, SqlTableKey)
		case shared.SqlMethodGroup:
			fields = rmFields(fields, SqlMethodKey)
		}
	}
	groupFields = strings.Join(fields, ",")
	// 分组条件
	groupCondition = fmt.Sprintf("group by %s", groupFields)

	// tls旧架构需要通过分析语句来实现对keyword的模糊查询，等切换到tls新架构可以使用检索语句支持SQLTemplate模糊查询
	//SQLTemplate:"Update insert" 即检索SQLTemplate中包含Update或insert的sql模版，分析语句中包含sql语法会影响性能
	query = fmt.Sprintf("* | select %s,%s,", groupFields, countField)
	// queryTime的统计信息
	query += strings.Join([]string{sumQueryTimeField, maxQueryTimeField, minQueryTimeField, averageQueryTimeField}, ",") + ","
	// lockTime的统计信息
	query += strings.Join([]string{sumLockTimeField, maxLockTimeField, minLockTimeField, averageLockTimeField}, ",") + ","
	// rowsSent的统计信息
	query += strings.Join([]string{sumRowsSentField, maxRowsSentField, minRowsSentField, averageRowsSentField}, ",") + ","
	// rowsExamined的统计信息
	query += strings.Join([]string{sumRowsExaminedField, maxRowsExaminedField, minRowsExaminedField, averageRowsExaminedField}, ",") + ","
	// firstAppearTime以及lastAppearTime的信息
	query += strings.Join([]string{minTimestampField, maxTimestampField}, ",")

	whereConditions, hasWhere = generateAggregateSlowLogSearchWhereConditions(searchParam)
	switch dataSourceType {
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		} else {
			query += fmt.Sprintf(" where (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		}
	case shared.VeDBMySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s'", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s'", instanceKey, req.InstanceId)
		}
	case shared.Postgres:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s'", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s'", instanceKey, req.InstanceId)
		}
	}
	// 规避TLS bug
	query += fmt.Sprintf(" and %s ", whereConditionsMust)
	query += fmt.Sprintf(" and %s ", filterSqlParseFailed)

	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s ", filter)
	}
	switch dataSourceType {
	case shared.Postgres:
		sqlTypeFilter := filterSqlTypeCondition(SQLType, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		sqlTypeFilter := filterSqlTypeCondition(SqlMethodKey, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	case shared.VeDBMySQL:
		sqlTypeFilter := filterSqlTypeCondition(SqlMethodKey, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, dataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}
	query += " " + groupCondition + " "
	query += "limit 2000"

	return query, nil
}

func (c *client) getInternalUsers(ctx context.Context, DSType string) []string {
	//var (
	//	dbInternalUsers map[string]string
	//)
	//internalUsers := make([]string, 0)
	//err := json.Unmarshal([]byte(c.conf.Get(ctx).DBInternalUsers), &dbInternalUsers)
	//if err != nil {
	//	log.Warn(ctx, "parse json str failed %+v", err)
	//
	//} else {
	//	internalUsers = strings.Split(dbInternalUsers[DSType], ",")
	//}
	return c.connection.InternalUsers
}

func (c *client) CreateSlowLogsExportTask(ctx context.Context, req *shared.CreateSlowLogsExportTaskReq) (*shared.CreateSlowLogsExportTaskResp, error) {
	dataSourceType := req.DataSourceType

	var (
		query           string
		hasWhere        bool
		whereConditions string
		groupFields     string
	)
	if dataSourceType == shared.Postgres {
		groupFields = strings.Join([]string{DateTimeKey, SQLType, dbKey, sqlTextKey, userKey, sourceIPKey, queryTimeKey, lockTimeKey, rowsSendKey, rowsExaminedKey, connectionIdKey}, ",")
	} else {
		groupFields = strings.Join([]string{DateTimeKey, dbKey, sqlTextKey, userKey, sourceIPKey, queryTimeKey, lockTimeKey, rowsSendKey, rowsExaminedKey, connectionIdKey}, ",")
	}
	query = fmt.Sprintf("* | select %s", groupFields)
	searchParam := req.SearchParam
	whereConditions, hasWhere = generateAggregateSlowLogSearchWhereConditions(searchParam)
	switch dataSourceType {
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		} else {
			query += fmt.Sprintf(" where (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		}
	case shared.VeDBMySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s' ", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s' ", instanceKey, req.InstanceId)
		}
	case shared.Postgres:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s' ", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s' ", instanceKey, req.InstanceId)
		}
	}
	// 规避TLS bug
	query += fmt.Sprintf(" and %s ", whereConditionsMust)
	query += fmt.Sprintf(" and %s ", filterSqlParseFailed)
	internalUsers := c.getInternalUsers(ctx, dataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s ", filter)
	}
	// 仅支持PG
	switch dataSourceType {
	case shared.Postgres:
		sqlTypeFilter := filterSqlTypeCondition(SQLType, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		sqlTypeFilter := filterSqlTypeCondition(SqlMethodKey, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	case shared.VeDBMySQL:
		sqlTypeFilter := filterSqlTypeCondition(SqlMethodKey, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, dataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	maxLimit := c.conf.Get(ctx).MaxSlowLogDownloadLimits
	query += fmt.Sprintf(" limit %d", maxLimit)
	log.Info(ctx, "Call TLS CreateDownloadTask query is %s", query)
	resp, err := c.realClient.CreateDownloadTask(&sdk.CreateDownloadTaskRequest{
		TaskName:    req.TaskName,
		TopicID:     c.connection.TopicId,
		StartTime:   req.StartTime,
		EndTime:     req.EndTime,
		DataFormat:  strings.ToLower(req.DataFormat),
		Compression: strings.ToLower(req.Compression),
		Query:       query,
		Limit:       int(req.Limit),
		Sort:        strings.ToLower(req.Sort.String()),
	})
	if err != nil {
		log.Warn(ctx, "CreateDownloadTask: Download slowLog from topic %s error: %v", c.connection.TopicId, err)
		return nil, err
	}
	res := &shared.CreateSlowLogsExportTaskResp{
		TaskId: resp.TaskId,
	}
	return res, nil
}

func (c *client) DescribeSlowLogsExportTasks(ctx context.Context, req *shared.DescribeSlowLogsExportTasksReq) (*shared.DescribeSlowLogsExportTasksResp, error) {
	rreq := &sdk.DescribeDownloadTasksRequest{
		TopicID:    c.connection.TopicId,
		PageNumber: utils.IntRef(int(req.PageNumber)),
		PageSize:   utils.IntRef(int(req.PageSize)),
	}
	ret := &shared.DescribeSlowLogsExportTasksResp{}
	// TODO 上线后仅保留新版查询实现逻辑
	if c.conf.Get(ctx).EnableSlowLogUpgradeSwitch {
		if req.GetKeyword() != "" {
			rreq.TaskName = utils.StringRef(req.GetKeyword() + "-dbw") // 新版默认加个后缀
		}
		resp, err := c.realClient.DescribeDownloadTasks(rreq)
		if err != nil {
			log.Warn(ctx, "DescribeSlowLogsExportTasks: search log task from topic %s error: %v", c.connection.TopicId, err)
			return nil, err
		}
		for _, detail := range resp.Tasks {
			taskInfo := &shared.TaskInfo{
				TaskId:      detail.TaskId,
				TaskName:    detail.TaskName,
				TopicId:     detail.TopicId,
				Query:       detail.Query,
				DataFormat:  detail.DataFormat,
				CreateTime:  detail.CreateTime,
				StartTime:   detail.StartTime,
				EndTime:     detail.EndTime,
				LogSize:     detail.LogSize,
				LogCount:    detail.LogCount,
				TaskStatus:  conv.ToSharedDownloadTaskStatusType(detail.TaskStatus),
				Compression: detail.Compression,
			}
			ret.TaskInfos = append(ret.TaskInfos, taskInfo)
		}
		ret.Total = int32(resp.Total)
	} else {
		if req.GetKeyword() != "" {
			rreq.TaskName = utils.StringRef(req.GetKeyword())
		}
		resp, err := c.realClient.DescribeDownloadTasks(rreq)
		if err != nil {
			log.Warn(ctx, "DescribeSlowLogsExportTasks: search log task from topic %s error: %v", c.connection.TopicId, err)
			return nil, err
		}
		for _, detail := range resp.Tasks {
			taskInfo := &shared.TaskInfo{
				TaskId:      detail.TaskId,
				TaskName:    detail.TaskName,
				TopicId:     detail.TopicId,
				Query:       detail.Query,
				DataFormat:  detail.DataFormat,
				CreateTime:  detail.CreateTime,
				StartTime:   detail.StartTime,
				EndTime:     detail.EndTime,
				LogSize:     detail.LogSize,
				LogCount:    detail.LogCount,
				TaskStatus:  conv.ToSharedDownloadTaskStatusType(detail.TaskStatus),
				Compression: detail.Compression,
			}
			ret.TaskInfos = append(ret.TaskInfos, taskInfo)
			//if matched := strings.HasSuffix(detail.TaskName, suffix); matched {
			//	taskInfo := &shared.TaskInfo{
			//		TaskId:      detail.TaskId,
			//		TaskName:    detail.TaskName,
			//		TopicId:     detail.TopicId,
			//		Query:       detail.Query,
			//		DataFormat:  detail.DataFormat,
			//		CreateTime:  detail.CreateTime,
			//		StartTime:   detail.StartTime,
			//		EndTime:     detail.EndTime,
			//		LogSize:     detail.LogSize,
			//		LogCount:    detail.LogCount,
			//		TaskStatus:  conv.ToSharedDownloadTaskStatusType(detail.TaskStatus),
			//		Compression: detail.Compression,
			//	}
			//	ret.TaskInfos = append(ret.TaskInfos, taskInfo)
			//}
		}
		ret.Total = int32(resp.Total)
	}
	return ret, nil
}

func (c *client) DescribeLogsDownloadUrl(ctx context.Context, req *shared.DescribeLogsDownloadUrlReq) (*shared.DescribeLogsDownloadUrlResp, error) {
	resp, err := c.realClient.DescribeDownloadUrl(&sdk.DescribeDownloadUrlRequest{
		TaskId: req.TaskId,
	})
	log.Info(ctx, "Call TLS DescribeDownloadUrl resp is %s", resp)
	if err != nil {
		log.Warn(ctx, "DescribeLogsDownloadUrl: get download URL from TaskId %s error: %v", req.TaskId, err)
		return nil, err
	}
	ret := &shared.DescribeLogsDownloadUrlResp{
		FileUrl: resp.DownloadUrl,
	}
	return ret, nil
}

func (c *client) DescribeSlowLogs(ctx context.Context, req *shared.DescribeSlowLogsReq) (*shared.DescribeSlowLogsResp, error) {
	query := `* | select `

	searchParam := req.SearchParam
	if searchParam.SQLTemplate != "" {
		newStr := fmt.Sprintf(`"%s"`, searchParam.SQLTemplate)
		query = strings.Replace(query, "*", newStr, 1)
	}

	selectCountField := countField
	countQuery := fmt.Sprintf("%s%s", query, selectCountField)

	selectField := []string{sqlTemplateKey, timestampKey, sqlTextKey, userKey, dbKey, sourceIPKey, connectionIdKey, queryTimeKey, lockTimeKey, rowsExaminedKey, rowsSendKey}
	query += strings.Join(selectField, ",")
	whereConditions, hasWhere := generateSlowLogSearchWhereCondition(req.InstanceId, searchParam, req.DataSourceType)
	if hasWhere {
		query += whereConditions
		countQuery += whereConditions
	}
	// 规避TLS bug
	query += fmt.Sprintf(" and %s ", whereConditionsMust)
	query += fmt.Sprintf(" and %s ", filterSqlParseFailed)
	countQuery += fmt.Sprintf(" and %s ", whereConditionsMust)
	countQuery += fmt.Sprintf(" and %s ", filterSqlParseFailed)
	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s ", filter)
		countQuery += fmt.Sprintf(" and %s ", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
		countQuery += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	query += generateSortParamStringForSlowLog(req.GetSortBy(), req.GetOrderBy())
	log.Info(ctx, "DescribeSlowLogs call TLS SearchLogs query content is %s", countQuery)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     countQuery,
	})
	if err != nil {
		log.Warn(ctx, "DescribeSlowLogs: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, countQuery)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data := resp.AnalysisResult.Data
	if len(data) != 1 {
		err = fmt.Errorf("DescribeSlowLogs: query total expect 1, got %d", len(data))
		log.Warn(ctx, "%s", err.Error())
		return nil, err
	}

	total, err := parseInt64(data[0][countKey])
	if err != nil {
		log.Warn(ctx, "DescribeSlowLogs: convert total result to int64 error: %s", err.Error())
		return nil, err
	}

	query += " limit 5000"
	log.Info(ctx, "DescribeSlowLogs call TLS SearchLogs query content is %s", query)
	resp, err = c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "DescribeSlowLogs: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	slowLogList := make([]*shared.SlowLog, len(resp.AnalysisResult.Data))
	data = resp.AnalysisResult.Data
	for i := 0; i < len(resp.AnalysisResult.Data); i++ {
		slowLogList[i] = fetchSlowLog(data[i])
	}

	return &shared.DescribeSlowLogsResp{
		SlowLogs: slowLogList,
		Total:    int32(total),
	}, nil
}

func (c *client) DescribeUsers(ctx context.Context, req *shared.DescribeUsersReq) (*shared.DescribeUsersResp, error) {
	query := fmt.Sprintf(` * | select DISTINCT(%s)`, userKey)
	countQuery := fmt.Sprintf(` * | select count(DISTINCT(%s)) As Count`, userKey)

	searchParam := req.SearchParam
	if searchParam.SQLTemplate != "" {
		newStr := fmt.Sprintf(`"%s"`, searchParam.SQLTemplate)
		query = strings.Replace(query, "*", newStr, 1)
		countQuery = strings.Replace(countQuery, "*", newStr, 1)
	}

	whereConditions, hasWhere := generateSlowLogSearchWhereCondition(req.InstanceId, searchParam, req.DataSourceType)
	if hasWhere {
		query += whereConditions
		countQuery += whereConditions
	}
	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s", filter)
		countQuery += fmt.Sprintf(" and %s", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
		countQuery += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}
	log.Info(ctx, "DescribeUsers call TLS SearchLogs query content is %s", countQuery)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     countQuery,
	})
	if err != nil {
		log.Warn(ctx, "DescribeUsers: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data := resp.AnalysisResult.Data
	if len(data) != 1 {
		err = fmt.Errorf("DescribeUsers: query total expect 1, got %d", len(data))
		log.Warn(ctx, "%s", err.Error())
		return nil, err
	}

	distinctCountKey := countKey
	total, err := parseInt64(data[0][distinctCountKey])
	if err != nil {
		log.Warn(ctx, "DescribeUsers: convert total result to int64 error: %s", err.Error())
		return nil, err
	}

	query += " limit 5000"
	log.Info(ctx, "DescribeUsers call TLS SearchLogs query content is %s", query)
	resp, err = c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "DescribeUsers: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data = resp.AnalysisResult.Data
	users := make([]string, 0, len(data))
	for _, value := range data {
		if user, ok := value[userKey]; ok {
			users = append(users, user.(string))
		}
	}

	return &shared.DescribeUsersResp{
		Users: users,
		Total: int32(total),
	}, nil
}

func (c *client) DescribeSourceIPs(ctx context.Context, req *shared.DescribeSourceIPsReq) (*shared.DescribeSourceIPsResp, error) {
	query := fmt.Sprintf(` * | select DISTINCT(%s)`, sourceIPKey)
	countQuery := fmt.Sprintf(` * | select count(DISTINCT(%s)) as Count`, sourceIPKey)

	searchParam := req.SearchParam
	if searchParam.SQLTemplate != "" {
		newStr := fmt.Sprintf(`"%s"`, searchParam.SQLTemplate)
		query = strings.Replace(query, "*", newStr, 1)
		countQuery = strings.Replace(countQuery, "*", newStr, 1)
	}

	whereConditions, hasWhere := generateSlowLogSearchWhereCondition(req.InstanceId, searchParam, req.DataSourceType)
	if hasWhere {
		query += whereConditions
		countQuery += whereConditions
	}

	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s", filter)
		countQuery += fmt.Sprintf(" and %s", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
		countQuery += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}
	log.Info(ctx, "DescribeSourceIPs call TLS SearchLogs query content is %s", countQuery)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     countQuery,
	})
	if err != nil {
		log.Warn(ctx, "DescribeSourceIPs: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data := resp.AnalysisResult.Data
	if len(data) != 1 {
		err = fmt.Errorf("DescribeSourceIPs: query total expect 1, got %d", len(data))
		log.Warn(ctx, "%s", err.Error())
		return nil, err
	}

	distinctCountKey := countKey
	total, err := parseInt64(data[0][distinctCountKey])
	if err != nil {
		log.Warn(ctx, "DescribeSourceIPs: convert total result to int64 error: %s", err.Error())
		return nil, err
	}

	query += " limit 5000"
	log.Info(ctx, "DescribeSourceIPs call TLS SearchLogs query content is %s", query)
	resp, err = c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "DescribeSourceIPs: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data = resp.AnalysisResult.Data
	sourceIPs := make([]string, 0, len(data))
	for _, value := range data {
		if user, ok := value[sourceIPKey]; ok {
			sourceIPs = append(sourceIPs, user.(string))
		}
	}

	return &shared.DescribeSourceIPsResp{
		SourceIPs: sourceIPs,
		Total:     int32(total),
	}, nil
}

func (c *client) DescribeDBs(ctx context.Context, req *shared.DescribeDBsReq) (*shared.DescribeDBsResp, error) {
	query := fmt.Sprintf(` * | select DISTINCT(%s)`, dbKey)
	countQuery := fmt.Sprintf(` * | select count(DISTINCT(%s)) As Count`, dbKey)

	searchParam := req.SearchParam
	if searchParam.SQLTemplate != "" {
		newStr := fmt.Sprintf(`"%s"`, searchParam.SQLTemplate)
		query = strings.Replace(query, "*", newStr, 1)
		countQuery = strings.Replace(countQuery, "*", newStr, 1)
	}

	whereConditions, hasWhere := generateSlowLogSearchWhereCondition(req.InstanceId, searchParam, req.DataSourceType)
	if hasWhere {
		query += whereConditions
		countQuery += whereConditions
	}

	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s", filter)
		countQuery += fmt.Sprintf(" and %s", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
		countQuery += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}
	log.Info(ctx, "DescribeDBs call TLS SearchLogs query content is %s", countQuery)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     countQuery,
	})
	if err != nil {
		log.Warn(ctx, "DescribeDBs: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data := resp.AnalysisResult.Data
	if len(data) != 1 {
		err = fmt.Errorf("DescribeDBs: query total expect 1, got %d", len(data))
		log.Warn(ctx, "%s", err.Error())
		return nil, err
	}

	distinctCountKey := countKey
	total, err := parseInt64(data[0][distinctCountKey])
	if err != nil {
		log.Warn(ctx, "DescribeDBs: convert total result to int64 error: %s", err.Error())
		return nil, err
	}

	query += " limit 5000"
	log.Info(ctx, "DescribeDBs call TLS SearchLogs query content is %s", query)
	resp, err = c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "DescribeDBs: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data = resp.AnalysisResult.Data
	DBs := make([]string, 0, len(data))
	for _, value := range data {
		if user, ok := value[dbKey]; ok {
			DBs = append(DBs, user.(string))
		}
	}

	return &shared.DescribeDBsResp{
		DBs:   DBs,
		Total: int32(total),
	}, nil
}

func (c *client) DescribeExampleSQL(ctx context.Context, req *shared.DescribeExampleSQLReq) (*shared.DescribeExampleSQLResp, error) {
	query := fmt.Sprintf(` * | select %s,%s`, sqlTextKey, queryTimeKey)

	searchParam := req.SearchParam
	if searchParam.SQLTemplate != "" {
		newStr := fmt.Sprintf(`"%s"`, searchParam.SQLTemplate)
		query = strings.Replace(query, "*", newStr, 1)
	}

	whereConditions, hasWhere := generateSlowLogSearchWhereCondition(req.InstanceId, searchParam, req.DataSourceType)
	if hasWhere {
		query += whereConditions
	}

	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	query += fmt.Sprintf(" order by %s DESC limit 1", queryTimeKey)
	log.Info(ctx, "DescribeExampleSQL call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "DescribeExampleSQL: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	if len(resp.AnalysisResult.Data) == 0 {
		err = fmt.Errorf("DescribeExampleSQL: get no exampleSQL for sql template %s", req.SearchParam.SQLTemplate)
		return nil, err
	}

	exampleSQL := resp.AnalysisResult.Data[0][sqlTextKey].(string)
	return &shared.DescribeExampleSQLResp{
		SQLText: exampleSQL,
	}, nil
}

func (c *client) DescribeDialogSnapshots(ctx context.Context, req *DescribeDialogSnapshotsReq) (*DescribeDialogSnapshotsResp, error) {
	var query string
	switch req.InstanceType {
	// 无引擎快照
	case model.DSType_Mongo, model.DSType_ByteDoc, model.DSType_Postgres:
		if req.NodeId != "" {
			query = fmt.Sprintf("* | select DISTINCT(CollectTime) where InstanceId='%s' and NodeId='%s' "+
				"order by CollectTime DESC limit 10000", req.InstanceId, req.NodeId) // ignore_security_alert
		} else {
			query = fmt.Sprintf("* | select DISTINCT(CollectTime) where InstanceId='%s' "+
				"order by CollectTime DESC limit 10000", req.InstanceId) // ignore_security_alert
		}
	default: // mysql、vedb、sharding
		if req.NodeId != "" {
			query = fmt.Sprintf("* | select DISTINCT(CollectTime) where InstanceId='%s' and NodeId='%s' and Index='0' "+
				"order by CollectTime DESC limit 10000", req.InstanceId, req.NodeId) // ignore_security_alert
		} else {
			query = fmt.Sprintf("* | select DISTINCT(CollectTime) where InstanceId='%s' and Index='0' "+
				"order by CollectTime DESC limit 10000", req.InstanceId) // ignore_security_alert
		}
	}
	log.Info(ctx, "DescribeDialogSnapshots call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.WarnS(ctx, "failed to search log details from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	ret := &DescribeDialogSnapshotsResp{}
	for _, snapshot := range resp.AnalysisResult.Data {
		var collectTime int64
		collectTime, err = strconv.ParseInt(snapshot["CollectTime"].(string), 10, 64)
		if err != nil {
			log.WarnS(ctx, "failed to convert CollectTime to int64",
				"CollectTime", snapshot["CollectTime"].(string), "err", err)
			continue
		}
		ret.Snapshots = append(ret.Snapshots, &Snapshot{
			CollectTime: collectTime,
		})
	}
	ret.Total = int64(resp.Count)
	return ret, nil
}

func (c *client) parseTLSNumber(data interface{}) (int64, error) {
	switch d := data.(type) {
	case string:
		return strconv.ParseInt(d, 10, 64)
	case json.Number:
		return d.Int64()
	}
	return 0, nil
}
func (c *client) computeID(str string) string {
	hash := sha256.New()
	hash.Write([]byte(str))
	// 获取计算后的哈希值
	hashBytes := hash.Sum(nil)
	// 将字节数组转换为十六进制字符串
	sha256Str := hex.EncodeToString(hashBytes)
	return sha256Str
}

func (c *client) DescribeDialogDetailSnapshot(ctx context.Context, req *DescribeDialogDetailSnapshotReq) (*DescribeDialogDetailSnapshotResp, error) {
	var (
		query              string
		startTime, endTime int64
	)
	switch req.InstanceType {
	case model.DSType_Mongo, model.DSType_ByteDoc:
		if req.NodeId != "" {
			query = fmt.Sprintf("* | select InstanceId,CollectTime,ProcessId,Host,Command,Time,Info,State,NodeId,Desc,Namespace,PlanSummary where InstanceId='%s' and NodeId='%s' ", req.InstanceId, req.NodeId)
		} else {
			query = fmt.Sprintf("* | select InstanceId,CollectTime,ProcessId,Host,Command,Time,Info,State,NodeId,Desc,Namespace,PlanSummary where InstanceId='%s' ", req.InstanceId)
		}
	default:
		if req.NodeId != "" {
			query = fmt.Sprintf("* | select InstanceId,CollectTime,ProcessId,User,Host,DB,Command,Time,State,Info,NodeId,PSM where InstanceId='%s'  and NodeId='%s' and Component='%s' ", req.InstanceId, req.NodeId, req.Component)
		} else {
			query = fmt.Sprintf("* | select InstanceId,CollectTime,ProcessId,User,Host,DB,Command,Time,State,Info,NodeId,PSM where InstanceId='%s' and Component='%s' ", req.InstanceId, req.Component)
		}
	}
	if req.SQLTemplateID != "" {
		query += fmt.Sprintf(" and %s='%s' ", SQLTemplateID, req.SQLTemplateID)
	}
	if req.CollectTime > 0 {
		query += fmt.Sprintf(" and CollectTime='%v' ", req.CollectTime)
	}
	// 内场rds不过滤内部用户而是统一转换为system_user
	if req.InstanceType != model.DSType_ByteRDS {
		filter := filterInternalUserCondition(req.InternalUsers)
		if filter != "" {
			query += fmt.Sprintf(" and %s", filter)
		}
	}
	maxLimit := c.conf.Get(ctx).MaxSlowLogDownloadLimits
	query += fmt.Sprintf(" limit %d", maxLimit)
	log.Info(ctx, "DescribeDialogDetailSnapshot call TLS SearchLogs query content is %s", query)
	// 若配置了StartTime,EndTime
	if req.EndTime > req.StartTime {
		startTime = req.StartTime
		endTime = req.EndTime
	} else {
		startTime = req.CollectTime - 30
		endTime = req.CollectTime + 30
	}
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: startTime,
		EndTime:   endTime,
		Query:     query,
	})
	//log.Info(ctx, "TLS SearchLogs resp is: %v", utils.Show(resp))
	if err != nil {
		log.WarnS(ctx, "failed to search dialog detail log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	var snapshots []*DialogLog
	if req.InstanceType == model.DSType_Mongo || req.InstanceType == model.DSType_ByteDoc {
		for _, detail := range resp.AnalysisResult.Data {
			var (
				collectTime int64
			)
			if detail["ProcessId"].(string) == "" {
				continue
			}
			collectTime, err = strconv.ParseInt(detail["CollectTime"].(string), 10, 64)
			if err != nil {
				log.WarnS(ctx, "failed to convert CollectTime to int64",
					"CollectTime", detail["CollectTime"].(string), "err", err)
				continue
			}
			snapshots = append(snapshots, &DialogLog{
				InstanceId:  detail["InstanceId"].(string),
				CollectTime: collectTime,
				ProcessID:   detail["ProcessId"].(string),
				Host:        detail["Host"].(string),
				Command:     detail["Command"].(string),
				Time:        detail["Time"].(string),
				State:       detail["State"].(string),
				Info:        detail["Info"].(string),
				NodeId:      detail["NodeId"].(string),
				PlanSummary: detail["PlanSummary"].(string),
				Desc:        detail["Desc"].(string),
				Namespace:   detail["Namespace"].(string),
			})
		}
	} else { // RDS 数据源
		for _, detail := range resp.AnalysisResult.Data {
			var (
				userName    string
				collectTime int64
			)
			if req.InstanceType == model.DSType_ByteRDS {
				if strings.Contains(strings.Join(req.InternalUsers, ","), detail["User"].(string)) {
					userName = "system_user" // 统一命名为system_user
				} else {
					userName = detail["User"].(string)
				}
			} else {
				userName = detail["User"].(string)
			}
			collectTime, err = strconv.ParseInt(detail["CollectTime"].(string), 10, 64)
			if err != nil {
				log.WarnS(ctx, "failed to convert CollectTime to int64",
					"CollectTime", detail["CollectTime"].(string), "err", err)
				continue
			}
			snapshots = append(snapshots, &DialogLog{
				InstanceId:  detail["InstanceId"].(string),
				CollectTime: collectTime,
				ProcessID:   detail["ProcessId"].(string),
				User:        userName,
				Host:        detail["Host"].(string),
				DB:          detail["DB"].(string),
				Command:     detail["Command"].(string),
				Time:        detail["Time"].(string),
				State:       detail["State"].(string),
				Info:        detail["Info"].(string),
				NodeId:      detail["NodeId"].(string),
				PSM:         detail["PSM"].(string),
			})
		}
	}

	finalRet := c.filterDialogSnapShot(ctx, snapshots, req)
	// 默认按Time倒序
	SortDialog(finalRet.Details, shared.DESC, "Time")
	return finalRet, nil
}

func (c *client) DescribeEngineStatusSnapshot(ctx context.Context, req *DescribeEngineStatusSnapshotReq) (*DescribeEngineStatusSnapshotResp, error) {
	var query string
	if req.NodeId != "" {
		//query = fmt.Sprintf("* | select Index,InstanceId,CollectTime,Type,Name,Status where InstanceId='%s' and CollectTime='%v' and NodeId='%s'", req.InstanceId, req.CollectTime, req.NodeId)
		query = fmt.Sprintf("InstanceId:'%s' AND CollectTime:'%v' AND NodeId:'%s' ", req.InstanceId, req.CollectTime, req.NodeId)
	} else {
		//query = fmt.Sprintf("* | select Index,InstanceId,CollectTime,Type,Name,Status where InstanceId='%s' and CollectTime='%v'", req.InstanceId, req.CollectTime)
		query = fmt.Sprintf("InstanceId:'%s' AND CollectTime:'%v' ", req.InstanceId, req.CollectTime)
	}

	log.Info(ctx, "DescribeEngineStatusSnapshot call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.CollectTime - 100,
		EndTime:   req.CollectTime + 100,
		Query:     query,
		Limit:     10,
	})
	log.Info(ctx, "TLS SearchLogs resp is %s", resp)
	if err != nil {
		log.WarnS(ctx, "failed to search engine status log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	datas := resp.Logs
	ret := &DescribeEngineStatusSnapshotResp{}
	if len(datas) <= 0 {
		log.WarnS(ctx, "search no engine status log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", utils.Show(err))
		return ret, nil
	}

	var statuses []*EngineStatusLog
	fp.StreamOf(datas).Map(func(data map[string]interface{}) *EngineStatusLog {
		index, _ := strconv.ParseInt(data["Index"].(string), 10, 64)
		return &EngineStatusLog{
			InstanceId:  data["InstanceId"].(string),
			CollectTime: req.CollectTime,
			Type:        data["Type"].(string),
			Name:        data["Name"].(string),
			Status:      data["Status"].(string),
			Index:       index,
			NodeId:      req.NodeId,
		}
	}).SortBy(func(v1 *EngineStatusLog, v2 *EngineStatusLog) bool {
		return v1.Index < v2.Index
	}).ToSlice(&statuses)
	var status bytes.Buffer
	for i := range statuses {
		status.WriteString(statuses[i].Status)
	}
	ret.Status = &EngineStatusLog{
		InstanceId:  statuses[0].InstanceId,
		CollectTime: req.CollectTime,
		Type:        statuses[0].Type,
		Name:        statuses[0].Name,
		Status:      status.String(),
		NodeId:      req.NodeId,
		//CpuUsage:    cpuUsage,
	}
	return ret, nil
}

func (c *client) ModifyTopic(ctx context.Context, req ModifyTopicReq) error {
	request := &sdk.ModifyTopicRequest{
		TopicID: req.TopicID,
		Ttl:     utils.Uint16Ref(req.SqlRetentionDay),
	}
	if _, err := c.realClient.ModifyTopic(request); err != nil {
		return err
	}
	return nil
}

func (c *client) DescribeTicketLogDetail(ctx context.Context, req *DescribeTicketLogDetailReq) (*DescribeTicketLogDetailResp, error) {
	query := fmt.Sprintf("* | select * where ticketid='%s'",
		req.TicketId)
	log.Info(ctx, "DescribeTicketLogDetail call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogs(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.CollectTime,
		EndTime:   time.Now().Unix() + 100, // TODO 收集的截止时间为工单执行成功时间
		Query:     query,
	})
	if err != nil {
		log.WarnS(ctx, "failed to search engine status log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return nil, err
	}

	datas := resp.AnalysisResult.Data
	ret := &DescribeTicketLogDetailResp{}
	if len(datas) <= 0 {
		log.WarnS(ctx, "search no engine status log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return ret, nil
	}
	return ret, nil
}

func (c *client) DescribeLockWaitsDetailSnapshot(ctx context.Context, req *DescribeLockWaitsDetailSnapshotReq) ([]*DescribeLockCurrentWaitsDetail, error) {
	var (
		querySql, orderByStr string
		ret                  []*DescribeLockCurrentWaitsDetail
	)
	if req.NodeId != "" {
		querySql = fmt.Sprintf("* | select NodeId,InstanceId,CollectTime,DbName,RTrxMysqlThreadId,RTrxId,RTrxState,RWaitingQuery,RTrxStarted,RTrxWaitStarted,RBlockedWaitSecs,RTrxRowsModified,RTrxRowsLocked,RTrxOperationState,BTrxMysqlThreadId,BTrxId,BTrxState,BBlockingQuery,BTrxStarted,BTrxWaitStarted,BBlockingWaitSecs,BTrxRowsModified,BTrxRowsLocked,BTrxOperationState"+
			" where InstanceId='%s' and NodeId='%s' ", req.InstanceId, req.NodeId)
	} else {
		querySql = fmt.Sprintf("* | select NodeId,InstanceId,CollectTime,DbName,RTrxMysqlThreadId,RTrxId,RTrxState,RWaitingQuery,RTrxStarted,RTrxWaitStarted,RBlockedWaitSecs,RTrxRowsModified,RTrxRowsLocked,RTrxOperationState,BTrxMysqlThreadId,BTrxId,BTrxState,BBlockingQuery,BTrxStarted,BTrxWaitStarted,BBlockingWaitSecs,BTrxRowsModified,BTrxRowsLocked,BTrxOperationState"+
			" where InstanceId='%s' ", req.InstanceId)
	}
	if req.OrderBy.String() != "<UNSET>" {
		orderByStr = fmt.Sprintf("order by %s %s ", req.OrderBy.String(), req.SortBy.String()) // ignore_security_alert
	} else {
		// 默认按阻塞sql的时间排序
		orderByStr = fmt.Sprintf("order by %s %s ", "RBlockedWaitSecs", "DESC") // ignore_security_alert
	}
	querySql += " " + orderByStr // ignore_security_alert
	maxLimit := c.conf.Get(ctx).MaxSlowLogDownloadLimits
	querySql += fmt.Sprintf(" limit %d", maxLimit)
	log.Info(ctx, "DescribeLockWaitsDetailSnapshot call TLS SearchLogs query content is %s", querySql)
	resp, err := c.realClient.SearchLogs(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: int64(req.StartTime),
		EndTime:   int64(req.EndTime),
		Query:     querySql,
	})
	if err != nil {
		log.WarnS(ctx, "failed to DescribeLockCurrentWaits log from TLS", "topic", c.connection.TopicId, "sql", querySql, "err", err)
		return nil, err
	}

	datas := resp.AnalysisResult.Data
	if len(datas) <= 0 {
		log.WarnS(ctx, "search no engine status log from TLS",
			"topic", c.connection.TopicId, "sql", querySql, "err", err)
		return ret, nil
	}
	for _, detail := range datas {
		ret = append(ret, &DescribeLockCurrentWaitsDetail{
			InstanceId:         detail["InstanceId"].(string),
			NodeId:             detail["NodeId"].(string),
			CollectTime:        detail["CollectTime"].(string),
			DbName:             detail["DbName"].(string),
			RTrxMysqlThreadId:  detail["RTrxMysqlThreadId"].(string),
			RTrxId:             detail["RTrxId"].(string),
			RTrxState:          detail["RTrxState"].(string),
			RWaitingQuery:      detail["RWaitingQuery"].(string),
			RTrxStarted:        detail["RTrxStarted"].(string),
			RTrxWaitStarted:    detail["RTrxWaitStarted"].(string),
			RBlockedWaitSecs:   detail["RBlockedWaitSecs"].(string),
			RTrxRowsModified:   detail["RTrxRowsModified"].(string),
			RTrxRowsLocked:     detail["RTrxRowsLocked"].(string),
			RTrxOperationState: detail["RTrxOperationState"].(string),
			BTrxMysqlThreadId:  detail["BTrxMysqlThreadId"].(string),
			BTrxId:             detail["BTrxId"].(string),
			BTrxState:          detail["BTrxState"].(string),
			BBlockingQuery:     detail["BBlockingQuery"].(string),
			BTrxStarted:        detail["BTrxStarted"].(string),
			BTrxWaitStarted:    detail["BTrxWaitStarted"].(string),
			BBlockingWaitSecs:  detail["BBlockingWaitSecs"].(string),
			BTrxRowsModified:   detail["BTrxRowsModified"].(string),
			BTrxRowsLocked:     detail["BTrxRowsLocked"].(string),
			BTrxOperationState: detail["BTrxOperationState"].(string),
		})
	}

	finalRet := c.filterLockWaitSnapShot(ctx, ret, req)
	return finalRet, nil
}
func (c *client) DescribeAggregateDialogs(ctx context.Context, req *DescribeAggregateDialogsReq) (*DescribeAggregateDialogsResp, error) {
	var (
		query          string
		groupCondition string
		groupFields    string
	)

	// 分组字段
	// groupFields = strings.Join([]string{sqlTemplateKey, SQLTemplateID, StateKey}, ",")
	groupFields = strings.Join([]string{sqlTemplateKey, SQLTemplateID, dbKey, SQLType}, ",")
	// 分组条件
	groupCondition = fmt.Sprintf("group by %s", groupFields)
	query = fmt.Sprintf("* | select %s,%s,", groupFields, DialogExecuteCountField)
	// state持续时间的统计信息
	query += strings.Join([]string{sumStateKeepTimeField, maxStateKeepTimeField, averageStateKeepTimeField}, ",")
	query += fmt.Sprintf(" where InstanceId='%s' and Component='%s' and Info!='NULL' ", req.InstanceId, req.Component)
	if req.SearchParam.GetSQLTemplate() != "" {
		query += fmt.Sprintf(" and %s LIKE '%%%s%%'", sqlTemplateKey, req.SearchParam.GetSQLTemplate())
	}
	if req.SearchParam.GetDB() != "" {
		query += fmt.Sprintf(" and %s LIKE '%%%s%%'", dbKey, req.SearchParam.GetDB())
	}
	if req.SearchParam.GetSqlType() != "" {
		query += fmt.Sprintf(" and %s LIKE '%%%s%%'", SQLType, strings.ToUpper(req.SearchParam.GetSqlType()))
	}
	filter := filterInternalUserCondition(req.InternalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s", filter)
	}
	stateFilter := filterNonActiveState([]string{"idle", "sleep"})
	if stateFilter != "" {
		query += fmt.Sprintf(" and %s", stateFilter)
	}
	nodeFilter := filterNodeIdCondition(req.NodeIds)
	if nodeFilter != "" {
		query += fmt.Sprintf(" and %s", nodeFilter)
	}
	orderByStr := fmt.Sprintf("order by %s %s ", req.OrderBy.String(), req.SortBy.String()) // ignore_security_alert
	query += " " + groupCondition + " " + orderByStr                                        // ignore_security_alert
	maxLimit := c.conf.Get(ctx).MaxSlowLogDownloadLimits
	query += fmt.Sprintf(" limit %d", maxLimit)
	log.Info(ctx, "DescribeAggregateDialogs call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "failed to search aggregate dialogs log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	//log.Info(ctx, "TLS SearchLogs resp is: %v", utils.Show(resp.AnalysisResult.Data))
	ret := &DescribeAggregateDialogsResp{
		Total: int64(len(resp.AnalysisResult.Data)),
	}

	for _, detail := range resp.AnalysisResult.Data {
		executeCount, err := parseInt64(detail[DialogExecuteCountKey])
		if err != nil {
			log.Warn(ctx, "DescribeAggregateDialogs: convert total result to int64 error: %s", err.Error())
			executeCount = 0
		}
		totalStateKeepTime, err := parseFloat64(detail[sumStateKeepTimeKey])
		if err != nil {
			log.Warn(ctx, "DescribeAggregateDialogs: convert totalStateKeepTime result to float64 error: %s", err.Error())
			totalStateKeepTime = 0
		}
		maxStateKeepTime, err := parseFloat64(detail[maxStateKeepTimeKey])
		if err != nil {
			log.Warn(ctx, "DescribeAggregateDialogs: convert maxStateKeepTime result to float64 error: %s", err.Error())
			maxStateKeepTime = 0
		}
		averageStateKeepTime, err := parseFloat64(detail[averageStateKeepTimeKey])
		if err != nil {
			log.Warn(ctx, "DescribeAggregateDialogs: convert averageStateKeepTime result to float64 error: %s", err.Error())
			averageStateKeepTime = 0
		}
		stateKeepStatistic := &model.StatisticResult_{
			Total:   totalStateKeepTime,
			Max:     maxStateKeepTime,
			Average: averageStateKeepTime,
		}
		ret.Details = append(ret.Details, &model.AggregateDialog{
			SQLTemplate:   detail[sqlTemplateKey].(string),
			SQLTemplateID: detail[SQLTemplateID].(string),
			ExecuteCount:  executeCount,
			KeepTimeStats: stateKeepStatistic,
			DB:            detail[dbKey].(string),
			SqlType:       detail[SQLType].(string),
		})
	}
	return ret, nil
}
func (c *client) DescribeDialogHotspots(ctx context.Context, req *DescribeDialogHotspotsReq) (*DescribeDialogHotspotsResp, error) {
	var (
		query          string
		countQuery     string
		queryRawSql    string
		groupCondition string
		groupFields    string
		wg             sync.WaitGroup
	)
	// 分组字段
	groupFields = strings.Join([]string{SQLType, sqlTextKey, SQLTemplateID}, ",")
	// 分组条件
	groupCondition = fmt.Sprintf("group by %s", groupFields)
	// 忽略sql中的hint
	hintReg := `TRIM(REGEXP_REPLACE(Info, '/\*.*?\*/', ''))`
	query = fmt.Sprintf("NOT Info:NULL AND InstanceId: %s | select %s AS %s,%s,%s,%s ", req.InstanceId, hintReg, sqlTextKey, SQLType, SQLTemplateID, DialogExecuteCountField)
	countQuery = fmt.Sprintf("NOT Info:NULL AND InstanceId: %s | select %s ", req.InstanceId, countField)
	queryRawSql = fmt.Sprintf("NOT Info:NULL AND InstanceId: %s | select DISTINCT(Info) AS %s ", req.InstanceId, sqlTextKey)
	query += fmt.Sprintf(" where Component='%s' ", req.Component)
	countQuery += fmt.Sprintf(" where Component='%s' ", req.Component)
	queryRawSql += fmt.Sprintf(" where Component='%s' ", req.Component)
	filter := filterInternalUserCondition(req.InternalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s", filter)
		countQuery += fmt.Sprintf(" and %s", filter)
		queryRawSql += fmt.Sprintf(" and %s", filter)
	}
	nodeFilter := filterNodeIdCondition(req.NodeIds)
	if nodeFilter != "" {
		query += fmt.Sprintf(" and %s", nodeFilter)
		countQuery += fmt.Sprintf(" and %s", nodeFilter)
		queryRawSql += fmt.Sprintf(" and %s", nodeFilter)
	}
	if req.SearchParam != nil {
		sqlTypeFilter := filterSqlTypeCondition(SQLType, req.SearchParam.GetSqlType())
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s", sqlTypeFilter)
			countQuery += fmt.Sprintf(" and %s", sqlTypeFilter)
			queryRawSql += fmt.Sprintf(" and %s", sqlTypeFilter)

		}
	}
	orderByStr := fmt.Sprintf("order by %s %s ", DialogExecuteCountKey, model.Order_Desc.String()) // ignore_security_alert
	query += " " + groupCondition + " " + orderByStr                                               // ignore_security_alert
	query += fmt.Sprintf(" limit %d", 5)                                                           // 固定返回top5
	ret := &DescribeDialogHotspotsResp{
		SqlHotspots: &model.SqlHotspots{},
	}
	log.Info(ctx, "DescribeDialogHotspots call TLS SearchLogs countQuery content is %s", countQuery)
	rsp, er0 := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     countQuery,
	})
	if er0 != nil {
		log.Warn(ctx, "failed to search dialogs hotspots log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", er0)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	data := rsp.AnalysisResult.Data
	if len(data) != 1 {
		err := fmt.Errorf("DescribeDialogHotspots: query total expect 1, got %d", len(data))
		log.Warn(ctx, "%s", err.Error())
		return nil, err
	}

	totalAll, err := parseInt64(data[0][countKey])
	if err != nil {
		log.Warn(ctx, "DescribeDialogHotspots: convert total result to int64 error: %s", err.Error())
		return nil, err
	}
	if totalAll == 0 {
		log.Warn(ctx, "count is 0")
		return ret, nil
	}
	log.Info(ctx, "DescribeDialogHotspots call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "failed to search dialogs hotspots log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	var sqlHotSpotDistribution []*model.SqlHotspotsDistribution
	for _, detail := range resp.AnalysisResult.Data {
		var (
			sqlType, sqlText, sqlTemplateId string
		)
		totalExecuteCount, err := parseInt64(detail[DialogExecuteCountKey])
		if err != nil {
			log.Warn(ctx, "DescribeDialogHotspots: convert total result to int64 error: %s", err.Error())
			totalExecuteCount = 0
		}
		if sql, ok := detail[sqlTextKey]; ok {
			if sql != nil {
				sqlText = sql.(string)
			} else {
				sqlText = ""
			}
		}

		if sqlTp, ok := detail[SQLType]; ok {
			if sqlTp != nil {
				sqlType = sqlTp.(string)
			} else {
				sqlType = "-"
			}
		}
		if sqlMd5, ok := detail[SQLTemplateID]; ok {
			if sqlMd5 != nil {
				sqlTemplateId = sqlMd5.(string)
			} else {
				sqlMd5 = ""
			}
		}
		sqlHotSpotDistribution = append(sqlHotSpotDistribution, &model.SqlHotspotsDistribution{
			TotalExecCount: int32(totalExecuteCount),
			SqlType:        sqlType,
			SqlText:        sqlText,
			Percentage:     (float64(totalExecuteCount) / float64(totalAll)) * 100, // 单位百分比
			//RawSqlList:     tempRawSqlList,
			SQLTemplateID: sqlTemplateId,
		})
	}
	if len(sqlHotSpotDistribution) > 0 {
		for i, item := range sqlHotSpotDistribution {
			sqlTemplateId := item.SQLTemplateID
			if sqlTemplateId != "" {
				wg.Add(1)
				go func(i int, md5 string) {
					defer wg.Done()
					var (
						tempQuery      string
						tempRawSqlList []string
					)
					tempQuery = queryRawSql + fmt.Sprintf(" and %s='%s' limit 1000", SQLTemplateID, sqlTemplateId)
					log.Info(ctx, "DescribeDialogHotspots call TLS SearchLogs queryRawSql content is %s", tempQuery)
					rawResp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
						TopicID:   c.connection.TopicId,
						StartTime: req.StartTime,
						EndTime:   req.EndTime,
						Query:     tempQuery,
					})
					if err != nil {
						log.Warn(ctx, "failed to search dialogs hotspots log from TLS",
							"topic", c.connection.TopicId, "sql", tempQuery, "err", err)
						return
					}
					for _, item := range rawResp.AnalysisResult.Data {
						if rawSql, ok := item[sqlTextKey]; ok {
							if rawSql != nil {
								tempRawSqlList = append(tempRawSqlList, rawSql.(string))
							}
						}
					}
					sqlHotSpotDistribution[i].RawSqlList = tempRawSqlList
				}(i, sqlTemplateId)
			}
		}
		wg.Wait()
	}
	ret.SqlHotspots.SqlHotspotsDistribution = sqlHotSpotDistribution
	return ret, nil
}
func (c *client) filterDialogSnapShot(ctx context.Context, data []*DialogLog, req *DescribeDialogDetailSnapshotReq) *DescribeDialogDetailSnapshotResp {
	queryFilter := req.QueryFilter
	internalUsers := strings.Join(req.InternalUsers, ",")
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			if req.InstanceType == model.DSType_ByteDoc {
				fp.StreamOf(tData).Reject(func(d *DialogLog) bool {
					return strings.ToLower(d.State) == "false"
				}).ToSlice(&tData)
			} else {
				fp.StreamOf(tData).Reject(func(d *DialogLog) bool {
					return strings.ToLower(d.Command) == "sleep"
				}).ToSlice(&tData)
			}

		}
		if pID := queryFilter.ProcessID; pID != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.ProcessID, pID)
			}).ToSlice(&tData)
		}
		if user := queryFilter.User; user != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.User, user) || (user == "system_user" && strings.Contains(internalUsers, d.User))
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.Host, host)
			}).ToSlice(&tData)
		}
		if fDB := queryFilter.DB; fDB != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.DB, fDB)
			}).ToSlice(&tData)
		}
		if command := queryFilter.Command; command != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.Command, command)
			}).ToSlice(&tData)
		}
		if ns := queryFilter.Namespace; ns != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.Namespace, ns)
			}).ToSlice(&tData)
		}
		if ps := queryFilter.PlanSummary; ps != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.PlanSummary, ps)
			}).ToSlice(&tData)
		}
		if desc := queryFilter.Desc; desc != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.Desc, desc)
			}).ToSlice(&tData)
		}
		if state := queryFilter.State; state != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.State, state)
			}).ToSlice(&tData)
		}
		if timeLimit := queryFilter.LowerExecTimeLimit; timeLimit != "" {
			//limitInt, er := strconv.Atoi(queryFilter.LowerExecTimeLimit)
			limitFloat, er := strconv.ParseFloat(queryFilter.LowerExecTimeLimit, 64)
			if er == nil {
				fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
					execTime, _ := strconv.ParseFloat(strings.TrimSuffix(d.Time, "s"), 64)
					return execTime >= limitFloat
				}).ToSlice(&tData)
			}
		}
		if info := queryFilter.Info; info != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.Info, info)
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				return strings.Contains(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if nodeType := queryFilter.NodeType; nodeType != "" {
			switch nodeType {
			case model.NodeType_Primary.String():
				nodeType = "master"
			case model.NodeType_Secondary.String():
				nodeType = "slave"
			default:
				nodeType = "master"
			}
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				if strings.Contains(d.NodeType, nodeType) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if psm := queryFilter.PSM; psm != "" {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				targetPSM := datasource.ExtractRdsPsm(d.Info)
				return strings.Contains(targetPSM.Psm, psm)
			}).ToSlice(&tData)
		}
		// 过滤多节点(支持shard)
		if nodeIdList := queryFilter.NodeIds; len(nodeIdList) > 0 {
			fp.StreamOf(tData).Filter(func(d *DialogLog) bool {
				var existed bool
				for _, nodeId := range nodeIdList {
					if d.NodeId == nodeId {
						existed = true
						break
					}
				}
				return existed
			}).ToSlice(&tData)
		}
	}
	return &DescribeDialogDetailSnapshotResp{
		Details: tData,
		Total:   int64(len(tData)),
	}
}
func (c *client) filterLockWaitSnapShot(ctx context.Context, data []*DescribeLockCurrentWaitsDetail, req *DescribeLockWaitsDetailSnapshotReq) []*DescribeLockCurrentWaitsDetail {
	queryFilter := req.QueryFilter
	tData := data
	if len(req.NodeIds) > 0 {
		fp.StreamOf(tData).Filter(func(d *DescribeLockCurrentWaitsDetail) bool {
			var existed bool
			for _, nodeId := range req.NodeIds {
				if d.NodeId == nodeId {
					existed = true
					break
				}
			}
			return existed
		}).ToSlice(&tData)
	}
	mp := map[string]string{
		"RUNNING":      "RUNNING",
		"LOCKWAIT":     "LOCK WAIT",
		"ROLLING_BACK": "ROLLING BACK",
		"COMMITTING":   "COMMITTING",
	}
	// filter dialog details if desired
	if queryFilter != nil {
		if rTrxId := queryFilter.GetRTrxId(); rTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *DescribeLockCurrentWaitsDetail) bool {
				return strings.Contains(d.RTrxId, rTrxId)
			}).ToSlice(&tData)
		}
		if bTrxId := queryFilter.GetBTrxId(); bTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *DescribeLockCurrentWaitsDetail) bool {
				return strings.Contains(d.BTrxId, bTrxId)
			}).ToSlice(&tData)
		}
		if rTrxState := queryFilter.GetRTrxState(); rTrxState != "" {
			fp.StreamOf(tData).Filter(func(d *DescribeLockCurrentWaitsDetail) bool {
				state := mp[rTrxState]
				if state == "" {
					state = rTrxState
				}
				return strings.Contains(d.RTrxState, state)
			}).ToSlice(&tData)
		}
		if bTrxState := queryFilter.GetBTrxState(); bTrxState != "" {
			fp.StreamOf(tData).Filter(func(d *DescribeLockCurrentWaitsDetail) bool {
				state := mp[bTrxState]
				if state == "" {
					state = bTrxState
				}
				return strings.Contains(d.BTrxState, state)
			}).ToSlice(&tData)
		}

		if rWaitingQuery := queryFilter.GetRWaitingQuery(); rWaitingQuery != "" {
			fp.StreamOf(tData).Filter(func(d *DescribeLockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.RWaitingQuery), strings.ToLower(rWaitingQuery))
			}).ToSlice(&tData)
		}
		if bBlockingQuery := queryFilter.GetBBlockingQuery(); bBlockingQuery != "" {
			fp.StreamOf(tData).Filter(func(d *DescribeLockCurrentWaitsDetail) bool {
				return strings.Contains(strings.ToLower(d.BBlockingQuery), strings.ToLower(bBlockingQuery))
			}).ToSlice(&tData)
		}
	}
	return tData
}
func (c *client) DescribeTrxSnapshots(ctx context.Context, req *DescribeTrxSnapshotsReq) (*DescribeTrxSnapshotsResp, error) {
	var query string
	if req.NodeId != "" {
		query = fmt.Sprintf("* | select DISTINCT(CollectTime) where InstanceId='%s' and NodeId='%s' "+
			"order by CollectTime DESC limit 10000", req.InstanceId, req.NodeId)
	} else {
		query = fmt.Sprintf("* | select DISTINCT(CollectTime) where InstanceId='%s' "+
			"order by CollectTime DESC limit 10000", req.InstanceId)
	}
	log.Info(ctx, "DescribeTrxSnapshots call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.WarnS(ctx, "failed to search log details from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	ret := &DescribeTrxSnapshotsResp{}
	for _, snapshot := range resp.AnalysisResult.Data {
		var collectTime int64
		collectTime, err = strconv.ParseInt(snapshot["CollectTime"].(string), 10, 64)
		if err != nil {
			log.WarnS(ctx, "failed to convert CollectTime to int64",
				"CollectTime", snapshot["CollectTime"].(string), "err", err)
			continue
		}
		ret.Snapshots = append(ret.Snapshots, &Snapshot{
			CollectTime: collectTime,
		})
	}
	ret.Total = int64(resp.Count)
	return ret, nil
}
func (c *client) DescribeTrxDetailSnapshot(ctx context.Context, req *DescribeTrxDetailSnapshotReq) (*DescribeTrxDetailSnapshotResp, error) {
	var (
		query, orderByStr  string
		startTime, endTime int64
	)
	switch req.InstanceType {
	case model.DSType_Postgres:
		if req.NodeId != "" {
			query = fmt.Sprintf("* | select InstanceId,CollectTime,ProcessId,LockStatus,SqlBlocked,TrxId,BlockTrxId,LockList,TrxExecTime,NodeId,TrxStartTime,TrxStatus where InstanceId='%s' and NodeId='%s' ", req.InstanceId, req.NodeId)
		} else {
			query = fmt.Sprintf("* | select InstanceId,CollectTime,ProcessId,LockStatus,SqlBlocked,TrxId,BlockTrxId,LockList,TrxExecTime,NodeId,TrxStartTime,TrxStatus where InstanceId='%s' ", req.InstanceId)
		}
	default:
		if req.NodeId != "" {
			query = fmt.Sprintf("* | select InstanceId,CollectTime,ProcessId,LockStatus,SqlBlocked,TrxId,BlockTrxId,LockList,TrxIsoLevel,TrxExecTime,TrxRowsLocked,NodeId,TrxRowsModified,TrxStartTime,TrxStatus,TrxTablesLocked,TrxWaitStartTime where InstanceId='%s' and NodeId='%s' ", req.InstanceId, req.NodeId)
		} else {
			query = fmt.Sprintf("* | select InstanceId,CollectTime,ProcessId,LockStatus,SqlBlocked,TrxId,BlockTrxId,LockList,TrxIsoLevel,TrxExecTime,TrxRowsLocked,NodeId,TrxRowsModified,TrxStartTime,TrxStatus,TrxTablesLocked,TrxWaitStartTime where InstanceId='%s' ", req.InstanceId)
		}
	}
	if req.CollectTime > 0 {
		query += fmt.Sprintf(" and CollectTime='%v' ", req.CollectTime)
	}
	if req.OrderBy.String() != "<UNSET>" {
		orderByStr = fmt.Sprintf("order by %s %s ", req.OrderBy.String(), req.SortBy.String()) // ignore_security_alert
	} else {
		// 默认按阻塞sql的时间排序
		orderByStr = fmt.Sprintf("order by %s %s ", trxExecTimeKey, "DESC") // ignore_security_alert
	}
	query += " " + orderByStr // ignore_security_alert
	query += fmt.Sprintf(" limit %d", 10000)
	log.Info(ctx, "DescribeTrxDetailSnapshot call TLS SearchLogs query content is %s", query)
	// 若配置了StartTime,EndTime
	if req.EndTime > req.StartTime {
		startTime = req.StartTime
		endTime = req.EndTime
	} else {
		startTime = req.CollectTime - 30
		endTime = req.CollectTime + 30
	}
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: startTime,
		EndTime:   endTime,
		Query:     query,
	})
	if err != nil {
		log.WarnS(ctx, "failed to search dialog detail log from TLS",
			"topic", c.connection.TopicId, "sql", query, "err", err)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	var snapshots []*TrxLog
	for _, detail := range resp.AnalysisResult.Data {
		// 过滤掉无效的数据
		if detail["ProcessId"].(string) == "" {
			continue
		}
		var trxExecTime, trxRowsLocked, trxRowsModified, trxTablesLocked, collectTime int64
		var trxIsoLevel, trxWaitStartTime string
		collectTime, err = strconv.ParseInt(detail["CollectTime"].(string), 10, 64)
		if err != nil {
			log.WarnS(ctx, "failed to convert CollectTime to int64",
				"CollectTime", detail["CollectTime"].(string), "err", err)
			continue
		}
		lockSlice := make([]*Lock, 0)
		if trxExecTimeStr, ok := detail[trxExecTimeKey]; ok {
			trxExecTime, _ = strconv.ParseInt(trxExecTimeStr.(string), 10, 64)
		}
		if trxRowsLockedStr, ok := detail[trxRowsLockedKey]; ok {
			trxRowsLocked, _ = strconv.ParseInt(trxRowsLockedStr.(string), 10, 64)
		}
		if trxRowsModifiedStr, ok := detail[trxRowsModifiedKey]; ok {
			trxRowsModified, _ = strconv.ParseInt(trxRowsModifiedStr.(string), 10, 64)
		}
		if trxTablesLockedStr, ok := detail[trxTablesLockedKey]; ok {
			trxTablesLocked, _ = strconv.ParseInt(trxTablesLockedStr.(string), 10, 64)
		}
		if trxLevel, ok := detail["TrxIsoLevel"]; ok {
			trxIsoLevel = trxLevel.(string)

		}
		if waitStartTime, ok := detail["TrxWaitStartTime"]; ok {
			trxWaitStartTime = waitStartTime.(string)

		}
		if lockList, ok := detail["LockList"]; ok {
			if lockList.(string) != "[]" {
				lockStr := lockList.(string)
				err := json.Unmarshal([]byte(lockStr), &lockSlice)
				if err != nil {
					log.Warn(ctx, "unmarshal failed %s", err)
				}
			}
		}
		snapshots = append(snapshots, &TrxLog{
			InstanceId:       detail["InstanceId"].(string),
			CollectTime:      collectTime,
			ProcessId:        detail["ProcessId"].(string),
			LockStatus:       detail["LockStatus"].(string),
			SqlBlocked:       detail["SqlBlocked"].(string),
			TrxId:            detail["TrxId"].(string),
			BlockTrxId:       detail["BlockTrxId"].(string),
			TrxIsoLevel:      trxIsoLevel,
			TrxStartTime:     detail["TrxStartTime"].(string),
			TrxWaitStartTime: trxWaitStartTime,
			TrxStatus:        detail["TrxStatus"].(string),
			NodeId:           detail["NodeId"].(string),
			LockList:         lockSlice,
			TrxExecTime:      int32(trxExecTime),
			TrxRowsLocked:    int32(trxRowsLocked),
			TrxRowsModified:  int32(trxRowsModified),
			TrxTablesLocked:  int32(trxTablesLocked),
		})
	}
	finalRet := c.filterTrxSnapShot(ctx, snapshots, req)
	// 默认按事务执行时间倒序
	//SortTrx(finalRet.Details, shared.DESC, trxExecTimeKey)
	return finalRet, nil

}

func (c *client) filterTrxSnapShot(ctx context.Context, data []*TrxLog, req *DescribeTrxDetailSnapshotReq) *DescribeTrxDetailSnapshotResp {
	queryFilter := req.QueryFilter
	tData := data
	// filter trx details if desired
	if queryFilter != nil {
		if pID := queryFilter.ProcessId; pID != "" {
			fp.StreamOf(tData).Filter(func(d *TrxLog) bool {
				return strings.Contains(d.ProcessId, pID)
			}).ToSlice(&tData)
		}
		if tId := queryFilter.TrxId; tId != "" {
			fp.StreamOf(tData).Filter(func(d *TrxLog) bool {
				return strings.Contains(d.TrxId, tId)
			}).ToSlice(&tData)
		}
		if trxStatus := queryFilter.TrxStatus; trxStatus != "" && trxStatus != "<UNSET>" {
			fp.StreamOf(tData).Filter(func(d *TrxLog) bool {
				return strings.Contains(d.TrxStatus, trxStatus)
			}).ToSlice(&tData)
		}
		if command := queryFilter.SqlBlocked; command != "" {
			fp.StreamOf(tData).Filter(func(d *TrxLog) bool {
				return strings.Contains(d.SqlBlocked, command)
			}).ToSlice(&tData)
		}
		if limitTime := queryFilter.TrxExecTime; limitTime >= 0 {
			fp.StreamOf(tData).Filter(func(d *TrxLog) bool {
				return d.TrxExecTime >= limitTime
			}).ToSlice(&tData)
		}
		if blockTrxId := queryFilter.BlockTrxId; blockTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *TrxLog) bool {
				return strings.Contains(d.BlockTrxId, blockTrxId)
			}).ToSlice(&tData)
		}
		if lockStatus := queryFilter.LockStatus; lockStatus != "" && lockStatus != "<UNSET>" {
			fp.StreamOf(tData).Filter(func(d *TrxLog) bool {
				return strings.Contains(d.LockStatus, lockStatus)
			}).ToSlice(&tData)
		}
		if trxIsoLevel := queryFilter.TrxIsoLevel; trxIsoLevel != "" {
			fp.StreamOf(tData).Filter(func(d *TrxLog) bool {
				return strings.Contains(d.TrxIsoLevel, trxIsoLevel)
			}).ToSlice(&tData)
		}

	}
	return &DescribeTrxDetailSnapshotResp{
		Details: tData,
		Total:   int64(len(tData)),
	}
}

package tls

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/slowquery"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	biz_utils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/hashicorp/golang-lru/v2/expirable"
	"github.com/shopspring/decimal"
	sdk "github.com/volcengine/volc-sdk-golang/service/tls"
)

const (
	offsetKey = "TimestampIndex" // searchLogsV2&2.0架构下不再输出DIVIDE(SUBTRACT(Timestamp))

	sumQueryTimeKey       = "SumQueryTime"
	minQueryTimeKey       = "MinQueryTime"
	maxQueryTimeKey       = "MaxQueryTime"
	averageQueryTimeKey   = "AvgQueryTime"
	sumQueryTimeField     = "SUM(QueryTime) AS SumQueryTime"
	minQueryTimeField     = "MIN(QueryTime) AS MinQueryTime"
	maxQueryTimeField     = "MAX(QueryTime) AS MaxQueryTime"
	averageQueryTimeField = "AVG(QueryTime) AS AvgQueryTime"

	DialogExecuteCountKey     = "ExecuteCount"
	sumStateKeepTimeKey       = "TotalStateKeepTime"
	maxStateKeepTimeKey       = "MaxStateKeepTime"
	averageStateKeepTimeKey   = "AvgStateKeepTime"
	DialogExecuteCountField   = "COUNT(*) AS ExecuteCount"
	sumStateKeepTimeField     = "SUM(TO_SECONDS(Time)) AS TotalStateKeepTime"
	maxStateKeepTimeField     = "MAX(TO_SECONDS(Time)) AS MaxStateKeepTime"
	averageStateKeepTimeField = "AVG(TO_SECONDS(Time)) AS AvgStateKeepTime"

	sumLockTimeKey     = "SumLockTime"
	minLockTimeKey     = "MinLockTime"
	maxLockTimeKey     = "MaxLockTime"
	averageLockTimeKey = "AvgLockTime"

	sumLockTimeField     = "SUM(LockTime) AS SumLockTime"
	minLockTimeField     = "MIN(LockTime) AS MinLockTime"
	maxLockTimeField     = "MAX(LockTime) AS MaxLockTime"
	averageLockTimeField = "AVG(LockTime) AS AvgLockTime"

	sumRowsExaminedKey     = "SumRowsExamined"
	minRowsExaminedKey     = "MinRowsExamined"
	maxRowsExaminedKey     = "MaxRowsExamined"
	averageRowsExaminedKey = "AvgRowsExamined"

	sumRowsExaminedField     = "SUM(RowsExamined) AS SumRowsExamined"
	minRowsExaminedField     = "MIN(RowsExamined) AS MinRowsExamined"
	maxRowsExaminedField     = "MAX(RowsExamined) AS MaxRowsExamined"
	averageRowsExaminedField = "AVG(RowsExamined) AS AvgRowsExamined"

	sumRowsSentKey     = "SumRowsSent"
	minRowsSentKey     = "MinRowsSent"
	maxRowsSentKey     = "MaxRowsSent"
	averageRowsSentKey = "AvgRowsSent"

	sumRowsSentField     = "SUM(RowsSent) AS SumRowsSent"
	minRowsSentField     = "MIN(RowsSent) AS MinRowsSent"
	maxRowsSentField     = "MAX(RowsSent) AS MaxRowsSent"
	averageRowsSentField = "AVG(RowsSent) AS AvgRowsSent"

	countKey          = "Count"
	countField        = "COUNT(*) AS Count" // 兼容TLS 2.0架构
	maxTimestampKey   = "MaxTimestamp"
	minTimestampKey   = "MinTimestamp"
	maxTimestampField = "MAX(Timestamp) AS MaxTimestamp"
	minTimestampField = "MIN(Timestamp) AS MinTimestamp"

	sqlTemplateKey            = "SQLTemplate"
	SQLTemplateID             = "SQLTemplateID"
	sqlTextKey                = "SQLText"
	sourceIPKey               = "SourceIP"
	userKey                   = "User"
	stateKey                  = "State"
	dbKey                     = "DB"
	connectionIdKey           = "ConnectionId"
	queryTimeKey              = "QueryTime"
	lockTimeKey               = "LockTime"
	rowsExaminedKey           = "RowsExamined"
	rowsSendKey               = "RowsSent"
	DateTimeKey               = "`DateTime`"
	SQLType                   = "SQLType"
	timestampKey              = "Timestamp"
	instanceKey               = "__tag__instance__"
	instanceIDKey             = "Instance"
	SqlMethodKey              = "sql_type"
	SqlTableKey               = "sql_tables"
	PSMKey                    = "Psm"
	offsetKeyTemplate         = "FLOOR(DIVIDE(SUBTRACT(Timestamp,%d),%d))" // 2.0架构下输出结果为小数，向下取整
	podNameKey                = "__pod_name__"
	nodeIdKey                 = "__tag__instanceNodeID__"
	clusterInstanceIDKey      = "__tag__clusterInstanceID__"
	clusterLogicInstanceIDKey = "__tag__clusterLogicInstanceID__"
	trxExecTimeKey            = "TrxExecTime"
	trxRowsLockedKey          = "TrxRowsLocked"
	trxTablesLockedKey        = "TrxTablesLocked"
	trxRowsModifiedKey        = "TrxRowsModified"
	// 规避TLS Bug
	whereConditionsMust = "QueryTime>=0 and LockTime>=0 and RowsSent>=0 and RowsExamined>=0 and Timestamp>=0 "
	// 过滤SQLText解析失败慢日志
	filterSqlParseFailed = "(SQLText IS NOT NULL) "

	logicalTablesKey         = "logical_tables"
	logicalDBNamesKey        = "logical_db_names"
	logicalSqlTemplateKey    = "logical_sql_template"
	logicalSqlFingerprintKey = "logical_sql_fingerprint"
	contextDBKey             = "context_db"
)

func GetField(field string, isLogical bool) string {
	if !isLogical {
		return field
	}
	switch field {
	case sqlTemplateKey:
		return logicalSqlTemplateKey
	case SQLTemplateID:
		return logicalSqlFingerprintKey
	case dbKey:
		return logicalDBNamesKey
	case SqlTableKey:
		return logicalTablesKey
	}
	return field
}

type slowlogClient struct {
	connection *ConnectionInfo
	realClient sdk.Client
	conf       config.ConfigProvider
}

func NewSlowlogClient(connectionInfo *ConnectionInfo, conf config.ConfigProvider) SlowLogClient {
	c := &slowlogClient{
		connection: connectionInfo,
		realClient: sdk.NewClient(
			connectionInfo.Endpoint,
			connectionInfo.AccessKeyID,
			connectionInfo.AccessKeySecret,
			"",
			connectionInfo.Region,
		),
		conf: conf,
	}
	c.realClient.SetTimeout(time.Second * 25)
	return WithTimeout(c, time.Second*27)
}

var lru = expirable.NewLRU(100, func(k string, v SlowLogClient) {}, time.Minute*30)

func GetSlowClient(ctx context.Context, conf config.ConfigProvider, c3Cfg c3.ConfigProvider, dsType string, instanceId string) (SlowLogClient, error) {
	if c, ok := lru.Get(instanceId); ok {
		return c, nil
	} else {
		cli, err := InitTLSConnection(ctx, conf, c3Cfg, dsType, instanceId)
		if err != nil {
			log.Warn(ctx, "init tls client err")
			return nil, err
		}
		lru.Add(instanceId, cli)
		return cli, nil
	}
}
func InitTLSConnection(ctx context.Context, cnf config.ConfigProvider, c3CfgP c3.ConfigProvider, dsType string, instanceId string) (SlowLogClient, error) {
	var slowLogTopic string
	conf := cnf.Get(ctx)
	c3Cfg := c3CfgP.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tlsAK := c3Cfg.TLSServiceAccessKey
	tlsSK := c3Cfg.TLSServiceSecretKey
	if conf.EnableNewVersionSlowLogTopic || biz_utils.IsByteCloud() {
		slowLogTopic = getTopicId(ctx, c3CfgP, dsType, instanceId)
	} else {
		tlsSlowLogTopic := c3Cfg.TLSSlowLogTopic
		topicSet := &datasource.TLSSlowLogTopic{}
		if err := json.Unmarshal([]byte(tlsSlowLogTopic), topicSet); err != nil {
			log.Warn(ctx, " tlsSlowLogTopic unmarshal failed %v", err)
			return nil, err
		}
		for _, topic := range topicSet.Topics {
			if topic.InstanceType == dsType {
				slowLogTopic = topic.TopicID
			}
		}
	}
	log.Info(ctx, "current topic is %s", slowLogTopic)

	var dbInternalUsers map[string]string

	err := json.Unmarshal([]byte(conf.DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return nil, err
	}
	tlsRegion, _ := cnf.Get(ctx).GetVolcTlsRegion()
	internalUsers := strings.Split(dbInternalUsers[dsType], ",")
	connectionInfo := &ConnectionInfo{
		Endpoint:        cnf.Get(ctx).GetVolcTlsEndpoint(),
		AccessKeyID:     tlsAK,
		AccessKeySecret: tlsSK,
		Region:          tlsRegion,
		TopicId:         slowLogTopic,
		InternalUsers:   internalUsers,
	}

	tlsClient := NewSlowlogClient(connectionInfo, cnf)
	return tlsClient, nil
}

func getTopicId(ctx context.Context, c3Cfg c3.ConfigProvider, DSType string, instanceId string) string {
	var (
		tlsSlowLogTopicV2 string
		topicId           string
	)
	c3conf := c3Cfg.GetNamespace(ctx, consts.C3ApplicationNamespace)
	topicSet := &datasource.TLSSlowLogTopicV2{}
	lastChar := instanceId[len(instanceId)-1:] + "$"
	log.Info(ctx, "getTopicId instanceId: %s, DSType: %s", instanceId, DSType)
	switch DSType {
	case model.DSType_MySQL.String():
		tlsSlowLogTopicV2 = c3conf.TLSRdsSlowLogTopicV2
	case model.DSType_VeDBMySQL.String():
		tlsSlowLogTopicV2 = c3conf.TLSNdbSlowLogTopicV2
	case model.DSType_Postgres.String():
		tlsSlowLogTopicV2 = c3conf.TLSPgSlowLogTopicV2
	case model.DSType_MySQLSharding.String():
		switch ConvShardingDataSourceType(shared.DataSourceType(model.DSType_MySQLSharding), instanceId) {
		case shared.MySQL:
			tlsSlowLogTopicV2 = c3conf.TLSRdsSlowLogTopicV2
		case shared.VeDBMySQL:
			tlsSlowLogTopicV2 = c3conf.TLSNdbSlowLogTopicV2
		}
	case model.DSType_MetaMySQL.String():
		tlsSlowLogTopicV2 = c3conf.TLSMetaSlowLogTopicV2
	case model.DSType_MetaRDS.String():
		tlsSlowLogTopicV2 = c3conf.TLSMetaSlowLogTopicV2
	case model.DSType_ByteRDS.String():
		topic, err := slowquery.GetTopicId(ctx, DSType, instanceId)
		if err != nil {
			log.Warn(ctx, "get topic id failed %v", err)
			return ""
		}
		return topic
	default:
		log.Warn(ctx, "Not support instance type %s", DSType)
		return topicId
	}
	if err := json.Unmarshal([]byte(tlsSlowLogTopicV2), topicSet); err != nil {
		log.Warn(ctx, " tlsSlowLogTopic unmarshal failed %v", err)
		return topicId
	}
	for _, topic := range topicSet.Topics {
		for _, item := range topic.TopicList {
			if item.MatchedRegex == lastChar {
				topicId = item.TopicID
				return topicId
			}
		}
	}
	log.Warn(ctx, "not found topic id, instanceId: %s, DSType: %s", instanceId, DSType)
	return topicId
}

func (c *slowlogClient) DescribeSlogLogTimeSeriesStats(ctx context.Context, req *shared.DescribeSlowLogTimeSeriesStatsReq) (*shared.DescribeSlowLogTimeSeriesStatsResp, error) {
	var (
		query            string
		limitStr         string
		slowLogCountList []*shared.SlowLogCount
		whereConditions  string
	)
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)

	query = "* | "
	interval := req.Interval
	left := req.StartTime / interval * interval
	right := req.EndTime / interval * interval
	if req.EndTime%interval != 0 {
		right = (req.EndTime/interval + 1) * interval
	}

	if req.SearchParam != nil && req.SearchParam.SQLTemplate != "" {
		query = fmt.Sprintf(`"%s" | `, req.SearchParam.SQLTemplate)
	}

	if req.Limit == 0 {
		diff := right - left
		totalCount := int(math.Ceil(float64(diff) / float64(interval)))
		slowLogCountList = make([]*shared.SlowLogCount, totalCount)
		maxLimit := c.conf.Get(ctx).MaxSlowLogDownloadLimits
		limitStr = fmt.Sprintf("limit %d", maxLimit) //默认返回10000条，修复聚合点数限制为100的问题
	} else {
		topN := req.Limit
		slowLogCountList = make([]*shared.SlowLogCount, topN)
		limitStr = fmt.Sprintf("limit %d", req.Limit)
	}

	groupCondition := fmt.Sprintf(offsetKeyTemplate, left, interval)
	groupField := groupCondition + " AS TimestampIndex" // 兼容TLS 2.0架构
	switch req.DataSourceType {
	case shared.MySQL:
		whereConditions = fmt.Sprintf("where (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
	case shared.VeDBMySQL:
		whereConditions = fmt.Sprintf("where %s='%s' ", instanceKey, req.InstanceId)
	case shared.Postgres:
		whereConditions = fmt.Sprintf("where %s='%s' ", instanceKey, req.InstanceId)
	case shared.ByteRDS:
		whereConditions = fmt.Sprintf("where %s='%s' ", instanceIDKey, req.InstanceId)
	default:
		whereConditions = "where "
	}

	whereConditions += fmt.Sprintf(" and %s ", filterSqlParseFailed)
	// 添加SqlTemplateID
	if req.SearchParam != nil && req.SearchParam.SQLTemplateID != "" {
		whereConditions += fmt.Sprintf(" and %s='%s' ", GetField(SQLTemplateID, req.SearchParam.IsLogical), req.SearchParam.SQLTemplateID)
	}
	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		whereConditions += fmt.Sprintf(" and %s ", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		whereConditions += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	query = fmt.Sprintf("%s select %s,%s %s group by %s",
		query, groupField, countField, whereConditions, groupCondition)

	if req.Limit != 0 {
		// topN 倒序排序
		query += fmt.Sprintf(" order by %s DESC ", countKey)
	}
	if limitStr != "" {
		query = fmt.Sprintf("%s %s", query, limitStr)
	}
	searchLogsRequest := &sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		Query:     query,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
	log.Info(ctx, "DescribeSlogLogTimeSeriesStats call TLS SearchLogs request is %s", searchLogsRequest)
	searchLogsResponse, err := c.realClient.SearchLogsV2(searchLogsRequest)
	if err != nil {
		log.Warn(ctx, "DescribeSlogLogTimeSeriesStats: search log from topic %s error: %v, SQL: %s", searchLogsRequest.TopicID, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	schema := searchLogsResponse.AnalysisResult.Schema
	if len(schema) != 2 {
		err = fmt.Errorf("the length of result schema expect 2, got %d", len(schema))
		log.Warn(ctx, "DescribeSlogLogTimeSeriesStats: search log from topic %s error: %v, SQL: %s", searchLogsRequest.TopicID, err, query)
		return nil, err
	}

	// topN给的是整点的时间 topN分支
	if req.Limit != 0 {
		var realLength int
		for _, data := range searchLogsResponse.AnalysisResult.Data {
			slowLogCount := new(shared.SlowLogCount)
			if offsetJson, ok := data[offsetKey]; ok {
				offset, err := parseInt64(offsetJson)
				if err != nil {
					continue
				}
				slowLogCount.Timestamp = left + interval*offset
			}

			if countJson, ok := data[countKey]; ok {
				count, _ := parseInt64(countJson)
				slowLogCount.Count = count
			}
			slowLogCountList[realLength] = slowLogCount
			realLength++
		}

		slowLogCountList = slowLogCountList[:realLength]
		return &shared.DescribeSlowLogTimeSeriesStatsResp{
			SlowLogCountStats: slowLogCountList,
		}, nil
	}

	for i := 0; i < len(slowLogCountList); i++ {
		if i == 0 {
			slowLogCountList[i] = &shared.SlowLogCount{
				Timestamp: req.StartTime,
				Count:     0,
			}
			continue
		}
		slowLogCountList[i] = &shared.SlowLogCount{
			Timestamp: left + int64(i)*interval,
			Count:     0,
		}
	}
	for _, data := range searchLogsResponse.AnalysisResult.Data {
		var index int64
		if offsetJson, ok := data[offsetKey]; ok {
			offset, err := parseInt64(offsetJson)
			if err != nil {
				continue
			}
			index = offset
		}
		// 修复 index out of range panic
		if index < 0 || index >= int64(len(slowLogCountList)) {
			continue
		}
		slowLogCount := slowLogCountList[index]
		if countJson, ok := data[countKey]; ok {
			count, _ := parseInt64(countJson)
			slowLogCount.Count = count
		}
	}

	return &shared.DescribeSlowLogTimeSeriesStatsResp{
		SlowLogCountStats: slowLogCountList,
	}, nil
}

func (c *slowlogClient) DescribeAggregateSlowLogs(ctx context.Context, req *shared.DescribeAggregateSlowLogsReq) (*shared.DescribeAggregateSlowLogsResp, error) {
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)

	totalRecordCount := int64(0)
	cquery, err := c.GenAggrSlowLogCountQuery(ctx, req, c.getInternalUsers(ctx, req.DataSourceType.String()))
	if err != nil {
		return nil, err
	}
	cRequest := &sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		Query:     cquery,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
	log.Info(ctx, "DescribeAggregateSlowLogs call TLS SearchLogs request is %s", cRequest)
	cresp, err := c.realClient.SearchLogsV2(cRequest)
	if err != nil || len(cresp.AnalysisResult.Data) == 0 {
		log.Warn(ctx, "DescribeAggregateSlowLogs: search log from topic %s error: %v, SQL: %s", cRequest.TopicID, err, cquery)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	cdata := cresp.AnalysisResult.Data[0]
	if count, ok := cdata[countKey]; ok {
		cnt, _ := parseInt64(count)
		totalRecordCount = cnt
	}
	log.Info(ctx, "totalRecordCount:%d", totalRecordCount)
	if totalRecordCount == 0 {
		return &shared.DescribeAggregateSlowLogsResp{
			AggregateSlowLogs: nil,
			Total:             0,
		}, nil
	}

	query, err := c.GenAggrSlowLogQuery(ctx, req, c.getInternalUsers(ctx, req.DataSourceType.String()))
	if err != nil {
		return nil, err
	}
	searchLogsRequest := &sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		Query:     query,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
	log.Info(ctx, "DescribeAggregateSlowLogs call TLS SearchLogs request is %s", searchLogsRequest)
	resp, err := c.realClient.SearchLogsV2(searchLogsRequest)
	if err != nil {
		log.Warn(ctx, "DescribeAggregateSlowLogs: search log from topic %s error: %v, SQL: %s", searchLogsRequest.TopicID, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}
	aggregateSlowLogList := make([]*shared.AggregateSlowLog, len(resp.AnalysisResult.Data))
	totalLockTime := 0.0
	totalQueryTime := 0.0
	totalRowsExamined := 0.0
	totalRowsSent := 0.0
	totalExecuteCount := 0.0

	statQuery := c.GenAggrTotalStats(ctx, req)
	statRequest := &sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		Query:     statQuery,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
	log.Info(ctx, "DescribeAggregateSlowLogs call TLS SearchLogs request is %s", statRequest)
	statResp, err := c.realClient.SearchLogsV2(statRequest)
	if err != nil || len(statResp.AnalysisResult.Data) == 0 {
		log.Warn(ctx, "DescribeAggregateSlowLogs: search log from topic %s error: %v, SQL: %s", statRequest.TopicID, err, statQuery)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	aggregate := statResp.AnalysisResult.Data[0]
	if count, ok := aggregate[countKey]; ok {
		cnt, _ := parseInt64(count)
		totalExecuteCount = float64(cnt)
	}
	if sumLockTime, ok := aggregate[sumLockTimeKey]; ok {
		cnt, _ := parseFloat64(sumLockTime)
		totalLockTime = cnt
	}
	if sumQueryTime, ok := aggregate[sumQueryTimeKey]; ok {
		cnt, _ := parseFloat64(sumQueryTime)
		totalQueryTime = cnt
	}
	if sumRowsSent, ok := aggregate[sumRowsSentKey]; ok {
		cnt, _ := parseFloat64(sumRowsSent)
		totalRowsSent = cnt
	}
	if sumRowsExamined, ok := aggregate[sumRowsExaminedKey]; ok {
		cnt, _ := parseFloat64(sumRowsExamined)
		totalRowsExamined = cnt
	}
	log.Info(ctx, "totalLockTime:%f, totalQueryTime:%f, totalRowsExamined:%f, totalRowsSent:%f, totalExecuteCount:%f",
		totalLockTime, totalQueryTime, totalRowsExamined, totalRowsSent, totalExecuteCount)

	for i := 0; i < len(resp.AnalysisResult.Data); i++ {
		aggregateSlowLog := new(shared.AggregateSlowLog)
		aggregateSlowLog.LockTimeStats = new(shared.StatisticResult)
		aggregateSlowLog.QueryTimeStats = new(shared.StatisticResult)
		aggregateSlowLog.RowsExaminedStats = new(shared.StatisticResult)
		aggregateSlowLog.RowsSentStats = new(shared.StatisticResult)

		if sqlTemplate, ok := resp.AnalysisResult.Data[i][GetField(sqlTemplateKey, req.SearchParam.IsLogical)]; ok {
			if sqlTemplate != nil {
				aggregateSlowLog.SQLTemplate = sqlTemplate.(string)
			} else {
				aggregateSlowLog.SQLTemplate = ""
			}
		}
		if sqlTemplateID, ok := resp.AnalysisResult.Data[i][GetField(SQLTemplateID, req.SearchParam.IsLogical)]; ok {
			if sqlTemplateID != nil {
				aggregateSlowLog.SQLTemplateID = sqlTemplateID.(string)
			} else {
				aggregateSlowLog.SQLTemplateID = ""
			}
		}

		if db, ok := resp.AnalysisResult.Data[i][GetField(dbKey, req.SearchParam.IsLogical)]; ok {
			if db != nil {
				aggregateSlowLog.DB = db.(string)
			} else {
				aggregateSlowLog.DB = ""
			}
		}

		if user, ok := resp.AnalysisResult.Data[i][userKey]; ok {
			if user != nil {
				aggregateSlowLog.User = user.(string)
			} else {
				aggregateSlowLog.User = ""
			}

		}

		if sourceIP, ok := resp.AnalysisResult.Data[i][sourceIPKey]; ok {
			if sourceIP != nil {
				aggregateSlowLog.SourceIP = sourceIP.(string)
			} else {
				aggregateSlowLog.SourceIP = ""
			}
		}

		if sqlType, ok := resp.AnalysisResult.Data[i][SQLType]; ok {
			if sqlType != nil {
				aggregateSlowLog.SQLType = sqlType.(string)
			} else {
				aggregateSlowLog.SQLType = "-"
			}
		}

		if count, ok := resp.AnalysisResult.Data[i][countKey]; ok {
			aggregateSlowLog.ExecuteCount, _ = parseInt64(count)
		}

		// lockTime stats
		if sumLockTime, ok := resp.AnalysisResult.Data[i][sumLockTimeKey]; ok {
			aggregateSlowLog.LockTimeStats.Total, _ = parseFloat64(sumLockTime)
		}
		if minLockTime, ok := resp.AnalysisResult.Data[i][minLockTimeKey]; ok {
			aggregateSlowLog.LockTimeStats.Min, _ = parseFloat64(minLockTime)
		}
		if maxLockTime, ok := resp.AnalysisResult.Data[i][maxLockTimeKey]; ok {
			aggregateSlowLog.LockTimeStats.Max, _ = parseFloat64(maxLockTime)
		}
		if avgLockTime, ok := resp.AnalysisResult.Data[i][averageLockTimeKey]; ok {
			aggregateSlowLog.LockTimeStats.Average, _ = parseFloat64(avgLockTime)
		}

		// queryTime stats
		if sumQueryTime, ok := resp.AnalysisResult.Data[i][sumQueryTimeKey]; ok {
			aggregateSlowLog.QueryTimeStats.Total, _ = parseFloat64(sumQueryTime)
		}
		if minQueryTime, ok := resp.AnalysisResult.Data[i][minQueryTimeKey]; ok {
			aggregateSlowLog.QueryTimeStats.Min, _ = parseFloat64(minQueryTime)
		}
		if maxQueryTime, ok := resp.AnalysisResult.Data[i][maxQueryTimeKey]; ok {
			aggregateSlowLog.QueryTimeStats.Max, _ = parseFloat64(maxQueryTime)
		}
		if avgQueryTime, ok := resp.AnalysisResult.Data[i][averageQueryTimeKey]; ok {
			aggregateSlowLog.QueryTimeStats.Average, _ = parseFloat64(avgQueryTime)
		}

		// rowsSent stats
		if sumRowsSent, ok := resp.AnalysisResult.Data[i][sumRowsSentKey]; ok {
			aggregateSlowLog.RowsSentStats.Total, _ = parseFloat64(sumRowsSent)
		}
		if minRowsSent, ok := resp.AnalysisResult.Data[i][minRowsSentKey]; ok {
			aggregateSlowLog.RowsSentStats.Min, _ = parseFloat64(minRowsSent)
		}
		if maxRowsSent, ok := resp.AnalysisResult.Data[i][maxRowsSentKey]; ok {
			aggregateSlowLog.RowsSentStats.Max, _ = parseFloat64(maxRowsSent)
		}
		if averageRowsSent, ok := resp.AnalysisResult.Data[i][averageRowsSentKey]; ok {
			aggregateSlowLog.RowsSentStats.Average, _ = parseFloat64(averageRowsSent)
		}

		// rowsExamined stats
		if sumRowsExamined, ok := resp.AnalysisResult.Data[i][sumRowsExaminedKey]; ok {
			aggregateSlowLog.RowsExaminedStats.Total, _ = parseFloat64(sumRowsExamined)
		}
		if minRowsExamined, ok := resp.AnalysisResult.Data[i][minRowsExaminedKey]; ok {
			aggregateSlowLog.RowsExaminedStats.Min, _ = parseFloat64(minRowsExamined)
		}
		if maxRowsExamined, ok := resp.AnalysisResult.Data[i][maxRowsExaminedKey]; ok {
			aggregateSlowLog.RowsExaminedStats.Max, _ = parseFloat64(maxRowsExamined)
		}
		if averageRowsExamined, ok := resp.AnalysisResult.Data[i][averageRowsExaminedKey]; ok {
			aggregateSlowLog.RowsExaminedStats.Average, _ = parseFloat64(averageRowsExamined)
		}

		if maxTimestamp, ok := resp.AnalysisResult.Data[i][maxTimestampKey]; ok {
			maxTimestampStr, _ := parseString(maxTimestamp)
			d, _ := decimal.NewFromString(maxTimestampStr)
			aggregateSlowLog.LastAppearTime = d.IntPart()
		}

		if minTimestamp, ok := resp.AnalysisResult.Data[i][minTimestampKey]; ok {
			minTimestampStr, _ := parseString(minTimestamp)
			d, _ := decimal.NewFromString(minTimestampStr)
			aggregateSlowLog.FirstAppearTime = d.IntPart()
		}

		if field, ok := resp.AnalysisResult.Data[i][PSMKey]; ok {
			if field != nil {
				aggregateSlowLog.PSM = field.(string)
			} else {
				aggregateSlowLog.PSM = "-"
			}
		}
		if field, ok := resp.AnalysisResult.Data[i][GetField(SqlTableKey, req.SearchParam.IsLogical)]; ok {
			if field != nil {
				aggregateSlowLog.Table = field.(string)
			} else {
				aggregateSlowLog.Table = "-"
			}
		}
		if field, ok := resp.AnalysisResult.Data[i][SqlMethodKey]; ok {
			if field != nil {
				aggregateSlowLog.SQLType = field.(string)
			} else {
				aggregateSlowLog.SQLType = "-"
			}
		}
		aggregateSlowLogList[i] = aggregateSlowLog
	}

	// TODO 方差、中位数、p95等指标需要等TLS就绪
	for i := 0; i < len(aggregateSlowLogList); i++ {
		// 计算百分比
		if totalExecuteCount != 0.0 {
			ratio := float64(aggregateSlowLogList[i].ExecuteCount) / totalExecuteCount * 100
			aggregateSlowLogList[i].ExecuteCountRatio = decimalFn(ratio)
		}

		if totalQueryTime != 0.0 {
			ratio := aggregateSlowLogList[i].QueryTimeStats.Total / totalQueryTime * 100
			aggregateSlowLogList[i].QueryTimeRatio = decimalFn(ratio)
		}

		if totalLockTime != 0.0 {
			ratio := aggregateSlowLogList[i].LockTimeStats.Total / totalLockTime * 100
			aggregateSlowLogList[i].LockTimeRatio = decimalFn(ratio)
		}

		if totalRowsSent != 0.0 {
			ratio := aggregateSlowLogList[i].RowsSentStats.Total / totalRowsSent * 100
			aggregateSlowLogList[i].RowsSentRatio = decimalFn(ratio)
		}

		if totalRowsExamined != 0.0 {
			ratio := aggregateSlowLogList[i].RowsExaminedStats.Total / totalRowsExamined * 100
			aggregateSlowLogList[i].RowsExaminedRatio = decimalFn(ratio)
		}
	}

	for i := 0; i < len(aggregateSlowLogList); i++ {
		aggregateSlowLogList[i].PTAnalysisResult = fetchPTAnalysisResult(ctx, aggregateSlowLogList[i])
	}
	return &shared.DescribeAggregateSlowLogsResp{
		AggregateSlowLogs: aggregateSlowLogList,
		Total:             int32(totalRecordCount),
	}, nil

}

func (c *slowlogClient) GenAggrSlowLogQuery(ctx context.Context, req *shared.DescribeAggregateSlowLogsReq, internalUsers []string) (string, error) {
	var (
		query           string
		groupCondition  string
		groupFields     string
		hasWhere        bool
		whereConditions string
		fields          []string
	)
	dataSourceType := req.DataSourceType

	searchParam := req.SearchParam
	// 分组字段
	switch dataSourceType {
	case shared.Postgres:
		fields = []string{GetField(dbKey, req.SearchParam.IsLogical), userKey, sourceIPKey, GetField(SQLTemplateID, req.SearchParam.IsLogical)}
	case shared.MySQL:
		fields = []string{GetField(dbKey, req.SearchParam.IsLogical), userKey, sourceIPKey, GetField(SQLTemplateID, req.SearchParam.IsLogical), PSMKey}
	case shared.VeDBMySQL:
		fields = []string{GetField(dbKey, req.SearchParam.IsLogical), userKey, sourceIPKey, GetField(SQLTemplateID, req.SearchParam.IsLogical), PSMKey}
	case shared.ByteRDS:
		fields = []string{GetField(dbKey, req.SearchParam.IsLogical), userKey, sourceIPKey, SQLTemplateID, PSMKey}
	default:
		log.Warn(ctx, "unknown instance type")
		return "", consts.ErrorOf(model.ErrorCode_InstanceTypeNotSupport)
	}
	for _, group := range searchParam.GroupIgnored {
		switch group {
		case shared.User:
			fields = rmFields(fields, userKey)
		case shared.SourceIP:
			fields = rmFields(fields, sourceIPKey)
		case shared.PSMGroup:
			fields = rmFields(fields, PSMKey)
		case shared.TableGroup:
			fields = rmFields(fields, GetField(SqlTableKey, req.SearchParam.IsLogical))
		case shared.SqlMethodGroup:
			fields = rmFields(fields, SqlMethodKey)
		case shared.DBGroup:
			fields = rmFields(fields, dbKey)
		}
	}
	groupFields = strings.Join(fields, ",")
	// 分组条件
	groupCondition = fmt.Sprintf("group by %s", groupFields)

	query = fmt.Sprintf("* | select %s, ", groupFields)

	query += countField + ","

	query += strings.Join([]string{
		ArbitraryField(GetField(sqlTemplateKey, req.SearchParam.IsLogical)),
		ArbitraryField(GetSqlMethod(dataSourceType)),
		ArbitraryField(GetField(SqlTableKey, req.SearchParam.IsLogical)),
	}, ",") + ","

	// queryTime的统计信息
	query += strings.Join([]string{sumQueryTimeField, maxQueryTimeField, minQueryTimeField, averageQueryTimeField}, ",") + ","
	// lockTime的统计信息
	query += strings.Join([]string{sumLockTimeField, maxLockTimeField, minLockTimeField, averageLockTimeField}, ",") + ","
	// rowsSent的统计信息
	query += strings.Join([]string{sumRowsSentField, maxRowsSentField, minRowsSentField, averageRowsSentField}, ",") + ","
	// rowsExamined的统计信息
	query += strings.Join([]string{sumRowsExaminedField, maxRowsExaminedField, minRowsExaminedField, averageRowsExaminedField}, ",") + ","
	// firstAppearTime以及lastAppearTime的信息
	query += strings.Join([]string{minTimestampField, maxTimestampField}, ",")

	whereConditions, hasWhere = generateAggregateSlowLogSearchWhereConditions(searchParam)
	switch dataSourceType {
	case shared.MySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		} else {
			query += fmt.Sprintf(" where (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		}
	case shared.VeDBMySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s'", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s'", instanceKey, req.InstanceId)
		}
	case shared.Postgres:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s'", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s'", instanceKey, req.InstanceId)
		}
	case shared.ByteRDS:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s'", instanceIDKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s'", instanceIDKey, req.InstanceId)
		}
	}
	// 排除解析失败
	query += fmt.Sprintf(" and %s ", filterSqlParseFailed)

	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s ", filter)
	}

	sqlTypeFilter := filterSqlTypeCondition(GetSqlMethod(dataSourceType), req.SearchParam.SqlMethod)
	if sqlTypeFilter != "" {
		query += fmt.Sprintf(" and %s ", sqlTypeFilter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, dataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	query += " " + groupCondition + " "

	query += fmt.Sprintf(" order by %s %s", sortFn(req.OrderBy, req.SearchParam.IsLogical), req.SortBy.String())
	query += fmt.Sprintf(" offset %d ", req.Offset)
	query += fmt.Sprintf(" limit %d ", req.Limit)

	return query, nil
}

func (c *slowlogClient) GenAggrSlowLogCountQuery(ctx context.Context, req *shared.DescribeAggregateSlowLogsReq, internalUsers []string) (string, error) {
	var (
		query           string
		groupCondition  string
		groupFields     string
		hasWhere        bool
		whereConditions string
		fields          []string
	)
	dataSourceType := req.DataSourceType

	searchParam := req.SearchParam
	// 分组字段
	switch dataSourceType {
	case shared.Postgres:
		fields = []string{GetField(dbKey, req.SearchParam.IsLogical), userKey, sourceIPKey, GetField(SQLTemplateID, req.SearchParam.IsLogical)}
	case shared.MySQL:
		fields = []string{GetField(dbKey, req.SearchParam.IsLogical), userKey, sourceIPKey, GetField(SQLTemplateID, req.SearchParam.IsLogical), PSMKey}
	case shared.VeDBMySQL:
		fields = []string{GetField(dbKey, req.SearchParam.IsLogical), userKey, sourceIPKey, GetField(SQLTemplateID, req.SearchParam.IsLogical), PSMKey}
	case shared.ByteRDS:
		fields = []string{GetField(dbKey, req.SearchParam.IsLogical), userKey, sourceIPKey, SQLTemplateID, PSMKey}
	default:
		log.Warn(ctx, "unknown instance type")
		return "", consts.ErrorOf(model.ErrorCode_InstanceTypeNotSupport)
	}
	for _, group := range searchParam.GroupIgnored {
		switch group {
		case shared.User:
			fields = rmFields(fields, userKey)
		case shared.SourceIP:
			fields = rmFields(fields, sourceIPKey)
		case shared.PSMGroup:
			fields = rmFields(fields, PSMKey)
		case shared.TableGroup:
			fields = rmFields(fields, GetField(SqlTableKey, req.SearchParam.IsLogical))
		case shared.SqlMethodGroup:
			fields = rmFields(fields, SqlMethodKey)
		case shared.DBGroup:
			fields = rmFields(fields, dbKey)
		}
	}
	groupFields = strings.Join(fields, ",")
	// 分组条件
	groupCondition = fmt.Sprintf("group by %s", groupFields)

	query = fmt.Sprintf("* | SELECT %s FROM ( select 1 ", countField)

	whereConditions, hasWhere = generateAggregateSlowLogSearchWhereConditions(searchParam)
	switch dataSourceType {
	case shared.MySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		} else {
			query += fmt.Sprintf(" where (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		}
	case shared.VeDBMySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s'", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s'", instanceKey, req.InstanceId)
		}
	case shared.Postgres:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s'", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s'", instanceKey, req.InstanceId)
		}
	case shared.ByteRDS:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceIDKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		} else {
			query += fmt.Sprintf(" where (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceIDKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		}
	}
	// 排除解析失败
	query += fmt.Sprintf(" and %s ", filterSqlParseFailed)

	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s ", filter)
	}

	sqlTypeFilter := filterSqlTypeCondition(GetSqlMethod(dataSourceType), req.SearchParam.SqlMethod)
	if sqlTypeFilter != "" {
		query += fmt.Sprintf(" and %s ", sqlTypeFilter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, dataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}
	query += " " + groupCondition + " "
	query += " ) "
	return query, nil
}

func (c *slowlogClient) getInternalUsers(ctx context.Context, DSType string) []string {
	//var (
	//	dbInternalUsers map[string]string
	//)
	//internalUsers := make([]string, 0)
	//err := json.Unmarshal([]byte(c.conf.Get(ctx).DBInternalUsers), &dbInternalUsers)
	//if err != nil {
	//	log.Warn(ctx, "parse json str failed %+v", err)
	//
	//} else {
	//	internalUsers = strings.Split(dbInternalUsers[DSType], ",")
	//}
	return c.connection.InternalUsers
}

func (c *slowlogClient) CreateSlowLogsExportTask(ctx context.Context, req *shared.CreateSlowLogsExportTaskReq) (*shared.CreateSlowLogsExportTaskResp, error) {
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)
	dataSourceType := req.DataSourceType

	var (
		query           string
		hasWhere        bool
		whereConditions string
		groupFields     string
	)
	if dataSourceType == shared.Postgres {
		groupFields = strings.Join([]string{DateTimeKey, SQLType, dbKey, sqlTextKey, userKey, sourceIPKey, queryTimeKey, lockTimeKey, rowsSendKey, rowsExaminedKey, connectionIdKey}, ",")
	} else {
		groupFields = strings.Join([]string{DateTimeKey, dbKey, sqlTextKey, userKey, sourceIPKey, queryTimeKey, lockTimeKey, rowsSendKey, rowsExaminedKey, connectionIdKey}, ",")
	}
	query = fmt.Sprintf("* | select %s", groupFields)
	searchParam := req.SearchParam
	whereConditions, hasWhere = generateAggregateSlowLogSearchWhereConditions(searchParam)
	switch dataSourceType {
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		} else {
			query += fmt.Sprintf(" where (%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, req.InstanceId, clusterInstanceIDKey, req.InstanceId, clusterLogicInstanceIDKey, req.InstanceId, "Instance", req.InstanceId)
		}
	case shared.VeDBMySQL:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s' ", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s' ", instanceKey, req.InstanceId)
		}
	case shared.Postgres:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s' ", instanceKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s' ", instanceKey, req.InstanceId)
		}
	case shared.ByteRDS:
		if hasWhere {
			query += whereConditions
			query += fmt.Sprintf(" and %s='%s' ", instanceIDKey, req.InstanceId)
		} else {
			query += fmt.Sprintf(" where %s='%s' ", instanceIDKey, req.InstanceId)
		}
	}
	// 规避TLS bug
	query += fmt.Sprintf(" and %s ", whereConditionsMust)
	query += fmt.Sprintf(" and %s ", filterSqlParseFailed)
	internalUsers := c.getInternalUsers(ctx, dataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s ", filter)
	}
	// 仅支持PG
	switch dataSourceType {
	case shared.Postgres:
		sqlTypeFilter := filterSqlTypeCondition(SQLType, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		sqlTypeFilter := filterSqlTypeCondition(SqlMethodKey, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	case shared.VeDBMySQL:
		sqlTypeFilter := filterSqlTypeCondition(SqlMethodKey, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	case shared.ByteRDS:
		sqlTypeFilter := filterSqlTypeCondition(SqlMethodKey, req.SearchParam.SqlMethod)
		if sqlTypeFilter != "" {
			query += fmt.Sprintf(" and %s ", sqlTypeFilter)
		}
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, dataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	maxLimit := c.conf.Get(ctx).MaxSlowLogDownloadLimits
	query += fmt.Sprintf(" limit %d", maxLimit)
	log.Info(ctx, "Call TLS CreateDownloadTask query is %s", query)
	resp, err := c.realClient.CreateDownloadTask(&sdk.CreateDownloadTaskRequest{
		TaskName:    req.TaskName,
		TopicID:     c.connection.TopicId,
		StartTime:   req.StartTime,
		EndTime:     req.EndTime,
		DataFormat:  strings.ToLower(req.DataFormat),
		Compression: strings.ToLower(req.Compression),
		Query:       query,
		Limit:       int(req.Limit),
		Sort:        strings.ToLower(req.Sort.String()),
	})
	if err != nil {
		log.Warn(ctx, "CreateDownloadTask: Download slowLog from topic %s error: %v", c.connection.TopicId, err)
		return nil, err
	}
	res := &shared.CreateSlowLogsExportTaskResp{
		TaskId: resp.TaskId,
	}
	return res, nil
}

func (c *slowlogClient) DescribeSlowLogsExportTasks(ctx context.Context, req *shared.DescribeSlowLogsExportTasksReq) (*shared.DescribeSlowLogsExportTasksResp, error) {
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)
	rreq := &sdk.DescribeDownloadTasksRequest{
		TopicID:    c.connection.TopicId,
		PageNumber: utils.IntRef(int(req.PageNumber)),
		PageSize:   utils.IntRef(int(req.PageSize)),
	}
	ret := &shared.DescribeSlowLogsExportTasksResp{}
	// TODO 上线后仅保留新版查询实现逻辑
	if c.conf.Get(ctx).EnableSlowLogUpgradeSwitch {
		if req.GetKeyword() != "" {
			rreq.TaskName = utils.StringRef(req.GetKeyword() + "-dbw") // 新版默认加个后缀
		}
		resp, err := c.realClient.DescribeDownloadTasks(rreq)
		if err != nil {
			log.Warn(ctx, "DescribeSlowLogsExportTasks: search log task from topic %s error: %v", c.connection.TopicId, err)
			return nil, err
		}
		for _, detail := range resp.Tasks {
			taskInfo := &shared.TaskInfo{
				TaskId:      detail.TaskId,
				TaskName:    detail.TaskName,
				TopicId:     detail.TopicId,
				Query:       detail.Query,
				DataFormat:  detail.DataFormat,
				CreateTime:  detail.CreateTime,
				StartTime:   detail.StartTime,
				EndTime:     detail.EndTime,
				LogSize:     detail.LogSize,
				LogCount:    detail.LogCount,
				TaskStatus:  conv.ToSharedDownloadTaskStatusType(detail.TaskStatus),
				Compression: detail.Compression,
			}
			ret.TaskInfos = append(ret.TaskInfos, taskInfo)
		}
		ret.Total = int32(resp.Total)
	} else {
		if req.GetKeyword() != "" {
			rreq.TaskName = utils.StringRef(req.GetKeyword())
		}
		resp, err := c.realClient.DescribeDownloadTasks(rreq)
		if err != nil {
			log.Warn(ctx, "DescribeSlowLogsExportTasks: search log task from topic %s error: %v", c.connection.TopicId, err)
			return nil, err
		}
		for _, detail := range resp.Tasks {
			taskInfo := &shared.TaskInfo{
				TaskId:      detail.TaskId,
				TaskName:    detail.TaskName,
				TopicId:     detail.TopicId,
				Query:       detail.Query,
				DataFormat:  detail.DataFormat,
				CreateTime:  detail.CreateTime,
				StartTime:   detail.StartTime,
				EndTime:     detail.EndTime,
				LogSize:     detail.LogSize,
				LogCount:    detail.LogCount,
				TaskStatus:  conv.ToSharedDownloadTaskStatusType(detail.TaskStatus),
				Compression: detail.Compression,
			}
			ret.TaskInfos = append(ret.TaskInfos, taskInfo)
			//if matched := strings.HasSuffix(detail.TaskName, suffix); matched {
			//	taskInfo := &shared.TaskInfo{
			//		TaskId:      detail.TaskId,
			//		TaskName:    detail.TaskName,
			//		TopicId:     detail.TopicId,
			//		Query:       detail.Query,
			//		DataFormat:  detail.DataFormat,
			//		CreateTime:  detail.CreateTime,
			//		StartTime:   detail.StartTime,
			//		EndTime:     detail.EndTime,
			//		LogSize:     detail.LogSize,
			//		LogCount:    detail.LogCount,
			//		TaskStatus:  conv.ToSharedDownloadTaskStatusType(detail.TaskStatus),
			//		Compression: detail.Compression,
			//	}
			//	ret.TaskInfos = append(ret.TaskInfos, taskInfo)
			//}
		}
		ret.Total = int32(resp.Total)
	}
	return ret, nil
}

func (c *slowlogClient) DescribeLogsDownloadUrl(ctx context.Context, req *shared.DescribeLogsDownloadUrlReq) (*shared.DescribeLogsDownloadUrlResp, error) {
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)
	resp, err := c.realClient.DescribeDownloadUrl(&sdk.DescribeDownloadUrlRequest{
		TaskId: req.TaskId,
	})
	log.Info(ctx, "Call TLS DescribeDownloadUrl resp is %s", resp)
	if err != nil {
		log.Warn(ctx, "DescribeLogsDownloadUrl: get download URL from TaskId %s error: %v", req.TaskId, err)
		return nil, err
	}
	ret := &shared.DescribeLogsDownloadUrlResp{
		FileUrl: resp.DownloadUrl,
	}
	return ret, nil
}

func (c *slowlogClient) DescribeSlowLogs(ctx context.Context, req *shared.DescribeSlowLogsReq) (*shared.DescribeSlowLogsResp, error) {
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)
	query := `* | select `

	searchParam := req.SearchParam
	if searchParam.SQLTemplate != "" {
		newStr := fmt.Sprintf(`"%s"`, searchParam.SQLTemplate)
		query = strings.Replace(query, "*", newStr, 1)
	}

	selectCountField := countField
	countQuery := fmt.Sprintf("%s%s", query, selectCountField)

	selectField := []string{
		GetField(sqlTemplateKey, req.SearchParam.IsLogical),
		timestampKey,
		sqlTextKey,
		userKey,
		GetField(dbKey, req.SearchParam.IsLogical),
		sourceIPKey,
		connectionIdKey,
		queryTimeKey,
		lockTimeKey,
		rowsExaminedKey,
		rowsSendKey,
		//contextDBKey,
	}
	query += strings.Join(selectField, ",")
	whereConditions, hasWhere := generateSlowLogSearchWhereCondition(req.InstanceId, searchParam, req.DataSourceType)
	if hasWhere {
		query += whereConditions
		countQuery += whereConditions
	}
	// 规避TLS bug
	query += fmt.Sprintf(" and %s ", whereConditionsMust)
	query += fmt.Sprintf(" and %s ", filterSqlParseFailed)
	countQuery += fmt.Sprintf(" and %s ", whereConditionsMust)
	countQuery += fmt.Sprintf(" and %s ", filterSqlParseFailed)
	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s ", filter)
		countQuery += fmt.Sprintf(" and %s ", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
		countQuery += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	query += generateSortParamStringForSlowLog(req.GetSortBy(), req.GetOrderBy())
	log.Info(ctx, "DescribeSlowLogs call TLS SearchLogs query content is %s", countQuery)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     countQuery,
	})
	if err != nil {
		log.Warn(ctx, "DescribeSlowLogs: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, countQuery)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data := resp.AnalysisResult.Data
	if len(data) != 1 {
		err = fmt.Errorf("DescribeSlowLogs: query total expect 1, got %d", len(data))
		log.Warn(ctx, "%s", err.Error())
		return nil, err
	}

	total, err := parseInt64(data[0][countKey])
	if err != nil {
		log.Warn(ctx, "DescribeSlowLogs: convert total result to int64 error: %s", err.Error())
		return nil, err
	}

	//query += " limit 5000"
	query += fmt.Sprintf(" offset %d ", req.Offset)
	query += fmt.Sprintf(" limit %d ", req.Limit)
	log.Info(ctx, "DescribeSlowLogs call TLS SearchLogs query content is %s", query)
	resp, err = c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "DescribeSlowLogs: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	slowLogList := make([]*shared.SlowLog, len(resp.AnalysisResult.Data))
	data = resp.AnalysisResult.Data
	for i := 0; i < len(resp.AnalysisResult.Data); i++ {
		slowLogList[i] = fetchSlowLog(data[i])
	}

	return &shared.DescribeSlowLogsResp{
		SlowLogs: slowLogList,
		Total:    int32(total),
	}, nil
}

func (c *slowlogClient) DescribeSlowLogsV2(ctx context.Context, req *shared.DescribeSlowLogsReq) (*shared.DescribeSlowLogsResp, error) {
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)

	query := GenerateSlowLogSearchWhereCondition(req, req.DataSourceType, c.getInternalUsers(ctx, req.DataSourceType.String()))

	log.Info(ctx, "DescribeSlowLogs call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		CommonRequest: sdk.CommonRequest{},
		TopicID:       c.connection.TopicId,
		Query:         query,
		StartTime:     req.StartTime,
		EndTime:       req.EndTime,
		Limit:         int(req.Limit),
		Context:       req.Context,
	})
	if err != nil {
		log.Warn(ctx, "DescribeSlowLogs: search log from topic %s error: %s, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	slowLogList := make([]*shared.SlowLog, len(resp.Logs))
	data := resp.Logs
	for i := 0; i < len(data); i++ {
		slowLogList[i] = fetchSlowLog(data[i])
	}

	return &shared.DescribeSlowLogsResp{
		SlowLogs: slowLogList,
		Total:    int32(resp.HitCount),
		ListOver: resp.ListOver,
		Context:  resp.Context,
	}, nil
}

func (c *slowlogClient) DescribeCandidate(ctx context.Context, req *shared.DescribeSlowLogsReq, cdField model.SlowLogCandidateField) (*model.DescribeCandidateResp, error) {
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)
	candidateField := convFieldKey(cdField, req.SearchParam.IsLogical)
	query := fmt.Sprintf(` * | select DISTINCT(%s)`, candidateField)

	searchParam := req.SearchParam
	if searchParam.SQLTemplate != "" {
		newStr := fmt.Sprintf(`"%s"`, searchParam.SQLTemplate)
		query = strings.Replace(query, "*", newStr, 1)
	}

	whereConditions, hasWhere := generateSlowLogSearchWhereCondition(req.InstanceId, searchParam, req.DataSourceType)
	if hasWhere {
		query += whereConditions
	}
	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	query += " limit 2000"
	log.Info(ctx, "DescribeUsers call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "DescribeUsers: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	data := resp.AnalysisResult.Data
	candidates := make([]string, 0, len(data))
	for _, value := range data {
		if field, ok := value[candidateField]; ok {
			if field == nil {
				continue
			}
			candidates = append(candidates, field.(string))
		}
	}

	return &model.DescribeCandidateResp{
		Candidates: candidates,
	}, nil
}

func convFieldKey(field model.SlowLogCandidateField, isLogical bool) string {
	switch field {
	case model.SlowLogCandidateField_User:
		return userKey
	case model.SlowLogCandidateField_SourceIP:
		return sourceIPKey
	case model.SlowLogCandidateField_DB:
		return GetField(dbKey, isLogical)
	case model.SlowLogCandidateField_Table:
		return GetField(SqlTableKey, isLogical)
	case model.SlowLogCandidateField_Method:
		return SQLType
	case model.SlowLogCandidateField_PSM:
		return PSMKey
	default:
		return ""
	}
}

func (c *slowlogClient) DescribeExampleSQL(ctx context.Context, req *shared.DescribeExampleSQLReq) (*shared.DescribeExampleSQLResp, error) {
	req.DataSourceType = ConvShardingDataSourceType(req.DataSourceType, req.InstanceId)
	query := fmt.Sprintf(` * | select %s,%s`, sqlTextKey, queryTimeKey)

	searchParam := req.SearchParam
	if searchParam.SQLTemplate != "" {
		newStr := fmt.Sprintf(`"%s"`, searchParam.SQLTemplate)
		query = strings.Replace(query, "*", newStr, 1)
	}

	whereConditions, hasWhere := generateSlowLogSearchWhereCondition(req.InstanceId, searchParam, req.DataSourceType)
	if hasWhere {
		query += whereConditions
	}

	internalUsers := c.getInternalUsers(ctx, req.DataSourceType.String())
	filter := filterInternalUserCondition(internalUsers)
	if filter != "" {
		query += fmt.Sprintf(" and %s", filter)
	}

	nodeIdsFilter := generateNodeIdFilter(req.NodeId, req.DataSourceType, req.InstanceId)
	if nodeIdsFilter != "" {
		query += fmt.Sprintf(" and %s ", nodeIdsFilter)
	}

	query += fmt.Sprintf(" limit 1 ")
	log.Info(ctx, "DescribeExampleSQL call TLS SearchLogs query content is %s", query)
	resp, err := c.realClient.SearchLogsV2(&sdk.SearchLogsRequest{
		TopicID:   c.connection.TopicId,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Query:     query,
	})
	if err != nil {
		log.Warn(ctx, "DescribeExampleSQL: search log from topic %s error: %v, SQL: %s", c.connection.TopicId, err, query)
		return nil, consts.ErrorOf(model.ErrorCode_CallTLSAPIFailed)
	}

	if len(resp.AnalysisResult.Data) == 0 {
		err = fmt.Errorf("DescribeExampleSQL: get no exampleSQL for sql template %s", req.SearchParam.SQLTemplate)
		return &shared.DescribeExampleSQLResp{}, err
	}

	exampleSQL := resp.AnalysisResult.Data[0][sqlTextKey].(string)
	return &shared.DescribeExampleSQLResp{
		SQLText: exampleSQL,
	}, nil
}

func (c *slowlogClient) GenAggrTotalStats(ctx context.Context, req *shared.DescribeAggregateSlowLogsReq) string {
	//	totalLockTime totalQueryTime totalRowsExamined totalRowsSent totalExecuteCount
	query := BaseInstanceFilter(req.GetInstanceId(), req.GetNodeId(), req.GetDataSourceType())
	return fmt.Sprintf("%s | select %s, %s, %s, %s, %s  limit 1", query, sumLockTimeField, sumQueryTimeField, sumRowsExaminedField, sumRowsSentField, countField)
}

func BaseInstanceFilter(instanceId string, nodeId string, datasourceType shared.DataSourceType) string {
	var whereConditions []string
	switch datasourceType {
	case shared.MySQL:
		whereConditions = append(whereConditions, fmt.Sprintf("( %s: \"%s\" )", clusterLogicInstanceIDKey, instanceId))
	case shared.VeDBMySQL:
		whereConditions = append(whereConditions, fmt.Sprintf("( %s: \"%s\" ) ", instanceKey, instanceId))
	case shared.Postgres:
		whereConditions = append(whereConditions, fmt.Sprintf("( %s: \"%s\" ) ", instanceKey, instanceId))
	case shared.ByteRDS:
		whereConditions = append(whereConditions, fmt.Sprintf("( %s: \"%s\" ) ", instanceIDKey, instanceId))
	}
	if nodeId != "" {
		whereConditions = extractList([]string{nodeId}, nodeIdKey, whereConditions)
	}
	query := strings.Join(whereConditions, " AND ")
	return query
}

func rmFields(arr []string, strToRemove string) []string {
	var result []string
	for _, str := range arr {
		if str != strToRemove {
			result = append(result, str)
		}
	}
	return result
}

func ArbitraryField(s string) string {
	return fmt.Sprintf("ARBITRARY(%s) as %s", s, s)
}

func GetSqlMethod(dsType shared.DataSourceType) string {
	switch dsType {
	case shared.Postgres:
		return SQLType
	default:
		return SqlMethodKey
	}
}

func sortFn(orderBy shared.OrderByForAggregateSlowLog, isLogical bool) string {
	switch orderBy {
	case shared.ExecuteCount:
		return countKey
	case shared.TotalQueryTime:
		return sumQueryTimeKey
	case shared.MaxQueryTime:
		return maxQueryTimeKey
	case shared.MaxLockTime:
		return maxLockTimeKey
	case shared.MaxRowsSent:
		return maxRowsSentKey
	case shared.MaxRowsExamined:
		return maxRowsExaminedKey
	case shared.AverageQueryTime:
		return averageQueryTimeKey
	case shared.AverageLockTime:
		return averageLockTimeKey
	case shared.AverageRowsSent:
		return averageRowsSentKey
	case shared.AverageRowsExamined:
		return averageRowsExaminedKey
	case shared.SQLTemplate:
		return GetField(sqlTemplateKey, isLogical)
	}
	return " "
}

func ConvShardingDataSourceType(dsType shared.DataSourceType, instanceId string) shared.DataSourceType {
	if dsType == shared.MySQLSharding {
		if strings.Contains(instanceId, "rds") || strings.Contains(instanceId, "mysql") {
			return shared.MySQL
		} else if strings.Contains(instanceId, "vedbm") {
			return shared.VeDBMySQL
		}
	}
	if dsType == shared.MetaRDS {
		return shared.MySQL
	}
	if dsType == shared.MetaMySQL {
		return shared.MySQL
	}
	return dsType
}

func GenerateSlowLogSearchWhereCondition(req *shared.DescribeSlowLogsReq, dsType shared.DataSourceType, innerUser []string) string {
	switch dsType {
	case shared.MySQL:
		return MysqlGenWhere(req, innerUser)
	case shared.VeDBMySQL:
		return MysqlGenWhere(req, innerUser)
	case shared.Postgres:
		return MysqlGenWhere(req, innerUser)
	case shared.ByteRDS:
		return MysqlGenWhere(req, innerUser)
	default:
		return " * "
	}
}
func MysqlGenWhere(req *shared.DescribeSlowLogsReq, innerUser []string) string {
	var whereConditions []string
	instanceID := req.GetInstanceId()
	switch req.DataSourceType {
	case shared.MySQL:
		whereConditions = append(whereConditions, fmt.Sprintf("( %s: \"%s\" )", clusterLogicInstanceIDKey, instanceID))
	case shared.VeDBMySQL:
		whereConditions = append(whereConditions, fmt.Sprintf(" ( %s: \"%s\" ) ", instanceKey, instanceID))
	case shared.Postgres:
		whereConditions = append(whereConditions, fmt.Sprintf(" ( %s: \"%s\" ) ", instanceKey, instanceID))
	case shared.ByteRDS:
		whereConditions = append(whereConditions, fmt.Sprintf(" ( %s: \"%s\" ) ", instanceIDKey, instanceID))
	}

	whereConditions = extractList(req.SearchParam.DBs, GetField(dbKey, req.SearchParam.IsLogical), whereConditions)
	whereConditions = extractList(req.SearchParam.Users, userKey, whereConditions)
	whereConditions = extractList(req.SearchParam.SourceIPs, sourceIPKey, whereConditions)
	if req.NodeId != "" {
		whereConditions = extractList([]string{req.NodeId}, nodeIdKey, whereConditions)
	}
	//whereConditions = extractList(append(ConvSmToEx(req.SearchParam.SqlMethods), req.SearchParam.SqlCommands...), " sql_type", whereConditions)
	if req.SearchParam.SQLTemplate != "" {
		whereConditions = extractListFuzzyQuery([]string{req.SearchParam.SQLTemplate}, GetField(sqlTemplateKey, req.SearchParam.IsLogical), whereConditions)
	}

	var sqlTemplateIDs []string
	if req.SearchParam.SQLTemplateID != "" {
		sqlTemplateIDs = append(sqlTemplateIDs, req.SearchParam.SQLTemplateID)
	}
	whereConditions = extractList(sqlTemplateIDs, GetField(SQLTemplateID, req.SearchParam.IsLogical), whereConditions)
	//var sqlFingerprints []string
	//whereConditions = extractList(sqlFingerprints, " sql_fingerprint", whereConditions)
	if req.SearchParam.MinQueryTime > 0 {
		whereConditions = append(whereConditions, queryTimeKey+" :>="+strconv.FormatInt(int64(req.SearchParam.MinQueryTime), 10))
	}
	if req.SearchParam.MaxQueryTime > 0 {
		whereConditions = append(whereConditions, queryTimeKey+" :<="+strconv.FormatInt(int64(req.SearchParam.MaxQueryTime), 10))
	}
	sort.Strings(whereConditions)
	query := strings.Join(whereConditions, " AND ")
	if strings.TrimSpace(query) == "" {
		query = " * "
	}

	return query
}

func extractList(searchParams []string, f string, whereConditions []string) []string {
	sort.Strings(searchParams)
	if len(searchParams) > 0 {
		var x []string
		for i := 0; i < len(searchParams); i++ {
			x = append(x, fmt.Sprintf(" %s: \"%s\"", f, searchParams[i]))
		}
		s := strings.Join(x, " OR ")
		s = " ( " + s + " ) "
		return append(whereConditions, s)
	} else {
		return whereConditions
	}
}

// extractListFuzzyQuery 日志服务支持的模糊查询，模糊查询的字段需要用单引号括起来
func extractListFuzzyQuery(searchParams []string, f string, whereConditions []string) []string {
	sort.Strings(searchParams)
	if len(searchParams) > 0 {
		var x []string
		for i := 0; i < len(searchParams); i++ {
			x = append(x, fmt.Sprintf(" %s: '%s'", f, searchParams[i]))
		}
		s := strings.Join(x, " OR ")
		s = " ( " + s + " ) "
		return append(whereConditions, s)
	} else {
		return whereConditions
	}
}

package tls

import (
	"bytes"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"embed"
	"encoding/json"
	"fmt"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"html/template"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/ds-lib/common/log"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
)

func decimalFn(value float64) float64 {
	num, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", value), 64)
	return num
}

func generateNodeIdFilter(nodeId string, InstanceType shared.DataSourceType, InstanceId string) string {
	if nodeId == "" {
		switch InstanceType {
		case shared.MySQLSharding:
			fallthrough
		case shared.MetaRDS:
			fallthrough
		case shared.MetaMySQL:
			fallthrough
		case shared.MySQL:
			//return fmt.Sprintf("(%s IS NOT NULL or  %s IS NULL or %s='')", roleKey, nodeIdKey, nodeIdKey)
			return fmt.Sprintf("regexp_replace(__pod_name__, '^mysql-r') NOT LIKE %s", `'%-r%'`)
		case shared.VeDBMySQL:
			return ""
		case shared.Postgres:
			return fmt.Sprintf("%s='%s'", nodeIdKey, InstanceId)
		case shared.ByteRDS:
			return ""
		default:
			return ""
		}

	}
	switch InstanceType {
	case shared.MySQLSharding:
		fallthrough
	case shared.MetaRDS:
		fallthrough
	case shared.MetaMySQL:
		fallthrough
	case shared.MySQL:
		podNameSlice := strings.Split(nodeId, "-")
		nodeInstanceId := strings.Join(podNameSlice[0:len(podNameSlice)-1], "-")
		//return fmt.Sprintf("(%s='%s' or %s='%s' or %s='%s')", podNameKey, nodeId, nodeIdKey, nodeInstanceId, "InstanceNodeID", nodeInstanceId)
		return fmt.Sprintf("(regexp_replace(__pod_name__, '-0$') = '%s' or %s='%s' ) ", nodeInstanceId, nodeIdKey, nodeInstanceId)
	case shared.VeDBMySQL:
		return fmt.Sprintf("(%s='%s' or %s='%s')", podNameKey, nodeId, nodeIdKey, nodeId)
	case shared.Postgres:
		return fmt.Sprintf("%s='%s'", nodeIdKey, nodeId)
	case shared.ByteRDS:
		return fmt.Sprintf("%s='%s'", nodeIdKey, nodeId)
	default:
		return fmt.Sprintf("(%s='%s' or %s='%s')", podNameKey, nodeId, nodeIdKey, nodeId)
	}
}

func generateSlowLogSearchWhereCondition(instanceID string, searchParam *shared.SlowLogSearchParam, instanceType shared.DataSourceType) (string, bool) {
	if searchParam == nil {
		return "", false
	}

	hasWhere := false
	whereConditions := " where "
	for index, value := range searchParam.Users {
		if index == 0 {
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}

		whereConditions += fmt.Sprintf("%s='%s'", userKey, value)

		if index == len(searchParam.Users)-1 {
			whereConditions += ") "
		}
	}

	for index, value := range searchParam.SourceIPs {
		if index == 0 {
			if hasWhere {
				whereConditions += " and "
			}
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}

		whereConditions += fmt.Sprintf("%s='%s'", sourceIPKey, value)

		if index == len(searchParam.SourceIPs)-1 {
			whereConditions += ") "
		}
	}

	for index, value := range searchParam.DBs {
		if index == 0 {
			if hasWhere {
				whereConditions += " and "
			}
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}

		whereConditions += fmt.Sprintf("%s='%s'", GetField(dbKey, searchParam.IsLogical), value)

		if index == len(searchParam.DBs)-1 {
			whereConditions += ") "
		}
	}

	if searchParam.MinQueryTime != 0.0 {
		if hasWhere {
			whereConditions += " and "
		}
		hasWhere = true
		whereConditions += fmt.Sprintf("%s>%f", queryTimeKey, searchParam.MinQueryTime)
	}

	if searchParam.MaxQueryTime != 0.0 {
		if hasWhere {
			whereConditions += " and "
		}
		hasWhere = true
		whereConditions += fmt.Sprintf("%s<%f", queryTimeKey, searchParam.MaxQueryTime)
	}

	if searchParam.SQLTemplateID != "" {
		if hasWhere {
			whereConditions += " and "
		}
		hasWhere = true
		whereConditions += fmt.Sprintf("%s='%s'", GetField(SQLTemplateID, searchParam.IsLogical), searchParam.SQLTemplateID)
	}

	if instanceID != "" {
		if hasWhere {
			whereConditions += " and "
		}

		hasWhere = true
		switch instanceType {
		case shared.MySQLSharding:
			fallthrough
		case shared.MetaRDS:
			fallthrough
		case shared.MetaMySQL:
			fallthrough
		case shared.MySQL:
			whereConditions += fmt.Sprintf("(%s='%s' or %s='%s' or %s='%s' or %s='%s')", instanceKey, instanceID, clusterInstanceIDKey, instanceID, clusterLogicInstanceIDKey, instanceID, "Instance", instanceID)
		case shared.VeDBMySQL:
			whereConditions += fmt.Sprintf("%s='%s' ", instanceKey, instanceID)
		case shared.Postgres:
			whereConditions += fmt.Sprintf("%s='%s' ", instanceKey, instanceID)
		case shared.ByteRDS:
			whereConditions += fmt.Sprintf("%s='%s' ", instanceIDKey, instanceID)
		}
	}

	return whereConditions, hasWhere
}

func generateAggregateSlowLogSearchWhereConditions(searchParam *shared.AggregateSlowLogSearchParam) (string, bool) {
	hasWhere := false
	// where条件 先where再group by 减少运算量
	whereConditions := " where "

	// user条件
	for index, value := range searchParam.Users {
		if index == 0 {
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}

		whereConditions += fmt.Sprintf("%s='%s'", userKey, value)

		if index == len(searchParam.Users)-1 {
			whereConditions += ") "
		}
	}

	// db条件
	for index, value := range searchParam.DBs {
		if index == 0 {
			if hasWhere {
				whereConditions += " and "
			}
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}

		whereConditions += fmt.Sprintf("%s='%s'", GetField(dbKey, searchParam.IsLogical), value)

		if index == len(searchParam.DBs)-1 {
			whereConditions += ") "
		}
	}

	// sqlTemplateIds条件
	for index, value := range searchParam.SQLTemplateIds {
		if index == 0 {
			if hasWhere {
				whereConditions += " and "
			}
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}
		whereConditions += fmt.Sprintf("%s='%s'", GetField(SQLTemplateID, searchParam.IsLogical), value)

		if index == len(searchParam.SQLTemplateIds)-1 {
			whereConditions += ") "
		}
	}

	// sourceIP条件
	for index, value := range searchParam.SourceIPs {
		if index == 0 {
			if hasWhere {
				whereConditions += " and "
			}
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}

		whereConditions += fmt.Sprintf("%s='%s'", sourceIPKey, value)

		if index == len(searchParam.SourceIPs)-1 {
			whereConditions += ") "
		}
	}
	//for index, value := range searchParam.SqlMethod {
	//	if index == 0 {
	//		if hasWhere {
	//			whereConditions += " and "
	//		}
	//		whereConditions += "("
	//		hasWhere = true
	//	}
	//
	//	if index != 0 {
	//		whereConditions += " or "
	//	}
	//
	//	whereConditions += fmt.Sprintf("%s='%s'", SqlMethodKey, value)
	//
	//	if index == len(searchParam.SqlMethod)-1 {
	//		whereConditions += ") "
	//	}
	//}
	for index, value := range searchParam.Tables {
		if index == 0 {
			if hasWhere {
				whereConditions += " and "
			}
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}

		whereConditions += fmt.Sprintf("%s='%s'", GetField(SqlTableKey, searchParam.IsLogical), value)

		if index == len(searchParam.Tables)-1 {
			whereConditions += ") "
		}
	}
	for index, value := range searchParam.PSMs {
		if index == 0 {
			if hasWhere {
				whereConditions += " and "
			}
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}

		whereConditions += fmt.Sprintf("%s='%s'", PSMKey, value)

		if index == len(searchParam.PSMs)-1 {
			whereConditions += ") "
		}
	}

	// keyword条件
	for index, value := range searchParam.Keywords {
		if index == 0 {
			if hasWhere {
				whereConditions += " and "
			}
			whereConditions += "("
			hasWhere = true
		}

		if index != 0 {
			whereConditions += " or "
		}
		whereConditions += fmt.Sprintf(" %s like '%%%s%%'", GetField(sqlTemplateKey, searchParam.IsLogical), value)

		if index == len(searchParam.Keywords)-1 {
			whereConditions += ") "
		}
	}

	// 执行时间条件
	if searchParam != nil && searchParam.MinQueryTime >= 0.0 && searchParam.MaxQueryTime > 0.0 {
		if hasWhere {
			whereConditions += " and "
		}
		hasWhere = true
		whereConditions += "("
		whereConditions += fmt.Sprintf("%s>=%f", queryTimeKey, searchParam.MinQueryTime)
		whereConditions += " and "
		whereConditions += fmt.Sprintf("%s<=%f", queryTimeKey, searchParam.MaxQueryTime)
		whereConditions += ")"
	}
	return whereConditions, hasWhere
}

func fetchSlowLog(data map[string]interface{}) *shared.SlowLog {
	slowLog := &shared.SlowLog{}
	if timestampStr, ok := data[timestampKey]; ok && timestampStr != nil {
		timestampC, _ := parseString(timestampStr)
		timestamp, _ := strconv.ParseInt(timestampC, 10, 64)
		slowLog.Timestamp = timestamp
	}

	if sqlText, ok := data[sqlTextKey]; ok && sqlText != nil {
		slowLog.SQLText = sqlText.(string)
	}

	if db, ok := data[dbKey]; ok && db != nil {
		slowLog.DB = db.(string)
	}

	if user, ok := data[userKey]; ok && user != nil {
		slowLog.User = user.(string)
	}

	if sourceIP, ok := data[sourceIPKey]; ok && sourceIP != nil {
		slowLog.SourceIP = sourceIP.(string)
	}

	if queryTimeStr, ok := data[queryTimeKey]; ok && queryTimeStr != nil {
		queryTime, _ := strconv.ParseFloat(queryTimeStr.(string), 64)
		slowLog.QueryTime = queryTime
	}

	if lockTimeStr, ok := data[lockTimeKey]; ok && lockTimeStr != nil {
		lockTime, _ := strconv.ParseFloat(lockTimeStr.(string), 64)
		slowLog.LockTime = lockTime
	}

	if rowsExaminedStr, ok := data[rowsExaminedKey]; ok && rowsExaminedStr != nil {
		rowsExamined, _ := strconv.ParseInt(rowsExaminedStr.(string), 10, 64)
		slowLog.RowsExamined = rowsExamined
	}

	if rowsSentStr, ok := data[rowsSendKey]; ok && rowsSentStr != nil {
		rowsSent, _ := strconv.ParseInt(rowsSentStr.(string), 10, 64)
		slowLog.RowsSent = rowsSent
	}

	if connectionIdStr, ok := data[connectionIdKey]; ok && connectionIdStr != nil {
		connectionId, _ := strconv.ParseInt(connectionIdStr.(string), 10, 64)
		slowLog.ConnectionId = connectionId
	}

	if sqlTemplate, ok := data[sqlTemplateKey]; ok && sqlTemplate != nil {
		slowLog.SQLTemplate = sqlTemplate.(string)
	}
	//if _, ok := data[SqlTableKey]; ok {
	//}
	//
	//if _, ok := data[SQLTemplateID]; ok {
	//}

	if sqlTemplate, ok := data[logicalSqlTemplateKey]; ok && sqlTemplate != nil {
		slowLog.SQLTemplate = sqlTemplate.(string)
	}

	if db, ok := data[logicalDBNamesKey]; ok && db != nil {
		slowLog.DB = db.(string)
	}

	//if _, ok := data[logicalTablesKey]; ok {
	//}
	//if _, ok := data[logicalSqlFingerprintKey]; ok {
	//}
	if contextDB, ok := data[contextDBKey]; ok && contextDB != nil {
		slowLog.ContextDB = contextDB.(string)
	}

	return slowLog
}

func anyToFloat64(v interface{}) float64 {
	var f float64
	switch v1 := v.(type) {
	case uint:
		f = float64(v1)
	case uint64:
		f = float64(v1)
	case *uint64:
		f = float64(*v1)
	case float64:
		f = v1
	case *float64:
		f = *v1
	default:
		fmt.Fprintf(os.Stderr, "unknown type: %T", v)
	}
	return f
}

var funcMap = template.FuncMap{
	"percent": func(a, b interface{}) float64 {
		ta := anyToFloat64(a)
		tb := anyToFloat64(b)
		return ta / tb * 100
	},
	"per": func(a, b interface{}) float64 {
		ta := anyToFloat64(a)
		tb := anyToFloat64(b)
		return ta / tb
	},
	"rank": func(a int) int {
		return a + 1
	},
	"shortTime": func(v interface{}) string {
		var format string
		f := anyToFloat64(v)
		if f < 0.000000001 {
			format = "%.0f"
		} else if f < 0.000001 {
			f = f * 1000000000
			format = "%.1fns"
		} else if f < 0.001 {
			f = f * 1000000
			format = "%.1fus"
		} else if f < 1 {
			f = f * 1000
			format = "%.1fms"
		} else {
			format = "%.2fs"
		}
		return fmt.Sprintf(format, f)
	},
	"shortByteInt": func(v interface{}) string {
		var format string
		f := anyToFloat64(v)
		if f >= 1024*1024*1024 {
			f = f / (1024 * 1024 * 1024)
			format = "%.0fG"
		} else if f >= 1024*1024 {
			f = f / (1024 * 1024)
			format = "%.0fM"
		} else if f >= 1024 {
			f = f / 1024
			format = "%.0fk"
		} else {
			format = "%.0f"
		}
		return fmt.Sprintf(format, f)
	},
	"shortByte": func(v interface{}) string {
		var format string
		f := anyToFloat64(v)
		if f >= 1024*1024*1024 {
			f = f / (1024 * 1024 * 1024)
			format = "%.2fG"
		} else if f >= 1024*1024 {
			f = f / (1024 * 1024)
			format = "%.2fM"
		} else if f >= 1024 {
			f = f / 1024
			format = "%.2fk"
		} else if f == 0 {
			format = "%.0f"
		} else {
			format = "%.2f"
		}
		return fmt.Sprintf(format, f)
	},
	"shortInt": func(v interface{}) string {
		var format string
		f := anyToFloat64(v)
		if f >= 1_000_000_000 {
			f = f / 1_000_000_000
			format = "%.2fG"
		} else if f >= 1_000_000 {
			f = f / 1_000_000
			format = "%.2fM"
		} else if f >= 1_000 {
			f = f / 1_000
			format = "%.2fk"
		} else {
			format = "%.0f"
		}
		return fmt.Sprintf(format, f)
	},
	"short": func(v interface{}) string {
		var format string
		f := anyToFloat64(v)
		if f >= 1_000_000_000 {
			f = f / 1_000_000_000
			format = "%.2fG"
		} else if f >= 1_000_000 {
			f = f / 1_000_000
			format = "%.2fM"
		} else if f >= 1_000 {
			f = f / 1_000
			format = "%.2fk"
		} else if f == 0 {
			format = "%.0f"
		} else {
			format = "%.2f"
		}
		return fmt.Sprintf(format, f)
	},
}

//go:embed templates
var fs embed.FS

func fetchPTAnalysisResult(ctx context.Context, aggregateSlowLog *shared.AggregateSlowLog) string {
	var buff bytes.Buffer
	temp, err := template.New("").Funcs(funcMap).ParseFS(fs, "templates/report.tmpl")
	if err != nil {
		log.Warn(ctx, "parseFS error: %s", err.Error())
		return ""
	}

	err = temp.ExecuteTemplate(&buff, "report.tmpl", aggregateSlowLog)
	if err != nil {
		log.Warn(ctx, "executeTemplate error: %s", err.Error())
		return ""
	}
	return buff.String()
}

func filterInternalUserCondition(internalUsers []string) string {
	if len(internalUsers) == 0 || (len(internalUsers) == 1 && internalUsers[0] == "") { // 兼容internalUsers = []
		return ""
	}

	var wrappedInternalUsers []string
	for _, user := range internalUsers {
		wrappedInternalUser := fmt.Sprintf("'%s'", user)
		wrappedInternalUsers = append(wrappedInternalUsers, wrappedInternalUser)
	}

	condition := userKey + " NOT IN ("
	condition += strings.Join(wrappedInternalUsers, ",")
	condition += ")"
	return condition
}
func filterNonActiveState(StateList []string) string {
	if len(StateList) == 0 || (len(StateList) == 1 && StateList[0] == "") {
		return ""
	}

	var wrappedNonActiveStateList []string
	for _, state := range StateList {
		wrappedNonActiveState := fmt.Sprintf("'%s'", state)
		wrappedNonActiveStateList = append(wrappedNonActiveStateList, wrappedNonActiveState)
	}

	condition := stateKey + " NOT IN ("
	condition += strings.Join(wrappedNonActiveStateList, ",")
	condition += ")"
	return condition
}

func filterNodeIdCondition(nodeIds []string) string {
	if len(nodeIds) == 0 {
		return ""
	}

	var wrappedNodeIds []string
	for _, nodeId := range nodeIds {
		wrappedNodeId := fmt.Sprintf("'%s'", nodeId)
		wrappedNodeIds = append(wrappedNodeIds, wrappedNodeId)
	}

	condition := " NodeId IN ("
	condition += strings.Join(wrappedNodeIds, ",")
	condition += ")"
	return condition
}

func filterSqlTypeCondition(s string, SqlMethods []string) string {
	if len(SqlMethods) == 0 || strings.Contains(strings.ToUpper(strings.Join(SqlMethods, ",")), "ALL") {
		return ""
	}

	var wrappedSqlMethods []string
	for _, method := range SqlMethods {
		wrappedMethod := fmt.Sprintf("'%s'", method)
		wrappedSqlMethods = append(wrappedSqlMethods, wrappedMethod)
	}

	condition := s + " IN ("
	condition += strings.Join(wrappedSqlMethods, ",")
	condition += ")"
	return condition
}

func generateSortParamStringForSlowLog(sortBy shared.SortBy, orderBy shared.OrderByForSlowLog) string {
	return fmt.Sprintf(" order by %s %s", orderBy.String(), sortBy.String())
}

func parseInt64(input interface{}) (int64, error) {
	switch number := input.(type) {
	case string:
		return strconv.ParseInt(number, 10, 64)
	case json.Number:
		return number.Int64()
	default:
		return 0, fmt.Errorf("can't convert %v to int64", input)
	}
}

func parseFloat64(input interface{}) (float64, error) {
	switch number := input.(type) {
	case string:
		return strconv.ParseFloat(number, 64)
	case json.Number:
		return number.Float64()
	default:
		return 0, fmt.Errorf("can't convert %v to float64", input)
	}
}

func parseString(input interface{}) (string, error) {
	switch str := input.(type) {
	case string:
		return str, nil
	case json.Number:
		return str.String(), nil
	default:
		return "", fmt.Errorf("can't convert %v to string", input)
	}
}

func SortDialog(dialogs []*DialogLog, sortBy shared.SortBy, orderBy string) {
	switch orderBy {
	case "Time":
		if sortBy == shared.ASC {
			sort.Slice(dialogs, func(i, j int) bool {
				// str转换为seconds e.g:12s->12
				var t0, t1 string
				if !strings.Contains(dialogs[i].Time, "s") {
					t0 = fmt.Sprintf("%ss", dialogs[i].Time)
					t1 = fmt.Sprintf("%ss", dialogs[j].Time)
				} else {
					t0 = dialogs[i].Time
					t1 = dialogs[j].Time
				}
				t00, _ := time.ParseDuration(t0)
				t11, _ := time.ParseDuration(t1)
				return t00 < t11
			})
		} else if sortBy == shared.DESC {
			sort.Slice(dialogs, func(i, j int) bool {
				var t0, t1 string
				if !strings.Contains(dialogs[i].Time, "s") {
					t0 = fmt.Sprintf("%ss", dialogs[i].Time)
					t1 = fmt.Sprintf("%ss", dialogs[j].Time)
				} else {
					t0 = dialogs[i].Time
					t1 = dialogs[j].Time
				}
				t00, _ := time.ParseDuration(t0)
				t11, _ := time.ParseDuration(t1)
				return t00 > t11
			})
		}
	}
}

func ConvTlsTaskInfoToModelTaskInfo(t *tls_sdk.DownloadTaskResp) *model.TaskInfo {
	parse, _ := time.Parse(consts.NormalTimeFormat, t.CreateTime)

	return &model.TaskInfo{
		TaskId:         t.TaskId,
		TaskName:       t.TaskName,
		TopicId:        t.TopicId,
		Query:          t.Query,
		StartTime:      t.StartTime,
		EndTime:        t.EndTime,
		TaskStatus:     convTlsTaskStatus(t.TaskStatus),
		CreateTime:     t.CreateTime,
		ExpirationTime: parse.Add(24 * time.Hour).Format(consts.NormalTimeFormat),
		LogSize:        strconv.FormatInt(t.LogSize, 10),
		LogCount:       strconv.FormatInt(t.LogCount, 10),
		DataFormat:     &t.DataFormat,
		Compression:    &t.Compression,
	}
}

func convTlsTaskStatus(status string) model.DownloadTaskStatus {
	switch status {
	case "creating":
		return model.DownloadTaskStatus_Creating
	case "created_cut":
		return model.DownloadTaskStatus_CreatedCut
	case "success":
		return model.DownloadTaskStatus_Success
	case "wait":
		return model.DownloadTaskStatus_Wait
	case "fail":
		return model.DownloadTaskStatus_Fail
	}
	return model.DownloadTaskStatus_Fail
}

func SortTrx(trxLogs []*TrxLog, sortBy shared.SortBy, orderBy string) {
	switch orderBy {
	case trxExecTimeKey:
		if sortBy == shared.ASC {
			sort.Slice(trxLogs, func(i, j int) bool {
				t0 := trxLogs[i].TrxExecTime
				t1 := trxLogs[j].TrxExecTime
				return t0 < t1
			})
		} else if sortBy == shared.DESC {
			sort.Slice(trxLogs, func(i, j int) bool {
				t0 := trxLogs[i].TrxExecTime
				t1 := trxLogs[j].TrxExecTime
				return t0 > t1
			})
		}
	}
}

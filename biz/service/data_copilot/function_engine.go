package data_copilot

import (
	strings2 "code.byted.org/gopkg/lang/strings"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	consts2 "code.byted.org/infcs/dbw-mgr/biz/service/data_copilot/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	dbw_utils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"github.com/qjpcpu/fp"
	"k8s.io/utils/pointer"
	"strconv"
	"strings"
)

const DATA_BASE_NOT_SUPPORT = "当前数据库类型不支持该功能，请委婉的向用户说明"

type Function func(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error)

func (self *copilotFunctionService) InitFunctionMap() map[string]Function {
	mysqlValidationFunc := map[string]Function{
		"query_sessions_by_instance":          self.querySessionsByInstance,
		"query_instance_specifications":       self.queryInstanceSpecifications,
		"query_instance_parameters":           self.queryInstanceParameters,
		"query_instances":                     self.queryInstanceList,
		"query_memory_usage":                  self.queryMemoryUsage,
		"query_cpu_usage":                     self.queryCpuUsage,
		"query_disk_usage":                    self.queryDiskUsage,
		"query_QPS":                           self.queryQPS,
		"query_TPS":                           self.queryTPS,
		"query_top_slow_sql":                  self.queryTopSlowSql,
		"query_aggregation_data_by_SQL_table": self.DescribeAggregationSQLTable,
		"query_storage_capacity":              self.QueryStorageCapacity,
		"query_table_space_top10":             self.QueryTableSpaceTop10,
		"query_hot_spot_sql":                  self.QueryHotSpotSQL,
		"query_database_transaction_detail":   self.DescribeTrxDetailSnapshot,
	}
	return mysqlValidationFunc
}

func (self *copilotFunctionService) DescribeTrxDetailSnapshot(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {

	instanceID := toolRequest.InstanceID
	instanceType := toolRequest.InstanceType
	var messageList []*consts2.ChatMessage
	if instanceType != model.InstanceType_MySQL && instanceType != model.InstanceType_ByteRDS && instanceType != model.InstanceType_VeDBMySQL {
		messageList = append(messageList, buildFailedMessage(DATA_BASE_NOT_SUPPORT, toolRequest))
		return messageList, nil
	}

	number := int32(1)
	size := int32(20)
	req := &model.DescribeTrxDetailSnapshotReq{
		InstanceType: model.DSType(instanceType),
		InstanceId:   instanceID,
		PageSize:     &size,
		PageNumber:   &number,
		StartTime:    &toolRequest.StartTime,
		EndTime:      &toolRequest.EndTime,
		QueryFilter: &model.TrxQueryFilter{
			TrxExecTime: &toolRequest.TrxExecTime,
		},
	}
	if !strings2.IsEmpty(toolRequest.RegionId) {
		req.RegionId = &toolRequest.RegionId
	}

	snapshot, err := self.DescribeTrxDetailSnapshotHandler.DescribeTrxDetailSnapshot(ctx, req)
	if err != nil {
		log.Info(ctx, "copilotFunctionService DescribeTrxDetailSnapshot  json.Unmarshal DBInternalUsers error: %v", err.Error())
		messageList = append(messageList, buildFailedMessage("查询事务明细数据失败。请委婉的向用户说明，并让用户稍后再试", toolRequest))
		return messageList, nil
	}

	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(snapshot.TrxAndLocks),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) QueryHotSpotSQL(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	instanceID := toolRequest.InstanceID
	instanceType := toolRequest.InstanceType
	var messageList []*consts2.ChatMessage
	if instanceType != model.InstanceType_MySQL && instanceType != model.InstanceType_ByteRDS && instanceType != model.InstanceType_VeDBMySQL {
		messageList = append(messageList, buildFailedMessage(DATA_BASE_NOT_SUPPORT, toolRequest))
		return messageList, nil
	}

	//首先判断一下当前实例是否开启了全量SQL洞察
	respContent := "本次查询结果为空，选定时间范围内不存在热点SQL数据"
	startTime := toolRequest.StartTime
	endTime := toolRequest.EndTime
	if !self.isOpenFullSqlOption(ctx, instanceID, instanceType, toolRequest.RegionId, toolRequest.VRegion) {
		//如果没开，那么就查询热点SQL，将热点SQL的内容返回出去就行了
		component := model.Component_DBEngine
		dsType := model.DSType(instanceType)
		req := &model.DescribeDialogHotspotsReq{
			InstanceId:   instanceID,
			InstanceType: &dsType,
			StartTime:    startTime,
			EndTime:      endTime,
			SearchParam: &model.HotspotsFilter{
				SqlType: []string{"ALL"},
			},
			Component: &component,
		}
		if !strings2.IsEmpty(toolRequest.RegionId) {
			req.RegionId = toolRequest.RegionId
		} else {
			req.RegionId = self.loc.RegionID()
		}
		resp, err := self.DescribeDialogHotspotsHandler.DescribeDialogHotspots(ctx, req)
		if err != nil || resp.SqlHotspots == nil {
			log.Info(ctx, "copilotFunctionService QueryHotSpotSQL DescribeDialogHotspots failed, err is: %v", err)
			messageList = append(messageList, buildFailedMessage("查询热点SQL数据失败。请委婉的向用户说明，并让用户稍后再试", toolRequest))
			return messageList, nil
		}

		respContent = utils.Show(resp.SqlHotspots.SqlHotspotsDistribution)
		if len(resp.SqlHotspots.SqlHotspotsDistribution) == 0 {
			respContent = "本次查询结果为空，选定时间范围内不存在热点SQL数据（有可能为选定时间内该实例无活跃会话）"
		}

		//将分析结果写入数据库
		result := &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      respContent,
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		}
		messageList = append(messageList, result)
		return messageList, nil
	}

	//如果结束时间 - 开始时间大于24小时，则此时只查询最近1小时范围内的数据
	if endTime-startTime > 24*60*60 {
		endTime = startTime + 1*60*60 - 1
	}

	//如果当前实例开启了全量SQL洞察，那么就将全量SQL洞察的明细数据作为结果返回给用户
	execTime := model.OrderByForSqlItem_ExecTime
	CollectSqlFingerprint := false
	desc := model.SortBy_DESC
	req := &model.DescribeSqlTemplatesContrastReq{
		InstanceType:          instanceType,
		InstanceId:            instanceID,
		FirstStartTime:        int32(startTime),
		FirstEndTime:          int32(endTime),
		SecondStartTime:       int32(startTime),
		SecondEndTime:         int32(endTime),
		SortBy:                &desc,
		OrderBy:               &execTime,
		CollectSqlFingerprint: &CollectSqlFingerprint,
	}
	if !strings2.IsEmpty(toolRequest.RegionId) {
		req.RegionId = &toolRequest.RegionId
		req.VRegion = &toolRequest.VRegion
	}
	contrast, err := self.DescribeSqlTemplatesContrastHandler.DescribeSqlTemplatesContrast(ctx, req)
	if err != nil {
		log.Info(ctx, "copilotFunctionService QueryHotSpotSQL DescribeDialogHotspots failed, err is: %v", err)
		messageList = append(messageList, buildFailedMessage("查询热点SQL数据失败。请委婉的向用户说明，并让用户稍后再试", toolRequest))
		return messageList, nil
	}

	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(contrast.Details),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) QueryTableSpaceTop10(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	instanceID := toolRequest.InstanceID
	instanceType := toolRequest.InstanceType
	var messageList []*consts2.ChatMessage
	if instanceType != model.InstanceType_MySQL && instanceType != model.InstanceType_VeDBMySQL && instanceType != model.InstanceType_Postgres && instanceType != model.InstanceType_ByteRDS {
		messageList = append(messageList, buildFailedMessage(DATA_BASE_NOT_SUPPORT, toolRequest))
		return messageList, nil
	}

	cnf := self.cnf.Get(ctx)
	c3Cfg := self.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	dbwAdmin := c3Cfg.DBWAccountName
	dbwAdminPwd := dbw_utils.GetAccountPassword(c3Cfg.DbwAccountPasswordGenKey, instanceID)
	if instanceType.String() == model.InstanceType_ByteRDS.String() {
		dbwAdmin = cnf.C3Config.DBWAccountName
		dbwAdminPwd = cnf.C3Config.DbwPwd
	}
	ds := &shared.DataSource{
		Type:             shared.DataSourceType(instanceType),
		LinkType:         shared.Volc,
		User:             dbwAdmin,
		Password:         dbwAdminPwd,
		InstanceId:       instanceID,
		ConnectTimeoutMs: cnf.ConnectionConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.ConnectionReadTimeout * 1000,
		WriteTimeoutMs:   cnf.ConnectionWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.ConnectionIdleTimeout * 1000,
	}
	if !strings2.IsEmpty(toolRequest.RegionId) {
		ds.Region = toolRequest.RegionId
	}

	// 验证账号是否存在
	if err := self.ds.EnsureAccount(ctx, &datasource.EnsureAccountReq{Source: ds}); err != nil {
		log.Info(ctx, "copilotFunctionService QueryTableSpaceTop10 EnsureAccount error: %v", err.Error())
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询Top10的表空间明细数据失败。请委婉的向用户说明，并让用户稍后再试",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	req := &datasource.DescribeTableSpaceReq{
		Source:     ds,
		InstanceId: instanceID,
		Product:    int64(instanceType),
		Offset:     0,
		Limit:      10,
		OrderItem:  "TableSpace",
		OrderRule:  "DESC",
	}
	space, err := self.ds.DescribeTableSpace(ctx, req)
	if err != nil {
		log.Info(ctx, "copilotFunctionService QueryTableSpaceTop10 DescribeTableSpace error: %v", err.Error())
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询Top10的表空间明细数据失败。请委婉的向用户说明，并让用户稍后再试",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(space.TableStats),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) QueryStorageCapacity(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	instanceID := toolRequest.InstanceID
	instanceType := toolRequest.InstanceType
	var messageList []*consts2.ChatMessage
	if instanceType != model.InstanceType_MySQL && instanceType != model.InstanceType_VeDBMySQL && instanceType != model.InstanceType_Postgres {
		messageList = append(messageList, buildFailedMessage(DATA_BASE_NOT_SUPPORT, toolRequest))
		return messageList, nil
	}

	availableDays := self.getAvailableDays(ctx, instanceID, instanceType)

	diskSize, err := self.ds.GetDiskSize(ctx, &datasource.GetDiskSizeReq{InstanceType: shared.DataSourceType(instanceType), InstanceId: instanceID})
	if err != nil {
		log.Info(ctx, "copilotFunctionService QueryStorageCapacity GetDiskSize error: %v", err.Error())
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询磁盘空间数据失败。请委婉的向用户说明，并让用户稍后再试",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}
	storageSpace := self.getStorageSpace(ctx, instanceID, instanceType)
	futureSize := self.getFutureSize(ctx, instanceID, instanceType)
	resp := self.ds.FormatDescribeStorageCapacityResp(shared.DataSourceType(instanceType), diskSize, storageSpace)
	// VeDB，理论是无上限容量，所以无法计算可用天数
	if instanceType != model.InstanceType_VeDBMySQL {
		resp.AvailableDay = &availableDays
	} else {
		resp.FutureDiskSize = diagnosis.DecimalPointFloat(futureSize)
	}

	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(resp),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) DescribeAggregationSQLTable(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	var messageList []*consts2.ChatMessage
	if toolRequest.InstanceType != model.InstanceType_MySQL && toolRequest.InstanceType != model.InstanceType_VeDBMySQL && toolRequest.InstanceType != model.InstanceType_ByteRDS {
		messageList = append(messageList, buildFailedMessage(DATA_BASE_NOT_SUPPORT, toolRequest))
		return messageList, nil
	}

	instanceId := toolRequest.InstanceID
	instanceType := toolRequest.InstanceType
	if !self.isOpenFullSqlOption(ctx, instanceId, instanceType, toolRequest.RegionId, toolRequest.VRegion) {
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "当前数据库实例未开启全量SQL洞察功能，请参考火山引擎官方文档内容，要求用户自行开启全量SQL洞察后，才能使用该工具",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	//超级用户冒充普通用户
	ctx, err := self.FullSqlService.SuperAccountFakeInstanceOwner(ctx, instanceId)
	if err != nil {
		log.Error(ctx, "DescribeAggregationSQLTable SuperAccountFakeInstanceOwner error:%v", err)
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询写分析数据失败。请委婉的向用户说明，并让用户稍后再试",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	orderBy := model.OrderByForTableSqlItem_DmlExecTimeTotal
	sortBy := model.SortBy_DESC
	req := model.DescribeAggregationSQLTableReq{
		InstanceType: toolRequest.InstanceType,
		InstanceId:   instanceId,
		StartTime:    int32(toolRequest.StartTime),
		EndTime:      int32(toolRequest.EndTime),
		SortBy:       &sortBy,
		OrderBy:      &orderBy,
	}

	//查询数据
	resp, err := self.FullSqlService.GetAggregationTableDetail(ctx, &req)
	if err != nil {
		log.Warn(ctx, "DescribeAggregationSQLTable get sql execTime distribution detail error:%s", err.Error())
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询写分析数据失败，失败信息为：" + err.Error() + "。请结合报错信息委婉的向用户说明",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	details := resp.Details
	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(details),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)

	return messageList, nil
}

func (self *copilotFunctionService) queryTopSlowSql(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	var messageList []*consts2.ChatMessage
	if toolRequest.InstanceType == model.InstanceType_Redis || toolRequest.InstanceType == model.InstanceType_MSSQL || toolRequest.InstanceType == model.InstanceType_Mongo {
		messageList = append(messageList, buildFailedMessage(DATA_BASE_NOT_SUPPORT, toolRequest))
		return messageList, nil
	}

	RegionId := dbw_utils.GetRegion()
	if len(toolRequest.RegionId) == 0 {
		RegionId = toolRequest.RegionId
	}
	groups := []model.Group{model.Group_User, model.Group_SourceIP}

	asc := model.SortBy_ASC
	averageQueryTime := model.OrderByForAggregateSlowLog_AverageQueryTime
	number := int32(1)
	size := int32(20)
	dsType := model.DSType(toolRequest.InstanceType)
	msg := &model.DescribeAggregateSlowLogsReq{
		RegionId:     RegionId,
		InstanceId:   toolRequest.InstanceID,
		DSType:       &dsType,
		InstanceType: &dsType,
		StartTime:    int32(toolRequest.StartTime),
		EndTime:      int32(toolRequest.EndTime),
		SortBy:       &asc,
		OrderBy:      &averageQueryTime,
		PageNumber:   &number,
		PageSize:     &size,
		SearchParam: &model.AggregateSlowLogSearchParam{
			GroupIgnored: groups,
		},
	}

	response, err := self.DescribeAggregateSlowLogsHandler.DescribeAggregateSlowLogs(ctx, msg)
	if err != nil {
		log.Warn(ctx, "DescribeAggregateSlowLogs actor return error is %v", err)
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询慢SQL信息失败，失败信息为：" + err.Error() + "。请结合报错信息委婉的向用户说明",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	if response.Total == 0 {
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询慢SQL信息的返回结果为空",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(response),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) queryMemoryUsage(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {

	dataList := self.functionCallService.QueryMetricData(ctx, toolRequest.InstanceID, toolRequest.RegionId, "MEM", shared.DataSourceType(toolRequest.InstanceType), toolRequest.StartTime, toolRequest.EndTime)

	var messageList []*consts2.ChatMessage
	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(dataList),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) queryQPS(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	dataList := self.functionCallService.QueryMetricData(ctx, toolRequest.InstanceID, toolRequest.RegionId, "QPS", shared.DataSourceType(toolRequest.InstanceType), toolRequest.StartTime, toolRequest.EndTime)

	var messageList []*consts2.ChatMessage
	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(dataList),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) queryTPS(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	dataList := self.functionCallService.QueryMetricData(ctx, toolRequest.InstanceID, toolRequest.RegionId, "TPS", shared.DataSourceType(toolRequest.InstanceType), toolRequest.StartTime, toolRequest.EndTime)

	var messageList []*consts2.ChatMessage
	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(dataList),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) queryDiskUsage(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	dataList := self.functionCallService.QueryMetricData(ctx, toolRequest.InstanceID, toolRequest.RegionId, "DiskUsage", shared.DataSourceType(toolRequest.InstanceType), toolRequest.StartTime, toolRequest.EndTime)

	var messageList []*consts2.ChatMessage
	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(dataList),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) queryCpuUsage(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	dataList := self.functionCallService.QueryMetricData(ctx, toolRequest.InstanceID, toolRequest.RegionId, "CPU", shared.DataSourceType(toolRequest.InstanceType), toolRequest.StartTime, toolRequest.EndTime)

	var messageList []*consts2.ChatMessage
	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(dataList),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) querySessionsByInstance(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	var messageList []*consts2.ChatMessage
	if toolRequest.InstanceType == model.InstanceType_Redis || toolRequest.InstanceType == model.InstanceType_MSSQL {
		messageList = append(messageList, buildFailedMessage(DATA_BASE_NOT_SUPPORT, toolRequest))
		return messageList, nil
	}

	size := int32(1000)
	page := int32(1)
	req := &model.DescribeDialogDetailsReq{
		DSType:     model.DSType(toolRequest.InstanceType),
		InstanceId: toolRequest.InstanceID,
		PageSize:   &size,
		PageNumber: &page,
	}
	if len(toolRequest.RegionId) > 0 {
		region := toolRequest.RegionId
		req.RegionId = &region
	}
	snapshots, err := self.DescribeDialogDetails(ctx, req)
	if err != nil {
		log.Warn(ctx, "querySessionsByInstance error %s", err)
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询实时会话信息失败，失败信息为：" + err.Error() + "。请结合报错信息委婉的向用户说明",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	content := utils.Show(snapshots.DialogDetails)
	if len(snapshots.DialogDetails) == 0 {
		content = "查询实时会话信息为空，请委婉的向用户说明"
	}

	//将分析结果写入数据库
	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      content,
		FunctionCall: toolRequest.FunctionCall,
		ToolCallID:   toolRequest.ToolCallID,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) queryInstanceSpecifications(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	var messageList []*consts2.ChatMessage

	req := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: toolRequest.InstanceID,
		Type:       shared.DataSourceType(toolRequest.InstanceType),
	}
	if len(toolRequest.RegionId) > 0 {
		req.RegionId = toolRequest.RegionId
	}
	detail, err := self.ds.DescribeDBInstanceDetailForPilot(ctx, req)
	if err != nil {
		log.Info(ctx, "queryInstanceSpecifications err, instanceID is:%v, err is:%v", toolRequest.InstanceID, err)
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询实例规格失败，失败信息为：" + err.Error() + "。请结合报错信息委婉的向用户说明",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      detail,
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) queryInstanceParameters(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	var messageList []*consts2.ChatMessage

	req := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: toolRequest.InstanceID,
		Type:       shared.DataSourceType(toolRequest.InstanceType),
	}
	if len(toolRequest.RegionId) > 0 {
		req.RegionId = toolRequest.RegionId
	}
	detail, err := self.ds.DescribeDBInstanceParametersForPilot(ctx, req)
	if err != nil {
		log.Info(ctx, "queryInstanceSpecifications err, instanceID is:%v, err is:%v", toolRequest.InstanceID, err)
		messageList = append(messageList)
		return messageList, nil
	}

	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      detail,
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (self *copilotFunctionService) queryInstanceList(ctx context.Context, toolRequest *consts2.ToolRequest) ([]*consts2.ChatMessage, error) {
	var messageList []*consts2.ChatMessage

	req := &model.DescribeInstancesReq{}
	dsType := model.DSType(toolRequest.InstanceType)
	if dsType != 0 {
		req.DSType = &dsType
	}
	list := toolRequest.SearchControlMode
	if len(list) >= 0 {
		req.SearchControlMode = list
	}
	if len(toolRequest.RegionId) > 0 {
		req.RegionId = &toolRequest.RegionId
	}

	instanceList, err := self.dbwInstanceSvc.DescribeInstances(ctx, req)

	if err != nil {
		log.Info(ctx, "queryInstanceSpecifications err, instanceID is:%v, err is:%v", toolRequest.InstanceID, err)
		messageList = append(messageList, &consts2.ChatMessage{
			Role:         consts2.RoleTypeTool,
			Content:      "查询实例列表数据集合失败，失败信息为：" + err.Error() + "。请结合报错信息委婉的向用户说明",
			ToolCallID:   toolRequest.ToolCallID,
			FunctionCall: toolRequest.FunctionCall,
		})
		return messageList, nil
	}

	result := &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      utils.Show(instanceList),
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
	messageList = append(messageList, result)
	return messageList, nil
}

func (h *copilotFunctionService) DescribeDialogDetails(ctx context.Context, req *model.DescribeDialogDetailsReq) (*model.DialogDetails, error) {
	if shared.DataSourceType(req.GetDSType()) == shared.Postgres {
		return h.DescribeDialogDetailsForPG(ctx, req)
	}
	cnf := h.cnf.Get(ctx)

	c3Cfg := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	dbwAdmin := c3Cfg.DBWAccountName
	dbwAdminPwd := dbw_utils.GetAccountPassword(c3Cfg.DbwAccountPasswordGenKey, req.GetInstanceId())
	if req.GetDSType() == model.DSType_ByteRDS {
		dbwAdmin = cnf.C3Config.DBWAccountName
		dbwAdminPwd = cnf.C3Config.DbwPwd
	}
	ds := &shared.DataSource{
		Type:       shared.DataSourceType(req.GetDSType()),
		LinkType:   shared.Volc,
		User:       dbwAdmin,
		Password:   dbwAdminPwd,
		InstanceId: req.GetInstanceId(),
	}
	if req.RegionId != nil {
		ds.Region = *req.RegionId
	}
	// 验证账号是否存在
	if err := h.ds.EnsureAccount(ctx, &datasource.EnsureAccountReq{Source: ds}); err != nil {
		log.Warn(ctx, "Check dbw_admin account failed %s", err)
		return nil, err
	}

	cfg := h.cnf.Get(ctx)
	internalUsers := make([]string, 0)
	dbInternalUsers := map[string]string{}
	err := json.Unmarshal([]byte(cfg.DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		internalUsers = strings.Split(dbInternalUsers[req.GetDSType().String()], ",")
	}

	queryFilter := &shared.DialogQueryFilter{
		ShowSleepConnection: "false",
	}
	dialogInfosReq := &datasource.DescribeDialogInfosReq{
		Offset:        0,
		Limit:         10000, // 默认返回10000条
		Source:        ds,
		QueryFilter:   queryFilter,
		InternalUsers: internalUsers,
		Component:     model.Component_DBEngine.String(),
	}
	dialogInfosResp, err := h.ds.DescribeDialogInfos(ctx, dialogInfosReq)
	if err != nil {
		log.Warn(ctx, "err is %s", err)
		return nil, err
	}

	ret := &model.DialogDetails{}
	if err := fp.StreamOf(dialogInfosResp.DialogDetails.Details).Map(func(detail *shared.DialogDetail) *model.DialogDetail {
		return &model.DialogDetail{
			ProcessID:    detail.ProcessID,
			User:         detail.User,
			Host:         detail.Host,
			DB:           detail.DB,
			Command:      detail.Command,
			Time:         detail.Time,
			State:        detail.State,
			Info:         detail.Info,
			BlockingPid:  detail.BlockingPid,
			NodeId:       pointer.String(detail.NodeId),
			NodeType:     pointer.String(detail.NodeType),
			PSM:          &detail.PSM,
			EndpointName: &detail.EndpointName,
			EndpointId:   &detail.EndpointId,
		}
	}).ToSlice(&ret.DialogDetails); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	return ret, nil
}

func (h *copilotFunctionService) DescribeDialogDetailsForPG(ctx context.Context, req *model.DescribeDialogDetailsReq) (*model.DialogDetails, error) {
	var (
		sessionId       string
		dbInternalUsers map[string]string
	)

	sessionId, err := handler.GetInstanceSession(ctx, h.actorClient, req.InstanceId, req.GetDSType())
	if err != nil {
		log.Warn(ctx, "fail to get instance session, err=%v", err)
		return nil, nil
	}
	defer handler.GiveBackInstanceSession(ctx, h.actorClient, req.InstanceId, sessionId)

	queryFilter := &shared.DialogQueryFilter{
		ShowSleepConnection: "false",
	}

	cfg := h.cnf.Get(ctx)
	var resp interface{}
	internalUsers := make([]string, 0)
	err = json.Unmarshal([]byte(cfg.DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	internalUsers = strings.Split(dbInternalUsers[req.GetDSType().String()], ",")
	resp, err = h.actorClient.KindOf(consts.SessionActorKind).
		Call(ctx, sessionId, &shared.DescribeDialogInfos{
			DSType:        conv.ToSharedType(req.GetDSType()),
			PageSize:      req.GetPageSize(),
			PageNumber:    req.GetPageNumber(),
			QueryFilter:   queryFilter,
			InternalUsers: internalUsers,
			InternalIPs:   strings.Split(cfg.InternalIPs, ","),
			Component:     model.Component_DBEngine.String(),
		})
	if err != nil {
		log.Warn(ctx, "failed to describe dialog details, err=%v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	var infos *shared.DialogInfos
	switch rsp := resp.(type) {
	case *shared.DialogInfos:
		infos = rsp
	case *shared.DataSourceOpFailed:
		err = consts.ErrorOf(model.ErrorCode_DataSourceOpFail)
		log.Warn(ctx, "failed to describe dialog infos, err=%v", rsp.ErrorMessage)
		return nil, err
	default:
		err = consts.ErrorOf(model.ErrorCode_DataSourceOpFail)
		log.Warn(ctx, "unknown response, resp=%+v", resp)
		return nil, err
	}

	ret := &model.DialogDetails{}
	fp.StreamOf(infos.Details.Details).Map(func(detail *shared.DialogDetail) *model.DialogDetail {
		return &model.DialogDetail{
			ProcessID:   detail.ProcessID,
			User:        detail.User,
			Host:        detail.Host,
			DB:          detail.DB,
			Command:     detail.Command,
			Time:        detail.Time,
			State:       detail.State,
			Info:        detail.Info,
			BlockingPid: detail.BlockingPid,
			NodeId:      &detail.NodeId,
			NodeType:    &detail.NodeType,
		}
	}).ToSlice(&ret.DialogDetails)

	return ret, nil
}

func (h *copilotFunctionService) getAvailableDays(ctx context.Context, instanceID string, instanceType model.InstanceType) string {
	if instanceType == model.InstanceType_VeDBMySQL {
		// VedDB，无法将计算
		return "30+"
	}
	diskAvailableResp, err := h.ds.GetDiskAvailableDays(ctx, &datasource.GetDiskAvailableDaysReq{
		DSType:     shared.DataSourceType(instanceType),
		InstanceId: instanceID,
		TenantId:   fwctx.GetTenantID(ctx),
	})
	if err != nil {
		return "-"
	}
	availableDayStr := strconv.Itoa(diskAvailableResp.AvailableDays)
	if diskAvailableResp.AvailableDays == -1 {
		availableDayStr = "-"
	} else if diskAvailableResp.AvailableDays <= 0 || diskAvailableResp.AvailableDays >= 31 {
		availableDayStr = "30+"
	}
	return availableDayStr
}

func (h *copilotFunctionService) getStorageSpace(ctx context.Context, instanceID string, instanceType model.InstanceType) float64 {
	if instanceType == model.InstanceType_VeDBMySQL {
		return 0
	}
	instanceDetail, err := h.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		Type:       shared.DataSourceType(instanceType),
		InstanceId: instanceID,
	})
	if err != nil || instanceDetail == nil {
		return 0
	}

	return float64(instanceDetail.StorageSpace)
}

func (h *copilotFunctionService) getFutureSize(ctx context.Context, instanceID string, instanceType model.InstanceType) float64 {
	if instanceType != model.InstanceType_VeDBMySQL {
		// VedDB，无法将计算
		return 0
	}

	diskFutureSizeResp, err := h.ds.GetDiskFutureSize(ctx, &datasource.GetDiskFutureSizeReq{
		DSType:     shared.DataSourceType(instanceType),
		InstanceId: instanceID,
		TenantId:   fwctx.GetTenantID(ctx),
	})
	if err != nil {
		return 0
	}
	return diskFutureSizeResp.FutureSize
}

func (self *copilotFunctionService) isOpenFullSqlOption(ctx context.Context, instanceID string, instanceType model.InstanceType, regionID string, VRegion string) bool {

	preCheck := false
	req := &model.DescribeFullSqlStatusReq{
		FollowInstanceID: instanceID,
		DSType:           model.DSType(instanceType),
		PreCheck:         &preCheck,
	}
	if !strings2.IsEmpty(VRegion) && !strings2.IsEmpty(regionID) {
		req.RegionId = &regionID
		req.VRegion = &VRegion
	}
	fullSqlStatus, err := self.FullSqlService.DescribeFullSqlStatus(ctx, req)
	if err != nil {
		log.Info(ctx, "copilotFunctionService isOpenFullSqlOption DescribeFullSqlStatus error, err is:%v", err)
		return false
	}

	//DetailStatus = FullSqlFuncStatus_RUN && SqlAnalysisFunStatus = FullSqlFuncStatus_RUN 就代表开启了全量SQL洞察
	if fullSqlStatus.DetailStatus == model.FullSqlFuncStatus_RUN && fullSqlStatus.SqlAnalysisFunStatus == model.FullSqlFuncStatus_RUN {
		log.Info(ctx, "copilotFunctionService isOpenFullSqlOption finished, result is true!, fullSqlStatus is: %v", utils.Show(fullSqlStatus))
		return true
	}

	log.Info(ctx, "copilotFunctionService isOpenFullSqlOption finished, result is false!, fullSqlStatus is: %v", utils.Show(fullSqlStatus))
	return false
}

func buildFailedMessage(FailedDescription string, toolRequest *consts2.ToolRequest) *consts2.ChatMessage {
	return &consts2.ChatMessage{
		Role:         consts2.RoleTypeTool,
		Content:      FailedDescription,
		ToolCallID:   toolRequest.ToolCallID,
		FunctionCall: toolRequest.FunctionCall,
	}
}

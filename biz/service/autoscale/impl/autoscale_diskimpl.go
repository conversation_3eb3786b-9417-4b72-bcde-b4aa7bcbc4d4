package impl

import (
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/dig"
	"k8s.io/utils/pointer"
	"strconv"
	"time"
)

const (
	RDS_MySQL_Version_V2 = `2022-01-01`
)

type diskAutoScaleServiceImpl struct {
	config       config.ConfigProvider
	autoScale    repository.AutoScaleRepo
	idg          idgen.Service
	crossAuthSvc crossauth.CrossServiceAuthorizationService
	cli          cli.ActorClient
	mysql        mgr.Provider
	I18nSvc      i18n.I18nServiceInterface
}

type NewDiskAutoScaleServiceIn struct {
	dig.In
	Config       config.ConfigProvider
	AutoScale    repository.AutoScaleRepo
	IDgen        idgen.Service
	CrossAuthSvc crossauth.CrossServiceAuthorizationService
	Cli          cli.ActorClient
	MySQLMgr     mgr.Provider `name:"mysql"`
	I18nSvc      i18n.I18nServiceInterface
}

type EnglishNodeInfo struct {
	NodeId          string  `json:"NodeID"`
	ZoneId          string  `json:"AvailabilityZone"`
	InstanceFamily  string  `json:"SpecificationType"`
	CpuNum          int     `json:"Cpu"`
	MemInGb         int     `json:"Mem"`
	StorageSpace    int     `json:"StorageSpace"`
	DiskUsedPercent float64 `json:"DiskUsedPercent,omitempty"`
}

func NewDiskAutoScaleService(p NewDiskAutoScaleServiceIn) autoscale.AutoScaleDiskService {
	return &diskAutoScaleServiceImpl{
		config:       p.Config,
		autoScale:    p.AutoScale,
		idg:          p.IDgen,
		crossAuthSvc: p.CrossAuthSvc,
		cli:          p.Cli,
		mysql:        p.MySQLMgr,
		I18nSvc:      p.I18nSvc,
	}
}

func (b *diskAutoScaleServiceImpl) DescribeDiskDBAutoScalingConfig(ctx context.Context, req *model.DescribeDiskDBAutoScalingConfigReq) (*model.DescribeDiskDBAutoScalingConfigResp, error) {
	log.Info(ctx, "DiskAutoScale-DescribeDiskDBAutoScalingConfig: ret is %s", utils.Show(req))
	rreq := &rdsModel_v2.DescribeDBAutoScalingConfigReq{
		InstanceId: req.InstanceId,
	}
	rresp := &rdsModel_v2.DescribeDBAutoScalingConfigResp{}
	err := b.mysql.Get().Call(ctx, rdsModel_v2.Action_DescribeDBAutoScalingConfig.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "DiskAutoScale-DescribeDiskDBAutoScalingConfig: err is %s", err)
		return nil, err
	}

	ret := b.mapRdsResponseToAutoScalingConfig(ctx, rresp)
	log.Info(ctx, "DiskAutoScale-DescribeDiskDBAutoScalingConfig: ret is %s", utils.Show(ret))
	return ret, nil
}
func (b *diskAutoScaleServiceImpl) ModifyDiskDBAutoScalingConfig(ctx context.Context, req *model.ModifyDiskDBAutoScalingConfigReq) (*model.ModifyDiskDBAutoScalingConfigResp, error) {
	log.Info(ctx, "DiskAutoScale-ModifyDiskDBAutoScalingConfigReq: req is %s", utils.Show(req))
	result := &model.ModifyDiskDBAutoScalingConfigResp{}

	storageConfig := &rdsModel_v2.AutoStorageScalingConfig{}
	storageConfig.EnableStorageAutoScale = req.DiskDBAutoScalingConfig.EnableStorageAutoScale
	storageConfig.StorageThreshold = req.DiskDBAutoScalingConfig.StorageThreshold
	storageConfig.StorageUpperBound = req.DiskDBAutoScalingConfig.StorageUpperBound
	rreq := &rdsModel_v2.ModifyDBAutoScalingConfigReq{
		InstanceId:    req.InstanceId,
		StorageConfig: storageConfig,
	}
	rresp := &rdsModel_v2.ModifyDBAutoScalingConfigResp{}
	err := b.mysql.Get().Call(ctx, rdsModel_v2.Action_ModifyDBAutoScalingConfig.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2))

	if err != nil {
		log.Warn(ctx, "DiskAutoScale-ModifyDiskDBAutoScalingConfigResp: err is %s", err)
		return nil, err
	}
	log.Info(ctx, "DiskAutoScale-ModifyDiskDBAutoScalingConfig: rresp is %s", utils.Show(rresp))
	return result, nil
}

// precheck
// ModifyDBAutoStorageScaling NOTE 这个接口后面不会调用,因为要兼容云盘的存储扩缩容
func (b *diskAutoScaleServiceImpl) ModifyDBAutoStorageScaling(ctx context.Context, req *model.ModifyDBAutoStorageScalingReq) (*model.ModifyDBAutoStorageScalingResp, error) {
	log.Info(ctx, "DiskAutoScale-ModifyDiskDBAutoScalingConfigReq: req is %s", utils.Show(req))
	result := &model.ModifyDBAutoStorageScalingResp{}

	/*	configReq := &model.DescribeDiskDBAutoScalingConfigReq{
			InstanceType: req.InstanceType,
			InstanceId:   req.InstanceId,
			RegionId:     req.RegionId,
		}
		configres, err := b.DescribeDiskDBAutoScalingConfig(ctx, configReq)
		if err != nil {
			if se, ok := err.(consts.StandardError); ok {
				log.Warn(ctx, "StandardError occurred: %+v", se)
				return nil, err
			}
		}
	*/

	rreq := &rdsModel_v2.ModifyDBAutoStorageScalingReq{
		InstanceId:   req.InstanceId,
		EstimateOnly: pointer.Bool(true),
		StorageType:  rdsModel_v2.StorageType(v2.StorageType_LocalSSD),
		StorageSpace: -1,
	}
	rresp := &rdsModel_v2.ModifyDBAutoStorageScalingResp{}
	err := b.mysql.Get().Call(ctx, rdsModel_v2.Action_InnerModifyDBAutoStorageScaling.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2))

	if err != nil {
		if handledErr := b.handleStandardError(err); handledErr != nil {
			return nil, handledErr
		}
	}
	log.Info(ctx, "DiskAutoScale-ModifyDiskDBAutoScalingConfig: rresp is %s", utils.Show(rresp))
	return result, nil
}

// 提取的错误处理函数
func (b *diskAutoScaleServiceImpl) handleStandardError(err error) error {
	se, ok := err.(consts.StandardError)
	if !ok {
		return err
	}
	switch se.GetCode() {
	case 40102:
		return consts.ErrorOf(model.ErrorCode_OperationDeniedResourceSoldOut)
	case 40007:
		return consts.ErrorOf(model.ErrorCode_InsufficientBalance)
	case 40002:
		return consts.ErrorOf(model.ErrorCode_InvalidParameter)
	default:
		return nil
	}
}

func (b *diskAutoScaleServiceImpl) DescribeDiskAutoScaleEvents(ctx context.Context, req *model.DescribeDiskAutoScaleEventsReq) (*model.DescribeDiskAutoScaleEventsResp, error) {
	log.Info(ctx, "DiskAutoScale-DescribeDiskAutoScaleEvents: req is %s", utils.Show(req))
	rreq := &rdsModel_v2.InnerDescribeDBAutoScalingTasksReq{}
	rreq.InstanceId = req.InstanceId
	if req.PageSize != nil {
		rreq.PageSize = *req.PageSize
	} else {
		rreq.PageSize = 10 // 默认值，可以根据需要修改
	}
	if req.PageNumber != nil {
		rreq.PageNumber = *req.PageNumber
	} else {
		rreq.PageNumber = 1 // 默认值，可以根据需要修改
	}
	rreq.TaskActions = []string{rdsModel_v2.Action_InnerModifyDBAutoStorageScaling.String(), rdsModel_v2.Action_ModifyDBLocalSpecByInplace.String()}

	if req.CreationEndTime != nil && *req.CreationEndTime != "" {
		// 同上，转换为 int64
		endSec, err := strconv.ParseInt(*req.CreationEndTime, 10, 64)
		if err != nil {
			log.Warn(ctx, "Cannot parse CreationEndTime: %v", err)
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}
		rreq.CreationEndTime = time.Unix(endSec, 0).UTC().Format(time.RFC3339)
	} else {
		rreq.CreationEndTime = time.Now().UTC().Format(time.RFC3339)
	}
	if req.CreationStartTime != nil && *req.CreationStartTime != "" {
		// 转成 int64
		startSec, err := strconv.ParseInt(*req.CreationStartTime, 10, 64)
		if err != nil {
			log.Warn(ctx, "Cannot parse CreationStartTime: %v", err)
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}
		// 秒转时间，并格式化为 RFC3339
		rreq.CreationStartTime = time.Unix(startSec, 0).UTC().Format(time.RFC3339)
	} else {
		rreq.CreationStartTime = time.Now().UTC().AddDate(0, 0, -7).Format(time.RFC3339)
	}

	rresp := &rdsModel_v2.InnerDescribeDBAutoScalingTasksResp{}
	log.Info(ctx, "DiskAutoScale-DescribeDiskAutoScaleEvents-Action_DescribeTasks: rreq is %s", utils.Show(rreq))

	if b.mysql == nil {
		return nil, errors.New("b.mysql is nil ")
	}
	err := b.mysql.Get().Call(ctx, rdsModel_v2.Action_InnerDescribeDBAutoScalingTasks.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "DiskAutoScale-DescribeDiskAutoScaleEvents: err is %s", err)
		return nil, err
	}
	log.Info(ctx, "DiskAutoScale-DescribeDiskAutoScaleEvents-Action_DescribeTasks: rresp is %s", utils.Show(rresp))

	ret := &model.DescribeDiskAutoScaleEventsResp{}
	ret.Total = rresp.Total
	for _, task := range rresp.GetTasks() {
		// 将字符串转为 float64
		parsedValue, err := strconv.ParseFloat(task.TriggerConfigValue, 64)
		if err != nil {
			log.Warn(ctx, "DiskAutoScale-TriggerConfigValue: task is %s", utils.Show(task))
		}
		var (
			beforeScale, afterScale, // 扩容前,扩容后
			autoScaleType, // 实例规格、存储空间
			autoScaleTriggerCondition, // 触发条件：CPU使用率、磁盘使用率
			autoScaleTriggerMetricUnit string // 触发指标的单位
			autoScaleTriggerEventType   model.AutoScaleTriggerEventType // 扩缩容触发事件类型:手动、自动
			autoScaleTriggerEventTypeCN string
			triggerConditionComparison  string
		)
		switch task.TriggerEventType.String() {
		case rdsModel_v2.TriggerEventType_Manual.String():
			autoScaleTriggerEventType = model.AutoScaleTriggerEventType_Manual
			autoScaleTriggerEventTypeCN = "手动"
		case rdsModel_v2.TriggerEventType_AutoRule.String():
			autoScaleTriggerEventType = model.AutoScaleTriggerEventType_Auto
			autoScaleTriggerEventTypeCN = "自动"
		case rdsModel_v2.TriggerEventType_InstanceChanged.String():
			autoScaleTriggerEventTypeCN = "实例变更"
		case rdsModel_v2.TriggerEventType_AutoRuleReset.String():
			autoScaleTriggerEventTypeCN = "规则关闭"
		case rdsModel_v2.TriggerEventType_Unknown.String():
			autoScaleTriggerEventTypeCN = "未知"
		default:
			autoScaleTriggerEventTypeCN = "未知"
		}
		log.Info(ctx, "autoScaleTriggerEventTypeCN is %v,task.TriggerEventType is %v", autoScaleTriggerEventTypeCN, task.TriggerEventType)
		switch task.TaskAction {
		case rdsModel_v2.Action_ModifyDBLocalSpecByInplace.String():
			if autoScaleTriggerEventTypeCN == "自动" || autoScaleTriggerEventTypeCN == "手动" {
				autoScaleType = fmt.Sprintf("%v - 实例规格", autoScaleTriggerEventTypeCN)
			} else {
				autoScaleType = fmt.Sprintf("%v ", autoScaleTriggerEventTypeCN)
			}
			autoScaleTriggerCondition = "CPU平均使用率"
			autoScaleTriggerMetricUnit = "%"
			beforeScale = fmt.Sprintf("%vC", task.ScalingBeforeSpecValue)
			afterScale = fmt.Sprintf("%vC", task.ScalingAfterSpecValue)
		case rdsModel_v2.Action_InnerModifyDBAutoStorageScaling.String():
			autoScaleType = "存储空间"
			autoScaleTriggerCondition = "可用存储空间占比"
			autoScaleTriggerMetricUnit = "%"
			beforeScale = fmt.Sprintf("%vGB", task.ScalingBeforeSpecValue)
			afterScale = fmt.Sprintf("%vGB", task.ScalingAfterSpecValue)
		}
		autoScaleAction := model.AutoScaleAction_Expand
		triggerConditionComparison = ">="
		if conv.StringToInt64(task.ScalingAfterSpecValue, 0) < conv.StringToInt64(task.ScalingBeforeSpecValue, 0) {
			autoScaleAction = model.AutoScaleAction_Reduce
			triggerConditionComparison = "<="
		}
		// 存储空间,只有可用空间小于等于这种情况
		if task.TaskAction == rdsModel_v2.Action_InnerModifyDBAutoStorageScaling.String() {
			triggerConditionComparison = "<="
		}
		diskEvent := &model.DiskAutoScaleEventItem{
			EventId:                    task.TaskId,
			BeforeAutoScale:            beforeScale,                                 // 触发前的值
			AfterAutoScale:             afterScale,                                  // 触发后的值
			AutoScaleType:              utils.StringRef(autoScaleType),              // 存储空间、手动 - 扩容规格、自动 - 扩容规格
			AutoScaleMetric:            utils.StringRef(autoScaleTriggerCondition),  // 触发指标：CPU平均使用率、可用存储空间占比
			MetricTriggerValue:         utils.StringRef(task.TriggerMetricValue),    // 触发值
			AutoScaleTriggerMetricUnit: utils.StringRef(autoScaleTriggerMetricUnit), // 触发值单位
			TriggerConditionThreshold:  task.TriggerMetricDuration,                  // 持续时间
			TriggerConditionComparison: utils.StringRef(triggerConditionComparison),
			MetricValue:                &parsedValue,                         // 触发配置值
			TriggerDateTime:            &task.TriggerDateTime,                // 使用任务创建时间
			TriggerState:               (*model.TaskStatus)(&task.TaskState), // 根据需求设置
			EventReason:                pointer.String(task.EventReason),
			IsPreCheck:                 pointer.Bool(task.IsPreCheck),
			AutoScaleAction:            model.AutoScaleActionPtr(autoScaleAction),
			AutoScaleTriggerEventType:  model.AutoScaleTriggerEventTypePtr(autoScaleTriggerEventType),
		}
		site := b.I18nSvc.GetLanguage(ctx)
		b.setAutoScaleTypeAndMetric(ctx, diskEvent, site)
		ret.DiskAutoScaleEvents = append(ret.DiskAutoScaleEvents, diskEvent)

	}
	log.Info(ctx, "DiskAutoScale-DescribeDiskAutoScaleEvents: ret is %s", utils.Show(ret))
	return ret, nil
}

func (b *diskAutoScaleServiceImpl) setAutoScaleTypeAndMetric(ctx context.Context,
	diskEvent *model.DiskAutoScaleEventItem, site string) {
	if site == "en" {
		diskEvent.AutoScaleType = pointer.String("Auto Scaling - Storage Space")
		diskEvent.AutoScaleMetric = pointer.String("Available Storage Space Ratio")
	}
	log.Info(ctx, "DiskAutoScale-DescribeDiskAutoScaleEvents-site is %s", utils.Show(site))
}

func parseConfig(config string) ([]EnglishNodeInfo, error) {
	if config == "" {
		return nil, nil
	}
	var nodeInfos []EnglishNodeInfo
	err := json.Unmarshal([]byte(config), &nodeInfos)
	if err != nil {
		return nil, err
	}
	return nodeInfos, nil
}

func (b *diskAutoScaleServiceImpl) mapRdsResponseToAutoScalingConfig(ctx context.Context, rresp *rdsModel_v2.DescribeDBAutoScalingConfigResp) *model.DescribeDiskDBAutoScalingConfigResp {
	describeDiskDBAutoScalingConfig := &model.DescribeDiskDBAutoScalingConfig{
		EnableStorageAutoScale: rresp.StorageConfig.EnableStorageAutoScale,
		StorageThreshold:       rresp.StorageConfig.StorageThreshold,
		StorageUpperBound:      rresp.StorageConfig.StorageUpperBound,
	}

	if rresp.StorageMaxTriggerThreshold != nil {
		describeDiskDBAutoScalingConfig.StorageMaxTriggerThreshold = *rresp.StorageMaxTriggerThreshold
	}
	if rresp.StorageMinTriggerThreshold != nil {
		describeDiskDBAutoScalingConfig.StorageMinTriggerThreshold = *rresp.StorageMinTriggerThreshold
	}
	if rresp.StorageMaxCapacity != nil {
		describeDiskDBAutoScalingConfig.StorageMaxCapacity = *rresp.StorageMaxCapacity
	}
	if rresp.StorageMinCapacity != nil {
		describeDiskDBAutoScalingConfig.StorageMinCapacity = *rresp.StorageMinCapacity
	}

	return &model.DescribeDiskDBAutoScalingConfigResp{
		DiskDBAutoScalingConfig: describeDiskDBAutoScalingConfig,
	}
}

// 将构造 InnerDescribeDBAutoScalingTasksReq 的逻辑提取为一个新方法
func (b *diskAutoScaleServiceImpl) buildInnerDescribeDBAutoScalingTasksReq(
	ctx context.Context,
	req *model.DescribeDiskAutoScaleEventsReq,
) (*rdsModel_v2.InnerDescribeDBAutoScalingTasksReq, error) {

	// 初始化请求结构
	rreq := &rdsModel_v2.InnerDescribeDBAutoScalingTasksReq{}
	rreq.InstanceId = req.InstanceId
	// 设置 TaskActions
	rreq.TaskActions = []string{rdsModel_v2.Action_InnerModifyDBAutoStorageScaling.String(), rdsModel_v2.Action_ModifyDBLocalSpecByInplace.String()}

	// 处理分页参数

	if req.PageSize != nil {
		rreq.PageSize = *req.PageSize
	} else {
		rreq.PageSize = 10 // 默认值，可以根据需要修改
	}

	if req.PageNumber != nil {
		rreq.PageNumber = *req.PageNumber
	} else {
		rreq.PageNumber = 1 // 默认值，可以根据需要修改
	}

	// 处理 CreationEndTime
	if req.CreationEndTime != nil && *req.CreationEndTime != "" {
		endSec, err := strconv.ParseInt(*req.CreationEndTime, 10, 64)
		if err != nil {
			log.Warn(ctx, "Cannot parse CreationEndTime: %v", err)
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}
		rreq.CreationEndTime = time.Unix(endSec, 0).UTC().Format(time.RFC3339)
	} else {
		rreq.CreationEndTime = time.Now().UTC().Format(time.RFC3339)
	}

	// 处理 CreationStartTime
	if req.CreationStartTime != nil && *req.CreationStartTime != "" {
		startSec, err := strconv.ParseInt(*req.CreationStartTime, 10, 64)
		if err != nil {
			log.Warn(ctx, "Cannot parse CreationStartTime: %v", err)
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}
		rreq.CreationStartTime = time.Unix(startSec, 0).UTC().Format(time.RFC3339)
	} else {
		// 如果请求里没传或为空字符串，则使用默认逻辑
		rreq.CreationStartTime = time.Now().UTC().AddDate(0, 0, -7).Format(time.RFC3339)
	}

	return rreq, nil
}

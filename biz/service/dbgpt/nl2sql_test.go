package dbgpt

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type NL2SQLServiceTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *NL2SQLServiceTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *NL2SQLServiceTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestNL2SQLTestSuite(t *testing.T) {
	suite.Run(t, new(NL2SQLServiceTestSuite))
}

func (suite *NL2SQLServiceTestSuite) TestNewSQLChatRequest() {
	var req *sqlReq
	req = NewSQLChatRequest(123, 123, "query", nil, false, false)
	suite.Equal("plaintext", req.SQLFormat)

	req = NewSQLChatRequest(123, 123, "query", nil, false, true)
	suite.Equal("markdown", req.SQLFormat)

	req = NewSQLChatRequest(123, 123, "query", nil, true, false)
	suite.Equal("plaintext", req.SQLFormat)

	req = NewSQLChatRequest(123, 123, "query", nil, false, true)
	suite.Equal("markdown", req.SQLFormat)
}

package dbgpt

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"io"
	http2 "net/http"
	syshttp "net/http"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/http"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"go.uber.org/dig"
)

type NewByteBrainNL2SQLServiceIn struct {
	dig.In
	Config            config.ConfigProvider
	Client            http.Client
	RiskControlClient RiskControlService
}

func NewByteBrainNL2SQLService(in NewByteBrainChatServiceIn) NL2SQLService {
	return &sqlHelperChat{
		config:      in.Config,
		client:      in.Client,
		riskControl: in.RiskControlClient,
	}
}

type sqlReq struct {
	ChatID         string `json:"chat_id"`
	MessageID      string `json:"message_id"`
	MessageContext string `json:"context"`
	Query          string `json:"query"`
	//ExternalKnowledge []string    `json:"external_knowledge"`
	DBMetadata     *DBMetadata `json:"db_metadata"`
	Model          string      `json:"model"`
	Stream         bool        `json:"stream"`
	UseExplanation bool        `json:"use_explanation"`
	Task           string      `json:"task"`
	SQLFormat      string      `json:"sql_format"`
}

func (s *sqlReq) SetModel(model string) {
	s.Model = model
}

func (s *sqlReq) SetStream(stream bool) {
	s.Stream = stream
}
func (s *sqlReq) SetUseExplanation(useExplanation bool) {
	s.UseExplanation = useExplanation
}

type DBMetadata struct {
	DBID   string   `json:"db_id"`
	Tables []*Table `json:"tables"`
}

type Table struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Columns     []Column `json:"columns"`
}

type Column struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description"`
	IsPrimary   bool   `json:"is_primary"`
	// ignore
	ForeignKeyReference string `json:"foreign_key_reference"`
}

type sqlResp struct {
	ID     string `json:"id"`
	Answer string `json:"answer"`
}

type sqlHelperChat struct {
	config      config.ConfigProvider
	client      http.Client
	riskControl RiskControlService
}

func (api *sqlHelperChat) Chat(ctx context.Context, req *sqlReq) (string, error) {
	if req.DBMetadata.Tables == nil {
		log.Warn(ctx, "no table metadata is provided")
		req.DBMetadata.Tables = make([]*Table, 0)
	}

	req.SetModel(api.config.Get(ctx).Text2SQLModel)
	resp, err := api.doRequest(ctx, req)
	if err != nil {
		log.Warn(ctx, "request bytebrain error: %s", err)
		return "", err
	}
	if resp.StatusCode != 200 {
		log.Info(ctx, "[dbgpt] request bytebrain chat error, resp: %s", utils.Show(resp))
		return "", errors.New("bytebrain chat server error")
	}

	var res sqlResp
	err = json.NewDecoder(resp.Body).Decode(&res)
	if err != nil {
		log.Info(ctx, "[dbgpt] decode bytebrain chat response error: %s", err)
		return "", err
	}
	log.Info(ctx, "bytebrain response %s", utils.Show(res))

	return res.Answer, nil
}

func (api *sqlHelperChat) ChatStream(ctx context.Context, req *sqlReq, dataCh chan StreamData) error {
	req.SetModel(api.config.Get(ctx).Text2SQLModel)
	resp, err := api.doRequest(ctx, req)
	if err != nil {
		log.Warn(ctx, "request bytebrain error: %s", err)
		return err
	}
	if resp.StatusCode == 500 {
		log.Info(ctx, "[dbgpt] request bytebrain chat error, resp: %s", utils.Show(resp))
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}

	go enterReadDataLoop(resp.Body, dataCh)

	return nil
}

func (api *sqlHelperChat) doRequest(ctx context.Context, req *sqlReq) (*http2.Response, error) {
	req.Model = api.config.Get(ctx).Text2SQLModel
	log.Info(ctx, "send request to bytebrain", utils.Show(req))
	body, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	host := api.config.Get(ctx).ByteBrainChatPath + "/text2sql"
	resp, err := syshttp.Post(host, "application/json", strings.NewReader(string(body)))
	if err != nil {
		return nil, err
	}
	//resp := api.client.Do(
	//	ctx,
	//	"POST",
	//	api.config.Get(ctx).ByteBrainChatPath+"/text2sql",
	//	bytes.NewBuffer(body),
	//	http.WithHeader("content-type", "application/json"),
	//	http.WithTimeout(25*time.Second),
	//)
	return resp, nil
}

func NewSQLChatRequest(chatID, messageID int64, query string, dbMetadata *DBMetadata, isStream bool, withExplanation bool) *sqlReq {
	sqlFormat := "markdown"
	if !withExplanation {
		sqlFormat = "plaintext"
	}

	return &sqlReq{
		ChatID:         strconv.FormatInt(chatID, 10),
		MessageID:      strconv.FormatInt(messageID, 10),
		Query:          query,
		DBMetadata:     dbMetadata,
		Stream:         isStream,
		UseExplanation: withExplanation,
		Task:           "text2sql",
		SQLFormat:      sqlFormat,
	}
}

func enterReadDataLoop(reader io.Reader, dataCh chan StreamData) {
	scanner := bufio.NewScanner(reader)
	for {
		data := readData(scanner)
		select {
		case d := <-data:
			if d.Data == "[DONE]" {
				close(dataCh)
				return
			}
			if d.Err != nil {
				dataCh <- d
				close(dataCh)
				return
			}
			dataCh <- d
		case <-time.After(time.Duration(30) * time.Second):
			close(dataCh)
			return
		}
	}
}

func enterReadDataLoopForAssistant(reader io.Reader, dataCh chan AssistantData) {
	scanner := bufio.NewScanner(reader)
	for {
		data := readDataForAssistant(scanner)
		select {
		case d := <-data:
			if d.state == "end" {
				close(dataCh)
				return
			}
			if d.Err != nil {
				dataCh <- d
				close(dataCh)
				return
			}
			dataCh <- d
		case <-time.After(time.Duration(30) * time.Second):
			close(dataCh)
			return
		}
	}
}

func readDataForAssistant(scanner *bufio.Scanner) <-chan AssistantData {
	dataCh := make(chan AssistantData)
	go func() {
		if ok := scanner.Scan(); !ok {
			dataCh <- AssistantData{
				Err: scanner.Err(),
			}
			return
		}
		data := scanner.Bytes()[:]
		var resp RespForAssistant
		json.Unmarshal(data, &resp)
		dataCh <- AssistantData{
			state:          resp.State,
			Data:           resp.Content,
			RelatedDocName: resp.RelatedDocName,
			RelatedDoc:     resp.RelatedDoc,
			Err:            nil,
		}
	}()
	return dataCh
}

type RespForAssistant struct {
	State          string `json:"state"`
	Content        string `json:"content"`
	RelatedDocName string `json:"related_doc_name"`
	RelatedDoc     string `json:"related_doc"`
}

func readData(scanner *bufio.Scanner) <-chan StreamData {
	dataCh := make(chan StreamData)
	go func() {
		if ok := scanner.Scan(); !ok {
			dataCh <- StreamData{
				Err: scanner.Err(),
			}
			return
		}
		data := scanner.Bytes()[:]
		var resp Resp
		json.Unmarshal(data, &resp)
		dataCh <- StreamData{
			Data: resp.Data,
			Err:  nil,
		}
	}()
	return dataCh
}

type Resp struct {
	Data string `json:"data"`
}

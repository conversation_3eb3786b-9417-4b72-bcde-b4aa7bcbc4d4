package data_migration

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/service/iam"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/tos"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	dslibutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"fmt"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"strconv"
	"strings"
	"time"
)

const (
	TicketFromDataMigration = "migration_ticket"
)

type dataMigrationService struct {
	dbwInstance       dal.DbwInstanceDAL
	workflowDal       dal.WorkflowDAL
	cnf               config.ConfigProvider
	c3cnf             c3.ConfigProvider
	idGen             idgen.Service
	actorClient       cli.ActorClient
	dsSvc             datasource.DataSourceService
	crossAuthSvc      crossauth.CrossServiceAuthorizationService
	iam               iam.IAM
	ticketService     workflow.TicketService
	preCheckDetailDal dal.PreCheckDetailDAL
	i18nSvc           i18n.I18nServiceInterface
	migRepo           repository.MigrationRepo
	userSvc           usermgmt.UserService
}
type NewDataMigrationServiceIn struct {
	dig.In
	WorkflowDal       dal.WorkflowDAL
	DbwInstanceDal    dal.DbwInstanceDAL
	Cnf               config.ConfigProvider
	C3                c3.ConfigProvider
	IdGen             idgen.Service
	ActorClient       cli.ActorClient
	DsSvc             datasource.DataSourceService
	CrossAuthSvc      crossauth.CrossServiceAuthorizationService
	IAM               iam.IAM
	TicketService     workflow.TicketService
	PreCheckDetailDal dal.PreCheckDetailDAL
	I18nSvc           i18n.I18nServiceInterface
	MigRepo           repository.MigrationRepo
	UserSvc           usermgmt.UserService
}

func NewDataMigrationService(d NewDataMigrationServiceIn) DataMigrationService {
	h := &dataMigrationService{
		workflowDal:       d.WorkflowDal,
		dbwInstance:       d.DbwInstanceDal,
		cnf:               d.Cnf,
		c3cnf:             d.C3,
		idGen:             d.IdGen,
		actorClient:       d.ActorClient,
		dsSvc:             d.DsSvc,
		crossAuthSvc:      d.CrossAuthSvc,
		iam:               d.IAM,
		ticketService:     d.TicketService,
		preCheckDetailDal: d.PreCheckDetailDal,
		i18nSvc:           d.I18nSvc,
		migRepo:           d.MigRepo,
		userSvc:           d.UserSvc,
	}
	return h
}

type DataMigrationService interface {
	IsUserExists(ctx context.Context, userId string, tenantId string) (bool, error)
	IsInstanceAvailable(ctx context.Context, tenantId string, instanceId string) (bool, error)

	GetDBAccount(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error)
	GetTicket(ctx context.Context, ticketId string) (*dao.Ticket, error)
	UpdateTicketTaskId(ctx context.Context, ticket *dao.Ticket) error
	GetPreCheckResult(ctx context.Context, ticket *dao.Ticket) (*model.PreCheckMigrationTicketResp, error)

	CreateMigrationTicket(ctx context.Context, req *model.CreateMigrationTicketReq, migrationTicketInfo *model.MigrationTicketInfo) (string, error)
	PreCheckMigrationTicket(ctx context.Context, req *model.PreCheckMigrationTicketReq) (*model.PreCheckMigrationTicketResp, error)
	SubmitMigrationTicket(ctx context.Context, req *model.SubmitMigrationTicketReq) (*model.SubmitMigrationTicketResp, error)
	DescribeMigrationTickets(ctx context.Context, req *model.DescribeMigrationTicketsReq) (*model.DescribeMigrationTicketsResp, error)
	DescribeMigrationTicketDetail(ctx context.Context, req *model.DescribeMigrationTicketDetailReq) (*model.DescribeMigrationTicketDetailResp, error)
	DescribeMigrationPreCheckDetail(ctx context.Context, req *model.DescribeMigrationPreCheckDetailReq) (*model.DescribeMigrationPreCheckDetailResp, error)
	StopMigrationTicket(ctx context.Context, req *model.StopMigrationTicketReq) (*model.StopMigrationTicketResp, error)
}

func (selfService *dataMigrationService) IsUserExists(ctx context.Context, userId string, tenantId string) (bool, error) {
	isUserExists, err := selfService.workflowDal.IsUserExists(ctx, userId, tenantId)
	if err != nil {
		log.Info(ctx, "failed to obtain user information: err：%v", err)
		return false, err
	}
	return isUserExists, nil
}

func (selfService *dataMigrationService) IsInstanceAvailable(ctx context.Context, tenantId string, instanceId string) (bool, error) {
	isInstanceAvailable, err := selfService.workflowDal.IsInstanceAvailable(ctx, tenantId, instanceId)
	if err != nil {
		log.Info(ctx, "failed to obtain instance information: err：%v", err)
		return false, err
	}
	return isInstanceAvailable, nil
}

func (selfService *dataMigrationService) GetDBAccount(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	// 这块需要实例管理dbw_instance的表记录
	instanceInfo, err := selfService.dbwInstance.Get(ctx, ds.InstanceId, ds.Type.String(), ds.LinkType.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		log.Info(ctx, "ticket： get Instance %s info error:%s", ds.InstanceId, err.Error())
		return nil, err
	}
	// 这里需要把密码进行解密
	ds.Password = utils.DecryptData(instanceInfo.DatabasePassword, instanceInfo.InstanceId)
	ds.User = instanceInfo.DatabaseUser
	return ds, nil
}

func (selfService *dataMigrationService) GetTicket(ctx context.Context, ticketId string) (*dao.Ticket, error) {
	return selfService.workflowDal.DescribeByTicketID(ctx, dslibutils.MustStrToInt64(ticketId))
}

func (selfService *dataMigrationService) UpdateTicketTaskId(ctx context.Context, ticket *dao.Ticket) error {
	return selfService.workflowDal.UpdateTicketTaskId(ctx, ticket)
}

func (selfService *dataMigrationService) GetPreCheckResult(ctx context.Context, ticket *dao.Ticket) (*model.PreCheckMigrationTicketResp, error) {
	ticketPreCheckResult, err := selfService.workflowDal.GetPreCheckResult(ctx, ticket.TicketId)
	if err != nil {
		log.Warn(ctx, "get pre check results failed，err:%v ", err.Error())
		return nil, err
	}
	allPass := true
	var checkItems = make([]*model.CheckItem, 0)
	var step int32
	for _, checkResult := range ticketPreCheckResult {
		checkItem := &model.CheckItem{
			Item:   checkResult.Item,
			Memo:   checkResult.Memo,
			Status: model.PreCheckStatus(checkResult.Status),
		}
		switch checkResult.Item {
		case workflow.PreCheckSyntax:
			checkItem.ItemType = model.ItemType_PreCheckSyntax
		case workflow.PreCheckPermission:
			checkItem.ItemType = model.ItemType_PreCheckPermission
		}
		if checkItem.Status != model.PreCheckStatus_Pass {
			allPass = false
		}
		if checkItem.Status != model.PreCheckStatus_Undo {
			step = step + 1
		}
		checkItems = append(checkItems, checkItem)
	}
	resp := &model.PreCheckMigrationTicketResp{
		AllPass:    allPass,
		CheckItems: checkItems,
		Step:       step,
	}
	if step == int32(len(checkItems)) {
		resp.PrecheckFinished = true
		resp.EnableSubmitTicket = true
	}
	selfService.changItemNameEnToCn(resp)
	// i18n
	site := selfService.i18nSvc.GetLanguage(ctx)
	if site == "en" {
		for _, res := range resp.CheckItems {
			res.Item = selfService.i18nSvc.TranslateByConf(res.Item)
		}
	}
	return resp, nil
}
func (selfService *dataMigrationService) CreateMigrationTicket(ctx context.Context, req *model.CreateMigrationTicketReq, migrationTicketInfo *model.MigrationTicketInfo) (string, error) {
	createTime := time.Now().UnixMilli()
	// 拿用户角色
	userRole, err := selfService.GetUserRole(ctx, migrationTicketInfo.UserId, migrationTicketInfo.TenantId, req.InstanceId)
	if err != nil {
		log.Warn(ctx, "get user role err:%v", err)
		return "", fmt.Errorf("create ticket failed: failed to get user role")
	}
	var sqlText string
	if req.GetSqlResultExportParam() != nil {
		sqlText = req.GetSqlResultExportParam().GetSqlText()
	}
	var MigrationTicketConfig = &model.MigrationTicketConfig{
		DBExportParam:        req.GetDBExportParam(),
		SqlResultExportParam: req.GetSqlResultExportParam(),
		ImportParam:          req.GetImportParam(),
	}
	ticketConfig := struct {
		MigrationTicketConfig *model.MigrationTicketConfig `json:"MigrationTicketConfig"`
	}{
		MigrationTicketConfig: MigrationTicketConfig,
	}
	res, err := json.Marshal(ticketConfig)
	if err != nil {
		log.Warn(ctx, "marshal ticketConfig err:%v", err)
		return "", fmt.Errorf("create ticket failed: failed to marshal ticketConfig")
	}
	// 写库
	ticket := &dao.Ticket{
		TicketId:            dslibutils.MustStrToInt64(migrationTicketInfo.TicketId),
		FlowConfigId:        dslibutils.MustStrToInt64(migrationTicketInfo.ApprovalTemplateId),
		WorkflowId:          dslibutils.MustStrToInt64(migrationTicketInfo.WorkflowId),
		ApprovalFlowId:      dslibutils.MustStrToInt64(migrationTicketInfo.ApprovalFlowId),
		TicketType:          int8(req.GetTicketType()),
		TicketStatus:        int8(model.TicketStatus_TicketUndo),
		FlowStep:            0,
		ExecuteType:         int8(req.GetTicketExecuteType()),
		CreateTime:          createTime,
		UpdateTime:          createTime,
		CreateUserId:        migrationTicketInfo.UserId,
		TenantId:            migrationTicketInfo.TenantId,
		CreatedFrom:         TicketFromDataMigration,
		CurrentUserIds:      migrationTicketInfo.UserId,
		CurrentUserRole:     userRole,
		AllOperatorId:       migrationTicketInfo.UserId,
		InstanceType:        req.InstanceType.String(),
		InstanceId:          req.InstanceId,
		SqlText:             sqlText,
		DbName:              req.DatabaseName,
		ExecutableStartTime: int64(0),
		ExecutableEndTime:   int64(0), // 直接执行
		Memo:                req.GetMemo(),
		Title:               req.GetTitle(),
		TicketConfig:        string(res),
	}
	if err = selfService.workflowDal.CreateTicket(ctx, ticket); err != nil {
		errMsg := fmt.Sprintf("create ticket error，ticketId:%d, err:%v", ticket.TicketId, err)
		log.Warn(ctx, errMsg)
		return "", fmt.Errorf("failed to create ticket: internal error")
	}
	log.Info(ctx, "create migration ticket is %v", utils.Show(ticket))
	return strconv.FormatInt(ticket.TicketId, 10), nil

}

func (selfService *dataMigrationService) GetUserRole(ctx context.Context, userId string, tenantId string, instanceId string) (string, error) {
	// 1.拿到用户所有的角色
	roles, err := selfService.workflowDal.GetUserRoles(ctx, userId, tenantId, instanceId)
	if err != nil {
		errMsg := fmt.Sprintf("failed to get user role:%s", err.Error())
		log.Warn(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	return (*roles)[0], nil
}

func (selfService *dataMigrationService) PreCheckMigrationTicket(ctx context.Context, req *model.PreCheckMigrationTicketReq) (*model.PreCheckMigrationTicketResp, error) {
	// 1、获取工单信息
	ticket, err := selfService.GetTicket(ctx, req.TicketId)
	if err != nil {
		log.Warn(ctx, "get ticket %v error is [%v]", req.TicketId, err)
		return nil, err
	}

	var userId = fwctx.GetUserID(ctx)
	if userId == "" {
		userId = fwctx.GetTenantID(ctx)
	}
	// 2、检查工单当前状态是否需要预检查
	if ticket.TicketStatus != int8(model.TicketStatus_TicketUndo) {
		log.Warn(ctx, "ticket %v status is %v, this ticket has already prechecked ,not need precheck", ticket.TicketId, ticket.TicketStatus)
		return selfService.GetPreCheckResult(ctx, &dao.Ticket{TicketId: ticket.TicketId})
	}
	if err = selfService.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
		TicketId:     ticket.TicketId,
		TicketStatus: int8(model.TicketStatus_TicketPreCheck),
		Description:  "ticket start precheck"}); err != nil {
		log.Warn(ctx, "ticket: %v change status error: %v", ticket.TicketId, err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	// 3、需要预检查,预检查分为2步：
	//	3.1 权限预检查
	//  3.2 sql预检查(如果不带SQL语句，这个时候检查是项是1,只有一个权限检查)
	log.Info(ctx, "ticket %v need precheck,ticket is %v", ticket.TicketId, utils.Show(ticket))
	var entrys = make([]*dao.TicketPreCheckDetail, 0)
	// 先校验权限
	permissionRet := make([]*model.CheckItem, 0)
	permissionRet = append(permissionRet, selfService.CheckUserPermission(ctx, ticket))
	log.Info(ctx, "ticket %v check user permission result is %v", ticket.TicketId, permissionRet)
	// a、导入工单或者导出数据库工单,处理不包含sql的情况
	if strings.TrimSpace(ticket.SqlText) == "" {
		log.Info(ctx, "ticket %v sql text is empty", ticket.TicketId)
		// 落库Detail表
		entry := &dao.TicketPreCheckDetail{
			TicketId:              ticket.TicketId,
			TenantId:              fwctx.GetTenantID(ctx),
			SqlText:               ticket.SqlText,
			ExplainResult:         "",
			SyntaxCheckResult:     "",
			PermissionCheckResult: permissionRet[0].Memo,
			SecurityCheckResult:   "",
		}
		entrys = append(entrys, entry)
		if createErr := selfService.preCheckDetailDal.CreatePreCheckDetail(ctx, entrys); createErr != nil {
			log.Warn(ctx, "create pre check detail error:%v", createErr)
		}
		if replaceErr := selfService.workflowDal.ReplaceIntoPreCheckResult(ctx, dslibutils.MustStrToInt64(req.TicketId), permissionRet); replaceErr != nil {
			log.Warn(ctx, "ticket %v replace error %v", ticket.TicketId, replaceErr)
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		selfService.ticketService.ChangePreCheckTicketStatusAndOperator(ctx, TicketDaoToShared(ticket), workflow.TicketExamine, &dao.BpmFlowInfo{})
		return &model.PreCheckMigrationTicketResp{
			AllPass:            true,
			CheckItems:         permissionRet,
			PrecheckFinished:   true,
			Step:               1,
			EnableSubmitTicket: true,
		}, nil
	}
	// b、如果是导出SQL结果集,则有对应的SQL语句
	if err = selfService.checkSqlText(ctx, ticket); err != nil {
		log.Warn(ctx, "check sql text error %v", err)
		// 语法检查失败
		var result = []*model.CheckItem{
			{
				Item:     workflow.PreCheckSyntax,
				Memo:     err.Error(),
				Status:   model.PreCheckStatus_Error,
				ItemType: model.ItemType_PreCheckSyntax,
			},
		}
		// 补充权限检查结果
		result = append(result, permissionRet[0])
		if replaceErr := selfService.workflowDal.ReplaceIntoPreCheckResult(ctx, dslibutils.MustStrToInt64(req.TicketId), result); replaceErr != nil {
			log.Warn(ctx, "ticket %v replace error %v", ticket.TicketId, replaceErr)
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		// 落库Detail表
		entry := &dao.TicketPreCheckDetail{
			TicketId:              ticket.TicketId,
			TenantId:              fwctx.GetTenantID(ctx),
			SqlText:               ticket.SqlText,
			ExplainResult:         "",
			SyntaxCheckResult:     err.Error(),
			PermissionCheckResult: permissionRet[0].Memo,
			SecurityCheckResult:   "",
		}
		entrys = append(entrys, entry)
		if createErr := selfService.preCheckDetailDal.CreatePreCheckDetail(ctx, entrys); createErr != nil {
			log.Warn(ctx, "create pre check detail error:%v", createErr)
		}
		selfService.ticketService.ChangeTicketStatus(ctx, ticket.TicketId, workflow.TicketPreCheckError,
			fmt.Sprintf("check sql text %v error : %v ", ticket.SqlText, err))
		return &model.PreCheckMigrationTicketResp{
			AllPass:            false,
			CheckItems:         result,
			PrecheckFinished:   true,
			Step:               2,
			EnableSubmitTicket: false, // sql不过的话,就不允许提交工单
		}, nil
	}
	log.Info(ctx, "check ticket %v sql text pass", ticket.TicketId)
	// c、导出sql结果集,并且sql检查成功
	var result = []*model.CheckItem{
		{
			Item:     workflow.PreCheckSyntax,
			Memo:     "Pass",
			Status:   model.PreCheckStatus_Pass,
			ItemType: model.ItemType_PreCheckSyntax,
		},
		permissionRet[0],
	}
	if replaceErr := selfService.workflowDal.ReplaceIntoPreCheckResult(ctx, dslibutils.MustStrToInt64(req.TicketId), result); replaceErr != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	entry := &dao.TicketPreCheckDetail{
		TicketId:              ticket.TicketId,
		TenantId:              fwctx.GetTenantID(ctx),
		SqlText:               ticket.SqlText,
		ExplainResult:         "",
		SyntaxCheckResult:     "Pass",
		PermissionCheckResult: permissionRet[0].Memo,
		SecurityCheckResult:   "",
	}
	entrys = append(entrys, entry)
	if createErr := selfService.preCheckDetailDal.CreatePreCheckDetail(ctx, entrys); createErr != nil {
		log.Warn(ctx, "create pre check detail error:%v", createErr)
		return nil, createErr
	}
	// 修改状态为执行中
	selfService.ticketService.ChangePreCheckTicketStatusAndOperator(ctx, TicketDaoToShared(ticket), workflow.TicketExamine, &dao.BpmFlowInfo{})

	return &model.PreCheckMigrationTicketResp{
		AllPass:            true,
		CheckItems:         result,
		PrecheckFinished:   true,
		Step:               2,
		EnableSubmitTicket: true,
	}, nil
}

func (selfService *dataMigrationService) checkSqlText(ctx context.Context, ticket *dao.Ticket) error {
	switch ticket.InstanceType {
	case model.InstanceType_MySQL.String(), model.InstanceType_VeDBMySQL.String():
		//return checkMySQLFormat(ctx, ticket)
		dbType, _ := model.InstanceTypeFromString(ticket.InstanceType)
		return selfService.ExplainSQL(ctx, int64(dbType), ticket.InstanceId, ticket.DbName, ticket.SqlText)
	//case model.InstanceType_Postgres.String(): // Pg语法解析单独写一份
	//	return checkPgFormat(ctx, ticket)
	default:
		return fmt.Errorf("instance type error")
	}
}
func (selfService *dataMigrationService) SubmitMigrationTicket(ctx context.Context, req *model.SubmitMigrationTicketReq) (*model.SubmitMigrationTicketResp, error) {
	res, err := selfService.ticketService.SubmitTicket(ctx, &model.SubmitTicketReq{
		TicketId: req.TicketId,
	})
	if err != nil {
		log.Warn(ctx, "submit ticket %v err %v", req.TicketId, err)
		return nil, err
	}
	return &model.SubmitMigrationTicketResp{
		Code:   res.Code,
		ErrMsg: res.ErrMsg,
	}, nil
}

func (selfService *dataMigrationService) DescribeMigrationTicketDetail(ctx context.Context, req *model.DescribeMigrationTicketDetailReq) (*model.DescribeMigrationTicketDetailResp, error) {
	rreq := &model.DescribeTicketDetailReq{
		TicketId: req.GetTicketId(),
	}
	detail, err := selfService.ticketService.DescribeTicketDetail(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "DescribeTicketDetail error:%s", err)
		return nil, err
	}
	type TicketConfig struct {
		MigrationTicketConfig *model.MigrationTicketConfig `json:"MigrationTicketConfig,omitempty"`
	}
	var ticketConfig = &TicketConfig{}
	log.Info(ctx, "ticket config is %v", detail.TicketConfig)
	if err := json.Unmarshal([]byte(detail.TicketConfig), ticketConfig); err != nil {
		return nil, fmt.Errorf(fmt.Sprintf("unmarshal ticket config error %v", err))
	}
	log.Info(ctx, "ticket config is %v", ticketConfig)
	var expiredAt int64
	if detail.TicketStatus == model.TicketStatus_TicketFinished && strings.TrimSpace(detail.TaskId) != "" {
		task, err := selfService.migRepo.GetTask(ctx, dslibutils.MustStrToInt64(detail.TaskId), fwctx.GetTenantID(ctx))
		if err != nil {
			log.Warn(ctx, "ticket %v get task %v error %v", detail.TicketId, detail.TaskId, err)
			return nil, err
		}
		if task != nil && task.CreatedAt != 0 {
			expiredAt = task.CreatedAt
		}
	}
	if detail.TicketType == model.TicketType_DataMigrationImport && ticketConfig.MigrationTicketConfig != nil && ticketConfig.MigrationTicketConfig.ImportParam != nil {
		selfService.formatImportFileAddress(ctx, req.TicketId, ticketConfig.MigrationTicketConfig)
	}
	return &model.DescribeMigrationTicketDetailResp{
		TicketId:              detail.TicketId,
		TicketType:            detail.TicketType,
		TicketStatus:          detail.TicketStatus,
		TicketExecuteType:     detail.TicketExecuteType,
		CreateUser:            detail.CreateUser,
		CurrentUser:           detail.CurrentUser,
		CreateTime:            detail.CreateTime,
		UpdateTime:            detail.UpdateTime,
		InstanceType:          detail.InstanceType,
		InstanceId:            detail.InstanceId,
		DbName:                detail.DbName,
		SqlText:               detail.SqlText,
		Description:           detail.Description,
		ExecStartTime:         detail.ExecStartTime,
		ExecEndTime:           detail.ExecEndTime,
		Progress:              detail.Progress,
		Memo:                  detail.Memo,
		Title:                 detail.Title,
		WorkflowId:            detail.WorkflowId,
		MigrationTicketConfig: ticketConfig.MigrationTicketConfig,
		TaskId:                detail.TaskId,
		ExpiredTime:           TimestampMSToDateTimeString(expiredAt + 86400000),
	}, nil
}

func (selfService *dataMigrationService) formatImportFileAddress(ctx context.Context, ticketId string, ticketConfig *model.MigrationTicketConfig) {
	tosClient := selfService.initTOSConnection(ctx)

	ticket, err := selfService.workflowDal.DescribeByTicketID(ctx, dslibutils.MustStrToInt64(ticketId))
	if err != nil {
		log.Warn(ctx, "ticket: get ticket err:%s", err.Error())
		return
	}

	bucketName := selfService.c3cnf.GetNamespace(ctx, consts.C3ApplicationNamespace).TOSBucketName

	objectName := ticket.TenantId + "/" + ticket.InstanceId + "/import/" + ticketConfig.ImportParam.SourceName

	log.Info(ctx, "taskType: %s ticket %s bucketName %s,objectName:%s", ticket.TicketType, ticketId, bucketName, objectName)
	url, err := tosClient.DescribeDownloadUrl(ctx, bucketName, objectName)
	if err != nil {
		log.Warn(ctx, "Get ticket %s Exported Url failed %v", ticket.TicketId, err)
		return
	}
	ret := &model.DescribeDbExportDownloadUrlResp{
		FileUrl: url,
	}
	log.Info(ctx, "ticket %s UpFile DownloadUrl is %s", ticketId, ret)
	ticketConfig.ImportParam.SourceFileAddress = &url
	return

}

func (selfService *dataMigrationService) initTOSConnection(ctx context.Context) tos.Client {
	connectionInfo := new(tos.ConnectionInfo)
	c3Cfg := selfService.c3cnf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tosAK := c3Cfg.TOSServiceAccessKey
	tosSK := c3Cfg.TOSServiceSecretKey
	tosRegion := c3Cfg.TOSServiceRegion
	tosEndpoint := c3Cfg.TOSServiceEndpoint
	tosEndpoint = strings.Replace(tosEndpoint, "-s3", "", 1)
	connectionInfo = &tos.ConnectionInfo{
		Endpoint:        tosEndpoint,
		AccessKeyID:     tosAK,
		AccessKeySecret: tosSK,
		Region:          tosRegion,
		BucketName:      c3Cfg.TOSBucketName,
	}
	var client tos.Client
	if selfService.isInnerRegion(utils.GetRegion()) {
		client = tos.NewByteCloudTOSClient(ctx, connectionInfo)
	} else {
		client = tos.NewTOSClient(ctx, connectionInfo)
	}
	return client
}

func (selfService *dataMigrationService) isInnerRegion(region string) bool {
	return region == "cn-north-1-dedicated"
}

func (selfService *dataMigrationService) DescribeMigrationTickets(ctx context.Context, req *model.DescribeMigrationTicketsReq) (*model.DescribeMigrationTicketsResp, error) {
	rreq := &model.DescribeTicketsReq{
		SearchParam: req.SearchParam,
		PageNumber:  req.PageNumber,
		PageSize:    req.PageSize,
		SortBy:      req.SortBy,
		OrderBy:     req.OrderBy,
		ListType:    req.ListType,
		CreateFrom:  dslibutils.StringRef(TicketFromDataMigration),
	}
	tickets, err := selfService.ticketService.DescribeTickets(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "DescribeTicketDetail error:%s", err)
		return nil, err
	}
	if tickets == nil {
		log.Warn(ctx, "DescribeTicketDetail get empty result")
		return nil, fmt.Errorf("DescribeTicketDetail get empty result")
	}
	return &model.DescribeMigrationTicketsResp{
		Tickets: tickets.Tickets,
		Total:   tickets.Total,
	}, nil
}

func (selfService *dataMigrationService) DescribeMigrationPreCheckDetail(ctx context.Context, req *model.DescribeMigrationPreCheckDetailReq) (*model.DescribeMigrationPreCheckDetailResp, error) {
	// 这里需要判断一下,当前工单是不是导出结果集的工单,如果是导出结果集的工单,则需要查询预检查表,否则不需要查询预检查表
	//ticketDetail, err := selfService.DescribeMigrationTicketDetail(ctx, &model.DescribeMigrationTicketDetailReq{TicketId: req.GetTicketId()})
	//if err != nil {
	//	log.Warn(ctx, "ticket %v DescribeMigrationTicketDetail error:%v", req.GetTicketId(), err)
	//	return nil, err
	//}
	//// 不是导出结果集的工单,则不需要查询预检查表
	//if ticketDetail.TicketType != model.TicketType_DataMigrationExportSqlResult {
	//	return &model.DescribeMigrationPreCheckDetailResp{
	//		TicketId:        req.GetTicketId(),
	//		PreCheckDetails: []*model.MigrationPrecheckDetail{},
	//	}, nil
	//}
	rreq := &model.DescribePreCheckDetailReq{
		TicketId: req.GetTicketId(),
	}
	detail, err := selfService.ticketService.DescribePreCheckDetail(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "DescribePreCheckDetail error:%s", err)
		return nil, err
	}
	log.Info(ctx, "describe precheck detail for ticket %v is %v", req.TicketId, utils.Show(detail))

	var minSize = 0
	for _, val := range detail.PreCheckDetails {
		if val.PreCheckItem == model.PreCheckItem_Syntax || val.PreCheckItem == model.PreCheckItem_Permission {
			itemSize := len(val.ItemDetails)
			if minSize > itemSize || minSize == 0 {
				// 这里是为了取最短的，担心out of range
				minSize = itemSize
			}
		}
	}

	var res = make([]*model.MigrationPrecheckDetail, 0)
	for i := 0; i < minSize; i++ {
		res = append(res, &model.MigrationPrecheckDetail{})
	}

	for _, val := range detail.PreCheckDetails {
		if val.PreCheckItem == model.PreCheckItem_Syntax {
			for i := 0; i < minSize; i++ {
				res[i].SQL = val.ItemDetails[i].SQL
				res[i].SyntaxCheckResult_ = val.ItemDetails[i].Result_
			}
		}
		if val.PreCheckItem == model.PreCheckItem_Permission {
			for i := 0; i < minSize; i++ {
				res[i].SyntaxCheckResult_ = val.ItemDetails[i].Result_
			}
		}
	}
	return &model.DescribeMigrationPreCheckDetailResp{
		TicketId:        detail.TicketId,
		PreCheckDetails: res,
	}, nil
}

func (selfService *dataMigrationService) changItemNameEnToCn(resp *model.PreCheckMigrationTicketResp) {
	itemMap := map[string]string{
		workflow.PreCheckSyntax:       workflow.PreCheckSyntaxCn,
		workflow.PreCheckPermission:   workflow.PreCheckPermissionCn,
		workflow.PreCheckExplain:      workflow.PreCheckExplainCn,
		workflow.PreCheckSecurityRule: workflow.PreCheckSecurityRuleCn,
	}
	for _, item := range resp.CheckItems {
		item.Item = itemMap[item.Item]
	}
}

func TicketDaoToShared(ticket *dao.Ticket) *shared.Ticket {
	if ticket == nil {
		return nil
	}
	return &shared.Ticket{
		TicketId:       ticket.TicketId,
		FlowConfigId:   ticket.FlowConfigId,
		WorkflowId:     ticket.WorkflowId,
		TicketType:     int32(ticket.TicketType),
		TicketStatus:   int32(ticket.TicketStatus),
		FlowStep:       ticket.FlowStep,
		ExecuteType:    int32(ticket.ExecuteType),
		CreateTime:     ticket.CreateTime,
		UpdateTime:     ticket.UpdateTime,
		CreateUserId:   ticket.CreateUserId,
		TenantId:       ticket.TenantId,
		CurrentUserIds: ticket.CurrentUserIds,
		InstanceType:   shared.DataSourceType(shared.DataSourceType_value[ticket.InstanceType]),
		InstanceId:     ticket.InstanceId,
		SqlText:        ticket.SqlText,
		Description:    ticket.Description,
		DbName:         ticket.DbName,
		ExecStartTime:  int32(ticket.ExecutableStartTime),
		ExecEndTime:    int32(ticket.ExecutableEndTime),
		TaskId:         ticket.TaskId,
		ApprovalFlowId: ticket.ApprovalFlowId,
		CreateFrom:     ticket.CreatedFrom,
		Memo:           ticket.Memo,
		Title:          ticket.Title,
	}
}

func (selfService *dataMigrationService) StopMigrationTicket(ctx context.Context, req *model.StopMigrationTicketReq) (*model.StopMigrationTicketResp, error) {
	rreq := &model.StopTicketReq{
		TicketId: req.TicketId,
	}
	rresp, err := selfService.ticketService.StopTicket(ctx, rreq)
	if err != nil {
		return nil, err
	}
	return &model.StopMigrationTicketResp{
		Code: rresp.Code, Message: rresp.Message,
	}, nil
}

// CheckPermission 查看当前用户是否拥有权限
func (selfService *dataMigrationService) CheckPermission(ctx context.Context, ticket *dao.Ticket) (bool, error) {
	// 1.判断用户是不是高权限账户，如果是主账户、实例dba、实例owner，这三个中的一个，那么就放行
	isUpperAccount, err := selfService.workflowDal.IsUpperAccount(ctx, ticket.TenantId, ticket.CreateUserId, ticket.InstanceId)
	if err != nil {
		log.Warn(ctx, "failed to get and create user role，err：%v", err)
		return false, err
	}
	log.Info(ctx, "ticket %v isUpperAccount is %v", ticket.TicketId, isUpperAccount)
	if isUpperAccount {
		// 主账号,不检查权限,直接放行
		log.Info(ctx, "ticket %v is created by super account", ticket.TicketId)
		return true, nil
	}
	// 2.不是高权限用户,开始捞用户的实例权限
	switch ticket.TicketType {
	case int8(model.TicketType_DataMigrationExportDB), int8(model.TicketType_DataMigrationExportSqlResult):
		// 导出只查看实例的数据库的查询权限
		if selfService.userSvc.CheckInstancePrivilege(ctx, ticket.InstanceId, ticket.InstanceType, ticket.CreateUserId, ticket.TenantId, model.DbwPrivilegeType_QUERY) {
			return true, nil
		} else {
			// 如果没有实例的查询权限,则判断是否有数据库的查询权限
			if selfService.userSvc.CheckDatabasePrivilege(ctx, ticket.InstanceId, ticket.DbName, ticket.CreateUserId, ticket.TenantId, model.DbwPrivilegeType_QUERY, true) {
				return true, nil
			}
			log.Warn(ctx, "ticket %v has no permission", ticket.TicketId)
			return false, fmt.Errorf("user %v has no privilege to operate instance %v, need instance query privilege or database query privilege. if you need to force submit this ticket, please ignore this error", ticket.CreateUserId, ticket.InstanceId)
		}
	case int8(model.TicketType_DataMigrationImport):
		// 导入需要查看实例的数据库的变更权限或者实例的结构变更权限
		correctFlag := selfService.userSvc.CheckInstancePrivilege(ctx, ticket.InstanceId, ticket.InstanceType, ticket.CreateUserId, ticket.TenantId, model.DbwPrivilegeType_CORRECT)
		structChangeFlag := selfService.userSvc.CheckInstancePrivilege(ctx, ticket.InstanceId, ticket.InstanceType, ticket.CreateUserId, ticket.TenantId, model.DbwPrivilegeType_CORRECT)
		if correctFlag || structChangeFlag {
			log.Info(ctx, "ticket %v has permission correctFlag [%v] structChangeFlag [%v] ", ticket.TicketId, correctFlag, structChangeFlag)
			return true, nil
		} else {
			log.Warn(ctx, "ticket %v has no permission", ticket.TicketId)
			return false, fmt.Errorf("user %v has no privilege to operate instance %v, need instance correct privilege or struct change privilege. if your sql statement only involves the database you have permission to access,or you need to force submit this ticket, please ignore this error", ticket.CreateUserId, ticket.InstanceId)
		}
	default:
		return false, fmt.Errorf("ticket %v type %v is not support", ticket.TicketId, ticket.TicketType)
	}
	return false, fmt.Errorf("ticket %v type %v is not support", ticket.TicketId, ticket.TicketType)
}

type UserPrivilege struct {
	instancePrivilege *[]*dao.UserInstancePrivilege
}

func (selfService *dataMigrationService) getUserAllInstancePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*UserPrivilege, error) {
	allUserPrivilege := &UserPrivilege{}
	// instance
	instancePrivilege, err := selfService.workflowDal.GetUserInstancePrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.instancePrivilege = instancePrivilege
	return allUserPrivilege, nil
}

func (selfService *dataMigrationService) checkInstancePrivilege(instanceId string, instancePrivilege *[]*dao.UserInstancePrivilege) bool {
	return fp.StreamOf(*instancePrivilege).Filter(func(instancePrivilege *dao.UserInstancePrivilege) bool {
		return instancePrivilege.InstanceId == instanceId
	}).Exists()
}

func (selfService *dataMigrationService) CheckUserPermission(ctx context.Context, ticket *dao.Ticket) *model.CheckItem {
	isPermissionPass, err := selfService.CheckPermission(ctx, ticket)
	if isPermissionPass {
		return &model.CheckItem{
			Item:     workflow.PreCheckPermission,
			Memo:     "Pass",
			Status:   model.PreCheckStatus_Pass,
			ItemType: model.ItemType_PreCheckPermission,
		}
	}
	return &model.CheckItem{
		Item:     workflow.PreCheckPermission,
		Memo:     fmt.Sprintf("Permission Not Pass,%v", err),
		Status:   model.PreCheckStatus_Error,
		ItemType: model.ItemType_PreCheckPermission,
	}
}

func (selfService *dataMigrationService) ExplainSQL(ctx context.Context, instanceType int64, instanceId string, database string, sqlText string) error {
	cnf := selfService.cnf.Get(ctx)
	ds := &shared.DataSource{
		Type:             shared.DataSourceType(instanceType),
		LinkType:         shared.Volc,
		ConnectTimeoutMs: cnf.TicketConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.TicketReadTimeout * 1000,
		WriteTimeoutMs:   cnf.TicketWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.TicketIdleTimeout * 1000,
		InstanceId:       instanceId,
		Db:               database,
	}
	// 获取安全管控的账号密码
	ds, err := selfService.ticketService.GetDBAccount(ctx, ds)
	if err != nil {
		log.Warn(ctx, "ticket: get db account err:", err.Error())
		return consts.ErrorOf(model.ErrorCode_ListAccountFail)
	}
	err = selfService.dsSvc.GetDatasourceAddress(ctx, ds)
	if err != nil {
		log.Warn(ctx, "ticket: get db address err:", err.Error())
		return consts.ErrorOf(model.ErrorCode_GetInstanceAddressFailed)
	}
	_, err = selfService.dsSvc.ExplainCommand(ctx, &datasource.ExplainCommandReq{
		Source:  ds,
		Command: sqlText,
	})
	if err != nil {
		log.Warn(ctx, "ticket: explain command err:", err.Error())
		return consts.ErrorWithParam(model.ErrorCode_ExplainCommandError, err.Error())
	}
	return nil
}

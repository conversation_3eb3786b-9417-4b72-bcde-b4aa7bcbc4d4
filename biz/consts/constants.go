package consts

import (
	"os"
	"time"
)

const (
	SessionActorKind         = `Session`
	ConnectionActorKind      = `Conn`
	IDGenActorKind           = `IdGenSeed`
	CounterKind              = `Counter`
	DeployActorKind          = `Deploy`
	UserProtocolKind         = `UserProtocol`
	ShuttleActorKind         = `Shuttle`
	ShuttleMgrActorKind      = `ShuttleMgr`
	DiscoActorKind           = `Disco`
	LogAnalysisActorKind     = `LogAnalysis`
	LogAuditActorKind        = `LogAudit`
	SessionMgrActorKind      = `SessionMgr`
	ApolloActorKind          = `C3Apollo`
	DialogMgrActorKind       = `DialogMgr`
	DialogActorKind          = `Dialog`
	InstanceActorKind        = `Instance`
	TenantMgrKind            = `UserMgr`
	AuditMonitorMgrActorKind = `AuditMonitorMgr`
	AuditMonitorActorKind    = `AuditMonitor`
	PgAuditActorKind         = `PgAudit`
	PgAuditCreateActorKind   = `PgAuditCreateActor`
	AuditDeleteActorKind     = `AuditDeleteActor`

	MysqlAuditCreateActorKind   = `MysqlAuditCreateActor`
	AuditStatisticActorKind     = `AuditCreateActor`
	MysqlFullSqlCreateActorKind = `MysqlFullSqlCreateActorKind`
	MysqlFullSqlDeleteActorKind = `MysqlFullSqlDeleteActorKind`
	RedisAuditCreateActorKind   = `RedisAuditCreateActor`
	MongoAuditCreateActorKind   = `MongoAuditCreateActor`

	InnerMysqlFullSqlCreateActorKind = `InnerMysqlFullSqlCreateActorKind`
	InnerMysqlFullSqlDeleteActorKind = `InnerMysqlFullSqlDeleteActorKind`

	MysqlFullSqlUpgradeTableAggrActorKind = "MysqlFullSqlUpgradeTableAggrActorKind"
	MysqlFullSqlDeleteResourceActorKind   = "MysqlFullSqlDeleteResourceActorKind"
	MysqlFullSqlUpgradeIndexActorKind     = "MysqlFullSqlUpgradeIndexActorKind"

	BillingScheduleCheckActorKind        = "BillingScheduleCheck"
	LogAuditBillingActorKind             = "LogAuditBilling"
	FullSqlBillingActorKind              = "FullSqlBillingActorKind"
	ScheduleInstanceCheckActorKind       = "ScheduleInstanceCheck"
	ScheduleInstanceDetailCheckActorKind = "ScheduleInstanceDetailCheckActorKind"
	SLAAuditMetricReporterActorKind      = "SLAAuditMetricReporterActor"

	SqlTaskActorKind = "SqlTaskActor"

	TaskActorKind              = `TaskActor`
	CleanActorKind             = `CleanActor`
	TaskInspectionActorKind    = `TaskInspectionActor`
	CCLRuleActorKind           = "CCLRuleActor"
	CCLRuleInspectionActorKind = `CCLRuleInspectionActor`

	ExecTicketActorKind          = `ExecTicketActor` // 这个是最开始的普通SQL变更工单
	OnlineDDLTicketActorKind     = `OnlineDDLTicketActor`
	VeDBDDLTicketActorKind       = `VeDBDDLTicketActor`
	InspectionTicketActorKind    = `InspectionTicketActor`
	FreeLockDMLActorKind         = `FreeLockDMLActor`
	ShardingFreeLockDMLActorKind = `ShardingFreeLockDMLActor`
	NormalDDLActorKind           = `NormalDDLActorKind`
	DbwTicketActorKind           = `DbwTicketActorKind`
	OnlineDDLActorKind           = `OnlineDDLActorKind`
	DbwInstanceTicketActorKind   = `DbwInstanceTicketActorKind`

	CronActorKind            = `CronActor`
	InspectionActorKind      = `InspectionActor`
	CheckInspectionActorKind = `CheckInspectionActor`

	BandwidthScaleActorKind = `BandwidthScaleActor`
	AutoScaleActorKind      = `AutoScaleActor`

	SqlAssistantActorKind      = `SqlAssistantActorKind`
	NL2SQLSessionActorKind     = `NL2SQLSessionActor`
	DetectionProducerActorKind = `AbnormalDetectionProducerActor`
	DetectionConsumerActorKind = `AbnormalDetectionConsumerActor`
	CloudMonitorActorKind      = `CloudMonitorActorKind`

	SQLAdvisorActorKind = `SqlAdvisorActorKind`
	SQLReviewActorKind  = `SqlReviewActorKind`

	DataArchiveActorKind       = `DataArchivingActor`
	DataArchiveConfigActorKind = `DataArchivingConfigActor`

	UserMgmtActorKind               = `UserMgmtActorKind`
	DbwInstanceActorKind            = `DbwInstanceActorKind`
	ByteDataActorKind               = `ByteDataActorKind`
	QueryResultActorKind            = `QueryResultActor`
	CommandSetResultBufferActor     = `CommandSetResultBufferActor`
	CommandSetResultBufferActorKind = `CommandSetResultBufferActorKind`
)

const (
	TaskFlowSchedulerActor = `TaskFlowSchedulerActorKind`
	TaskFlowJobActor       = `TaskFlowJobActorKind`
)

const (
	SingletonActorName        = `singleton`
	ApolloActorName           = `singleton`
	DialogSessionMgrActorName = `dialog_session_mgr`
)

const ProductName = "Dbw"
const ServiceName = "dbw"
const CommandSetProgressMax = 100
const InnerDatabase = "byte_rds_meta"
const PgPostgresSystemDatabase = "postgres"
const C3AppID = `infcs.Dbw.dbw_mgr`
const C3ConfigSecretNamePrefix = `dbw-c3-config-secret`
const C3ConfigSecretNamespace = `dbw`
const MgrNamespace = `default`
const TLSEndpointTemplate = "https://tls-%s-inner.ivolces.com"
const TLSSingleFieldMaxSize = 32766
const RDSV1WhiteListNameSuffix = `sole_v1_group_name`
const (
	RDSProxyShowProcessList  = "http://%s/proxy/show_processlist"
	VeDBProxyShowProcessList = "dbatman show processlist"
	RDSProxyShowVersion      = "http://%s/proxy/version"
)
const DBW_DIAGNOSIS_DEFAULT_HINT = " /*+ DBW DAS DEFAULT*/ "
const (
	DialogAggregatedTopNum = 5
	MySQLAccountError      = "using password: yes"
	PostgreAccountError    = "authentication"
	MySQLWhiteListError    = "ip not in white list"
	AccountErrorCn         = "请检查账户名"
)

const (
	TLSDescription           = "由云服务DBW创建"
	RDS_MySQL_Version_V2     = `2022-01-01`
	REGION_ID                = "BDC_REGION_ID"
	RDS_MySQL_Version_V1     = `2018-01-01`
	MySQLShardingAPIVersion2 = `2022-01-01`
)

// 获取每个事务相关的死锁信息正则表达式
const DeadlokcReg = `(\(\d\)\sTRANSACTION:(?s).*?\s\(\d\)\sWAITING\sFOR\sTHIS\sLOCK\sTO\sBE\sGRANTED:(?s).*?\n.*?[\n])`
const ActorSplitSymbol = "|"
const AccountTimeFormat = "2006-01-02 15:04:05"
const NormalTimeFormat = "2006-01-02 15:04:05"

var (
	// 不同服务的父子资源移动 https://bytedance.feishu.cn/wiki/wikcnEfQk3WkGtwxyzQCGpY2RYe#8WjPmm
	SystemRdsTagKey   = "volc:rds_mysql:linkedresource"
	SystemVeDBTagKey  = "volc:vedbm:linkedresource"
	SystemPGTagKey    = "volc:rds_postgresql:linkedresource"
	SystemRedisTagKey = "volc:Redis:linkedresource"
	SystemMongoTagKey = "volc:mongodb:linkedresource"

	// trn:rds_mysql:{{.region}}:{{.account}}:instance/{{.id}}
	RdsMysqlTrn = "trn:rds_mysql:%s:%s:instance/%s"
	// trn:vedbm:${Region}:${Account}:instance/${Id}
	VeDBTrn = "trn:vedbm:%s:%s:instance/%s"
	//
	RdsPostgresTrn = "trn:rds_postgresql:%s:%s:instance/%s"
	// trn:Redis:{{.region}}:{{.account}}:instance/{{.id}}
	RedisTrn = "trn:Redis:%s:%s:instance:%s"
	// trn:mongodb:{{.region}}:{{.account}}:instance/{{.id}}
	MongoTrn = "trn:mongodb:%s:%s:instance:%s"

	// trn:dbw:{{.region}}:{{.account}}:sqlaudit/{{.id}}
	SqlAuditTagResourceTrn = "trn:dbw:%s:%s:sqlaudit/%s"
)

const (
	Mongo_Version_V1 = `2018-01-01`
	Mongo_Version_V2 = `2022-01-01`
)

const (
	Postgres_Version_V1 = `2018-01-01`
	Postgres_Version_V2 = `2022-01-01`
)

const (
	Dts_Version_V1 = `2018-01-01`
	Dts_Version_V2 = `2022-10-01`
)

const (
	DefaultPageNumber int32 = 1
	DefaultPageSize   int32 = 100
)

const (
	DefaultChatName                    = "新对话"
	DefaultUniversalSecGroup           = "universal_default template"
	DefaultMySQLSecGroup               = "mysql default"
	DefaultVeDBMySQLSecGroup           = "vedb_mysql default"
	DefaultPostgresSecGroup            = "postgres default"
	DefaultMSSQLSecGroup               = "SQLSever default"
	DefaultRedisSecGroup               = "redis default"
	DefaultMongoSecGroup               = "mongo default"
	MultiCloudDefaultGroupName         = "多云管理平台默认规则"
	MultiCloudDefaultMySQLSecGroup     = "multi cloud mysql default"
	MultiCloudDefaultVeDBMySQLSecGroup = "multi cloud vedb mysql default"
	MultiCloudDefaultShardingSecGroup  = "multi cloud sharding default"
	MultiCloudDefaultRedisSecGroup     = "multi cloud redis default"
	MultiCloudDefaultMongoSecGroup     = "multi cloud mongo default"

	ByteRdsBOEDefaultGroupName     = "字节云rds测试环境默认规则"
	ByteRdsBOEDefaultGroupNameFile = "byte rds boe default"
	ByteRdsPRDDefaultGroupName     = "字节云rds生产环境默认规则"
	ByteRdsPRDDefaultGroupNameFile = "byte rds prd default"
)

const SecRuleTemplatePath = "conf/security_rule_template/"
const ByteRdsSecRuleTemplatePath = "/opt/tiger/dbwmgr/conf/security_rule_template/"
const SecRuleConfigurationPath = "conf/security_rule_configuration/configuration.json"

const (
	MysqlMetricDataMapPath = "conf/metric_data/mysql_metric_data.json"
)

const ApiJson = "conf/api.json"

const LanguageMap = "conf/i18n/i18n.json"

// 环境变量功能开关（需要重启）
// 停止MQ消费
// MQ_CONSUME_SWITCH=OFF
// mysql审计控制器
// MYSQL_AUDIT_WATCHER_SWITCH=OFF
// vedb控制器
// VEDB_AUDIT_WATCHER_SWITCH=OFF
// pg控制器
// PG_AUDIT_WATCHER_SWITCH=OFF
var (
	MQ_CONSUME_SWITCH          = os.Getenv("MQ_CONSUME_SWITCH") == OFF
	AUDIT_WATCHER_SWITCH_ON    = os.Getenv("AUDIT_WATCHER_SWITCH") == ON || os.Getenv("AUDIT_WATCHER_SWITCH") == ""
	MYSQL_AUDIT_WATCHER_SWITCH = os.Getenv("MYSQL_AUDIT_WATCHER_SWITCH") == OFF
	VEDB_AUDIT_WATCHER_SWITCH  = os.Getenv("VEDB_AUDIT_WATCHER_SWITCH") == OFF
	PG_AUDIT_WATCHER_SWITCH    = os.Getenv("PG_AUDIT_WATCHER_SWITCH") == OFF
	ABNORMAL_DETECTION_SWITCH  = os.Getenv("ABNORMAL_DETECTION_SWITCH") == OFF
	ON                         = "ON"
	OFF                        = "OFF"
)

const (
	StatusSuccess = "Success"
	StatusFailed  = "Failed"
)

const (
	TicketBatchLowest  = 1000
	TicketBatchLimit   = 5000
	TicketRowsForSleep = 10 * TicketBatchLimit
	TicketBatchSleepMs = 500
	TicketDBBatchNum   = 1
)

const (
	AcceptLanguageKey       = "X-Storage-Language"
	BytePlusSiteName        = "BytePlus"
	VolcanoEngineSiteName   = "VolcanoEngine"
	VolcanoEngineMsgProduct = "P00000736"
	BytePlusMsgProduct      = "P00001393"
)

var (
	NL2SQLSessionTimeoutSeconds          int64 = 2 * 3600
	NL2SQLSessionCacheCleanupInterval          = 10 * time.Minute
	NL2SQLSessionCacheExpirationDuration       = time.Duration(NL2SQLSessionTimeoutSeconds)*time.Second - NL2SQLSessionCacheCleanupInterval - 5*time.Minute
)

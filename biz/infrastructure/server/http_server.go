package server

import (
	"context"
	"net/http"
	"reflect"
	"runtime/pprof"
	"strings"
	"sync"

	handlerpkg "code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/metric"
	"code.byted.org/infcs/dbw-mgr/biz/middleware"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/server"
	"code.byted.org/infcs/ds-lib/common/function"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/gin"
	fwmid "code.byted.org/infcs/ds-lib/framework/gin/middleware"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"

	ggin "github.com/gin-gonic/gin"
)

type NewHttpServerIn struct {
	dig.In
	Sys       actorsystem.ActorSystem
	ActorCli  cli.ActorClient
	AutoScale repository.AutoScaleRepo
	Handlers  []*handlerpkg.HTTPHandlerImplementation `group:"http"`
}

// 定义一个结构体
type httpServer struct {
	srv         *gin.HttpEngine
	sys         actorsystem.ActorSystem
	cli         cli.ActorClient
	autoScale   repository.AutoScaleRepo
	lock        sync.Mutex
	handlers    []handler
	handlersMap map[string]ggin.HandlerFunc
}

type handler struct {
	names []string
	fn    ggin.HandlerFunc
}

// NewHttpServer 生成NewHttpServer的函数
func NewHttpServer(in NewHttpServerIn) server.Server {
	resHttp := &httpServer{
		sys:         in.Sys,
		cli:         in.ActorCli,
		autoScale:   in.AutoScale,
		handlersMap: make(map[string]ggin.HandlerFunc),
	}
	for _, h := range in.Handlers {
		log.Info(context.TODO(), "httpServer: handler is %v", utils.Show(h))
		resHttp.AddHandler(h.Impl, h.Alias...)
	}
	return resHttp
}

func (self *httpServer) addHandler(router *ggin.RouterGroup, fn ggin.HandlerFunc, alias ...string) {
	fp.StreamOf(alias).
		Uniq().
		Map(func(api string) string {
			if !strings.HasPrefix(api, `/`) {
				api = `/` + api
			}
			return api
		}).
		Map(func(api string) error {
			router.POST(api, fn)
			return nil
		}).
		Error()
}

// Start 结构体方法
func (h *httpServer) Start() error {
	serverGin := gin.Engine()
	serverGin.Use(middleware.ContextMW)
	serverGin.Use(fwmid.LogMW)
	serverGin.Use(fwmid.RecoverMW)
	h.srv = serverGin
	h.setHandler()
	for _, hd := range h.handlers {
		h.addHandler(h.actionGroup(), hd.fn, hd.names...)
	}
	// 启动一个channel
	//sigs := make(chan os.Signal, 1)
	//signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	go h.srv.Start()
	startPprof()
	// <-sigs
	log.Info(context.TODO(), "go routine over")
	return nil
}

// 后台运行pprof端口
func startPprof() {
	go func() {
		bindingAddress := "0.0.0.0"
		err := http.ListenAndServe(bindingAddress+":6060", nil)
		if err != nil {
			return
		}
	}()
}

func (h *httpServer) Shutdown() {
	ctx := context.Background()
	err := h.srv.Shutdown(ctx)
	if err != nil {
		return
	}
}

func (h *httpServer) setHandler() { // 匿名函数
	h.srv.GET("/getmgrmetricdata", func(ctx *ggin.Context) {
		// 获取线程数据
		var threadProfile = pprof.Lookup("threadcreate")
		metric.GlobalMetricData.MetricsThreadNum.WithLabel(metric.ThreadNumLabel{Component: "mgr"}).Value = threadProfile.Count()
		// 获取此次调用的打点内容
		ResponseMetricData := &metric.RespMgrMetricData{
			RespMgrAPICallNum:      metric.GlobalMetricData.MetricsMgrAPICallNum.MetricPoint(),
			RespMgrAPIResponseTime: metric.GlobalMetricData.MetricsMgrAPIResponseTime.MetricPoint(),
			RespMgrRdsNum:          metric.GlobalMetricData.MetricsMgrRdsNum.MetricPoint(),
			RespThreadNum:          metric.GlobalMetricData.MetricsThreadNum,
			RespMgrSessionNum:      metric.GlobalMetricData.MetricsMgrSessionNum.MetricPoint(),
			RespConnectionsConnNum: metric.GlobalMetricData.MetricsConnectionsConnNum.MetricPoint(),
			RespMgrExecCommandNum:  metric.GlobalMetricData.MetricsMgrExecCommandNum.MetricPoint(),
			RespMgrSlowQueryNum:    metric.GlobalMetricData.MetricsMgrSlowQueryNum.MetricPoint(),
		}
		ctx.JSON(http.StatusOK, ResponseMetricData)
	})

	h.srv.GET("/plb", func(ctx *ggin.Context) {
		log.Info(ctx, "test plb call")
		ctx.JSON(http.StatusOK, "test plb call")
	})
	h.srv.GET("/api/autoscaler", func(ctx *ggin.Context) {
		log.Info(ctx, "test tlb to plb call ")
		ctx.JSON(http.StatusOK, "test plb call")
	})
	h.srv.GET("/dbw/autoscaler", func(ctx *ggin.Context) {
		log.Info(ctx, "test tlb to plb call ")
		ctx.JSON(http.StatusOK, "test plb call")
	})

	h.srv.POST("/api/autoscaler/plb", h.pathAutoScale)
	h.srv.POST("/dbw/autoscaler/plb", h.pathAutoScale)
	h.srv.POST("/AutoScale", h.pathAutoScale)
	h.srv.POST("/action", h.action)
}

func (self *httpServer) action(ctx *ggin.Context) {
	action := ctx.Query(`Action`)
	if fn, ok := self.handlersMap[action]; ok {
		fn(ctx)
		return
	}
	ctx.JSON(http.StatusNotFound, nil)
}

func (self *httpServer) AddHandler(fn interface{}, alias ...string) {
	self.lock.Lock()
	defer self.lock.Unlock()
	self.handlers = append(self.handlers, handler{names: append([]string{function.F(fn).Name()}, alias...), fn: self.convertHandler(fn)})
	self.handlersMap[function.F(fn).Name()] = self.convertHandler(fn)
}

func (self *httpServer) actionGroup() *ggin.RouterGroup {
	return self.srv.Group(`/api/2018-01-01`)
}

func (self *httpServer) convertHandler(fn interface{}) ggin.HandlerFunc {
	var inTypes, outTypes []reflect.Type
	typ := reflect.TypeOf(fn)
	val := reflect.ValueOf(fn)
	fp.StreamOf(fp.NewCounter(typ.NumIn())).Map(func(i int) reflect.Type { return typ.In(i) }).ToSlice(&inTypes)
	fp.StreamOf(fp.NewCounter(typ.NumOut())).Map(func(i int) reflect.Type { return typ.Out(i) }).ToSlice(&outTypes)
	return func(ctx *ggin.Context) {
		input := []reflect.Value{reflect.ValueOf(ctx)}
		var isStreamReq = false
		if len(inTypes) == 2 {
			req, err := self.readPayload(ctx, inTypes[1])
			if err != nil {
				middleware.RenderErr(ctx, err)
				return
			}
			streamReq, ok := req.Interface().(IsStreamReq)
			isStreamReq = ok && streamReq.GetIsStream()
			input = append(input, req)
		}
		if isStreamReq {
			ctx.Header("content-type", "Text/event-stream")
		}
		output := val.Call(input)
		if len(outTypes) == 0 {
			//middleware.RenderSuccess(ctx, nil)
			return
		}
		if err := output[len(outTypes)-1].Interface(); err != nil && err.(error) != nil {
			log.Warn(ctx, "handle error %v", err)
			middleware.RenderErr(ctx, err.(error))
			return
		}
		if isStreamReq {
			return
		}
		if len(outTypes) > 1 {
			middleware.RenderSuccess(ctx, output[0].Interface())
			return
		}
		middleware.RenderSuccess(ctx, nil)
	}
}

func (self *httpServer) readPayload(ctx *ggin.Context, typ reflect.Type) (data reflect.Value, err error) {
	isPtr := typ.Kind() == reflect.Ptr
	if isPtr {
		data = reflect.New(typ.Elem())
	} else {
		data = reflect.New(typ)
	}
	if err = ctx.BindJSON(data.Interface()); err != nil {
		log.Info(ctx, "parse request fail %v object: %v", err, data.Elem().Type())
		return
	}
	if !isPtr {
		data = data.Elem()
	}
	return
}

type IsStreamReq interface {
	GetIsStream() bool
}

package mgr

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure"
	"code.byted.org/infcs/mgr/pkg/mw"

	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/utils"

	"go.uber.org/dig"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"code.byted.org/infcs/mgr/kitex_gen/infcs/mgr/framework"
	kiteCli "code.byted.org/kite/kitex/client"

	metrics "code.byted.org/infcs/dbw-mgr/biz/metric"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
)

type NewRDSMgrOut struct {
	dig.Out
	Provider mgr.Provider `name:"mysql"`
}

func NewRDSMgr(cnf config.ConfigProvider) NewRDSMgrOut {
	return NewRDSMgrOut{Provider: &rdsMgrProvider{cnf: cnf}}
}

type NewVeDBMgrOut struct {
	dig.Out
	Provider mgr.Provider `name:"vedb"`
}

func NewVeDBMgr(cnf config.ConfigProvider) NewVeDBMgrOut {
	return NewVeDBMgrOut{Provider: &vedbMgrProvider{cnf: cnf}}
}

type NewRedisMgrOut struct {
	dig.Out
	Provider mgr.Provider `name:"redis"`
}

func NewRedisMgr(cnf config.ConfigProvider) NewRedisMgrOut {
	return NewRedisMgrOut{Provider: &redisMgrProvider{cnf: cnf}}
}

type NewMongoMgrOut struct {
	dig.Out
	Provider mgr.Provider `name:"mongo"`
}

func NewMongoMgr(cnf config.ConfigProvider) NewMongoMgrOut {
	return NewMongoMgrOut{Provider: &mongoMgrProvider{cnf: cnf}}
}

type NewPgMgrOut struct {
	dig.Out
	Provider mgr.Provider `name:"postgres"`
}

func NewPgMgr(cnf config.ConfigProvider) NewPgMgrOut {
	return NewPgMgrOut{Provider: &pgMgrProvider{cnf: cnf}}
}

type MgrClient struct {
	realcli *client.Client
	product string
}

type commonClient struct {
	cli  *MgrClient
	opts []client.RpcOption
}

type rdsMgrProvider struct {
	cnf  config.ConfigProvider
	cli  mgr.Client
	once sync.Once
}

func (self *rdsMgrProvider) Get() mgr.Client {
	self.once.Do(func() {
		cli, err := client.New(
			client.Options{
				Product:    framework.Product_RDS_MySQL,
				SvcAddress: getWebControllerAddress(self.cnf),
			},
			kiteCli.WithRPCTimeout(25*time.Second),
			kiteCli.WithConnectTimeout(5*time.Second),
			kiteCli.WithSuite(mw.NewRateLimitSuite(infrastructure.ProductName, infrastructure.PodName, infrastructure.CluseterName)),
		)
		if err != nil {
			panic(err)
		}
		realCli := &MgrClient{realcli: cli, product: framework.Product_RDS_MySQL.String()}
		adminCli := &commonClient{
			cli: realCli,
			opts: []client.RpcOption{
				client.WithTenantID(`1`),
				client.WithExtra(map[string]string{"X-Rdsmgr-Source": "dbw"})}}
		self.cli = mgr.NewClientDecorator(&commonClient{cli: realCli, opts: []client.RpcOption{client.WithExtra(map[string]string{"X-Rdsmgr-Source": "dbw"})}}).
			DecorateCall(func(next mgr.CallFunc) mgr.CallFunc {
				return func(ctx context.Context, action string, req interface{}, resp interface{}, opts ...client.RpcOption) error {
					if action == rdsModel.Action_DescribeDBInstanceConnection.String() {
						return adminCli.Call(ctx, action, req, resp, opts...)
					} else {
						return next(ctx, action, req, resp, opts...)
					}
				}
			}).
			Export()
	})
	return self.cli
}

func (self *commonClient) Call(
	ctx context.Context,
	action string,
	req interface{},
	respPtr interface{},
	opts ...client.RpcOption) (err error) {
	var callOption []client.RpcOption
	callOption = append(callOption, self.opts...)
	callOption = append(callOption, opts...)

	start := time.Now()
	end := start
	defer func() {
		mgrApiCallResult := "success"
		if err != nil {
			mgrApiCallResult = "failed"
		}
		cost := end.Sub(start) / time.Millisecond
		metrics.
			GlobalMetricData.
			MetricsMgrAPICallNum.
			IncrWithLabel(metrics.MgrAPICallNumLabel{APIName: action, Product: self.cli.product, APICallResult: mgrApiCallResult})
		metrics.
			GlobalMetricData.
			MetricsMgrAPIResponseTime.
			SetWithLabel(cost.Milliseconds(), metrics.MgrAPIResponseTimeLabel{Product: self.cli.product, APIName: action})
	}()
	_, file0, line0, _ := runtime.Caller(3)
	log.InfoS(ctx, "[Mgr] called third party service",
		"action", action,
		"request", utils.Show(req),
		"realCall", fmt.Sprintf("%s:%d", filepath.Base(file0), line0),
	)
	resp := self.cli.realcli.Call(ctx, action, req, callOption...)
	if resp == nil {
		log.Info(ctx, "empty response from %s", action)
		return nil
	}
	end = time.Now()

	if resp.Error() != nil {
		err = resp.Error()
		log.WarnS(ctx, "[Mgr] called third party service",
			"action", action,
			"code", resp.Code(),
			"error", err,
			"cost", fmt.Sprintf("%dms", end.Sub(start)/time.Millisecond),
		)
		//获取到第三方返回的报错后，将报错信息封装为DBW业务错误码standardError进行返回
		return consts.BuildThirdPartyStandardError(err.Error())
	}
	if respPtr == nil {
		// 如果为空，不需要后面的打印
		return nil
	}
	if err = resp.Unmarshal(respPtr); err != nil {
		log.Warn(ctx, "err from %s %v", action, err)
		return
	}
	log.InfoS(ctx, "[Mgr] called third party service",
		"action", action,
		"response", utils.Show(respPtr),
		"cost", fmt.Sprintf("%dms", end.Sub(start)/time.Millisecond),
	)
	return
}

type vedbMgrProvider struct {
	cnf  config.ConfigProvider
	cli  mgr.Client
	once sync.Once
}

func (self *vedbMgrProvider) Get() mgr.Client {
	self.once.Do(func() {
		cli, err := client.New(
			client.Options{
				Product:    framework.Product_ByteNDB,
				SvcAddress: getWebControllerAddress(self.cnf),
			},
			kiteCli.WithRPCTimeout(15*time.Second),
			kiteCli.WithConnectTimeout(5*time.Second),
			kiteCli.WithSuite(mw.NewRateLimitSuite(infrastructure.ProductName, infrastructure.PodName, infrastructure.CluseterName)),
		)
		if err != nil {
			panic(err)
		}
		realCli := &MgrClient{realcli: cli, product: framework.Product_ByteNDB.String()}
		self.cli = mgr.NewClientDecorator(
			&commonClient{
				cli:  realCli,
				opts: []client.RpcOption{client.WithExtra(map[string]string{"srcProduct": "Dbw"})},
			}).
			DecorateCall(func(next mgr.CallFunc) mgr.CallFunc {
				return func(ctx context.Context, action string, req interface{}, resp interface{}, opts ...client.RpcOption) error {
					return next(ctx, action, req, resp, opts...)
				}
			}).
			Export()
	})
	return self.cli
}

type redisMgrProvider struct {
	cnf  config.ConfigProvider
	cli  mgr.Client
	once sync.Once
}

func (self *redisMgrProvider) Get() mgr.Client {
	self.once.Do(func() {
		cli, err := client.New(
			client.Options{
				Product:    framework.Product_Redis,
				SvcAddress: getWebControllerAddress(self.cnf),
			},
			kiteCli.WithRPCTimeout(15*time.Second),
			kiteCli.WithConnectTimeout(5*time.Second),
			kiteCli.WithSuite(mw.NewRateLimitSuite(infrastructure.ProductName, infrastructure.PodName, infrastructure.CluseterName)),
		)
		if err != nil {
			panic(err)
		}
		realCli := &MgrClient{realcli: cli, product: framework.Product_Redis.String()}
		self.cli = mgr.NewClientDecorator(&commonClient{cli: realCli}).
			DecorateCall(func(next mgr.CallFunc) mgr.CallFunc {
				return func(ctx context.Context, action string, req interface{}, resp interface{}, opts ...client.RpcOption) error {
					return next(ctx, action, req, resp, opts...)
				}
			}).
			Export()
	})
	return self.cli
}

type pgMgrProvider struct {
	cnf  config.ConfigProvider
	cli  mgr.Client
	once sync.Once
}

func (self *pgMgrProvider) Get() mgr.Client {
	self.once.Do(func() {
		cli, err := client.New(
			client.Options{
				Product:    framework.Product_RDS_PostgreSQL,
				SvcAddress: getWebControllerAddress(self.cnf),
			},
			kiteCli.WithRPCTimeout(15*time.Second),
			kiteCli.WithConnectTimeout(5*time.Second),
			kiteCli.WithSuite(mw.NewRateLimitSuite(infrastructure.ProductName, infrastructure.PodName, infrastructure.CluseterName)),
		)
		if err != nil {
			panic(err)
		}
		realCli := &MgrClient{realcli: cli, product: framework.Product_RDS_PostgreSQL.String()}
		self.cli = mgr.NewClientDecorator(&commonClient{cli: realCli}).
			DecorateCall(func(next mgr.CallFunc) mgr.CallFunc {
				return func(ctx context.Context, action string, req interface{}, resp interface{}, opts ...client.RpcOption) error {
					return next(ctx, action, req, resp, opts...)
				}
			}).
			Export()
	})
	return self.cli
}

type mongoMgrProvider struct {
	cnf  config.ConfigProvider
	cli  mgr.Client
	once sync.Once
}

func (self *mongoMgrProvider) Get() mgr.Client {
	self.once.Do(func() {
		cli, err := client.New(
			client.Options{
				Product:    framework.Product_MongoDB,
				SvcAddress: getWebControllerAddress(self.cnf),
			},
			kiteCli.WithRPCTimeout(15*time.Second),
			kiteCli.WithConnectTimeout(5*time.Second),
			kiteCli.WithSuite(mw.NewRateLimitSuite(infrastructure.ProductName, infrastructure.PodName, infrastructure.CluseterName)),
		)
		if err != nil {
			panic(err)
		}
		realCli := &MgrClient{realcli: cli, product: framework.Product_MongoDB.String()}
		self.cli = mgr.NewClientDecorator(&commonClient{cli: realCli}).
			DecorateCall(func(next mgr.CallFunc) mgr.CallFunc {
				return func(ctx context.Context, action string, req interface{}, resp interface{}, opts ...client.RpcOption) error {
					return next(ctx, action, req, resp, opts...)
				}
			}).
			Export()
	})
	return self.cli
}

type NewDtsMgrOut struct {
	dig.Out
	Provider mgr.Provider `name:"dts"`
}

func NewDtsMgr(cnf config.ConfigProvider) NewDtsMgrOut {
	return NewDtsMgrOut{Provider: &dtsMgrProvider{cnf: cnf}}
}

type dtsMgrProvider struct {
	cnf  config.ConfigProvider
	cli  mgr.Client
	once sync.Once
}

func (self *dtsMgrProvider) Get() mgr.Client {
	self.once.Do(func() {
		cli, err := client.New(
			client.Options{
				Product:    framework.Product_DTS,
				SvcAddress: getWebControllerAddress(self.cnf),
			},
			kiteCli.WithRPCTimeout(15*time.Second),
			kiteCli.WithConnectTimeout(5*time.Second),
			kiteCli.WithSuite(mw.NewRateLimitSuite(infrastructure.ProductName, infrastructure.PodName, infrastructure.CluseterName)),
		)
		if err != nil {
			panic(err)
		}
		realCli := &MgrClient{realcli: cli, product: framework.Product_DTS.String()}
		self.cli = mgr.NewClientDecorator(&commonClient{cli: realCli}).
			DecorateCall(func(next mgr.CallFunc) mgr.CallFunc {
				return func(ctx context.Context, action string, req interface{}, resp interface{}, opts ...client.RpcOption) error {
					return next(ctx, action, req, resp, opts...)
				}
			}).
			Export()
	})
	return self.cli
}

type NewDairMgrOut struct {
	dig.Out
	Provider mgr.Provider `name:"dair"`
}

func NewDairMgr(cnf config.ConfigProvider) NewDairMgrOut {
	return NewDairMgrOut{Provider: &dairMgrProvider{cnf: cnf}}
}

type dairMgrProvider struct {
	cnf  config.ConfigProvider
	cli  mgr.Client
	once sync.Once
}

func (self *dairMgrProvider) Get() mgr.Client {
	self.once.Do(func() {
		cli, err := client.New(
			client.Options{
				Product:    framework.Product_RDS_MySQL,
				SvcAddress: getWebControllerAddress(self.cnf),
			},
			kiteCli.WithRPCTimeout(15*time.Second),
			kiteCli.WithConnectTimeout(5*time.Second),
			kiteCli.WithSuite(mw.NewRateLimitSuite(infrastructure.ProductName, infrastructure.PodName, infrastructure.CluseterName)),
		)
		if err != nil {
			panic(err)
		}
		realCli := &MgrClient{realcli: cli, product: framework.Product_RDS_MySQL.String()}
		self.cli = mgr.NewClientDecorator(&commonClient{cli: realCli}).
			DecorateCall(func(next mgr.CallFunc) mgr.CallFunc {
				return func(ctx context.Context, action string, req interface{}, resp interface{}, opts ...client.RpcOption) error {
					return next(ctx, action, req, resp, opts...)
				}
			}).
			Export()
	})
	return self.cli
}

type NewMetaRDSMgrOut struct {
	dig.Out
	Provider mgr.Provider `name:"meta_rds"`
}

func NewMetaRDSMgr(cnf config.ConfigProvider) NewMetaRDSMgrOut {
	return NewMetaRDSMgrOut{Provider: &metaRDSMgrProvider{cnf: cnf}}
}

type metaRDSMgrProvider struct {
	cnf  config.ConfigProvider
	cli  mgr.Client
	once sync.Once
}

func (mp *metaRDSMgrProvider) Get() mgr.Client {
	mp.once.Do(func() {
		cli, err := client.New(
			client.Options{
				ProductStr: "Meta_MySQL",
				SvcAddress: getWebControllerAddress(mp.cnf),
			},
			kiteCli.WithRPCTimeout(15*time.Second),
			kiteCli.WithConnectTimeout(5*time.Second),
			kiteCli.WithSuite(mw.NewRateLimitSuite(infrastructure.ProductName, infrastructure.PodName, infrastructure.CluseterName)),
		)
		if err != nil {
			panic(err)
		}
		realCli := &MgrClient{realcli: cli}
		mp.cli = &commonClient{cli: realCli, opts: []client.RpcOption{client.WithTenantID(`1`)}}
	})
	return mp.cli
}

func getClusterWebControllerAddress() string {
	return os.Getenv("BDC_WEB_CONTROLLER_SERVICE") + ".default.svc." + os.Getenv("BDC_K8S_DOMAIN") +
		":" + os.Getenv("BDC_WEB_CONTROLLER_PORT")
}

func getRegionWebControllerAddress() string {
	return os.Getenv("BDC_REGION_WEB_CONTROLLER_SERVICE") + ":" + os.Getenv("BDC_REGION_WEB_CONTROLLER_PORT")
}

func getWebControllerAddress(cnf config.ConfigProvider) string {
	ctx := context.Background()
	if cnf.Get(ctx).WebControllerLevel == "cluster" {
		return getClusterWebControllerAddress()
	} else if cnf.Get(ctx).WebControllerLevel == "region" || cnf.Get(ctx).WebControllerLevel == "" {
		return getRegionWebControllerAddress()
	} else {
		panic("the param of the `WebControllerLevel` not right")
	}
}

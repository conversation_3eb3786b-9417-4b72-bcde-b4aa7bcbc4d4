package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"reflect"
	"strconv"
	strs "strings"

	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	volcroute "code.byted.org/infcs/lib-mgr-common/multi/route"

	"github.com/qjpcpu/fp"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
)

const PARAM_TAG = "param"

type Config struct {
	MaxConnectionPerDataSource int64    `default:"300" param:"每个数据源允许最大连接数" tcc:"max_connection_per_datasource"`
	MaxCommandResultCount      int64    `default:"3000" param:"单次查询最大返回行数" tcc:"max_command_result_count"`
	MaxCommandResultBytes      int64    `param:"单次查询最大返回字节数" tcc:"max_command_result_bytes"`
	MaxCommandResultCellBytes  int64    `default:"3000" param:"单次查询最大返回行的元素的大小" tcc:"max_command_result_cell_bytes"`
	CommandResultTimeout       int64    `param:"单次查询超时时间,单位秒" tcc:"command_result_timeout"`
	SessionTimeout             int64    `default:"1800" param:"会话超时时间,单位秒" tcc:"session_timeout"`
	ConnectionConnectTimeout   int64    `default:"5" param:"连接超时时间,单位秒"`
	ConnectionReadTimeout      int64    `default:"60" param:"读超时时间,单位秒" tcc:"connection_read_timeout"`
	ConnectionWriteTimeout     int64    `default:"30" param:"写超时时间,单位秒" tcc:"connection_write_timeout"`
	ConnectionIdleTimeout      int64    `default:"30" param:"连接空闲超时时间,单位秒" tcc:"connection_idle_timeout"`
	MaxCommandLength           int64    `default:"102400" param:"支持提交最大命令长度" tcc:"max_command_length"`
	MaxLoginFailCount          int64    `default:"5" param:"登录失败最大次数" tcc:"max_login_fail_count"`
	MaxLoginFailCDSeconds      int64    `default:"60" param:"登录失败冷却时间,单位秒"`
	EnableMySQLDelimiter       bool     `default:"true" param:"MySQL是否支持自定义DELIMITER"`
	EnableVeDB                 bool     `default:"true" param:"是否启用VeDB"`
	DisablePG                  bool     `yaml:"disable_pg" default:"false" param:"是否不启用PG"`
	DBLessTenantIdList         []string `yaml:"db_less_tenant_id_list" param:"启用不落库SQL查询的租户ID集合"`
	EnableDAIR                 bool     `yaml:"enable_dair" default:"true" param:"是否启用DAIR"`
	RDSMgrAddress              string   `yaml:"rds_mgr_address"`
	MetaRDSMgrAddress          string   `yaml:"meta_rds_mgr_address"`
	VeDBMgrAddress             string   `yaml:"vedb_mgr_address"`
	RedisMgrAddress            string   `yaml:"redis_mgr_address"`
	MongoMgrAddress            string   `yaml:"mongo_mgr_address"`
	PgMgrAddress               string   `yaml:"pg_mgr_address"`
	InfraMgrAddress            string   `yaml:"infra_mgr_address"`
	UpgradeMgrAddress          string   `yaml:"upgrade_mgr_address"`
	MgrSentryDSN               string   `yaml:"mgr_sentry_dsn" param:"mgr sentry dsn 地址"`
	ConnectionsSentryDSN       string   `yaml:"connections_sentry_dsn" param:"connections sentry dsn 地址"`
	CollectorDSN               string   `yaml:"collector_dsn" param:"采集器报警dsn地址" tcc:"collector_dsn"`
	BlackRegions               []string `default:"[]" param:"dbw region黑名单"`
	/* deploy related */
	DeployInfo                       []*shared.ClusterInfo `param:"部署信息"`
	ConnectionsReplica               int64                 `default:"3" param:"connections 模块 replica"`
	ConnectionsVersionSetID          int                   `param:"connections模块vs id"`
	ConnectionsImage                 string                `param:"connections模块镜像"`
	MonitorReplicas                  int64                 `default:"1" param:"monitor 模块副本数"`
	MonitorImage                     string                `param:"monitor镜像"`
	MonitorVersionSetID              int                   `param:"monitor模块vs id"`
	CollectorReplicas                int64                 `default:"1" param:"collector 模块副本数"`
	CollectorImage                   string                `param:"collector镜像"`
	CollectorVersionSetID            int                   `param:"collector模块vs id"`
	SyncerReplicas                   int64                 `default:"1" param:"Syncer 模块副本数"`
	SyncerImage                      string                `param:"Syncer镜像"`
	SyncerVersionSetID               int                   `param:"Syncer模块vs id"`
	UserEnv                          map[string]string     `param:"自定义环境变量"`
	EnableVeDBNewWhiteList           bool                  `yaml:"enable_vedb_new_white_list" default:"false" param:"启用vedb新版本的白名单"`
	IAMProfile                       string                `yaml:"iam_profile" default:"" param:"iam地址标识"`
	ServiceLinkRule                  string                `yaml:"service_link_rule" default:"" param:"跨服务授权规则"`
	ServiceLinkRuleAudit             string                `yaml:"service_link_rule_sql_audit" default:"DBWAuditLogArchiveTLSRole" param:"跨服务授权规则audit"`
	ServiceLinkRuleDataMigration     string                `yaml:"service_link_rule_data_migration" default:"DBWDataMigrationTOSRole" param:"跨服务授权规则数据dataMigration"`
	TempServiceLinkRuleDataMigration string                `yaml:"temp_service_link_rule_data_migration" default:"TempDBWDataMigrationTOSRole" param:"临时跨服务授权规则数据dataMigration"`
	NodePool                         string                `yaml:"node_pool" param:"节点池"`
	SessionPoolMaxIdleSeconds        int64                 `yaml:"session_pool_max_idle_seconds" default:"1500" param:"session池中session的最大空闲时间"`
	SessionPoolMaxUsedSeconds        int64                 `yaml:"session_pool_max_used_seconds" default:"1500" param:"session池中session分配未归还的最大时间"`
	SessionMgrCleanIntervalSeconds   int64                 `yaml:"sessionmgr_clean_interval_seconds" default:"43200" param:"sessionmgr定时清理的周期"`
	VPCServiceEndpoint               string                `yaml:"vpc_service_endpoint" default:"volcengineapi-boe-stable.byted.org" param:"获取 VPC 信息的访问地址"`
	TOPServiceAccessKey              string                `yaml:"top_service_access_key" default:"" param:"访问 TOP 网关的 access key"`
	TOPServiceSecretKey              string                `yaml:"top_service_secret_key" default:"" param:"访问 TOP 网关的 secret key"`
	CloudMonitorEndpoint             string                `yaml:"cloud_monitor_endpoint" default:"" param:"云监控的endpoint"`
	EnableVeDBNewOpenAPI             bool                  `yaml:"enable_vedb_new_open_api" default:"false" param:"启用vedb新版本的OpenAPI"`
	EnableRDSNewWhiteList            bool                  `yaml:"enable_rds_new_white_list" default:"false" param:"启用rds新版本的白名单"`
	EnableRemoveWhiteList            bool                  `yaml:"enable_remove_white_list" default:"true" param:"退出会话时是否解绑白名单"`
	EnableRDSMySQLNewOpenAPI         bool                  `yaml:"enable_mysql_new_openapi" default:"false" param:"启用RDS_MySQL新版本OpenAPI"`
	// shuttlemgr inspection timeout
	ShuttleMgrTimeoutSeconds        uint64 `default:"1800" param:"shuttlemgr检查过期shuttle的超时时间"`
	ShuttleExpiredSeconds           int64  `default:"86400" param:"shuttle最大存活的超时时间"`
	DialogCollectionIntervalSeconds int64  `yaml:"dialog_collection_interval_seconds" default:"15" param:"实例会话采集间隔"`
	TLSDialogDetailTopic            string `yaml:"tls_dialog_detail_topic" default:"1e2b7dce-224f-4cd3-9893-a17994f2156a" param:"tls实例会话topic"`
	TLSEngineStatusTopic            string `yaml:"tls_engine_status_topic" default:"7a6f5908-e2ba-4c7a-a0f8-54816f402b48" param:"tls实例引擎状态topic"`
	TlsLockCurrentWaitsTopic        string `yaml:"tls_lock_current_waits_topic" default:"aee95257-4577-46eb-8fd1-5c1f6d0c82da" param:"tls节点行锁信息topic"`
	TLSDialogDetailTopicV2          string `yaml:"tls_dialog_detail_topic_v2" default:"{\"VeDBMySQL\":\"b289ae60-e8ca-461c-a4d9-7c373422f9b7\",\"MySQL\":\"1e2b7dce-224f-4cd3-9893-a17994f2156a\"}" param:"tls实例会话topic V2版本"`
	TLSEngineStatusTopicV2          string `yaml:"tls_engine_status_topic_v2" default:"{\"VeDBMySQL\":\"5a5952ce-4b18-49be-9945-52179d9ebf78\",\"MySQL\":\"7a6f5908-e2ba-4c7a-a0f8-54816f402b48\"}" param:"tls实例引擎状态topic V2版本"`
	TLSTrxAndLockTopic              string `yaml:"tls_trx_and_lock_topic" default:"{\"VeDBMySQL\":\"5a5952ce-4b18-49be-9945-52179d9ebf78\",\"MySQL\":\"7a6f5908-e2ba-4c7a-a0f8-54816f402b48\"}" param:"tls 事务与锁快照"`

	// component
	ComponentsDeployInfo *shared.Components `yaml:"components" param:"dbw组件列表"`
	// 镜像
	AuditPodInitContainerImage  string   `yaml:"init_image" default:"hub.byted.org/rds-tob/init-packetbeat:dh" param:"initContainer镜像名"`
	AuditPodPacketbeatImage     string   `yaml:"packetbeat_image" default:"hub.byted.org/rds-tob/pb:dh" param:"packetbeat镜像名"`
	InternalIPs                 string   `yaml:"internal_ips" default:"*********/8" param:"内部ip"`
	AuditListenPorts            string   `yaml:"audit_listen_ports" default:"3679,3680,3681,3682,3683,3684,3685,3686,3687,3688,3689,3690,3691,3692,3693,3694,3695,2335" param:"审计pod监听proxy的端口"`
	InternalAccounts            []string `yaml:"internal_accounts" param:"内部账号"`
	DBInternalUsers             string   `yaml:"db_internal_users" default:"{\"MetaMySQL\":\"dbw_admin\",\"VeDBMySQL\":\"dbw_admin,internal_admin_user,event_scheduler,mysql.infoschema, mysql.session, mysql.sys,system user\",\"MySQL\":\"repl,byte_rds_agent,byte_rds_backup,byte_rds_proxy,dbw_admin,byte_rds_admin,byte_rds_heartbeat,system user,event_scheduler\",\"MySQLSharding\":\"repl,byte_rds_agent,byte_rds_backup,dbw_admin,byte_rds_admin,byte_rds_heartbeat,system user,event_scheduler\",\"Postgres\":\"dbw_admin,postgres,ccp_monitoring,byte_rds_repl,byte_rds_proxy,primaryuser\"}" tcc:"db_internal_users" param:"数据库内部账号"`
	AuditCheckIntervalMinutes   int64    `yaml:"audit_check_interval_minutes" default:"10" param:"审计定时检查时间间隔"`
	AuditJobMaxRetryTimes       int64    `yaml:"audit_job__max_retry_times" default:"5" param:"审计job最大创建次数"`
	AuditPodMaxPendingMinutes   int64    `yaml:"audit_pod__max_pending_minutes" default:"10" param:"审计pod最大pending时间"`
	AuditPodCpuFixedRequest     bool     `yaml:"audit_pod_cpu_fixed_request" default:"true" param:"审计pod cpu request是否为固定值"`
	AuditPodCpuFixedRequestSize int64    `yaml:"audit_pod_cpu_fixed_request_size" default:"200" param:"审计pod cpu request如果为固定值的大小，单位m"`
	AuditPodCpuLimitRate        int64    `yaml:"audit_pod_cpu_limit_rate" default:"100" param:"审计pod cpu limit缩放比例"`

	AuditSqlInsightInstanceList []string `yaml:"audit_sql_insight_instance_list" param:"启用审计全量sql分析mysql-instance-id "`

	ByteBrainServerReplicas int64                 `default:"3" param:"byteBrain server 副本数"`
	ByteBrainImage          string                `param:"byteBrain镜像"`
	ByteBrainDeployInfo     []*shared.ClusterInfo `param:"byteBrain 部署信息"`
	ByteBrainAddress        string                `param:"byteBrain ip:port"`
	// boe/boe_stable/pro
	VolcProfile          string `yaml:"volc_profile" default:"boe_stable" param:"调用火山引擎火种服务环境"`
	VolcServiceAccountAK string `yaml:"volc_service_account_ak" default:"" param:"火山DBW服务AK"`
	VolcServiceAccountSK string `yaml:"volc_service_account_sk" default:"" param:"火山DBW服务SK"`

	EnableBillingTenantIDList     []string `yaml:"enable_billing_tenant_id_list" param:"启用计费租户id "`
	DisableBillingTenantIDList    []string `yaml:"disable_billing_tenant_id_list" param:"禁用计费租户id "`
	BillingAK                     string   `yaml:"billing_ak" param:"火山计费 AK"`
	BillingSK                     string   `yaml:"billing_sk" param:"火山计费 SK"`
	BillingCluster                string   `yaml:"billing_cluster" param:"火山计费消息Cluster"`
	BillingTopic                  string   `yaml:"billing_topic" param:"火山计费消息Topic"`
	BillingConsumerGroup          string   `yaml:"billing_consumer_group" param:"火山计费消息消费组， 使用时会在该字段后加region"`
	BillingReleaseInstance        bool     `yaml:"billing_release_instance" param:"计费，收到释放消息后，是否需要取消任务"`
	EnableBillingScheduleCheck    bool     `yaml:"EnableBillingScheduleCheck" default:"true" param:"计费推量开关"`
	EnableNewVersionSlowLogTopic  bool     `yaml:"enable_new_version_slow_log_topic" default:"false" param:"启用新版慢日志主题格式"`
	EnableNewVersionDeadLockTopic bool     `yaml:"enable_new_version_dead_lock_topic" default:"false" param:"启用新版死锁主题格式"`

	// byteplus billing 配置信息
	//BytePlusBillingAK        string `yaml:"byte_plus_billing_ak" param:"BytePlus计费 AK"`
	//BytePlusBillingSK        string `yaml:"byte_plus_billing_sk" param:"BytePlus计费 SK"`
	//BytePlusServiceAccountAK string `yaml:"byte_plus_service_account_ak" param:"byteplus DBW服务AK"`
	//BytePlusServiceAccountSK string `yaml:"byte_plus_service_account_sk" param:"byteplus DBW服务sK"`
	EnableThallo                   bool `yaml:"enable_thallo" default:"false" param:"是否启用thallo"`
	EnableRoute                    bool `yaml:"enable_route" default:"false" param:"是否启用路由服务"`
	EnableRouteVolcPushMeasure     bool `yaml:"enable_route_volc_push_measure" default:"true" param:"是否启用路由服务volc"`
	EnableRouteBytePlusPushMeasure bool `yaml:"enable_route_byte_plus_push_measure" default:"true" param:"是否启用路由服务byteplus"`

	SLACheckTimeInterval int64    `default:"60" yaml:"sla_check_time_interval" param:"SLA检查时间间隔,单位秒"`
	SLATenantBlackList   []string `yaml:"sla_tenant_black_list" param:"SLA租户黑名单"`

	EnableOnlineDDLTenantIDList []string `yaml:"enable_online_ddl_tenant_id_list" default:"*" param:"启用Online DDL租户id "`

	PublishEventBlackList []string `yaml:"publish_event_black_list" param:"不允许发送事件的租户名单"`
	EnableDescribeDBProxy bool     `yaml:"enable_describe_db_proxy" default:"false" param:"启用DescribeDBProxyConfig V2"`
	//data_migration
	DataMigToolContainerImage     string `yaml:"data_mig_tool_container_image" default:"hub.byted.org/rds-tob/dmtool:latest" param:"dmContainer镜像名"`
	DataMigToolBatchNum           int    `yaml:"data_mig_tool_batch_num" default:"10" param:"批量插入SQL语句数目"`
	S3MaxBandWidthMb              int    `yaml:"s3_max_bandwidth_mb"     default:"100" param:"S3最大上传带宽"`
	DisableImportSqlStatementList string `yaml:"disable_import_sql_statement_list" default:"DropIndexStmt,DropSequenceStmt" param:"导入文件中禁用的sql语法 "`
	IsOpenAccountSysDB            bool   `yaml:"is_open_account_sys_db" param:"在授权时，是否授权系统库"`

	BpmDefaultConfigId          int64  `yaml:"bpm_default_config_id" param:"Bpm审批流默认配置模板ID"`
	BpmFlowUrl                  string `yaml:"bpm_flow_url" param:"Bpm地址，到workflow"`
	BpmFlowUrlBOE               string `yaml:"bpm_flow_url_boe" param:"BOE Bpm地址，到workflow"`
	BpmBasicAuthUser            string `yaml:"bpm_basic_auth_user" param:"Bpm鉴权需要的用户"`
	BpmBasicAuthPassword        string `yaml:"bpm_basic_auth_password" param:"Bpm鉴权需要的密码"`
	BpmMultiCloudDMLConfigId    int64  `yaml:"bpm_multicloud_dml_config_id" param:"多云Bpm审批流DML默认配置模板ID" default:"12281"`
	BpmMultiCloudDDLConfigId    int64  `yaml:"bpm_multicloud_ddl_config_id" param:"多云Bpm审批流DDL默认配置模板ID" default:"9695"`
	BpmMultiCloudDMLConfigIdBOE int64  `yaml:"bpm_multicloud_dml_config_id_boe" param:"多云Bpm审批流DML默认配置模板ID" default:"12281"`
	BpmMultiCloudDDLConfigIdBOE int64  `yaml:"bpm_multicloud_ddl_config_id_boe" param:"多云Bpm审批流DDL默认配置模板ID" default:"9695"`
	ByteCloudServiceEndPoint    string `yaml:"bpm_bytecloud_service_endpoint" param:"字节云服务账号默认终端" default:"multi-cloud.byted.org"`
	ByteCloudServiceEndPointBOE string `yaml:"bpm_bytecloud_service_endpoint_boe" param:"字节云服务账号默认BOE终端" default:"multi-cloud-sandbox.byted.org"`
	ByteCloudServiceRegionBOE   string `yaml:"bpm_bytecloud_service_region_boe" param:"字节云服务账号默认BOE Region" default:"cn-chongqing-sdv"`
	IAMServiceEndPoint          string `yaml:"iam_service_endpoint" param:"iam服务endpoint" default:"open.volcengineapi.com"`

	//IAM User
	DbwUserMgmtForIAMRole string `yaml:"dbw_user_mgmt_for_iam" default:"ServiceRoleForDbw" param:"跨服务授权规则DBW用户管理"`
	MgrPodCIDR            string `yaml:"mgr_pod_cidr" param:"dbw mgr pod的IP段，多个用逗号隔开"`
	ByteBrainChatPath     string `yaml:"byte_brain_chat_path" param:"bytebrain chat 路径" tcc:"bytebrain_chat_path"`
	ChatLLMModel          string `yaml:"chat_llm_model" param:"知识文档模型"`
	Text2SQLModel         string `yaml:"text2sql_model" param:"text2sql模型" tcc:"text2sql_model"`

	InspectionTopicId string `yaml:"inspection_topic_id" param:"巡检报告表需要写入的topic"`

	DetectionMetricTopicId string `yaml:"detection_metric_topic_id" param:"异常检测检测指标写入Topic"`
	DetectionErrorTopicId  string `yaml:"detection_error_topic_id" param:"异常检测检测，dbBarin回写Topic"`
	TlsProjectId           string `yaml:"tls_project_id" param:"TLS中的项目id"`
	TLSKafkaEndpoint       string `yaml:"tls_kafka_endpoint" param:"TLS kafka地址"`
	TlsZone                string `yaml:"tls_zone" param:"TLS 使用的region"`
	DetectionRedisIp       string `yaml:"detection_redis_ip" param:"异常检测用Redis IP"`
	DetectionRedisPort     int32  `yaml:"detection_redis_port" param:"异常检测用Redis Port"`
	DetectionRedisUserName string `yaml:"detection_redis_user_name" param:"异常检测用Redis user"`
	DetectionRedisPassword string `yaml:"detection_redis_password" param:"异常检测用Redis psw"`

	AbnormalDetectionByteBrainServerReplicas int64                 `default:"2" param:"异常检测byteBrain server 副本数"`
	AbnormalDetectionByteBrainImage          string                `param:"异常检测byteBrain镜像"`
	AbnormalDetectionByteBrainDeployInfo     []*shared.ClusterInfo `param:"异常检测byteBrain 部署信息"`
	// AbnormalDetectionByteBrainAddress        string                `param:"异常检测byteBrain ip:port"`

	TlsServiceEndpoint         string `yaml:"tls_service_endpoint" param:"火山审计tls地址"  tcc:"tls_service_endpoint"`
	BytePlusTlsServiceEndpoint string `yaml:"byteplus_tls_service_endpoint" param:"byteplus审计tls地址"`

	// slowLog upgrade switch
	EnableSlowLogUpgradeSwitch bool  `yaml:"enable_slow_log_upgrade_switch" default:"false" param:"启用慢日志升级开关"`
	MaxSlowLogDownloadLimits   int64 `yaml:"max_slow_log_download_limits" tcc:"max_slow_log_download_limits" default:"10000" param:"日志最大下载条数"`

	//AutoScaleBandWidth WebHook Address
	AutoScaleWebHookAddr  string `yaml:"auto_scale_webhook_addr" default:"http://**************:5000/AutoScale" param:"扩缩容云监控回调地址"`
	AutoScaleSilenceTime  int    `yaml:"auto_scale_silence_time" default:"5" param:"扩缩容云监控告警发送周期,单位为分钟。支持配置为5、10、15、30、60、180、360、720、1440。"`
	AutoScaleService      string `yaml:"auto_scale_Service" default:"Volc_Observe" param:"扩缩容云监控告警服务,如果是测试环境,则为volc_observe_boe"`
	AutoScaleVeDBEndpoint string `yaml:"auto_scale_vedb_endpoint" default:"volcengineapi-boe-stable.byted.org" param:"扩缩容vedb接口endpoint"`

	LogCollectorImage string `yaml:"log-collector_image" default:"hub.byted.org/infcs/tob.tls.logcollector:20230807.1630-epic_v2.6.2-f73af95" param:"log-collector默认镜像"`

	EnableRDSInternalConnectSwitch bool `yaml:"enable_rds_internal_connect_switch" default:"false" param:"打开mysql 内网连接开关 false为使用shuttle连接，true为使用内网连接"`

	TaskFlowExecuteLogTopic                string   `yaml:"task_flow_execute_log_topic" param:"任务执行日志topic"`
	RDSShuttleTenantIdList                 []string `yaml:"rds_shuttle_tenant_id_list" default:"*" param:"使用shuttle的白名单租户ID集合"`
	WebControllerLevel                     string   `yaml:"web_controller_level" default:"cluster" param:"使用的webcontroller的级别， 取值为cluster/region"`
	EnableRiskControl                      bool     `yaml:"enable_risk_control" default:"false" param:"是否开启机审"`
	DiaLogStartSwitch                      bool     `yaml:"dia_log_start_switch" default:"false" param:"dbwmgr历史会话采集开关"`
	DbwCollectorSwitch                     bool     `yaml:"dbw_collector_switch" default:"true" param:"dbw-collector采集服务启停开关"`
	DbwCollectorEnableLocalInstance        bool     `yaml:"dbw_collector_enable_local_instance" default:"true" param:"dbw-collector使用MetaStore开关"`
	DbwCollectorNotCollectInstanceIDList   []string `yaml:"dbw_collector_not_collect_instance_id_list" default:"*" param:"dbw-collector采集服务不进行采集的实例名单"`
	DbwCollectorNotCollectInstanceTypeList []string `yaml:"dbw_collector_not_collect_instance_type_list" default:"*" param:"dbw-collector采集服务不进行采集的实例类型名单"`
	RDSMultiPlatformTenants                []string `yaml:"rds_multi_platform_tenants" param:"rds多云平台租户"`
	IAMReadRole                            string   `yaml:"iam_read_role" param:"iam只读权限角色名"`
	MaxParallelJobsPerInstance             int      `yaml:"max_parallel_jobs_per_instance" default:"20" param:"实例任务最大并行数"`
	MaxTasksPerInstance                    int      `yaml:"max_tasks_per_instance" default:"10" param:"每个实例可以创建的任务数"`

	FullSqlInnerAccountId                  string                `yaml:"full_sql_inner_account_id" default:"**********" param:"内部账户租户ID" tcc:"full_sql_inner_account_id"`
	FullSqlTlsConsumerConcurrent           int64                 `yaml:"full_sql_tls_consumer_concurrent" default:"5" param:"全量sql消费RDS TLS每个客户端的并发数" tcc:"full_sql_tls_consumer_concurrent"`
	FullSqlZkHosts                         []string              `yaml:"full_sql_zk_hosts" param:"全量sql配置中心" tcc:"full_sql_zk_hosts"`
	InnerAccountTlsProjectTopicQuota       int64                 `yaml:"inner_account_tls_project_topic_quota"  default:"50" param:"内部账号tls project topic的quota,调整需要先调整TLS的配额后再改" tcc:"inner_account_tls_project_topic_quota"`
	FullSqlLogTransitReplicas              int64                 `yaml:"full_sql_log_transit_replicas" default:"3" param:"collector 模块副本数"`
	FullSqlLogTransitImage                 string                `param:"dbw-transit镜像"`
	FullSqlLogTransitVersionSetID          int                   `param:"dbw-transit模块vs id"`
	FullSqlSentryDSN                       string                `yaml:"full_sql_sentry_dsn" default:"" param:"Fullsql报警DSN链接"`
	FullSqlKafkaConfig                     string                `yaml:"full_sql_kafka_config" default:"" param:"Fullsql kafka配置文件"`
	FullSqlRedisAddr                       string                `yaml:"full_sql_redis_addr" default:"" param:"Fullsql redis地址"`
	FullSqlRedisPwd                        string                `yaml:"full_sql_redis_pwd" default:"" param:"Fullsql redis密码"`
	FullSqlDeployInfo                      []*shared.ClusterInfo `yaml:"full_sql_deploy_info" param:"Fullsql 转运程序部署信息"`
	FullSqlMinTtl                          int64                 `yaml:"full_sql_min_ttl" default:"1" param:"全量SQL存储最小TTL时间" tcc:"full_sql_min_ttl"`
	FullSqlMaxTtl                          int64                 `yaml:"full_sql_max_ttl" default:"3650" param:"全量SQL存储最大TTL时间" tcc:"full_sql_max_ttl"`
	FullSqlReportErrorRecord               bool                  `yaml:"full_sql_report_error_record" default:"true" param:"全量sql打开上报错误记录"`
	FullSqlSqlDesensitizationAccount       []string              `yaml:"full_sql_sql_desensitization_account"  default:"" param:"全量sql详情脱敏"`
	FullSqlInitConsumerNum                 int                   `yaml:"full_sql_init_consumer_num"  default:"2" param:"全量sql初始消费者数量，不能大于副本数"`
	FullSqlGlobalAnalysisOutputSwitch      bool                  `yaml:"FullSqlGlobalAnalysisOutputSwitch" default:"true" param:"输出到全量SQL洞察分析链路"`
	FullSqlGlobalDetailOutputSwitch        bool                  `yaml:"FullSqlGlobalDetailOutputSwitch" default:"true" param:"输出到全量SQL洞察详情链路"`
	FullSqlGlobalTemplateCacheOutputSwitch bool                  `yaml:"FullSqlGlobalTemplateCacheOutputSwitch" default:"true" param:"输出到全量SQL洞察SQL模版缓存链路"`

	PushBillingMeasureZero               bool     `yaml:"push_billing_measure_zero" default:"false" param:"是否推送0计量"`
	PushBillingMeasureZeroChargeItemCode []string `yaml:"push_billing_measure_zero_charge_item_code" default:"" param:"全量sql推送0计量的计费项"`
	PushBillingMeasureZeroTenantIdList   []string `yaml:"push_billing_measure_zero_tenant_id_list" default:"" param:"全量sql推送0计量的租户ID"`
	PushBillingMeasureZeroInstanceIdList []string `yaml:"push_billing_measure_zero_instance_id_list" default:"" param:"全量sql推送0计量的实例ID"`
	PushBillingMeasureZeroFreeHour       int64    `yaml:"push_billing_measure_zero_free_hour" default:"0" param:"全量sql推送0计费的免费时间"`

	FullSqlInnerBmqCluster         string            `yaml:"full_sql_inner_bmq_cluster" default:"bmq_boe_test3" param:"全量sql内场"  tcc:"full_sql_inner_bmq_cluster"`
	FullSqlInnerBmqTopic           string            `yaml:"full_sql_inner_bmq_topic" default:"dbw_rds_full_sql_boe" param:"全量sql内场"  tcc:"full_sql_inner_bmq_topic"`
	FullSqlInnerBmqGroup           string            `yaml:"full_sql_inner_bmq_group" default:"dbw-transit" param:"全量sql内场"  tcc:"full_sql_inner_bmq_group"`
	FullSqlInnerBmqProducerCluster string            `yaml:"full_sql_inner_bmq_producer_cluster" default:"" param:"全量sql内场"  tcc:"full_sql_inner_bmq_producer_cluster"`
	FullSqlInnerBmqProducerTopic   string            `yaml:"full_sql_inner_bmq_producer_topic" default:"" param:"全量sql内场"  tcc:"full_sql_inner_bmq_producer_topic"`
	FullSqlInnerHandlerConfig      string            `yaml:"full_sql_inner_handler_config" default:"" param:"全量sql内场"  tcc:"full_sql_inner_handler_config"`
	FullSqlZkAuthScheme            string            `yaml:"full_sql_zk_auth_scheme" default:"" param:"全量sql zk授权方式"  tcc:"full_sql_zk_auth_scheme"`
	FullSqlZkUser                  string            `yaml:"full_sql_zk_user" default:"" param:"全量sql zk用户名"  tcc:"full_sql_zk_user"`
	FullSqlZkPassword              string            `yaml:"full_sql_zk_password" default:"" param:"全量sql zk密码"  tcc:"full_sql_zk_password"`
	FullSqlRedisCluster            string            `yaml:"full_sql_redis_cluster" default:"" param:"Fullsql redis内场集群" tcc:"full_sql_redis_cluster"`
	FullSqlRedisMap                map[string]string `yaml:"full_sql_redis_map" default:"" param:"Fullsql redis内场集群" tcc:"full_sql_redis_map"`
	FullSqlGlobalDesensitization   string            `yaml:"full_sql_desensitization" default:"TRUE" param:"全量sql全局sql脱敏"  tcc:"full_sql_desensitization"`

	SlowLogBMQConsumerCluster string `yaml:"slow_log_bmq_consumer_cluster" tcc:"slow_log_bmq_consumer_cluster" param:"慢日志BMQ消费集群"`
	SlowLogBMQConsumerTopic   string `yaml:"slow_log_bmq_consumer_topic"   tcc:"slow_log_bmq_consumer_topic" param:"慢日志BMQ消费topic"`
	SlowLogBMQConsumerGroup   string `yaml:"slow_log_bmq_consumer_group"   tcc:"slow_log_bmq_consumer_group" param:"慢日志BMQ消费group"`

	CloudFolderToken string `yaml:"cloud_folder_token" default:"U8TGfK9xolL0HgdZsoVcqEVYnTc" param:"云文档 父目录的token"`

	InfieldSqlResultExportTaskLimit   int `yaml:"infield_sql_result_export_task_limit" default:"10" param:"内场sql结果集导出任务限制"`
	InfieldSqlResultExportResultLimit int `yaml:"infield_sql_result_export_result_limit" default:"100000" param:"内场sql结果集导出数量限制"`

	SQLAdvisorPlbEndPoint         string                 `yaml:"sql_advisor_plb_endpoint" default:"http://10.249.177.187:8389" param:"SQLAdvisor Inner Plb Endpoint"`
	MaxDescribeDBInstancePageSize int                    `yaml:"max_describe_db_instance_page_size" default:"500" param:"运维面查询实例列表单次返回最大记录数"`
	EnableSQLServer               bool                   `default:"true" param:"是否启用sqlserver"`
	EnableByteRDS                 bool                   `default:"true" tcc:"enable_byterds"`
	PsmTicketCreator              string                 `default:"zhaoguofeng.957" tcc:"psm_ticket_creator"`
	MultiplatformTableFilters     []string               `yaml:"multiplatform_table_filters" param:"多云平台表过滤规则" tcc:"table_filters"`
	C3Config                      C3Config               `yaml:"c3_config" tcc:"c3_config"`
	Tunnel                        TunnelConfig           `yaml:"tunnel" param:"Tunnel 配置" tcc:"tunnel"`
	ByteRDS2DsMGRGDID             BytedanceRegion2Dsmgrs `yaml:"byterds_2_dsmgr_gdid" tcc:"byterds_2_dsmgr_gdid"`
	ByteRDS                       ByteRDS                `yaml:"byterds" tcc:"byterds"`
	ByteDoc                       ByteDoc                `yaml:"bytedoc" tcc:"bytedoc"`
	DsmgrDomain                   string                 `yaml:"dsmgr_domain" param:"dsmgr域名" default:"virtual-ds-mgr:80" tcc:"dsmgr_domain"`
	EnableAPIGateway              bool                   `yaml:"enable_apigateway"`
	APIGatewayListen              string                 `yaml:"apigateway_listen"`
	MGRAddress                    string                 `yaml:"mgr_address"`
	EnableNewVersionDataSource    bool                   `default:"false" tcc:"enable_new_version_datasource" param:"运维观测是否启用新版数据源接入方式"`
	DialogCollectorDeptList       string                 `default:"Data-基础架构-数据库-生态工具," tcc:"dialog_collector_dept_list" param:"会话采集部门名单列表"`
	SpaceCollectorDeptList        string                 `default:"" tcc:"space_collector_dept_list" param:"空间采集部门名单列表"`

	TicketConnectTimeout int64 `default:"5" param:"工单连接超时时间,单位秒"`
	TicketReadTimeout    int64 `default:"600" param:"工单读超时时间,单位秒"`
	TicketWriteTimeout   int64 `default:"300" param:"工单写超时时间,单位秒"`
	TicketIdleTimeout    int64 `default:"300" param:"工单连接空闲超时时间,单位秒"`

	TicketSessionTimeout       int64    `default:"28800" param:"工单会话超时时间,单位秒"`
	LocationSvcEndPoint        string   `yaml:"location_svc_end_point" default:"volcengineapi-boe-stable.byted.org" param:"Location服务Endpoint"`
	MultiCloudSuperAdminUserID []string `yaml:"multi_cloud_super_admin_user_id" param:"多云调用API子账号列表"`

	OperateRecordDeleteDays          int64  `default:"30" param:"SQL工作台操作审计数据自动删除配置天数"`
	EnableCCLDetectNewVersion        bool   `yaml:"enable_ccl_detect_new_version" default:"false" param:"开启限流状态检测新版本"`
	EnableDialogVedbCreateAccount    bool   `yaml:"enable_dialog_vedb_create_account" default:"false" param:"临时：解决dbw_admin账号已创建未授权场景，vedb强制删除账号的开关"`
	EnableSharding                   bool   `yaml:"enable_sharding" default:"true" param:"是否开启sharding"`
	SecurityRuleGroupDefaultTemplate string `yaml:"security_rule_group_default_template" default:"" param:"安全规则组默认模板"`
	SecurityRuleConfiguration        string `yaml:"security_rule_configuration" default:"" param:"安全规则配置信息"`

	DataArchiveLogTopicId string `yaml:"data_archive_log_topic_id" default:"87f46af9-20ae-4254-a040-e3784738fc73" param:"数据归档时，中间状态写入的日志"`

	VolcDomain               string   `yaml:"volc_domain" default:"console.volcengine.com" param:"火山域名"`
	CallbackKey              string   `yaml:"callback_key" default:"DoUHaveFreeStyle" param:"加密key(16位)"`
	MessageTenantIdList      []string `yaml:"message_tenant_id_list" param:"启用消息通知的白名单租户ID集合"`
	VolcMessageAK            string   `yaml:"volc_message_ak" default:"MDgyYjM0NWRiYWY" param:"火山消息ak"`
	VolcMessageSK            string   `yaml:"volc_message_sk" default:"GMxZjk3MTczZTMxNGYzlhODg3NmRk" param:"火山消息sk"`
	BytePlusMessageAK        string   `yaml:"byteplus_message_ak" default:"MDgyYjM0NWRiYWY" param:"byteplus消息ak"`
	BytePlusMessageSK        string   `yaml:"byteplus_message_sk" default:"GMxZjk3MTczZTMxNGYzlhODg3NmRk" param:"byteplus消息sk"`
	EndpointCn               string   `yaml:"endpoint_cn" default:"cloud-online.byted.org/volc_message" param:"火山消息cn endpoint"`
	EndpointI18n             string   `yaml:"endpoint_i18n" default:"byteplus.byted.org/message" param:"火山消息i18n endpoint"`
	IAMPassportCnEndpoint    string   `yaml:"iam_passport_cn_endpoint" default:"cloud-boe-passport-cn-stable.byted.org" param:"iam passport cn endpoint"`
	TicketWaitApproveEventId int64    `yaml:"ticket_wait_approve_event_id" default:"25148" param:"工单待审批事件ID"`
	EventByInspectionReport  int64    `yaml:"event_by_inspection_report" default:"25149" param:"巡检报告事件ID"`
	InspectionReceivers      string   `yaml:"inspection_receivers" default:"" param:"巡检报告接受租户&用户"`

	//space analysis
	InfluxdbSpaceTimeout       int64 `yaml:"influxdb_space_timeout" default:"30" param:"空间分析Influxdb超时"`
	DbwListInstanceConcurrency int64 `yaml:"dbw_list_instance_concurrency" default:"40" param:"查询实例列表并发数"`
	// 连接池
	EnableNewConnectionPool bool   `yaml:"enable_new_connection_pool" default:"true" param:"是否启用连接池"`
	DBPoolCapacity          int64  `yaml:"db_pool_capacity" default:"1" param:"连接池容量"`
	MetaMySQLTenantId       string `yaml:"meta_mysql_tenant_id" default:"2103701551" param:"meta mysql租户id"`

	//DB Copilot
	CopilotMaxIntervals         string `yaml:"copilot_max_intervals" default:"60" param:"Copilot:单次查询监控数据的最大返回数量"`
	CopilotMaxChatContextLength int    `yaml:"copilot_max_chat_context_length" default:"5" param:"Copilot:单次查询对话过程中，传入大模型的用户上下文轮次，默认前五轮作为上下文"`
	CopilotProxyHttpUrl         string `yaml:"copilot_proxy_http_url" default:"" param:"Copilot:公网访问正向代理的URL，默认为线上URL，线下环境使用："`
	AgentBaseUrl                string `yaml:"agent_base_url" default:"https://ark-cn-beijing.bytedance.net/api/v3" param:"Copilot:访问域名，默认为内网域名，BOE访问需要使用公网域名（https://ark.cn-beijing.volces.com/api/v3）"`
	ArkApiKey                   string `yaml:"ark_api_key" default:"" param:"Copilot:火山方舟的API KEY"`
	DeepSeekR1                  string `yaml:"deep_seek_r1" default:"" param:"Copilot:DeepSeekR1的endpoint"`
	DeepSeekV3                  string `yaml:"deep_seek_v3" default:"" param:"Copilot:DeepSeekV3的endpoint"`
	Doubao1Dot5Pro32K           string `yaml:"doubao_1dot5_pro_32k" default:"" param:"Copilot:doubao1.5-pro的endpoint"`

	// ghost工具开关
	EnableGhostDDLTool bool `yaml:"enable_ghost_ddl_tool" default:"false" param:"是否启用ghost ddl工具"`

	// 实例同步
	LocalInstances            bool     `yaml:"local_instances" default:"false" param:"是否启用本地实例列表"`
	SyncInstances             bool     `yaml:"sync_instances" default:"false" param:"是否启用实例同步"`
	SyncInstancesFirstRunTime int      `yaml:"sync_instances_first_run_time" default:"5" param:"实例同步首次运行时间"`
	SyncInstancesSecondTime   int      `yaml:"sync_instances_second_time" default:"18" param:"实例同步第二次运行时间"`
	EnableInstanceInsert      bool     `yaml:"enable_instance_insert" default:"true" param:"开启安全管控是否启用实例插入"`
	SyncerSvcAddress          string   `yaml:"syncer_svc_address" default:"" param:"实例同步服务地址" tcc:"syncer_svc_address"`
	SyncTestTenantIds         []string `yaml:"sync_test_tenant_ids" default:"" param:"同步测试租户ID"`
	IsGOChina                 bool     `yaml:"is_go_china" default:"false" param:"是否是goChina"`
	EnableDeleteInstance      bool     `yaml:"enable_delete_instance" default:"false" param:"是否启用实例删除"`
	BatchSize                 int      `yaml:"batch_size" default:"10" param:"批量处理大小"`
	CheckInstanceExist        bool     `yaml:"check_instance_exist" default:"false" param:"是否用订单检查实例是否存在"`

	// 新online ddl白名单
	NewOnlineDDLWhiteList   []string `yaml:"new_online_ddl_white_list" default:"[]" param:"内置Ghost，新OnlineDDL租户白名单"`
	TurnOnAllNewOnlineDDL   bool     `yaml:"turn_on_all_new_online_ddl" default:"false" param:"内置Ghost，是否全量开启新OnlineDDL"`
	OnlineDDLAgentImage     string   `yaml:"online_ddl_agent_image" default:"hub.byted.org/rds-tob/online_ddl_agent:latest" param:"online ddl agent镜像"`
	OnlineDDLK8sClusterName string   `yaml:"online_ddl_k8s_cluster_name" default:"control" param:"online ddl任务的k8s集群名"`
	OnlineDDLAgentPodCpu    string   `yaml:"online_ddl_agent_pod_cpu" default:"2" param:"online ddl agent, pod cpu"`
	OnlineDDLAgentPodMem    string   `yaml:"online_ddl_agent_pod_mem" default:"4Gi" param:"online ddl agent, pod 内存"`
	OnlineDDlDmlBatchSize   int32    `yaml:"online_d_dl_dml_batch_size" default:"100" param:"online ddl 增量复制大小"`

	// 无锁DML
	FreeLockDMLFindRecordBorderTimeOut        int    `yaml:"freelock_dml_find_record_border_timeout" default:"1200" param:"无锁DML寻找记录边界超时时间,单位是秒"`
	DBInstanceStateWithoutConnectionBlackList string `yaml:"db_instance_state_without_connection_black_list" default:"{\"VeDBMySQL\":\"Creating,WaitingPaid,Scaling,Restarting,Upgrading,PrimaryChanging,Deleting\",\"MySQL\":\"Unknown,Creating,Deleting,Restoring\",\"Postgres\":\"\"}" param:"实例状态黑名单(非连接)"`
	DBInstanceStateWithConnectionBlackList    string `yaml:"db_instance_state_with_connection_black_list" default:"{\"VeDBMySQL\":\"Creating,WaitingPaid,Scaling,Restarting,Upgrading,PrimaryChanging,Deleting\",\"MySQL\":\"Unknown,Creating,Deleting,Restoring\",\"Postgres\":\"\"}" param:"实例状态黑名单(连接)"`

	PGCheckConnV2                 bool   `yaml:"pg_check_conn_v2" default:"true" param:"PG检查连接V2"`
	RedisPublicZoneWhiteListCIDRs string `yaml:"redis_public_zone_white_list_cidrs"  param:"redis public zone白名单cidr"`

	TosErrorLogSubDomain string `yaml:"tos_error_log_sub_domain" default:"" param:"内场错误日志subdomain"  tcc:"tos_error_log_sub_domain"`
	TosErrorLogBucket    string `yaml:"tos_error_log_bucket" default:"" param:"内场错误日志Bucket"  tcc:"tos_error_log_bucket"`
	TosErrorLogAk        string `yaml:"tos_error_log_ak" default:"" param:"内场错误日志ak"  tcc:"tos_error_log_ak"`
	TosErrorLogSk        string `yaml:"tos_error_log_sk" default:"" param:"内场错误日志sk"  tcc:"tos_error_log_sk"`

	ArchiveBackupTosBucket string `yaml:"archive_backup_tos_bucket" default:"" param:"归档备份至Tos的Bucket名字"`
	JWTUrl                 string `yaml:"jwt_url" default:"" param:"jwt地址" tcc:"jwt_url"`
	DisableOpenTOPAKSK     bool   `yaml:"disable_opentop_aksk" default:"false" param:"disable_opentop_aksk"`
}

type C3Config struct {
	Application
	Aksk
}

type Application struct {
	Namespaces string `json:"namespaces" yaml:"namespaces"`
	// TOP AK/SK for ListVPC
	VPCServiceEndpoint      string `json:"vpc_service_endpoint"`
	TOPServiceAccessKey     string `json:"top_service_access_key"`
	TOPServiceSecretKey     string `json:"top_service_secret_key"`
	ShuttleServiceAccessKey string `json:"shuttle_service_access_key"`
	ShuttleServiceSecretKey string `json:"shuttle_service_secret_key"`

	// secret for disco service
	CloudServiceSecret string `json:"cloud_service_secret"`

	// secret for tls service
	TLSServiceAccessKey         string `json:"tls_service_access_key"`
	TLSServiceSecretKey         string `json:"tls_service_secret_key"`
	ByteInnerTLSServiceEndpoint string `json:"byte_inner_tls_service_endpoint"`
	ByteInnerTLSServiceRegion   string `json:"byte_inner_tls_service_region"`
	TLSServiceTopic             string `json:"tls_service_topic"`
	TLSSlowLogTopic             string `json:"tls_slow_log_topic"`
	TLSRdsSlowLogTopicV2        string `json:"tls_rds_slow_log_topic_v2" yaml:"tls_rds_slow_log_topic_v2"`
	TLSNdbSlowLogTopicV2        string `json:"tls_ndb_slow_log_topic_v2"`
	TLSPgSlowLogTopicV2         string `json:"tls_pg_slow_log_topic_v2"`
	TLSMetaSlowLogTopicV2       string `json:"tls_meta_slow_log_topic_v2"`

	// secret for err log tls service
	TLSRdsErrLogTopic string `json:"tls_rds_err_log_topic" yaml:"tls_rds_err_log_topic"`
	TLSNdbErrLogTopic string `json:"tls_ndb_err_log_topic"`

	// tls for slow advice
	SlowAdviceTLSServiceAccessKey string `json:"slow_advice_tls_service_access_key"`
	SlowAdviceTLSServiceSecretKey string `json:"slow_advice_tls_service_secret_key"`
	SlowAdviceTLSRegion           string `json:"slow_advice_tls_region"`
	SlowAdviceTLSEndpoint         string `json:"slow_advice_tls_endpoint"`

	// secret for tos service
	TOSServiceAccessKey string `json:"tos_service_access_key"`
	TOSServiceSecretKey string `json:"tos_service_secret_key"`
	TOSBucketName       string `json:"tos_bucket_name"`
	TOSServiceRegion    string `json:"tos_service_region"`
	TOSServiceEndpoint  string `json:"tos_service_endpoint"`

	// account && password for RDS
	DBWAccountName           string `json:"dbw_account_name"`
	DbwAccountPasswordGenKey string `json:"dbw_account_password_gen_key"`
	DbwAccountId             string `json:"dbw_account_id"`
	DbwPwd                   string `json:"dbw_pwd"`

	// dbw mgr
	DbwMgrAccountId string `json:"dbw_mgr_account_id"`

	InfluxdbHost         string `json:"influxdb_host"`
	InfluxdbPort         string `json:"influxdb_port"`
	InfluxdbUser         string `json:"influxdb_user"`
	InfluxdbPWD          string `json:"influxdb_pwd"`
	InfluxdbDatabase     string `json:"influxdb_database"`
	InfluxdbVeDBDatabase string `json:"influxdb_vedb_database"`
	InfluxdbOpsHost      string `json:"influxdb_ops_host"`
	InfluxdbOpsPort      string `json:"influxdb_ops_port"`
	BTMysqlHost          string `json:"mysql_host"`
	BTMysqlPort          string `json:"mysql_port"`
	BTMysqlUser          string `json:"mysql_user"`
	BTMysqlPWD           string `json:"mysql_pwd"`
	BTMysqlDatabase      string `json:"mysql_database"`
	BillingAK            string `json:"billing_ak"`
	BillingSK            string `json:"billing_sk"`
	BytePlusBillingAK    string `json:"byte_plus_billing_ak"`
	BytePlusBillingSK    string `json:"byte_plus_billing_sk"`

	DBWServiceAccountAK string `json:"dbw_service_account_ak"`
	DBWServiceAccountSK string `json:"dbw_service_account_sk"`

	TicketLogTopic string `json:"ticket_log_topic"`
	TicketLogAk    string `json:"ticket_log_ak"`
	TicketLogSk    string `json:"ticket_log_sk"`

	RDSInnerAccountAK     string `json:"rds_inner_account_ak"` // 仅供全量SQL测试
	RDSInnerAccountSK     string `json:"rds_inner_account_sk"`
	VeDBInnerAccountAK    string `json:"vedb_inner_account_ak"`
	VeDBInnerAccountSK    string `json:"vedb_inner_account_sk"`
	MetaRDSInnerAccountAK string `json:"meta_rds_inner_account_ak"`
	MetaRDSInnerAccountSK string `json:"meta_rds_inner_account_sk"`

	FullSqlRedisAddress      string `json:"full_sql_redis_address"`
	FullSqlRedisPassword     string `json:"full_sql_redis_password"`
	TLSDeadLockTopicV2       string `json:"tls_dead_lock_topic_v2"`
	TLSDeadLockTopic         string `json:"tls_dead_lock_topic"`
	TLSDialogDetailTopicV2   string `json:"tls_dialog_detail_topic_v2"`
	TLSEngineStatusTopicV2   string `json:"tls_engine_status_topic_v2"`
	TLSTrxAndLockTopic       string `json:"tls_trx_and_lock_topic"`
	AppId                    string `json:"app_id"`
	AppSecret                string `json:"app_secret"`
	InnerTOPServiceAccessKey string `json:"inner_top_service_access_key"`
	InnerTOPServiceSecretKey string `json:"inner_top_service_secret_key"`
	MetaMySQLConfigs         string `json:"meta_mysql_configs"`

	TLSSlowAdviceTopic string `json:"tls_slow_advice_topic"`
	TTLSlowAdviceDays  int64  `json:"ttl_slow_advice_days"`

	InfluxdbHostpg     string `json:"influxdb_host_pg_sre"`
	InfluxdbPortpg     string `json:"influxdb_port_pg_sre"`
	InfluxdbPatabasepg string `json:"influxdb_database_pg_sre"`
	InfluxdbUserpg     string `json:"influxdb_user_pg_sre"`
	InfluxdbPwdpg      string `json:"influxdb_pwd_pg_sre"`

	CloudStorageUser string `json:"cloud_storage_user"`
	CloudStoragePwd  string `json:"cloud_storage_pwd"`

	InfluxdbVeDBDefaultDatabase string `json:"influxdb_vedb_default_database"`
}

type Aksk struct {
	IamAksk string `json:"iam_aksk" yaml:"iam_aksk"`
}

type ConfigItem struct {
	Name, Val, Desc, DefaultVal string
}

type TunnelConfig struct {
	Scheme         string `json:"scheme" yaml:"scheme"`
	Address        string `json:"address" yaml:"address"`
	SocksUser      string `json:"socks_user" yaml:"socks_user"`
	SocksPassword  string `json:"socks_password" yaml:"socks_password"`
	UseIngress     bool   `json:"use_ingress" yaml:"use_ingress"`
	IngressAddress string `json:"ingress_address" yaml:"ingress_address"`
	EnableTLS      bool   `json:"enable_tls" yaml:"enable_tls"`
	ChannelAuth    bool   `json:"channel_auth" yaml:"channel_auth"`
	ChannelRelay   string `json:"channel_relay" yaml:"channel_relay"`
	Channel        string `json:"channel" yaml:"channel"`
	Rule           string `json:"rule" yaml:"rule"`
}

type BytedanceRegion2Dsmgrs []BytedanceRegion2Dsmgr

type BytedanceRegion2Dsmgr struct {
	Region    []string
	DsMGRGDID string
}

type ByteRDS struct {
	Secret       string
	ProxyAddress string
	APIDomain    string
	RegionId     string
}
type ByteDoc struct {
	Secret    string
	APIDomain string
	RegionId  string
}

func (c *Config) FindDsMgr(region string) string {

	for _, item := range c.ByteRDS2DsMGRGDID {
		if item.DsMGRGDID != "" {
			for _, r := range item.Region {
				if r == region {
					return item.DsMGRGDID
				}
			}
		}
	}
	return ""
}

func (c Config) ListItems() []ConfigItem {
	typ := reflect.TypeOf(c)
	val := reflect.ValueOf(c)
	var out []ConfigItem
	fp.StreamOf(fp.Times(typ.NumField())).
		Filter(func(i int) bool {
			_, ok := typ.Field(i).Tag.Lookup(PARAM_TAG)
			return ok
		}).
		Map(func(i int) ConfigItem {
			ft, fv := typ.Field(i), val.Field(i)
			return ConfigItem{
				Name:       ft.Name,
				Val:        getVal(ft, fv),
				Desc:       ft.Tag.Get(PARAM_TAG),
				DefaultVal: ft.Tag.Get(`default`),
			}
		}).
		Sort().
		ToSlice(&out)
	return out
}

func (c *Config) SetValues(vals []ConfigItem) error {
	valMap := make(map[string]string)
	fp.StreamOf(vals).ToSetBy(func(item ConfigItem) (string, string) {
		return item.Name, item.Val
	}).To(&valMap)
	typ := reflect.TypeOf(c).Elem()
	val := reflect.ValueOf(c).Elem()
	for i := 0; i < typ.NumField(); i++ {
		ft, fv := typ.Field(i), val.Field(i)
		_, ok := ft.Tag.Lookup(PARAM_TAG)
		if !ok {
			continue
		}
		if _, ok = valMap[ft.Name]; !ok {
			continue
		}
		if err := setVal(ft.Type, fv, valMap[ft.Name]); err != nil {
			return err
		}
	}
	return nil
}

func (c *Config) SetDefaults() {
	typ := reflect.TypeOf(c).Elem()
	val := reflect.ValueOf(c).Elem()
	for i := 0; i < typ.NumField(); i++ {
		ft, fv := typ.Field(i), val.Field(i)
		val, ok := ft.Tag.Lookup(`default`)
		if !ok || val == "" {
			continue
		}
		if fv.IsValid() && !fv.IsZero() {
			continue
		}
		setVal(ft.Type, fv, val)
	}
}

func indirectType(typ reflect.Type) reflect.Type {
	for typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}
	return typ
}

func indirectValue(val reflect.Value) reflect.Value {
	for val.Kind() == reflect.Ptr && !val.IsNil() {
		val = val.Elem()
	}
	return val
}

func isPrimitiveType(typ reflect.Type) bool {
	switch typ.Kind() {
	case reflect.Bool:
	case reflect.String:
	case reflect.Float32, reflect.Float64:
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
	default:
		return false
	}
	return true
}

func getVal(ft reflect.StructField, val reflect.Value) string {
	typ := indirectType(ft.Type)
	if isPrimitiveType(typ) {
		val = indirectValue(val)
		if val.Kind() == reflect.Ptr && val.IsNil() {
			return fmt.Sprint(reflect.Zero(typ))
		}
		return fmt.Sprint(val.Interface())
	}
	data, _ := json.Marshal(val.Interface())
	return string(data)
}

func setVal(typ reflect.Type, val reflect.Value, vs string) error {
	if !isPrimitiveType(indirectType(typ)) {
		vv := reflect.New(indirectType(typ))
		if err := json.Unmarshal([]byte(vs), vv.Interface()); err != nil {
			return err
		}
		if typ.Kind() == reflect.Ptr {
			val.Set(vv)
		} else {
			val.Set(vv.Elem())
		}
		return nil
	}
	if typ.Kind() == reflect.Ptr {
		nvv := reflect.New(indirectType(typ))
		val.Set(nvv)
		val = nvv
	}
	typ = indirectType(typ)
	switch typ.Kind() {
	case reflect.Bool:
		if pv, err := strconv.ParseBool(vs); err == nil {
			val.SetBool(pv)
		} else {
			return err
		}
	case reflect.String:
		val.SetString(vs)
	case reflect.Float32, reflect.Float64:
		if pv, err := strconv.ParseFloat(vs, 64); err == nil {
			val.SetFloat(pv)
		} else {
			return err
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if pv, err := strconv.ParseInt(vs, 10, 64); err == nil {
			val.SetInt(pv)
		} else {
			return err
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if pv, err := strconv.ParseUint(vs, 10, 64); err == nil {
			val.SetUint(pv)
		} else {
			return err
		}
	}
	return nil
}

func (c *Config) GetVolcTlsEndpoint() string {
	return c.GetTlsRegionEndpoint(volcroute.VolcanoEngineSiteName)
}
func (c *Config) GetVolcTlsRegion() (string, error) {
	return c.GetTLSRegion(volcroute.VolcanoEngineSiteName)
}

func (c *Config) GetTlsRegionEndpoint(site string) string {
	// BytePlus 站点取用bp的tls endpoint
	if c.EnableRoute && site == volcroute.ByteplusSiteName {
		return c.BytePlusTlsServiceEndpoint
	} else {
		// 火山站点沿用现有模式
		if c.TlsServiceEndpoint == "" {
			return fmt.Sprintf(consts.TLSEndpointTemplate, os.Getenv(`BDC_REGION_ID`))
		} else {
			return c.TlsServiceEndpoint
		}
	}
}

// GetTLSRegion https://tls-cn-guilin-boe-inner.ivolces.com
func (c *Config) GetTLSRegion(site string) (string, error) {
	e := c.GetTlsRegionEndpoint(site)
	if len(e) > 12 && e[0:12] == "https://tls-" {
		if c.EnableRoute && site == volcroute.ByteplusSiteName {
			// byteplus
			//return strings.Sub(e, 12, len(e)-22), nil             // https://tls-cn-guilin-boe-inner.ibytepluses.com
			return strings.Sub(e, 12, len(e)-15), nil // https://tls-cn-chongqing-sdv.bytepluses.com
		} else {
			// 火山
			return strings.Sub(e, 12, len(e)-18), nil // https://tls-cn-guilin-boe-inner.ivolces.com
		}
	}
	if len(e) > 11 && e[0:11] == "http://tls-" {
		if c.EnableRoute && site == volcroute.ByteplusSiteName {
			//byteplus
			//return strings.Sub(e, 11, len(e)-22), nil          // http://tls-cn-guilin-boe-inner.ibytepluses.com
			return strings.Sub(e, 11, len(e)-15), nil // http://tls-cn-chongqing-sdv.bytepluses.com
		} else {
			// 火山
			return strings.Sub(e, 11, len(e)-18), nil
		}
	}
	if len(e) > 22 && strs.HasPrefix(e, "https://dualstack.tls-") && strs.HasSuffix(e, "-inner.ivolces.com") {
		// https://dualstack.tls-cn-guilin-boe-inner.ivolces.com
		return strings.Sub(e, 22, len(e)-18), nil
	} else if len(e) > 22 && strs.HasPrefix(e, "https://dualstack.tls-") && strs.HasSuffix(e, ".volces.com") {
		// https://dualstack.tls-cn-beijing.volces.com
		return strings.Sub(e, 22, len(e)-11), nil
	}
	return "", errors.New("tls endpoint format error " + e)
}

package utils

import (
	"context"
	"fmt"
	"os"

	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	volcroute "code.byted.org/infcs/lib-mgr-common/multi/route"
)

const (
	SiteVolcEngine  = "volcengine"
	SiteBytePlus    = "byteplus"
	IAMBoeEndpoint  = "iam.stable.volcengineapi-test.com"
	IAMEndpoint     = "iam.volcengineapi.com"
	VolcBoeEndpoint = "http://volcengineapi-boe-stable.byted.org"
)

func GetServiceEndpoint(ctx context.Context, service, profile string) string {
	if profile == "" {
		profile = classifyProfile()
	}
	if service == "iam" {
		if profile == "boe_stable" || profile == "boe" {
			return IAMBoeEndpoint
		}
		return IAMEndpoint
	}
	if profile == "boe_stable" || profile == "boe" {
		return VolcBoeEndpoint
	}
	region := os.Getenv("BDC_REGION_ID")
	if GetSiteName(ctx, profile) == "byteplus" {
		return fmt.Sprintf("https://%s.%s.byteplusapi.com", service, region)
	}
	return fmt.Sprintf("https://%s.%s.volcengineapi.com", service, region)
}

func GetSiteName(ctx context.Context, profile string) string {
	accountID := fwctx.GetTenantID(ctx)
	info := &volcroute.RouteInfo{
		AccountID: accountID,
	}
	enableConf := &volcroute.RetrievalClientConfig{
		EnableRoute: true,
		Env:         volcroute.GetCloudEnvByProfile(profile),
	}
	err := volcroute.InitRetrievalClient(enableConf)
	if err != nil {
		log.Warn(ctx, "InitRetrievalClient failed: %+v", err)
		return volcroute.VolcanoEngineSiteName
	}
	site, err := volcroute.GetRetrievalClient().GetSiteConfigName(volcroute.SetRouteAccountID(ctx, accountID), info, true)
	if err != nil {
		log.Warn(ctx, "Get site config name failed: %+v", err)
		return volcroute.VolcanoEngineSiteName
	}
	return site
}

func classifyProfile() string {
	region := os.Getenv("BDC_REGION_ID")
	switch region {
	case "chongqing-sdv-", "cn-guilin-boe", "cn-chongqing-sdv":
		return "boe_stable"
	}
	return "pro"
}

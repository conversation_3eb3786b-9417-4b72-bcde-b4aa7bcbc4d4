[{"Action": "AddControlInstance", "Version": "2018-01-01", "ApiName": "AddControlInstance", "ApiGroup": "", "ApiDesc": "AddControlInstance", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AddCustomSecurityRule", "Version": "2018-01-01", "ApiName": "新增自定义安全规则", "ApiGroup": "安全规则", "ApiDesc": "新增自定义安全规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AddFullSqlCollectionFingerprint", "Version": "2018-01-01", "ApiName": "添加收藏指纹", "ApiGroup": "全量SQL", "ApiDesc": "添加收藏指纹", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AddMyFavouriteSQL", "Version": "2018-01-01", "ApiName": "AddMyFavouriteSQL", "ApiGroup": "", "ApiDesc": "AddMyFavouriteSQL", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AddSecurityRuleGroup", "Version": "2018-01-01", "ApiName": "新增安全规则集", "ApiGroup": "安全规则", "ApiDesc": "新增安全规则集", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AddSqlReview", "Version": "2018-01-01", "ApiName": "创建SQL审核单", "ApiGroup": "", "ApiDesc": "创建SQL审核单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AddTagsToResource", "Version": "2018-01-01", "ApiName": "添加标签", "ApiGroup": "标签服务", "ApiDesc": "添加标签", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AddUser", "Version": "2018-01-01", "ApiName": "添加用户", "ApiGroup": "用户管理", "ApiDesc": "添加用户", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AgreeDataCollection", "Version": "2018-01-01", "ApiName": "同意数据采集", "ApiGroup": "通用接口", "ApiDesc": "同意数据采集", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AgreeSQLAdvisorProtocol", "Version": "2018-01-01", "ApiName": "同意索引优化协议", "ApiGroup": "", "ApiDesc": "同意索引优化协议", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AgreeSqlAssistantProtocol", "Version": "2018-01-01", "ApiName": "AgreeSqlAssistantProtocol", "ApiGroup": "", "ApiDesc": "AgreeSqlAssistantProtocol", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AgreeUserProtocol", "Version": "2018-01-01", "ApiName": "同意用户协议", "ApiGroup": "通用接口", "ApiDesc": "同意用户协议", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AlterDatabase", "Version": "2018-01-01", "ApiName": "修改数据库", "ApiGroup": "数据源接口", "ApiDesc": "修改数据库", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AlterKVs", "Version": "2018-01-01", "ApiName": "修改Redis的键值对", "ApiGroup": "数据源接口", "ApiDesc": "修改Redis的键值对", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AlterRows", "Version": "2018-01-01", "ApiName": "修改查询结果集", "ApiGroup": "命令操作接口", "ApiDesc": "修改查询结果集", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AlterTable", "Version": "2018-01-01", "ApiName": "修改表结构", "ApiGroup": "数据源接口", "ApiDesc": "修改表结构", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "AutoScale", "Version": "2018-01-01", "ApiName": "扩缩容接口", "ApiGroup": "带宽扩缩容", "ApiDesc": "扩缩容接口", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "BatchOpenInstanceFunction", "Version": "2018-01-01", "ApiName": "DBW实例批量开通", "ApiGroup": "运维管理", "ApiDesc": "DBW实例批量开通", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CancelCommandSet", "Version": "2018-01-01", "ApiName": "取消命令集执行", "ApiGroup": "命令操作接口", "ApiDesc": "取消命令集执行", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CancelMigrationTicket", "Version": "2018-01-01", "ApiName": "导入导出工单终止接口", "ApiGroup": "数据迁移接口", "ApiDesc": "导入导出工单终止接口", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CancelTicket", "Version": "2018-01-01", "ApiName": "取消工单", "ApiGroup": "", "ApiDesc": "取消工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ChangeDB", "Version": "2018-01-01", "ApiName": "切换数据库", "ApiGroup": "切换数据库", "ApiDesc": "切换数据库", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "Cha<PERSON>", "Version": "2018-01-01", "ApiName": "对话", "ApiGroup": "对话", "ApiDesc": "对话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CheckConn", "Version": "2018-01-01", "ApiName": "检查连接", "ApiGroup": "数据源接口", "ApiDesc": "检查连接", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CloseConnection", "Version": "2018-01-01", "ApiName": "关闭连接", "ApiGroup": "连接相关接口", "ApiDesc": "关闭连接", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CloseSession", "Version": "2018-01-01", "ApiName": "关闭会话", "ApiGroup": "连接相关接口", "ApiDesc": "关闭会话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CopilotButtonGeneration", "Version": "2018-01-01", "ApiName": "DBW_Copilot智能助手 查询按钮", "ApiGroup": "", "ApiDesc": "DBW_Copilot智能助手 查询按钮", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CopyTable", "Version": "2018-01-01", "ApiName": "复制table", "ApiGroup": "数据源接口", "ApiDesc": "复制table", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CopyTableSchemaOnly", "Version": "2018-01-01", "ApiName": "复制table结构", "ApiGroup": "数据源接口", "ApiDesc": "复制table结构", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateApprovalFlowConfig", "Version": "2018-01-01", "ApiName": "创建审批配置", "ApiGroup": "", "ApiDesc": "创建审批配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateApprovalNode", "Version": "2018-01-01", "ApiName": "创建审批节点", "ApiGroup": "", "ApiDesc": "创建审批节点", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateAutomaticInspection", "Version": "2018-01-01", "ApiName": "自动巡检", "ApiGroup": "数据库巡检接口", "ApiDesc": "自动巡检", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateChat", "Version": "2018-01-01", "ApiName": "创建对话", "ApiGroup": "创建对话", "ApiDesc": "创建对话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateConnection", "Version": "2018-01-01", "ApiName": "新建连接", "ApiGroup": "连接相关接口", "ApiDesc": "新建连接", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateCopilotChat", "Version": "2018-01-01", "ApiName": "DBW_Copilot 创建会话", "ApiGroup": "", "ApiDesc": "DBW_Copilot 创建会话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateDatabase", "Version": "2018-01-01", "ApiName": "创建数据库", "ApiGroup": "数据源接口", "ApiDesc": "创建数据库", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateDbExportTask", "Version": "2018-01-01", "ApiName": "创建数据导出任务", "ApiGroup": "数据迁移接口", "ApiDesc": "创建数据导出任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateDbImportTask", "Version": "2018-01-01", "ApiName": "创建数据导入任务", "ApiGroup": "数据迁移接口", "ApiDesc": "创建数据导入任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateEvent", "Version": "2018-01-01", "ApiName": "创建event", "ApiGroup": "数据源接口", "ApiDesc": "创建event", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateFullSqlExportTask", "Version": "2018-01-01", "ApiName": "创建全量SQL审计离线下载任务", "ApiGroup": "全量SQL", "ApiDesc": "创建全量SQL审计离线下载任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateFullSqlOrder", "Version": "2018-01-01", "ApiName": "创建全量SQL实例", "ApiGroup": "全量SQL", "ApiDesc": "创建全量SQL实例", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateFunction", "Version": "2018-01-01", "ApiName": "创建function", "ApiGroup": "命令操作接口", "ApiDesc": "创建function", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateLogExportTask", "Version": "2018-01-01", "ApiName": "创建下载任务", "ApiGroup": "内场api", "ApiDesc": "创建下载任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateManualCCLInspectionTask", "Version": "2018-01-01", "ApiName": "创建SQL限流手动巡检任务", "ApiGroup": "运维管理", "ApiDesc": "创建SQL限流手动巡检任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateManualInspection", "Version": "2018-01-01", "ApiName": "手动巡检", "ApiGroup": "数据库巡检接口", "ApiDesc": "手动巡检", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateMigrationTicket", "Version": "2018-01-01", "ApiName": "创建导入导出工单", "ApiGroup": "数据迁移接口", "ApiDesc": "创建导入导出工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateOrderContentFromMessages", "Version": "2018-01-01", "ApiName": "DBW_Copilot发起火山工单，生成工单内容", "ApiGroup": "", "ApiDesc": "DBW_Copilot发起火山工单，生成工单内容", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreatePrivilegeTicket", "Version": "2018-01-01", "ApiName": "权限申请工单创建", "ApiGroup": "用户管理", "ApiDesc": "权限申请工单创建", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateProcedure", "Version": "2018-01-01", "ApiName": "创建procudere", "ApiGroup": "命令操作接口", "ApiDesc": "创建procudere", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateSQLAdvisorTask", "Version": "2018-01-01", "ApiName": "创建索引优化任务", "ApiGroup": "", "ApiDesc": "创建索引优化任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateSession", "Version": "2018-01-01", "ApiName": "新建会话", "ApiGroup": "连接相关接口", "ApiDesc": "新建会话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateSlowLogsExportTask", "Version": "2018-01-01", "ApiName": "创建慢日志导出任务", "ApiGroup": "慢日志分析接口", "ApiDesc": "创建慢日志导出任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateSqlAudit", "Version": "2018-01-01", "ApiName": "开启SQL审计功能", "ApiGroup": "SQL审计相关接口", "ApiDesc": "开启SQL审计功能", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateSqlAuditExportTask", "Version": "2018-01-01", "ApiName": "启动离线导出任务", "ApiGroup": "SQL审计相关接口", "ApiDesc": "启动离线导出任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateSqlConcurrencyControlRule", "Version": "2018-01-01", "ApiName": "新建SQL限流任务", "ApiGroup": "SQL限流", "ApiDesc": "新建SQL限流任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateSqlKillRule", "Version": "2018-01-01", "ApiName": "创建sql kill配置", "ApiGroup": "连接相关接口", "ApiDesc": "创建sql kill配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateSqlTask", "Version": "2018-01-01", "ApiName": "创建Sql任务", "ApiGroup": "Sql任务", "ApiDesc": "创建Sql任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateTable", "Version": "2018-01-01", "ApiName": "创建table", "ApiGroup": "数据源接口", "ApiDesc": "创建table", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateTaskFlow", "Version": "2018-01-01", "ApiName": "CreateTaskFlow", "ApiGroup": "", "ApiDesc": "CreateTaskFlow", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateTerminateFullSqlOrder", "Version": "2018-01-01", "ApiName": "退订全量SQL实例", "ApiGroup": "全量SQL", "ApiDesc": "退订全量SQL实例", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateTicket", "Version": "2018-01-01", "ApiName": "创建工单", "ApiGroup": "工单审批接口", "ApiDesc": "创建工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Version": "2018-01-01", "ApiName": "创建trigger", "ApiGroup": "数据源接口", "ApiDesc": "创建trigger", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "CreateUserGroup", "Version": "2018-01-01", "ApiName": "创建DBW用户组", "ApiGroup": "用户管理", "ApiDesc": "创建DBW用户组", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Version": "2018-01-01", "ApiName": "创建view", "ApiGroup": "数据源接口", "ApiDesc": "创建view", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DataCancelExec", "Version": "2018-01-01", "ApiName": "OpenAPI-取消命令集执行", "ApiGroup": "命令操作接口", "ApiDesc": "OpenAPI-取消命令集执行", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DataCloseSession", "Version": "2018-01-01", "ApiName": "OpenAPI-关闭会话", "ApiGroup": "连接相关接口", "ApiDesc": "OpenAPI-关闭会话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DataConnectInstance", "Version": "2018-01-01", "ApiName": "OpenAPI-创建数据库连接", "ApiGroup": "连接相关接口", "ApiDesc": "OpenAPI-创建数据库连接", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DataDescribeCommandSet", "Version": "2018-01-01", "ApiName": "OpenAPI-命令集状态查询", "ApiGroup": "命令操作接口", "ApiDesc": "OpenAPI-命令集状态查询", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DataExecCommandSetAsync", "Version": "2018-01-01", "ApiName": "OpenAPI-异步命令集执行", "ApiGroup": "命令操作接口", "ApiDesc": "OpenAPI-异步命令集执行", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DataExecCommands", "Version": "2018-01-01", "ApiName": "OpenAPI-同步命令集执行", "ApiGroup": "命令操作接口", "ApiDesc": "OpenAPI-同步命令集执行", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DataGetCommandSetResult", "Version": "2018-01-01", "ApiName": "OpenAPI-命令集结果获取", "ApiGroup": "命令操作接口", "ApiDesc": "OpenAPI-命令集结果获取", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DataSessionKeepAlive", "Version": "2018-01-01", "ApiName": "OpenAPI-会话保持", "ApiGroup": "连接相关接口", "ApiDesc": "OpenAPI-会话保持", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteApprovalFlowConfig", "Version": "2018-01-01", "ApiName": "删除审批配置", "ApiGroup": "", "ApiDesc": "删除审批配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteApprovalNode", "Version": "2018-01-01", "ApiName": "删除审批节点", "ApiGroup": "", "ApiDesc": "删除审批节点", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteChat", "Version": "2018-01-01", "ApiName": "删除对话", "ApiGroup": "删除对话", "ApiDesc": "删除对话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteCopilotChat", "Version": "2018-01-01", "ApiName": "DBW_Copilot 删除会话", "ApiGroup": "", "ApiDesc": "DBW_Copilot 删除会话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteCustomRule", "Version": "2018-01-01", "ApiName": "根据规则ID删除自定义安全规则", "ApiGroup": "安全规则", "ApiDesc": "根据规则ID删除自定义安全规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteDBInstance", "Version": "2018-01-01", "ApiName": "删除实例", "ApiGroup": "数据源接口", "ApiDesc": "删除实例", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteDbTasks", "Version": "2018-01-01", "ApiName": "删除数据迁移任务", "ApiGroup": "数据迁移接口", "ApiDesc": "删除数据迁移任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteFullSqlCollectionFingerprint", "Version": "2018-01-01", "ApiName": "取消收藏指纹", "ApiGroup": "全量SQL", "ApiDesc": "取消收藏指纹", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteMyFavouriteSQL", "Version": "2018-01-01", "ApiName": "DeleteMyFavouriteSQL", "ApiGroup": "", "ApiDesc": "DeleteMyFavouriteSQL", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteSecurityRuleGroup", "Version": "2018-01-01", "ApiName": "删除安全规则集", "ApiGroup": "安全规则", "ApiDesc": "删除安全规则集", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteSqlAudit", "Version": "2018-01-01", "ApiName": "关闭SQL审计功能", "ApiGroup": "SQL审计相关接口", "ApiDesc": "关闭SQL审计功能", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteSqlConcurrencyControlRule", "Version": "2018-01-01", "ApiName": "删除SQL限流任务", "ApiGroup": "SQL限流", "ApiDesc": "删除SQL限流任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteSqlKillRule", "Version": "2018-01-01", "ApiName": "删除sql kill配置", "ApiGroup": "连接相关接口", "ApiDesc": "删除sql kill配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteTaskFlow", "Version": "2018-01-01", "ApiName": "DeleteTaskFlow", "ApiGroup": "", "ApiDesc": "DeleteTaskFlow", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteUser", "Version": "2018-01-01", "ApiName": "删除用户", "ApiGroup": "用户管理", "ApiDesc": "删除用户", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DeleteUserGroup", "Version": "2018-01-01", "ApiName": "删除DBW用户组", "ApiGroup": "用户管理", "ApiDesc": "删除DBW用户组", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAZs", "Version": "2018-01-01", "ApiName": "DescribeAZs", "ApiGroup": "", "ApiDesc": "DescribeAZs", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAbnormalDetectionConfig", "Version": "2018-01-01", "ApiName": "获取异常检测配置", "ApiGroup": "异常检测", "ApiDesc": "获取异常检测配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAbnormalDetectionDetail", "Version": "2018-01-01", "ApiName": "获取异常检测指标详情", "ApiGroup": "异常检测", "ApiDesc": "获取异常检测指标详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAbnormalDetectionInfo", "Version": "2018-01-01", "ApiName": "获取异常检信息", "ApiGroup": "异常检测", "ApiDesc": "获取异常检信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAccountTlsStatus", "Version": "2018-01-01", "ApiName": "查看账号是否开通tls", "ApiGroup": "SQL审计相关接口", "ApiDesc": "查看账号是否开通tls", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeActions", "Version": "2018-01-01", "ApiName": "查询动作集合", "ApiGroup": "安全规则", "ApiDesc": "查询动作集合", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAggregateDiagSlowQuery", "Version": "2018-01-01", "ApiName": "慢查询", "ApiGroup": "一键诊断", "ApiDesc": "慢查询", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAggregateDialogs", "Version": "2018-01-01", "ApiName": "获取聚合会话统计信息", "ApiGroup": "实例会话接口", "ApiDesc": "获取聚合会话统计信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAggregateSlowLogs", "Version": "2018-01-01", "ApiName": "获取慢日志聚合信息", "ApiGroup": "慢日志分析接口", "ApiDesc": "获取慢日志聚合信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAggregationSQLTable", "Version": "2018-01-01", "ApiName": "获取全量sql表聚合", "ApiGroup": "全量SQL", "ApiDesc": "获取全量sql表聚合", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAggregationSQLTemplates", "Version": "2018-01-01", "ApiName": "获取全量SQL聚合模板列表", "ApiGroup": "全量SQL", "ApiDesc": "获取全量SQL聚合模板列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeApprovalFlowConfig", "Version": "2018-01-01", "ApiName": "展示审批配置详情", "ApiGroup": "", "ApiDesc": "展示审批配置详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeApprovalFlowLogs", "Version": "2018-01-01", "ApiName": "展示审批流信息", "ApiGroup": "", "ApiDesc": "展示审批流信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeArchiveConfigs", "Version": "2018-01-01", "ApiName": "获取归档视图列表", "ApiGroup": "", "ApiDesc": "获取归档视图列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeArchiveTaskLogDetail", "Version": "2018-01-01", "ApiName": "获取归档任务日志详情", "ApiGroup": "", "ApiDesc": "获取归档任务日志详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeArchiveTasks", "Version": "2018-01-01", "ApiName": "获取归档任务列表", "ApiGroup": "", "ApiDesc": "获取归档任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAuditInstance", "Version": "2018-01-01", "ApiName": "获取审计实例详情（运维）", "ApiGroup": "SQL审计相关接口", "ApiDesc": "获取审计实例详情（运维）", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAuditInstances", "Version": "2018-01-01", "ApiName": "获取审计实例列表（运维）", "ApiGroup": "SQL审计相关接口", "ApiDesc": "获取审计实例列表（运维）", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAuditLogConfig", "Version": "2018-01-01", "ApiName": "查看审计日志保存时长", "ApiGroup": "", "ApiDesc": "查看审计日志保存时长", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAuditLogDetail", "Version": "2018-01-01", "ApiName": "SQL审计详情查询openapi", "ApiGroup": "SQL审计相关接口", "ApiDesc": "SQL审计详情查询openapi", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAutoKillSessionConfig", "Version": "2018-01-01", "ApiName": "查看sql kill配置", "ApiGroup": "连接相关接口", "ApiDesc": "查看sql kill配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAutoScaleEvents", "Version": "2018-01-01", "ApiName": "查看扩缩容事件", "ApiGroup": "带宽扩缩容", "ApiDesc": "查看扩缩容事件", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAutoScaleInstanceSpec", "Version": "2018-01-01", "ApiName": "查看实例扩缩容规格列表", "ApiGroup": "带宽扩缩容", "ApiDesc": "查看实例扩缩容规格列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAutoScaleRules", "Version": "2018-01-01", "ApiName": "查看扩缩容规则", "ApiGroup": "带宽扩缩容", "ApiDesc": "查看扩缩容规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeAvailableTLSTopic", "Version": "2018-01-01", "ApiName": "获取TLS连接信息", "ApiGroup": "通用接口", "ApiDesc": "获取TLS连接信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCharsets", "Version": "2018-01-01", "ApiName": "获取Charsets信息", "ApiGroup": "数据源接口", "ApiDesc": "获取Charsets信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeChatMessages", "Version": "2018-01-01", "ApiName": "DBW_Copilot智能助手 查询消息", "ApiGroup": "", "ApiDesc": "DBW_Copilot智能助手 查询消息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCluster", "Version": "2018-01-01", "ApiName": "获取审计所在集群列表（运维）", "ApiGroup": "SQL审计相关接口", "ApiDesc": "获取审计所在集群列表（运维）", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCollations", "Version": "2018-01-01", "ApiName": "获取字符集Collations信息", "ApiGroup": "数据源接口", "ApiDesc": "获取字符集Collations信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCollections", "Version": "2018-01-01", "ApiName": "MongoDB集合列表", "ApiGroup": "命令操作接口", "ApiDesc": "MongoDB集合列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCommand", "Version": "2018-01-01", "ApiName": "获取命令执行结果", "ApiGroup": "命令操作接口", "ApiDesc": "获取命令执行结果", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCommandSet", "Version": "2018-01-01", "ApiName": "命令集描述", "ApiGroup": "命令操作接口", "ApiDesc": "命令集描述", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeConsoleConnEnvs", "Version": "2018-01-01", "ApiName": "DescribeConsoleConnEnvs", "ApiGroup": "", "ApiDesc": "DescribeConsoleConnEnvs", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeConsoleRecordList", "Version": "2018-01-01", "ApiName": "查询SQL工作台操作审计列表", "ApiGroup": "", "ApiDesc": "查询SQL工作台操作审计列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCopilotChatList", "Version": "2018-01-01", "ApiName": "DBW_Copilot 查询会话列表", "ApiGroup": "", "ApiDesc": "DBW_Copilot 查询会话列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCurrentConnInfos", "Version": "2018-01-01", "ApiName": "获取实例连接信息", "ApiGroup": "实例会话接口", "ApiDesc": "获取实例连接信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeCustomRule", "Version": "2018-01-01", "ApiName": "根据规则ID查询自定义安全规则详情", "ApiGroup": "安全规则", "ApiDesc": "根据规则ID查询自定义安全规则详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDBDiagnosis", "Version": "2018-01-01", "ApiName": "发起诊断", "ApiGroup": "一键诊断", "ApiDesc": "发起诊断", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDBHealthScore", "Version": "2018-01-01", "ApiName": "诊断得分", "ApiGroup": "一键诊断", "ApiDesc": "诊断得分", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDBInspectionReport", "Version": "2018-01-01", "ApiName": "巡检报告", "ApiGroup": "数据库巡检接口", "ApiDesc": "巡检报告", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDBInspectionScore", "Version": "2018-01-01", "ApiName": "巡检扣分", "ApiGroup": "数据库巡检接口", "ApiDesc": "巡检扣分", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDBInspections", "Version": "2018-01-01", "ApiName": "巡检列表", "ApiGroup": "数据库巡检接口", "ApiDesc": "巡检列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDBProxyStatus", "Version": "2018-01-01", "ApiName": "获取实例代理状态信息", "ApiGroup": "通用接口", "ApiDesc": "获取实例代理状态信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDBs", "Version": "2018-01-01", "ApiName": "获取实例里产生慢日志的DB", "ApiGroup": "慢日志分析接口", "ApiDesc": "获取实例里产生慢日志的DB", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDasOperationTaskList", "Version": "2018-01-01", "ApiName": "查询DAS操作审计列表", "ApiGroup": "", "ApiDesc": "查询DAS操作审计列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDataBaseTables", "Version": "2018-01-01", "ApiName": "空间分析", "ApiGroup": "一键诊断", "ApiDesc": "空间分析", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDataSource", "Version": "2018-01-01", "ApiName": "dbw-collector专用：获取数据库连接", "ApiGroup": "", "ApiDesc": "dbw-collector专用：获取数据库连接", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDataSourceTypes", "Version": "2018-01-01", "ApiName": "获取数据源类型", "ApiGroup": "通用接口", "ApiDesc": "获取数据源类型", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDataTypes", "Version": "2018-01-01", "ApiName": "获取数据库支持的数据类型", "ApiGroup": "命令操作接口", "ApiDesc": "获取数据库支持的数据类型", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDatabases", "Version": "2018-01-01", "ApiName": "获取DB列表", "ApiGroup": "数据源接口", "ApiDesc": "获取DB列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDbExportDownloadUrl", "Version": "2018-01-01", "ApiName": "获取导出数据下载url", "ApiGroup": "数据迁移接口", "ApiDesc": "获取导出数据下载url", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDbMigrationTaskDetail", "Version": "2018-01-01", "ApiName": "获取数据迁移任务详情", "ApiGroup": "数据迁移接口", "ApiDesc": "获取数据迁移任务详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDbMigrationTasks", "Version": "2018-01-01", "ApiName": "获取数据迁移任务列表", "ApiGroup": "数据迁移接口", "ApiDesc": "获取数据迁移任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDbTreeMountInfo", "Version": "2018-01-01", "ApiName": "DescribeDbTreeMountInfo", "ApiGroup": "", "ApiDesc": "DescribeDbTreeMountInfo", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDeadlock", "Version": "2018-01-01", "ApiName": "获取死锁信息", "ApiGroup": "命令操作接口", "ApiDesc": "获取死锁信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDeadlockDetect", "Version": "2018-01-01", "ApiName": "获取死锁检测开关", "ApiGroup": "命令操作接口", "ApiDesc": "获取死锁检测开关", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDiagItemDetail", "Version": "2018-01-01", "ApiName": "诊断指标详情", "ApiGroup": "一键诊断", "ApiDesc": "诊断指标详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDiagRootCause", "Version": "2018-01-01", "ApiName": "诊断", "ApiGroup": "一键诊断", "ApiDesc": "诊断", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDiagType", "Version": "2018-01-01", "ApiName": "场景类型", "ApiGroup": "一键诊断", "ApiDesc": "场景类型", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDialogDetailSnapshot", "Version": "2018-01-01", "ApiName": "获取会话信息快照", "ApiGroup": "实例会话接口", "ApiDesc": "获取会话信息快照", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDialogDetails", "Version": "2018-01-01", "ApiName": "获取实时会话信息", "ApiGroup": "实例会话接口", "ApiDesc": "获取实时会话信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDialogHotspots", "Version": "2018-01-01", "ApiName": "获取会话热点SQL", "ApiGroup": "实例会话接口", "ApiDesc": "获取会话热点SQL", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDialogInfos", "Version": "2018-01-01", "ApiName": "获取会话信息", "ApiGroup": "实例会话接口", "ApiDesc": "获取会话信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDialogSnapshots", "Version": "2018-01-01", "ApiName": "获取会话快照", "ApiGroup": "实例会话接口", "ApiDesc": "获取会话快照", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDialogStatistics", "Version": "2018-01-01", "ApiName": "获取会话统计信息", "ApiGroup": "实例会话接口", "ApiDesc": "获取会话统计信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDiskAutoScaleEvents", "Version": "2018-01-01", "ApiName": "查看扩缩容事件", "ApiGroup": "磁盘扩缩容", "ApiDesc": "查看扩缩容事件", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDiskDBAutoScalingConfig", "Version": "2018-01-01", "ApiName": "查看扩缩容配置", "ApiGroup": "磁盘扩缩容", "ApiDesc": "查看扩缩容配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDmTasks", "Version": "2018-01-01", "ApiName": "获取导入导出任务列表", "ApiGroup": "运维管理", "ApiDesc": "获取导入导出任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeDownloadUrl", "Version": "2018-01-01", "ApiName": "生成TLS下载链接", "ApiGroup": "通用接口", "ApiDesc": "生成TLS下载链接", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeEngineStatusSnapShot", "Version": "2018-01-01", "ApiName": "获取数据库引擎状态快照", "ApiGroup": "实例会话接口", "ApiDesc": "获取数据库引擎状态快照", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeEvent", "Version": "2018-01-01", "ApiName": "查看event", "ApiGroup": "数据源接口", "ApiDesc": "查看event", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeEvents", "Version": "2018-01-01", "ApiName": "获取event列表", "ApiGroup": "数据源接口", "ApiDesc": "获取event列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeExampleSQL", "Version": "2018-01-01", "ApiName": "获取SQL样例", "ApiGroup": "慢日志分析接口", "ApiDesc": "获取SQL样例", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeExecuteLogs", "Version": "2018-01-01", "ApiName": "DescribeExecuteLogs", "ApiGroup": "", "ApiDesc": "DescribeExecuteLogs", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFactors", "Version": "2018-01-01", "ApiName": "查询因子集合", "ApiGroup": "安全规则", "ApiDesc": "查询因子集合", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFullSQLDetail", "Version": "2018-01-01", "ApiName": "查询全量SQL明细", "ApiGroup": "全量SQL", "ApiDesc": "查询全量SQL明细", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFullSQLFingerprintExample", "Version": "2018-01-01", "ApiName": "全量SQL指纹样例查询", "ApiGroup": "全量SQL", "ApiDesc": "全量SQL指纹样例查询", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFullSqlCandidate", "Version": "2018-01-01", "ApiName": "查询全量SQL字段候选项", "ApiGroup": "全量SQL", "ApiDesc": "查询全量SQL字段候选项", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFullSqlConfig", "Version": "2018-01-01", "ApiName": "查询全量SQL配置", "ApiGroup": "全量SQL", "ApiDesc": "查询全量SQL配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFullSqlExportTasks", "Version": "2018-01-01", "ApiName": "查看全量SQL审计离线下载任务列表", "ApiGroup": "全量SQL", "ApiDesc": "查看全量SQL审计离线下载任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFullSqlStatus", "Version": "2018-01-01", "ApiName": "检查全量sql状态", "ApiGroup": "全量SQL", "ApiDesc": "检查全量sql状态", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFunction", "Version": "2018-01-01", "ApiName": "查看function", "ApiGroup": "命令操作接口", "ApiDesc": "查看function", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeFunctions", "Version": "2018-01-01", "ApiName": "获取Function列表", "ApiGroup": "数据源接口", "ApiDesc": "获取Function列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeHealthSummary", "Version": "2018-01-01", "ApiName": "健康概要", "ApiGroup": "一键诊断", "ApiDesc": "健康概要", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeHistogramV1DBW", "Version": "2018-01-01", "ApiName": "直方图", "ApiGroup": "封装TLS", "ApiDesc": "直方图", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeIAMUsers", "Version": "2018-01-01", "ApiName": "查看IAM用户列表", "ApiGroup": "用户管理", "ApiDesc": "查看IAM用户列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeIndexDBW", "Version": "2018-01-01", "ApiName": "获取索引配置", "ApiGroup": "封装TLS", "ApiDesc": "获取索引配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeIndexs", "Version": "2018-01-01", "ApiName": "MongoDB索引列表", "ApiGroup": "命令操作接口", "ApiDesc": "MongoDB索引列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInnerRdsInstance", "Version": "2018-01-01", "ApiName": "内场", "ApiGroup": "内场", "ApiDesc": "内场", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceChargeItemUsage", "Version": "2018-01-01", "ApiName": "查询计费项使用量", "ApiGroup": "SQL审计订单", "ApiDesc": "查询计费项使用量", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceDatabases", "Version": "2018-01-01", "ApiName": "DescribeInstanceDatabases", "ApiGroup": "", "ApiDesc": "DescribeInstanceDatabases", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceDetail", "Version": "2018-01-01", "ApiName": "dbw-collector专用：查询实例详情信息", "ApiGroup": "", "ApiDesc": "dbw-collector专用：查询实例详情信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceFeatures", "Version": "2018-01-01", "ApiName": "查询实例特性", "ApiGroup": "实例会话接口", "ApiDesc": "查询实例特性", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceInfo", "Version": "2018-01-01", "ApiName": "DescribeInstanceInfo", "ApiGroup": "", "ApiDesc": "DescribeInstanceInfo", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceList", "Version": "2018-01-01", "ApiName": "dbw-collector专用：查询实例列表", "ApiGroup": "", "ApiDesc": "dbw-collector专用：查询实例列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceLogNodes", "Version": "2018-01-01", "ApiName": "查询节点", "ApiGroup": "全量SQL", "ApiDesc": "查询节点", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceManagement", "Version": "2018-01-01", "ApiName": "获取服务实例详情", "ApiGroup": "服务实例管控", "ApiDesc": "获取服务实例详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceNodes", "Version": "2018-01-01", "ApiName": "获取instance节点", "ApiGroup": "数据源接口", "ApiDesc": "获取instance节点", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstancePrice", "Version": "2018-01-01", "ApiName": "价格查询", "ApiGroup": "SQL审计订单", "ApiDesc": "价格查询", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceShards", "Version": "2018-01-01", "ApiName": "DescribeInstanceShards", "ApiGroup": "", "ApiDesc": "DescribeInstanceShards", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstanceVariables", "Version": "2018-01-01", "ApiName": "查询实例变量", "ApiGroup": "", "ApiDesc": "查询实例变量", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeInstances", "Version": "2018-01-01", "ApiName": "获取instance列表", "ApiGroup": "数据源接口", "ApiDesc": "获取instance列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "Describe<PERSON>eys", "Version": "2018-01-01", "ApiName": "获取Redis的所有key", "ApiGroup": "数据源接口", "ApiDesc": "获取Redis的所有key", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeLinkTypes", "Version": "2018-01-01", "ApiName": "获取连接类型", "ApiGroup": "通用接口", "ApiDesc": "获取连接类型", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeLockCurrentWaits", "Version": "2018-01-01", "ApiName": "查询实时锁阻塞信息", "ApiGroup": "事务与锁", "ApiDesc": "查询实时锁阻塞信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeLockWaitsDetailSnapshot", "Version": "2018-01-01", "ApiName": "获取锁阻塞快照详情", "ApiGroup": "事务与锁", "ApiDesc": "获取锁阻塞快照详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeLogCollectorClusters", "Version": "2018-01-01", "ApiName": "log-collector列表", "ApiGroup": "运维管理", "ApiDesc": "log-collector列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeLogContextDBW", "Version": "2018-01-01", "ApiName": "获取上下文日志", "ApiGroup": "封装TLS", "ApiDesc": "获取上下文日志", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeLogsDownloadUrl", "Version": "2018-01-01", "ApiName": "获取TLS日志下载链接", "ApiGroup": "通用接口", "ApiDesc": "获取TLS日志下载链接", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeManagedInstanceColumns", "Version": "2018-01-01", "ApiName": "获取管控实例的Column列表", "ApiGroup": "服务实例管控", "ApiDesc": "获取管控实例的Column列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeManagedInstanceDatabases", "Version": "2018-01-01", "ApiName": "获取管控实例的DB列表", "ApiGroup": "服务实例管控", "ApiDesc": "获取管控实例的DB列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeManagedInstanceTables", "Version": "2018-01-01", "ApiName": "获取管控实例的table列表", "ApiGroup": "服务实例管控", "ApiDesc": "获取管控实例的table列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeManagedInstances", "Version": "2018-01-01", "ApiName": "获取管控实例列表", "ApiGroup": "服务实例管控", "ApiDesc": "获取管控实例列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeManagedUsers", "Version": "2018-01-01", "ApiName": "查看用户列表", "ApiGroup": "用户管理", "ApiDesc": "查看用户列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeMigrationPreCheckDetail", "Version": "2018-01-01", "ApiName": "导入导出工单预检查详情", "ApiGroup": "数据迁移接口", "ApiDesc": "导入导出工单预检查详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeMigrationTicketDetail", "Version": "2018-01-01", "ApiName": "查看导入导出工单详情", "ApiGroup": "数据迁移接口", "ApiDesc": "查看导入导出工单详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeMigrationTickets", "Version": "2018-01-01", "ApiName": "查看导入导出工单列表", "ApiGroup": "数据迁移接口", "ApiDesc": "查看导入导出工单列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeMongoDBs", "Version": "2018-01-01", "ApiName": "MongoDB数据库列表", "ApiGroup": "命令操作接口", "ApiDesc": "MongoDB数据库列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeMyFavouriteSQL", "Version": "2018-01-01", "ApiName": "DescribeMyFavouriteSQL", "ApiGroup": "", "ApiDesc": "DescribeMyFavouriteSQL", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeOnlineDDLSecurityRule", "Version": "2018-01-01", "ApiName": "根据实例ID查询无锁变更参数安全规则", "ApiGroup": "安全规则", "ApiDesc": "根据实例ID查询无锁变更参数安全规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeOpsList", "Version": "2018-01-01", "ApiName": "运维接口列表", "ApiGroup": "运维管理", "ApiDesc": "运维接口列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribePgCollations", "Version": "2018-01-01", "ApiName": "获取排序规则列表", "ApiGroup": "数据源接口", "ApiDesc": "获取排序规则列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribePgTable", "Version": "2018-01-01", "ApiName": "获取PgTable信息", "ApiGroup": "数据源接口", "ApiDesc": "获取PgTable信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribePgUsers", "Version": "2018-01-01", "ApiName": "获取用户列表", "ApiGroup": "数据源接口", "ApiDesc": "获取用户列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribePreCheckDetail", "Version": "2018-01-01", "ApiName": "查看工单预检查详情", "ApiGroup": "", "ApiDesc": "查看工单预检查详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribePrimaryKeyRange", "Version": "2018-01-01", "ApiName": "查询主键范围", "ApiGroup": "", "ApiDesc": "查询主键范围", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribePrivilegeTicketDetail", "Version": "2018-01-01", "ApiName": "权限申请工单详情", "ApiGroup": "用户管理", "ApiDesc": "权限申请工单详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribePrivilegeTickets", "Version": "2018-01-01", "ApiName": "权限申请工单列表", "ApiGroup": "用户管理", "ApiDesc": "权限申请工单列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeProcedure", "Version": "2018-01-01", "ApiName": "查看procedure", "ApiGroup": "命令操作接口", "ApiDesc": "查看procedure", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeProcedures", "Version": "2018-01-01", "ApiName": "获取Procedure列表", "ApiGroup": "数据源接口", "ApiDesc": "获取Procedure列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeRealTimeInstances", "Version": "2018-01-01", "ApiName": "获取实时instance列表", "ApiGroup": "数据源接口", "ApiDesc": "获取实时instance列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeRegions", "Version": "2018-01-01", "ApiName": "获取region信息", "ApiGroup": "通用接口", "ApiDesc": "获取region信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeResourceTags", "Version": "2018-01-01", "ApiName": "查询实例标签", "ApiGroup": "标签服务", "ApiDesc": "查询实例标签", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeReviewDetailActionList", "Version": "2018-01-01", "ApiName": "SQL审核筛选：查询命中的规则名称集合", "ApiGroup": "", "ApiDesc": "SQL审核筛选：查询命中的规则名称集合", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeRole", "Version": "2018-01-01", "ApiName": "查看用户DBW角色", "ApiGroup": "用户管理", "ApiDesc": "查看用户DBW角色", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeRuleConfiguration", "Version": "2018-01-01", "ApiName": "查询系统安全规则的填写配置", "ApiGroup": "安全规则", "ApiDesc": "查询系统安全规则的填写配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeRuleExecuteRecordDetail", "Version": "2018-01-01", "ApiName": "分页查询安全规则执行记录的详情", "ApiGroup": "安全规则", "ApiDesc": "分页查询安全规则执行记录的详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeRuleExecuteRecordSummaryResult", "Version": "2018-01-01", "ApiName": "查询安全执行记录的汇总记录", "ApiGroup": "安全规则", "ApiDesc": "查询安全执行记录的汇总记录", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLAdvisorTableMeta", "Version": "2018-01-01", "ApiName": "查询表元信息", "ApiGroup": "", "ApiDesc": "查询表元信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLAdvisorTask", "Version": "2018-01-01", "ApiName": "查询索引优化任务", "ApiGroup": "", "ApiDesc": "查询索引优化任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLExecItemMetric", "Version": "2018-01-01", "ApiName": "获取某个指标的全量SQL执行曲线", "ApiGroup": "全量SQL", "ApiDesc": "获取某个指标的全量SQL执行曲线", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLExecNum", "Version": "2018-01-01", "ApiName": "获取全量SQL执行次数曲线", "ApiGroup": "全量SQL", "ApiDesc": "获取全量SQL执行次数曲线", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLExecTimeDetail", "Version": "2018-01-01", "ApiName": "查询全量SQL执行耗时阶段", "ApiGroup": "全量SQL", "ApiDesc": "查询全量SQL执行耗时阶段", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLExecTimeDistribution", "Version": "2018-01-01", "ApiName": "获取全量SQL执行耗时分布", "ApiGroup": "全量SQL", "ApiDesc": "获取全量SQL执行耗时分布", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLStatisticMetrics", "Version": "2018-01-01", "ApiName": "获取全量SQL模版指标折线图", "ApiGroup": "全量SQL", "ApiDesc": "获取全量SQL模版指标折线图", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLTemplateStatistic", "Version": "2018-01-01", "ApiName": "获取全量SQL模版统计情况", "ApiGroup": "全量SQL", "ApiDesc": "获取全量SQL模版统计情况", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLTimeElapseDistribution", "Version": "2018-01-01", "ApiName": "获取全量SQL执行耗时分布趋势", "ApiGroup": "全量SQL", "ApiDesc": "获取全量SQL执行耗时分布趋势", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSQLTimeElapseTotal", "Version": "2018-01-01", "ApiName": "获取全量SQL总耗时趋势", "ApiGroup": "全量SQL", "ApiDesc": "获取全量SQL总耗时趋势", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSampleData", "Version": "2018-01-01", "ApiName": "查询采样数据", "ApiGroup": "", "ApiDesc": "查询采样数据", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSchemas", "Version": "2018-01-01", "ApiName": "获取Schema列表", "ApiGroup": "数据源接口", "ApiDesc": "获取Schema列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSecurityGroupDetail", "Version": "2018-01-01", "ApiName": "获取安全规则集详情", "ApiGroup": "安全规则", "ApiDesc": "获取安全规则集详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSecurityGroups", "Version": "2018-01-01", "ApiName": "获取安全规则集列表", "ApiGroup": "安全规则", "ApiDesc": "获取安全规则集列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSecurityRule", "Version": "2018-01-01", "ApiName": "查看安全规则详情", "ApiGroup": "安全规则", "ApiDesc": "查看安全规则详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSequences", "Version": "2018-01-01", "ApiName": "获取Sequence列表", "ApiGroup": "数据源接口", "ApiDesc": "获取Sequence列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSession", "Version": "2018-01-01", "ApiName": "获取会话信息", "ApiGroup": "连接相关接口", "ApiDesc": "获取会话信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSlowLogTimeSeriesStats", "Version": "2018-01-01", "ApiName": "获取慢日志时序统计信息", "ApiGroup": "慢日志分析接口", "ApiDesc": "获取慢日志时序统计信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSlowLogs", "Version": "2018-01-01", "ApiName": "获取慢日志明细", "ApiGroup": "慢日志分析接口", "ApiDesc": "获取慢日志明细", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSlowLogsExportTasks", "Version": "2018-01-01", "ApiName": "获取慢日志导出任务列表", "ApiGroup": "慢日志分析接口", "ApiDesc": "获取慢日志导出任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSourceIPs", "Version": "2018-01-01", "ApiName": "获取聚合分组内的所有客户端IP", "ApiGroup": "慢日志分析接口", "ApiDesc": "获取聚合分组内的所有客户端IP", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlAuditCandidate", "Version": "2018-01-01", "ApiName": "SQL审计候选项查询", "ApiGroup": "SQL审计相关接口", "ApiDesc": "SQL审计候选项查询", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlAuditDetail", "Version": "2018-01-01", "ApiName": "SQL审计详情查询", "ApiGroup": "SQL审计相关接口", "ApiDesc": "SQL审计详情查询", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlAuditExportTasks", "Version": "2018-01-01", "ApiName": "离线导出任务列表", "ApiGroup": "SQL审计相关接口", "ApiDesc": "离线导出任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlAuditStatus", "Version": "2018-01-01", "ApiName": "SQL审计功能状态", "ApiGroup": "SQL审计相关接口", "ApiDesc": "SQL审计功能状态", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlAuditTls", "Version": "2018-01-01", "ApiName": "获取SQL审计日志服务信息", "ApiGroup": "SQL审计相关接口", "ApiDesc": "获取SQL审计日志服务信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlConcurrencyControlRuleDetail", "Version": "2018-01-01", "ApiName": "查看限流任务详情", "ApiGroup": "SQL限流", "ApiDesc": "查看限流任务详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlConcurrencyControlRules", "Version": "2018-01-01", "ApiName": "查看SQL限流任务列表", "ApiGroup": "SQL限流", "ApiDesc": "查看SQL限流任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlFingerPrint", "Version": "2018-01-01", "ApiName": "SQL指纹生成", "ApiGroup": "SQL限流", "ApiDesc": "SQL指纹生成", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlKeywords", "Version": "2018-01-01", "ApiName": "关键字生成和校验", "ApiGroup": "SQL限流", "ApiDesc": "关键字生成和校验", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlKillRules", "Version": "2018-01-01", "ApiName": "查询kill规则列表", "ApiGroup": "连接相关接口", "ApiDesc": "查询kill规则列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlReviewDetailList", "Version": "2018-01-01", "ApiName": "查询SQL审核详情单列表", "ApiGroup": "", "ApiDesc": "查询SQL审核详情单列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlReviewDetailSummaryResult", "Version": "2018-01-01", "ApiName": "查询SQL审核详情单汇总结果", "ApiGroup": "", "ApiDesc": "查询SQL审核详情单汇总结果", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlReviewList", "Version": "2018-01-01", "ApiName": "查询SQL审核列表", "ApiGroup": "", "ApiDesc": "查询SQL审核列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlTask", "Version": "2018-01-01", "ApiName": "查看单个Sql任务", "ApiGroup": "Sql任务", "ApiDesc": "查看单个Sql任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlTasks", "Version": "2018-01-01", "ApiName": "查看Sql任务列表", "ApiGroup": "Sql任务", "ApiDesc": "查看Sql任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeSqlTemplatesContrast", "Version": "2018-01-01", "ApiName": "获取全量SQL模版对比", "ApiGroup": "全量SQL", "ApiDesc": "获取全量SQL模版对比", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeStorageCapacity", "Version": "2018-01-01", "ApiName": "空间分析", "ApiGroup": "一键诊断", "ApiDesc": "空间分析", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTable", "Version": "2018-01-01", "ApiName": "获取Table信息", "ApiGroup": "数据源接口", "ApiDesc": "获取Table信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTableColumn", "Version": "2018-01-01", "ApiName": "表列信息获取", "ApiGroup": "一键诊断", "ApiDesc": "表列信息获取", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTableIndex", "Version": "2018-01-01", "ApiName": "表索引获取", "ApiGroup": "一键诊断", "ApiDesc": "表索引获取", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTableMetric", "Version": "2018-01-01", "ApiName": "获取全量sql表聚合", "ApiGroup": "全量SQL", "ApiDesc": "获取全量sql表聚合", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTableSpace", "Version": "2018-01-01", "ApiName": "表空间获取", "ApiGroup": "一键诊断", "ApiDesc": "表空间获取", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTableSpaceAutoIncr", "Version": "2018-01-01", "ApiName": "表空间自增列信息", "ApiGroup": "一键诊断", "ApiDesc": "表空间自增列信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTableSpaces", "Version": "2018-01-01", "ApiName": "获取表空间列表", "ApiGroup": "数据源接口", "ApiDesc": "获取表空间列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTables", "Version": "2018-01-01", "ApiName": "获取Table列表", "ApiGroup": "数据源接口", "ApiDesc": "获取Table列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTaskExecuteRecords", "Version": "2018-01-01", "ApiName": "DescribeTaskExecuteRecords", "ApiGroup": "", "ApiDesc": "DescribeTaskExecuteRecords", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTaskFlow", "Version": "2018-01-01", "ApiName": "DescribeTaskFlow", "ApiGroup": "", "ApiDesc": "DescribeTaskFlow", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTaskFlows", "Version": "2018-01-01", "ApiName": "DescribeTaskFlows", "ApiGroup": "", "ApiDesc": "DescribeTaskFlows", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTempCredentials", "Version": "2018-01-01", "ApiName": "获取STS临时凭证", "ApiGroup": "通用接口", "ApiDesc": "获取STS临时凭证", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTempTableSpace", "Version": "2018-01-01", "ApiName": "表空间获取临时表", "ApiGroup": "一键诊断", "ApiDesc": "表空间获取临时表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTicketDetail", "Version": "2018-01-01", "ApiName": "工单详情", "ApiGroup": "工单审批接口", "ApiDesc": "工单详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTicketLogDetail", "Version": "2018-01-01", "ApiName": "工单日志详情", "ApiGroup": "工单审批接口", "ApiDesc": "工单日志详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTicketRecordList", "Version": "2018-01-01", "ApiName": "查询工单管理操作审计列表", "ApiGroup": "", "ApiDesc": "查询工单管理操作审计列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTickets", "Version": "2018-01-01", "ApiName": "工单列表", "ApiGroup": "工单审批接口", "ApiDesc": "工单列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTopNFullSQLDetail", "Version": "2018-01-01", "ApiName": "获取全量sql执行topn sql", "ApiGroup": "全量SQL", "ApiDesc": "获取全量sql执行topn sql", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTopicDBW", "Version": "2018-01-01", "ApiName": "获取topic详情", "ApiGroup": "封装TLS", "ApiDesc": "获取topic详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTrigger", "Version": "2018-01-01", "ApiName": "获取Trigger信息", "ApiGroup": "数据源接口", "ApiDesc": "获取Trigger信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTriggers", "Version": "2018-01-01", "ApiName": "获取Trigger列表", "ApiGroup": "数据源接口", "ApiDesc": "获取Trigger列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTrxAndLocks", "Version": "2018-01-01", "ApiName": "获取事务和锁列表", "ApiGroup": "命令操作接口", "ApiDesc": "获取事务和锁列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTrxDetailSnapshot", "Version": "2018-01-01", "ApiName": "获取事务快照详情", "ApiGroup": "命令操作接口", "ApiDesc": "获取事务快照详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeTrxSnapshots", "Version": "2018-01-01", "ApiName": "获取事务快照列表", "ApiGroup": "命令操作接口", "ApiDesc": "获取事务快照列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeUser", "Version": "2018-01-01", "ApiName": "查看用户详情", "ApiGroup": "用户管理", "ApiDesc": "查看用户详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeUserGroup", "Version": "2018-01-01", "ApiName": "查看DBW用户组详情", "ApiGroup": "用户管理", "ApiDesc": "查看DBW用户组详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeUserGroups", "Version": "2018-01-01", "ApiName": "查看DBW用户组列表", "ApiGroup": "用户管理", "ApiDesc": "查看DBW用户组列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeUserPrivileges", "Version": "2018-01-01", "ApiName": "查看用户权限列表", "ApiGroup": "用户管理", "ApiDesc": "查看用户权限列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeUsers", "Version": "2018-01-01", "ApiName": "获取聚合分组内的所有用户", "ApiGroup": "慢日志分析接口", "ApiDesc": "获取聚合分组内的所有用户", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "Describe<PERSON>iew", "Version": "2018-01-01", "ApiName": "获取View信息", "ApiGroup": "数据源接口", "ApiDesc": "获取View信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeViews", "Version": "2018-01-01", "ApiName": "获取View列表", "ApiGroup": "数据源接口", "ApiDesc": "获取View列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DescribeWorkflow", "Version": "2018-01-01", "ApiName": "审批详情", "ApiGroup": "工单审批接口", "ApiDesc": "审批详情", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DisableInstanceManagement", "Version": "2018-01-01", "ApiName": "关闭安全管控", "ApiGroup": "服务实例管控", "ApiDesc": "关闭安全管控", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DisableSecurityRule", "Version": "2018-01-01", "ApiName": "禁用安全规则", "ApiGroup": "安全规则", "ApiDesc": "禁用安全规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DisableSqlConcurrencyControl", "Version": "2018-01-01", "ApiName": "关闭SQL限流功能", "ApiGroup": "SQL限流", "ApiDesc": "关闭SQL限流功能", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DropDatabase", "Version": "2018-01-01", "ApiName": "删除数据库", "ApiGroup": "数据源接口", "ApiDesc": "删除数据库", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DropEvent", "Version": "2018-01-01", "ApiName": "删除event", "ApiGroup": "数据源接口", "ApiDesc": "删除event", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DropFunction", "Version": "2018-01-01", "ApiName": "删除function", "ApiGroup": "命令操作接口", "ApiDesc": "删除function", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DropProcedure", "Version": "2018-01-01", "ApiName": "删除procudere", "ApiGroup": "命令操作接口", "ApiDesc": "删除procudere", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DropTable", "Version": "2018-01-01", "ApiName": "删除table", "ApiGroup": "数据源接口", "ApiDesc": "删除table", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DropTrigger", "Version": "2018-01-01", "ApiName": "删除trigger", "ApiGroup": "数据源接口", "ApiDesc": "删除trigger", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "DropView", "Version": "2018-01-01", "ApiName": "删除view", "ApiGroup": "数据源接口", "ApiDesc": "删除view", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "EditCustomSecurityRule", "Version": "2018-01-01", "ApiName": "根据规则ID更新自定义安全规则", "ApiGroup": "安全规则", "ApiDesc": "根据规则ID更新自定义安全规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "EditSecurityRuleGroup", "Version": "2018-01-01", "ApiName": "修改安全规则集", "ApiGroup": "安全规则", "ApiDesc": "修改安全规则集", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "EnableInstanceManagement", "Version": "2018-01-01", "ApiName": "开启安全管控", "ApiGroup": "服务实例管控", "ApiDesc": "开启安全管控", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "EnableSecurityRule", "Version": "2018-01-01", "ApiName": "启用安全规则", "ApiGroup": "安全规则", "ApiDesc": "启用安全规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "EnableSqlConcurrencyControl", "Version": "2018-01-01", "ApiName": "开启SQL限流功能", "ApiGroup": "SQL限流", "ApiDesc": "开启SQL限流功能", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ExecuteAssistantPrompt", "Version": "2018-01-01", "ApiName": "DBW_Copilot智能客服", "ApiGroup": "", "ApiDesc": "DBW_Copilot智能客服", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ExecuteCommandSet", "Version": "2018-01-01", "ApiName": "命令集执行", "ApiGroup": "命令操作接口", "ApiDesc": "命令集执行", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ExecuteFunction", "Version": "2018-01-01", "ApiName": "执行function", "ApiGroup": "命令操作接口", "ApiDesc": "执行function", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ExecuteMigrationTicket", "Version": "2018-01-01", "ApiName": "执行导入导出工单", "ApiGroup": "数据迁移接口", "ApiDesc": "执行导入导出工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ExecuteProcedure", "Version": "2018-01-01", "ApiName": "执行procudure", "ApiGroup": "命令操作接口", "ApiDesc": "执行procudure", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ExecutePrompt", "Version": "2018-01-01", "ApiName": "DBW_Copilot智能助手", "ApiGroup": "", "ApiDesc": "DBW_Copilot智能助手", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ExecuteTicket", "Version": "2018-01-01", "ApiName": "执行工单", "ApiGroup": "工单审批接口", "ApiDesc": "执行工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ForgetConsolePassword", "Version": "2018-01-01", "ApiName": "ForgetConsolePassword", "ApiGroup": "", "ApiDesc": "ForgetConsolePassword", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetArchiveNextSql", "Version": "2018-01-01", "ApiName": "获取下次执行delete-sql", "ApiGroup": "", "ApiDesc": "获取下次执行delete-sql", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetKeyMembers", "Version": "2018-01-01", "ApiName": "获取Key的值集合", "ApiGroup": "数据源接口", "ApiDesc": "获取Key的值集合", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetLogFiles", "Version": "2018-01-01", "ApiName": "获取下载列表", "ApiGroup": "内场api", "ApiDesc": "获取下载列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetMetricData", "Version": "2018-01-01", "ApiName": "获取监控数据", "ApiGroup": "异常诊断", "ApiDesc": "获取监控数据", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetMetricItems", "Version": "2018-01-01", "ApiName": "获取监控项", "ApiGroup": "异常诊断", "ApiDesc": "获取监控项", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetSQLAdvisorProtocol", "Version": "2018-01-01", "ApiName": "查询索引优化协议", "ApiGroup": "", "ApiDesc": "查询索引优化协议", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetSqlAdvice", "Version": "2018-01-01", "ApiName": "获取sql建议", "ApiGroup": "命令操作接口", "ApiDesc": "获取sql建议", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetSqlAssistantProtocol", "Version": "2018-01-01", "ApiName": "GetSqlAssistantProtocol", "ApiGroup": "", "ApiDesc": "GetSqlAssistantProtocol", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetSqlConcurrencyControlStatus", "Version": "2018-01-01", "ApiName": "获取SQL限流功能状态", "ApiGroup": "SQL限流", "ApiDesc": "获取SQL限流功能状态", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetTotalKeyNumber", "Version": "2018-01-01", "ApiName": "获取Redis的Key总数", "ApiGroup": "数据源接口", "ApiDesc": "获取Redis的Key总数", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GetUserProtocolState", "Version": "2018-01-01", "ApiName": "检查用户协议状态", "ApiGroup": "通用接口", "ApiDesc": "检查用户协议状态", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GrantUserGroupPrivilege", "Version": "2018-01-01", "ApiName": "授予用户组权限", "ApiGroup": "用户管理", "ApiDesc": "授予用户组权限", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "GrantUserPrivilege", "Version": "2018-01-01", "ApiName": "授予用户权限", "ApiGroup": "用户管理", "ApiDesc": "授予用户权限", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "IlmfHandleTradeMessage", "Version": "2018-01-01", "ApiName": "计费回调接口", "ApiGroup": "", "ApiDesc": "计费回调接口", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "InitUserManagementAdmin", "Version": "2018-01-01", "ApiName": "初始化管理员", "ApiGroup": "用户管理", "ApiDesc": "初始化管理员", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "KillProcess", "Version": "2018-01-01", "ApiName": "kill会话", "ApiGroup": "实例会话接口", "ApiDesc": "kill会话", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ListApprovalAssociatedInstance", "Version": "2018-01-01", "ApiName": "获取审批关联实例列表", "ApiGroup": "", "ApiDesc": "获取审批关联实例列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ListApprovalFlowConfig", "Version": "2018-01-01", "ApiName": "查询审批配置列表", "ApiGroup": "", "ApiDesc": "查询审批配置列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ListApprovalNode", "Version": "2018-01-01", "ApiName": "查询审批节点列表", "ApiGroup": "", "ApiDesc": "查询审批节点列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ListChatHistory", "Version": "2018-01-01", "ApiName": "对话历史", "ApiGroup": "对话历史", "ApiDesc": "对话历史", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ListChats", "Version": "2018-01-01", "ApiName": "对话列表", "ApiGroup": "对话列表", "ApiDesc": "对话列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ListSlowQueryAdvice", "Version": "2018-01-01", "ApiName": "获取慢日志诊断建议", "ApiGroup": "慢日志诊断接口", "ApiDesc": "获取慢日志诊断建议", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ListSlowQueryAdviceApi", "Version": "2018-01-01", "ApiName": "获取慢日志诊断建议", "ApiGroup": "慢日志诊断接口", "ApiDesc": "获取慢日志诊断建议", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ListSlowQueryAdviceConfig", "Version": "2018-01-01", "ApiName": "获取慢日志诊断任务列表", "ApiGroup": "慢日志诊断接口", "ApiDesc": "获取慢日志诊断任务列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyAbnormalDetectionConfig", "Version": "2018-01-01", "ApiName": "修改异常检测配置", "ApiGroup": "异常检测", "ApiDesc": "修改异常检测配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyApprovalFlowConfig", "Version": "2018-01-01", "ApiName": "修改审批配置", "ApiGroup": "", "ApiDesc": "修改审批配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyApprovalNode", "Version": "2018-01-01", "ApiName": "修改审批节点", "ApiGroup": "", "ApiDesc": "修改审批节点", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyAuditLogConfig", "Version": "2018-01-01", "ApiName": "修改审计日志保存时长", "ApiGroup": "", "ApiDesc": "修改审计日志保存时长", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyAuditPodImage", "Version": "2018-01-01", "ApiName": "更新审计实例采集器镜像（运维）", "ApiGroup": "SQL审计相关接口", "ApiDesc": "更新审计实例采集器镜像（运维）", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyAutoKillSessionConfig", "Version": "2018-01-01", "ApiName": "修改sql kill配置", "ApiGroup": "连接相关接口", "ApiDesc": "修改sql kill配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyDBAutoStorageScaling", "Version": "2018-01-01", "ApiName": "修改扩缩容配置", "ApiGroup": "磁盘扩缩容-precheck", "ApiDesc": "修改扩缩容配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyDiskDBAutoScalingConfig", "Version": "2018-01-01", "ApiName": "修改扩缩容配置", "ApiGroup": "磁盘扩缩容", "ApiDesc": "修改扩缩容配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyFullSqlConfig", "Version": "2018-01-01", "ApiName": "修改全量SQL配置", "ApiGroup": "全量SQL", "ApiDesc": "修改全量SQL配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyInstanceClusters", "Version": "2018-01-01", "ApiName": "修改实例集群", "ApiGroup": "修改实例集群", "ApiDesc": "修改实例集群", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyLogCollectorImage", "Version": "2018-01-01", "ApiName": "更新log-collector镜像", "ApiGroup": "运维管理", "ApiDesc": "更新log-collector镜像", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifySqlConcurrencyControlRule", "Version": "2018-01-01", "ApiName": "修改SQL限流规则", "ApiGroup": "SQL限流", "ApiDesc": "修改SQL限流规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "ModifyTicket", "Version": "2018-01-01", "ApiName": "修改工单", "ApiGroup": "工单审批接口", "ApiDesc": "修改工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "OrderCreateAudit", "Version": "2018-01-01", "ApiName": "SQL审计新建创建订单", "ApiGroup": "SQL审计订单", "ApiDesc": "SQL审计新建创建订单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "OrderTerminateAudit", "Version": "2018-01-01", "ApiName": "SQL审计新建删除订单", "ApiGroup": "SQL审计订单", "ApiDesc": "SQL审计新建删除订单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "PreCheckCreateFullSql", "Version": "2018-01-01", "ApiName": "全量SQL开启预检查", "ApiGroup": "全量SQL", "ApiDesc": "全量SQL开启预检查", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "PreCheckMigrationTicket", "Version": "2018-01-01", "ApiName": "预检查导入导出工单", "ApiGroup": "数据迁移接口", "ApiDesc": "预检查导入导出工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "PreCheckTicket", "Version": "2018-01-01", "ApiName": "预检查工单", "ApiGroup": "工单审批接口", "ApiDesc": "预检查工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "PrivilegeTicketAction", "Version": "2018-01-01", "ApiName": "权限申请工单操作", "ApiGroup": "用户管理", "ApiDesc": "权限申请工单操作", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "RateModelReply", "Version": "2018-01-01", "ApiName": "DBW_Copilot评价模型返回（点赞+点踩）", "ApiGroup": "", "ApiDesc": "DBW_Copilot评价模型返回（点赞+点踩）", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "RemoveTagsFromResource", "Version": "2018-01-01", "ApiName": "删除标签", "ApiGroup": "标签服务", "ApiDesc": "删除标签", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "RenameTable", "Version": "2018-01-01", "ApiName": "重命名table", "ApiGroup": "数据源接口", "ApiDesc": "重命名table", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "Report", "Version": "2018-01-01", "ApiName": "反馈", "ApiGroup": "反馈", "ApiDesc": "反馈", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "RestartSqlConcurrencyControlRule", "Version": "2018-01-01", "ApiName": "启用SQL限流任务", "ApiGroup": "SQL限流", "ApiDesc": "启用SQL限流任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "RevokeUserGroupPrivilege", "Version": "2018-01-01", "ApiName": "回收用户组权限", "ApiGroup": "用户管理", "ApiDesc": "回收用户组权限", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "RevokeUserPrivilege", "Version": "2018-01-01", "ApiName": "回收用户权限", "ApiGroup": "用户管理", "ApiDesc": "回收用户权限", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SaveConsolePassword", "Version": "2018-01-01", "ApiName": "SaveConsolePassword", "ApiGroup": "", "ApiDesc": "SaveConsolePassword", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SearchFunction", "Version": "2018-01-01", "ApiName": "搜索function", "ApiGroup": "数据源接口", "ApiDesc": "搜索function", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SearchLogDBW", "Version": "2018-01-01", "ApiName": "查询分析日志", "ApiGroup": "封装TLS", "ApiDesc": "查询分析日志", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SearchProcedure", "Version": "2018-01-01", "ApiName": "搜索procudere", "ApiGroup": "数据源接口", "ApiDesc": "搜索procudere", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SearchTable", "Version": "2018-01-01", "ApiName": "搜索table", "ApiGroup": "数据源接口", "ApiDesc": "搜索table", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SearchTrigger", "Version": "2018-01-01", "ApiName": "搜索trigger", "ApiGroup": "数据源接口", "ApiDesc": "搜索trigger", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SearchView", "Version": "2018-01-01", "ApiName": "搜索view", "ApiGroup": "数据源接口", "ApiDesc": "搜索view", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SessionKeepAlive", "Version": "2018-01-01", "ApiName": "会话保持", "ApiGroup": "连接相关接口", "ApiDesc": "会话保持", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SlowQueryAdviceTaskHistory", "Version": "2018-01-01", "ApiName": "获取慢日志诊断任务历史", "ApiGroup": "慢日志诊断接口", "ApiDesc": "获取慢日志诊断任务历史", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SlowQueryAdviceTaskHistoryApi", "Version": "2018-01-01", "ApiName": "获取慢日志诊断任务历史", "ApiGroup": "慢日志诊断接口", "ApiDesc": "获取慢日志诊断任务历史", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SqlAssistant", "Version": "2018-01-01", "ApiName": "sql助手", "ApiGroup": "sql助手", "ApiDesc": "sql助手", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SqlAssistant", "Version": "2018-01-01", "ApiName": "OpenAPI-NL2SQL", "ApiGroup": "sql助手", "ApiDesc": "自然语言生成SQL", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SqlCorrect", "Version": "2018-01-01", "ApiName": "SqlCorrect", "ApiGroup": "", "ApiDesc": "SqlCorrect", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "StopDataMigrationTask", "Version": "2018-01-01", "ApiName": "终止导入导出任务", "ApiGroup": "运维管理", "ApiDesc": "终止导入导出任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "StopSqlConcurrencyControlRule", "Version": "2018-01-01", "ApiName": "终止SQL限流任务", "ApiGroup": "SQL限流", "ApiDesc": "终止SQL限流任务", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "StopSqlKillRule", "Version": "2018-01-01", "ApiName": "终止sql kill配置", "ApiGroup": "连接相关接口", "ApiDesc": "终止sql kill配置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "StopTicket", "Version": "2018-01-01", "ApiName": "工单终止", "ApiGroup": "工单审批接口", "ApiDesc": "工单终止", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SubmitApproveSqlReview", "Version": "2018-01-01", "ApiName": "提交审批", "ApiGroup": "", "ApiDesc": "提交审批", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SubmitMigrationTicket", "Version": "2018-01-01", "ApiName": "提交导入导出工单", "ApiGroup": "数据迁移接口", "ApiDesc": "提交导入导出工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SubmitTicket", "Version": "2018-01-01", "ApiName": "提交工单", "ApiGroup": "", "ApiDesc": "提交工单", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "SyncDBInstances", "Version": "2018-01-01", "ApiName": "同步instance列表", "ApiGroup": "数据源接口", "ApiDesc": "同步instance列表", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "read"}, {"K": "policy", "V": "dbwDASReadOnlyAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateConsoleConnEnv", "Version": "2018-01-01", "ApiName": "UpdateConsoleConnEnv", "ApiGroup": "", "ApiDesc": "UpdateConsoleConnEnv", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateCopilotChatName", "Version": "2018-01-01", "ApiName": "DBW_Copilot 更新会话名称", "ApiGroup": "", "ApiDesc": "DBW_Copilot 更新会话名称", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateInstanceManagementConfig", "Version": "2018-01-01", "ApiName": "更新安全管控设置", "ApiGroup": "服务实例管控", "ApiDesc": "更新安全管控设置", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateMyFavouriteSQL", "Version": "2018-01-01", "ApiName": "UpdateMyFavouriteSQL", "ApiGroup": "", "ApiDesc": "UpdateMyFavouriteSQL", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateOnlineDDLSecurityRule", "Version": "2018-01-01", "ApiName": "更新无锁变更参数安全规则", "ApiGroup": "安全规则", "ApiDesc": "更新无锁变更参数安全规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateSecurityRule", "Version": "2018-01-01", "ApiName": "更新安全规则", "ApiGroup": "安全规则", "ApiDesc": "更新安全规则", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateSlowQueryAnalysis", "Version": "2018-01-01", "ApiName": "更新慢日志分析开关", "ApiGroup": "慢日志诊断接口", "ApiDesc": "更新慢日志分析开关", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateTaskFlow", "Version": "2018-01-01", "ApiName": "UpdateTaskFlow", "ApiGroup": "", "ApiDesc": "UpdateTaskFlow", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateUser", "Version": "2018-01-01", "ApiName": "更新用户信息", "ApiGroup": "用户管理", "ApiDesc": "更新用户信息", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpdateUserGroup", "Version": "2018-01-01", "ApiName": "更新DBW用户组", "ApiGroup": "用户管理", "ApiDesc": "更新DBW用户组", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "admin"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpgradeFullsql", "Version": "2018-01-01", "ApiName": "批量升级全量sql", "ApiGroup": "运维管理", "ApiDesc": "批量升级全量sql", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UpgradeFullsqlTableAggr", "Version": "2018-01-01", "ApiName": "升级全量sql支持表写入分析", "ApiGroup": "运维管理", "ApiDesc": "升级全量sql支持表写入分析", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "das"}, {"K": "privilege", "V": "readwrite"}, {"K": "policy", "V": "dbwDASFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "UserGroupSync", "Version": "2018-01-01", "ApiName": "同步用户组数据", "ApiGroup": "用户管理", "ApiDesc": "同步用户组数据", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}, {"Action": "WorkflowAction", "Version": "2018-01-01", "ApiName": "审批动作", "ApiGroup": "工单审批接口", "ApiDesc": "审批动作", "Psm": "", "Path": "/", "Annotations": [{"K": "modular", "V": "all"}, {"K": "privilege", "V": "pass"}, {"K": "policy", "V": "dbwFullAccess"}], "ApiPermission": {"Timeout": 30000, "QpsLimit": 0, "IsInner": 0, "IsAuth": 1, "IsSign": 1, "Is2xx": 1, "IsCross": 0}}]
package main

import (
	"context"
	"errors"
	"os"
	"sync"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing_msg"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_copilot"
	"code.byted.org/infcs/dbw-mgr/biz/service/db_copilot/agent_service"
	dbw_syncer_svc "code.byted.org/infcs/dbw-mgr/biz/service/dbw_syncer"
	"code.byted.org/infcs/dbw-mgr/biz/service/project"
	"code.byted.org/infcs/dbw-mgr/biz/service/slowquery"
	"code.byted.org/infcs/dbw-mgr/biz/service/tag"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/migration"
	"code.byted.org/infcs/ds-lib/framework/mgr"
	"code.byted.org/infcs/lib-mgr-common/volc"

	"code.byted.org/infcs/dbw-mgr/biz/instance/control"
	"code.byted.org/infcs/protoactor-go/cluster"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/middleware"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/iserver"
	"code.byted.org/infcs/ds-lib/framework/log/sentry"

	"code.byted.org/infcs/dbw-mgr/biz/apigateway"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/server"
	aci "code.byted.org/infcs/dbw-mgr/biz/service/audit/cache/instance"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit/k8s"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/byterds"
	"code.byted.org/infcs/dbw-mgr/biz/service/metrics"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
)

type NewAppIn struct {
	dig.In
	ActorSystem                 actorsystem.ActorSystem
	Server                      iserver.Server
	MetaMigration               *migration.MetaMigration
	Config                      config.ConfigProvider
	C3Config                    c3.ConfigProvider
	Metrics                     metrics.ConnectionMetrics
	ActorClient                 cli.ActorClient
	AuditResClientProvider      k8s.AuditResClientProvider
	PGAuditResClientProvider    k8s.AuditResClientPGProvider
	RedisAuditResClientProvider k8s.AuditResClientRedisProvider
	MongoAuditResClientProvider k8s.AuditResClientMongoProvider
	NewHTTPServer               server.Server
	BillingMsgService           billing_msg.BillingMsgService
	BillingService              billing.BillingService
	TagService                  tag.TagService
	ProjectService              project.ProjectService
	AuditInsCacheManager        aci.AuditInstanceManager
	InstanceManager             control.InstanceManager
	UserSvc                     usermgmt.UserService
	FornaxService               data_copilot.FornaxService
	MonitorAgentService         agent_service.MonitorAgentService
	DbwSyncer                   dbw_syncer_svc.SyncerInterface
	SlowLogService              slowquery.SlowLogService
}

type App struct {
	chain         []appHook
	shutdownChain []shutdownFunc
}

func NewApp(app NewAppIn) *App {
	return &App{
		chain: []appHook{
			{
				start: func(ctx context.Context) error {
					if utils.IsByteCloud() {
						mgr.AddMiddleware(middleware.Forward(byterds.NewByteRDSClient(app.Config)))
						return nil
					}
					mgr.AddMiddleware(middleware.AddMetaMySQLRegionName(app.InstanceManager))
					mgr.AddMiddleware(middleware.CheckByActionMW(app.UserSvc, app.Config))
					if app.Config.Get(context.Background()).DisableOpenTOPAKSK {
						volc.SetUseIndependentEndpointRegion(utils.GetRegion())
					}
					return nil
				},
			},
			/* publish config changed event */
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. publish config changed event.")
					app.Config.AddUpdateHook(func(evt *shared.ConfigUpdated) {
						app.ActorSystem.EventStream().Publish(context.Background(), evt)
					})
					log.Info(ctx, "init end. publish config changed event.")
					return nil
				},
			},
			/* subscribe events */
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. subscribe events.")
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *shared.ConnectionEstablished) {
						err := app.Metrics.MarkConnectionActive(context.Background(), evt.Source, evt.ConnectionId)
						if err != nil {
							log.Warn(ctx, "recv ConnectionEstablished, MarkConnectionActive error %s", err)
						}
					})
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *shared.ConnectionClosed) {
						err := app.Metrics.MarkConnectionInactive(context.Background(), evt.ConnectionId)
						if err != nil {
							log.Warn(ctx, "recv ConnectionClosed, MarkConnectionInactive error %s", err)
						}
					})
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *shared.ConfigUpdated) {
						app.Config.Refresh(ctx)
						app.DbwSyncer.ConfigUpdate(ctx)
					})
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *shared.C3ConfigRefresh) {
						app.ActorSystem.EventStream().Publish(ctx, &shared.C3ConfigUpdated{
							NamespaceName: evt.NamespaceName,
							NewConfig:     evt.NewConfig,
						})
					})
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *shared.ConfigUpdated) {
						err := app.ActorClient.KindOf(consts.DeployActorKind).
							Send(ctx, consts.SingletonActorName, &shared.StartDeploy{})
						if err != nil {
							log.Error(ctx, "DeployActor error %s", err)
							// 是否需要终止？
						}
						cnf := app.Config.Get(ctx)
						if cnf.ConnectionsSentryDSN != "" {
							app.ActorSystem.EventStream().Publish(ctx, &shared.ConnectionConfigUpdated{
								SentryDsn: cnf.ConnectionsSentryDSN,
							})
						}
						if cnf.MgrSentryDSN != "" {
							initSentry(ctx, cnf.MgrSentryDSN)
						}
					})
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *cluster.MemberJoinedEvent) {
						cnf := app.Config.Get(ctx)
						if cnf.ConnectionsSentryDSN != "" {
							app.ActorSystem.EventStream().Publish(ctx, &shared.ConnectionConfigUpdated{
								SentryDsn: cnf.ConnectionsSentryDSN,
							})
						}
					})
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *shared.C3ConfigUpdated) {
						app.C3Config.Update(ctx, evt)
					})
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *shared.NewAuditInstance) {
						app.AuditInsCacheManager.Add(evt.InstanceId)
					})
					app.ActorSystem.EventStream().On(func(ctx context.Context, evt *shared.RemoveAuditInstance) {
						app.AuditInsCacheManager.Remove(evt.InstanceId)
					})
					log.Info(ctx, "init end. subscribe events.")
					return nil
				},
			},
			/* migrate meta tables */
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. migrate meta tables.")
					err := app.MetaMigration.Do(ctx)
					if err != nil {
						log.Error(ctx, "init error. migrate meta tables. %s", err)
						return err
					}
					log.Info(ctx, "init end. migrate meta tables.")
					return nil
				},
			},
			/* start actor system */
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. actor system.")
					err := app.ActorSystem.Start()
					if err != nil {
						log.Error(ctx, "init error. actor system. %s", err)
						return err
					}
					log.Info(ctx, "init end. actor system.")
					return nil
				},
				shutdown: app.ActorSystem.Shutdown,
			},
			/* init sentry */
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. sentry.")
					initSentry(ctx, app.Config.Get(ctx).MgrSentryDSN)
					log.Info(ctx, "init end. sentry.")
					return nil
				},
				shutdown: func() {
					if sentryLogger != nil {
						sentryLogger.Flush()
					}
				},
			},
			// load audit instance
			{
				start: func(ctx context.Context) error {
					if utils.IsByteCloud() {
						return nil
					}
					if err := app.AuditInsCacheManager.Load(ctx); err != nil {
						log.Error(ctx, "load audit instance error, err=%s", err)
						return err
					}
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					go func() {
						defer func() {
							if r := recover(); r != nil {
								log.Error(ctx, "resume audit k8s watcher panic, err=%s", r)
								return
							}
						}()
						if utils.IsByteCloud() {
							return
						}
						for {
							log.Info(ctx, "init start. resume audit k8s watcher.")
							err := app.AuditResClientProvider.ResumeK8sWatcher(ctx)
							if err != nil {
								log.Error(ctx, "init error. resume audit k8s watcher. %s", err)
								time.Sleep(time.Second * 5)
								continue
							}
							log.Info(ctx, "init end. resume audit k8s watcher.")
							break
						}
					}()
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					go func() {
						defer func() {
							if r := recover(); r != nil {
								log.Error(ctx, "resume audit k8s watcher panic, err=%s", r)
								return
							}
						}()
						if utils.IsByteCloud() {
							return
						}
						for {
							log.Info(ctx, "init start. resume audit k8s watcher.")
							err := app.PGAuditResClientProvider.ResumePGWatcher(ctx)
							if err != nil {
								log.Error(ctx, "init error. resume audit k8s watcher. %s", err)
								time.Sleep(time.Second * 5)
								continue
							}
							log.Info(ctx, "init end. resume audit k8s watcher.")
							break
						}
					}()

					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					go func() {
						defer func() {
							if r := recover(); r != nil {
								log.Error(ctx, "resume audit k8s watcher panic, err=%s", r)
								return
							}
						}()
						if utils.IsByteCloud() {
							return
						}
						for {
							log.Info(ctx, "init start. resume audit k8s watcher.")
							err := app.RedisAuditResClientProvider.ResumeRedisWatcher(ctx)
							if err != nil {
								log.Error(ctx, "init error. resume audit k8s watcher. %s", err)
								time.Sleep(time.Second * 5)
								continue
							}
							log.Info(ctx, "init end. resume audit k8s watcher.")
							break
						}
					}()
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					go func() {
						defer func() {
							if r := recover(); r != nil {
								log.Error(ctx, "resume audit k8s watcher panic, err=%s", r)
								return
							}
						}()
						for {
							log.Info(ctx, "init start. resume mongo audit k8s daemonset.")
							err := app.MongoAuditResClientProvider.InspectionDaemonset(ctx, shared.Mongo)
							if err != nil {
								log.Error(ctx, "init error. resume mongo audit k8s daemonset. %s", err)
								time.Sleep(time.Hour * 1)
								continue
							}
							log.Info(ctx, "init end. resume mongo audit k8s daemonset.")
							break
						}
					}()
					return nil
				},
			},
			/* init c3 actor */
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. init c3 actor")
					// TODO 兼容字节云
					_, err := app.ActorClient.KindOf(consts.ApolloActorKind).
						Call(ctx, consts.ApolloActorName, &shared.AreYouOK{}, app.ActorClient.NewCallOpts().WithTimeout(time.Second*5))
					if err != nil {
						log.Error(ctx, "init error. init c3 actor. %s", err)
						return err
					}
					log.Info(ctx, "init end. init c3 actor")
					return nil
				},
			},
			/* init dialog mgr actor */
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. init dialog mgr actor")
					_, err := app.ActorClient.KindOf(consts.DialogMgrActorKind).
						Call(ctx, consts.SingletonActorName, &shared.AreYouOK{}, app.ActorClient.NewCallOpts().WithTimeout(time.Second*5))
					if err != nil {
						log.Error(ctx, "init error.init dialog mgr actor. %s", err)
						return err
					}
					log.Info(ctx, "init end. init dialog mgr actor")
					return nil
				},
			},
			/* init clean actor*/
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. init clean actor")
					_, err := app.ActorClient.KindOf(consts.CleanActorKind).
						Call(ctx, consts.SingletonActorName, &shared.AreYouOK{}, app.ActorClient.NewCallOpts().WithTimeout(time.Second*5))
					if err != nil {
						log.Error(ctx, "init error.init clean actor. %s", err)
						return err
					}
					log.Info(ctx, "init end. init clean actor")
					return nil
				},
			},
			/* init taskInspection actor*/
			{
				start: func(ctx context.Context) error {
					_, err := app.ActorClient.KindOf(consts.TaskInspectionActorKind).
						Call(ctx, consts.SingletonActorName, &shared.AreYouOK{}, app.ActorClient.NewCallOpts().WithTimeout(time.Second*5))
					return err
				},
			},
			/* init userMgmt sync actor*/
			{
				start: func(ctx context.Context) error {
					go func() {
						if utils.IsByteCloud() {
							return
						}
						time.Sleep(120 * time.Second)
						pid := app.ActorClient.KindOf(consts.UserMgmtActorKind).GetPID(consts.SingletonActorName)
						if pid == nil {
							err := errors.New("pid is nil")
							log.Error(ctx, "init error.init UserMgmtActor. %s", err)
							return
						}
						log.Info(ctx, "init start. init UserMgmtActor")
					}()
					return nil
				},
			},
			/* init ticketInspection actor*/
			{
				start: func(ctx context.Context) error {
					_, err := app.ActorClient.KindOf(consts.InspectionTicketActorKind).
						Call(ctx, consts.SingletonActorName, &shared.AreYouOK{}, app.ActorClient.NewCallOpts().WithTimeout(time.Second*5))
					return err
				},
			},
			/* init CCLRuleInspection actor*/
			{
				start: func(ctx context.Context) error {
					_, err := app.ActorClient.KindOf(consts.CCLRuleInspectionActorKind).
						Call(ctx, consts.SingletonActorName, &shared.AreYouOK{}, app.ActorClient.NewCallOpts().WithTimeout(time.Second*5))
					return err
				},
			},
			/* init check Inspection actor*/
			{
				start: func(ctx context.Context) error {
					_, err := app.ActorClient.KindOf(consts.CheckInspectionActorKind).
						Call(ctx, consts.SingletonActorName, &shared.AreYouOK{}, app.ActorClient.NewCallOpts().WithTimeout(time.Second*5))
					return err
				},
			},
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. init SLAAuditMetricReporterActor")
					if utils.IsByteCloud() {
						return nil
					}
					pid := app.ActorClient.KindOf(consts.SLAAuditMetricReporterActorKind).
						GetPID(consts.SingletonActorName, app.ActorClient.NewCallOpts().WithTimeout(time.Second*10))
					if pid == nil {
						err := errors.New("pid is nil")
						log.Error(ctx, "init error.init SLAAuditMetricReporterActor. %s", err)
						return err
					}
					log.Info(ctx, "init start. init SLAAuditMetricReporterActor")
					return nil
				},
			},
			/* init abnormal detection producer actor*/
			{
				start: func(ctx context.Context) error {
					if consts.ABNORMAL_DETECTION_SWITCH {
						log.Warn(ctx, "测试环境环境变量ABNORMAL_DETECTION_SWITCH=OFF，DetectionProducerActor")
						return nil
					}
					if utils.IsByteCloud() {
						return nil
					}
					app.ActorClient.KindOf(consts.DetectionProducerActorKind).GetPID(consts.SingletonActorName)
					return nil
				},
			},
			/* init abnormal detection consumer actor*/
			{
				start: func(ctx context.Context) error {
					if consts.ABNORMAL_DETECTION_SWITCH {
						log.Warn(ctx, "测试环境环境变量ABNORMAL_DETECTION_SWITCH=OFF，DetectionConsumerActor")
						return nil
					}
					if utils.IsByteCloud() {
						return nil
					}
					app.ActorClient.KindOf(consts.DetectionConsumerActorKind).GetPID(consts.SingletonActorName)
					return nil
				},
			},
			/* init cloud monitor actor */
			{
				start: func(ctx context.Context) error {
					if utils.IsByteCloud() {
						return nil
					}
					app.ActorClient.KindOf(consts.CloudMonitorActorKind).GetPID(consts.SingletonActorName)
					return nil
				},
			},
			/* 初始化SQL审核Actor，拉起SQL审核自动任务 */
			{
				start: func(ctx context.Context) error {
					app.ActorClient.KindOf(consts.SQLReviewActorKind).GetPID(consts.SingletonActorName)
					return nil
				},
			},
			/* init billing message */
			{
				start: func(ctx context.Context) error {
					if consts.MQ_CONSUME_SWITCH {
						log.Warn(ctx, "测试环境环境变量MQ_CONSUME_SWITCH=OFF，跳过方法StartHandleMsg")
						return nil
					}
					if utils.IsByteCloud() {
						return nil
					}
					log.Info(ctx, "init start. init billing message")
					app.BillingService.StartHandleMsg(ctx, app.BillingMsgService)
					log.Info(ctx, "init end. init billing message")
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. init tag service")
					if utils.IsByteCloud() {
						return nil
					}
					app.TagService.Init(ctx)
					log.Info(ctx, "init end. init tag service")
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					if utils.IsByteCloud() {
						return nil
					}
					log.Info(ctx, "init start. init project service")
					app.ProjectService.Init(ctx)
					log.Info(ctx, "init end. init project service")
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. init FornaxService")
					app.FornaxService.Init(ctx)
					app.MonitorAgentService.Init(ctx)
					log.Info(ctx, "init end. init FornaxService")
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					if consts.MQ_CONSUME_SWITCH {
						log.Warn(ctx, "测试环境环境变量MQ_CONSUME_SWITCH=OFF，跳过方法BillingScheduleCheckActor")
						return nil
					}
					if utils.IsByteCloud() {
						return nil
					}
					log.Info(ctx, "init start. init BillingScheduleCheckActor")
					pid := app.ActorClient.KindOf(consts.BillingScheduleCheckActorKind).
						GetPID(consts.SingletonActorName, app.ActorClient.NewCallOpts().WithTimeout(time.Second*10))
					if pid == nil {
						err := errors.New("pid is nil")
						log.Error(ctx, "init error.init BillingScheduleCheckActor. %s", err)
						return err
					}
					log.Info(ctx, "init end. init BillingScheduleCheckActor")
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					if consts.MQ_CONSUME_SWITCH {
						log.Warn(ctx, "测试环境环境变量MQ_CONSUME_SWITCH=OFF，跳过ScheduleInstanceCheckActor")
						return nil
					}
					if utils.IsByteCloud() {
						return nil
					}
					log.Info(ctx, "init start. init ScheduleInstanceCheckActor")
					pid := app.ActorClient.KindOf(consts.ScheduleInstanceCheckActorKind).
						GetPID(consts.SingletonActorName, app.ActorClient.NewCallOpts().WithTimeout(time.Second*10))
					if pid == nil {
						err := errors.New("pid is nil")
						log.Error(ctx, "init error.init ScheduleInstanceCheckActor. %s", err)
						return err
					}
					log.Info(ctx, "init end. init ScheduleInstanceCheckActor")
					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					if utils.IsByteCloud() {
						gateway := apigateway.NewGatewayService(app.Config)
						if err := gateway.Start(); err != nil {
							return err
						}
					}

					return nil
				},
			},
			{
				start: func(ctx context.Context) error {
					err := app.NewHTTPServer.Start()
					if err != nil {
						log.Error(ctx, "init error. server error. %s", err)
						return err
					}
					return nil
				},
				shutdown: app.NewHTTPServer.Shutdown,
			},
			{
				start: func(ctx context.Context) error {
					if !utils.IsByteCloud() {
						return nil
					}
					go func() {
						log.Info(ctx, "init start. InitInnerSlowLogTopics")

						err := app.SlowLogService.InitInnerSlowLogTopics(ctx, "inner-rds-slowlog", "inner-rds-slowlog")
						if err != nil {
							log.Warn(ctx, "init error. init inner slow log topics error. %s", err)
						}
					}()
					return nil
				},
			},
			/* start server 这个一定要放到chain的最后一个 */
			{
				start: func(ctx context.Context) error {
					log.Info(ctx, "init start. start server")
					err := app.Server.Serve()
					if err != nil {
						log.Error(ctx, "init error. server error. %s", err)
						return err
					}
					log.Info(ctx, "init end. server end")
					return nil
				},
				shutdown: app.Server.Shutdown,
			},
		},
	}
}

func (app *App) Start(ctx context.Context) error {
	return fp.StreamOf(app.chain).
		Map(func(f appHook) appHook {
			if f.start == nil {
				f.start = func(context.Context) error { return nil }
			}
			if f.shutdown == nil {
				f.shutdown = func() {}
			}
			return f
		}).
		Map(func(f appHook) (shutdownFunc, error) {
			return f.shutdown, f.start(ctx)
		}).
		ToSlice(&app.shutdownChain)
}

func (app *App) Shutdown() {
	fp.StreamOf(app.shutdownChain).
		Foreach(func(f shutdownFunc) {
			f()
		}).
		Run()
}

type startFunc func(context.Context) error

type shutdownFunc func()

type appHook struct {
	start    startFunc
	shutdown shutdownFunc
}

var (
	sentryLogger   log.Logger
	initSentryOnce sync.Once
)

func initSentry(ctx context.Context, dsn string) {
	if dsn == "" {
		return
	}
	var err error
	initSentryOnce.Do(func() {
		sentryLogger, err = sentry.New(sentry.Options{
			DSN:          dsn,
			Level:        "ERROR",
			SkipFrameNum: 2,
			Tags: map[string]string{
				"region": os.Getenv(`BDC_REGION_ID`),
				"pod":    os.Getenv(`POD_NAME`),
			},
		})
		if err != nil {
			log.Warn(ctx, "init sentry fail %v", err)
			return
		}
		log.AddHook(sentryLogger)
		log.Info(ctx, "use sentry: %s", dsn)
	})
}

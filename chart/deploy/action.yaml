Actions:
- ServiceName: dbw
  ActionList:
  - AddControlInstance
  - AddCustomSecurityRule
  - AddFullSqlCollectionFingerprint
  - AddMyFavouriteSQL
  - AddSecurityRuleGroup
  - AddSqlReview
  - AddTagsToResource
  - AddUser
  - AgreeDataCollection
  - AgreeSQLAdvisorProtocol
  - AgreeSqlAssistantProtocol
  - AgreeUserProtocol
  - AlterDatabase
  - AlterKVs
  - AlterRows
  - AlterTable
  - AutoScale
  - BatchOpenInstanceFunction
  - CancelCommandSet
  - CancelMigrationTicket
  - CancelTicket
  - ChangeDB
  - Chat
  - CheckConn
  - CheckMgrSelfHealth
  - CheckResHealth
  - CloseConnection
  - CloseSession
  - CopilotButtonGeneration
  - CopyTable
  - CopyTableSchemaOnly
  - CreateApprovalFlowConfig
  - CreateApprovalNode
  - CreateAutomaticInspection
  - CreateChat
  - CreateConnection
  - CreateCopilotChat
  - CreateDatabase
  - CreateDbExportTask
  - CreateDbImportTask
  - CreateEvent
  - CreateFullSqlExportTask
  - CreateFullSqlOrder
  - CreateFunction
  - CreateLogExportTask
  - CreateManualCCLInspectionTask
  - CreateManualInspection
  - CreateMigrationTicket
  - CreateOrderContentFromMessages
  - CreatePrivilegeTicket
  - CreateProcedure
  - CreateSQLAdvisorTask
  - CreateSession
  - CreateSlowLogsExportTask
  - CreateSqlAudit
  - CreateSqlAuditExportTask
  - CreateSqlConcurrencyControlRule
  - CreateSqlKillRule
  - CreateSqlTask
  - CreateTable
  - CreateTaskFlow
  - CreateTerminateFullSqlOrder
  - CreateTicket
  - CreateTrigger
  - CreateUserGroup
  - CreateView
  - DataCancelExec
  - DataCloseSession
  - DataConnectInstance
  - DataDescribeCommandSet
  - DataExecCommandSetAsync
  - DataExecCommands
  - DataGetCommandSetResult
  - DataSessionKeepAlive
  - DeleteApprovalFlowConfig
  - DeleteApprovalNode
  - DeleteChat
  - DeleteCopilotChat
  - DeleteCustomRule
  - DeleteDBInstance
  - DeleteDbTasks
  - DeleteFullSqlCollectionFingerprint
  - DeleteMyFavouriteSQL
  - DeleteSecurityRuleGroup
  - DeleteSqlAudit
  - DeleteSqlConcurrencyControlRule
  - DeleteSqlKillRule
  - DeleteTaskFlow
  - DeleteUser
  - DeleteUserGroup
  - Deploy
  - DescribeAZs
  - DescribeAbnormalDetectionConfig
  - DescribeAbnormalDetectionDetail
  - DescribeAbnormalDetectionInfo
  - DescribeAccountTlsStatus
  - DescribeActions
  - DescribeAggregateDiagSlowQuery
  - DescribeAggregateDialogs
  - DescribeAggregateSlowLogs
  - DescribeAggregationSQLTable
  - DescribeAggregationSQLTemplates
  - DescribeApprovalFlowConfig
  - DescribeApprovalFlowLogs
  - DescribeArchiveConfigs
  - DescribeArchiveTaskLogDetail
  - DescribeArchiveTasks
  - DescribeAuditInstance
  - DescribeAuditInstances
  - DescribeAuditLogConfig
  - DescribeAuditLogDetail
  - DescribeAutoKillSessionConfig
  - DescribeAutoScaleEvents
  - DescribeAutoScaleInstanceSpec
  - DescribeAutoScaleRules
  - DescribeAvailableTLSTopic
  - DescribeCharsets
  - DescribeChatMessages
  - DescribeCluster
  - DescribeCollations
  - DescribeCollections
  - DescribeCommand
  - DescribeCommandSet
  - DescribeConsoleConnEnvs
  - DescribeConsoleRecordList
  - DescribeCopilotChatList
  - DescribeCurrentConnInfos
  - DescribeCustomRule
  - DescribeDBDiagnosis
  - DescribeDBHealthScore
  - DescribeDBInspectionReport
  - DescribeDBInspectionScore
  - DescribeDBInspections
  - DescribeDBProxyStatus
  - DescribeDBs
  - DescribeDasOperationTaskList
  - DescribeDataBaseTables
  - DescribeDataSource
  - DescribeDataSourceTypes
  - DescribeDataTypes
  - DescribeDatabases
  - DescribeDbExportDownloadUrl
  - DescribeDbMigrationTaskDetail
  - DescribeDbMigrationTasks
  - DescribeDbTreeMountInfo
  - DescribeDeadlock
  - DescribeDeadlockDetect
  - DescribeDiagItemDetail
  - DescribeDiagRootCause
  - DescribeDiagType
  - DescribeDialogDetailSnapshot
  - DescribeDialogDetails
  - DescribeDialogHotspots
  - DescribeDialogInfos
  - DescribeDialogSnapshots
  - DescribeDialogStatistics
  - DescribeDiskAutoScaleEvents
  - DescribeDiskDBAutoScalingConfig
  - DescribeDmTasks
  - DescribeDownloadUrl
  - DescribeEngineStatusSnapShot
  - DescribeEvent
  - DescribeEvents
  - DescribeExampleSQL
  - DescribeExecuteLogs
  - DescribeFactors
  - DescribeFullSQLDetail
  - DescribeFullSQLFingerprintExample
  - DescribeFullSqlCandidate
  - DescribeFullSqlConfig
  - DescribeFullSqlExportTasks
  - DescribeFullSqlStatus
  - DescribeFunction
  - DescribeFunctions
  - DescribeHealthSummary
  - DescribeHistogramV1DBW
  - DescribeIAMUsers
  - DescribeIndexDBW
  - DescribeIndexs
  - DescribeInnerRdsInstance
  - DescribeInstanceChargeItemUsage
  - DescribeInstanceDatabases
  - DescribeInstanceDetail
  - DescribeInstanceFeatures
  - DescribeInstanceInfo
  - DescribeInstanceList
  - DescribeInstanceLogNodes
  - DescribeInstanceManagement
  - DescribeInstanceNodes
  - DescribeInstancePrice
  - DescribeInstanceShards
  - DescribeInstanceVariables
  - DescribeInstances
  - DescribeKeys
  - DescribeLinkTypes
  - DescribeLockCurrentWaits
  - DescribeLockWaitsDetailSnapshot
  - DescribeLogCollectorClusters
  - DescribeLogContextDBW
  - DescribeLogsDownloadUrl
  - DescribeManagedInstanceColumns
  - DescribeManagedInstanceDatabases
  - DescribeManagedInstanceTables
  - DescribeManagedInstances
  - DescribeManagedUsers
  - DescribeMgrParam
  - DescribeMigrationPreCheckDetail
  - DescribeMigrationTicketDetail
  - DescribeMigrationTickets
  - DescribeMongoDBs
  - DescribeMyFavouriteSQL
  - DescribeOnlineDDLSecurityRule
  - DescribeOpsList
  - DescribePgCollations
  - DescribePgTable
  - DescribePgUsers
  - DescribePreCheckDetail
  - DescribePrimaryKeyRange
  - DescribePrivilegeTicketDetail
  - DescribePrivilegeTickets
  - DescribeProcedure
  - DescribeProcedures
  - DescribeRealTimeInstances
  - DescribeRegions
  - DescribeResourceTags
  - DescribeReviewDetailActionList
  - DescribeRole
  - DescribeRuleConfiguration
  - DescribeRuleExecuteRecordDetail
  - DescribeRuleExecuteRecordSummaryResult
  - DescribeSQLAdvisorTableMeta
  - DescribeSQLAdvisorTask
  - DescribeSQLExecItemMetric
  - DescribeSQLExecNum
  - DescribeSQLExecTimeDetail
  - DescribeSQLExecTimeDistribution
  - DescribeSQLStatisticMetrics
  - DescribeSQLTemplateStatistic
  - DescribeSQLTimeElapseDistribution
  - DescribeSQLTimeElapseTotal
  - DescribeSampleData
  - DescribeSchemas
  - DescribeSecurityGroupDetail
  - DescribeSecurityGroups
  - DescribeSecurityRule
  - DescribeSequences
  - DescribeSession
  - DescribeSlowLogTimeSeriesStats
  - DescribeSlowLogs
  - DescribeSlowLogsExportTasks
  - DescribeSourceIPs
  - DescribeSqlAuditCandidate
  - DescribeSqlAuditDetail
  - DescribeSqlAuditExportTasks
  - DescribeSqlAuditStatus
  - DescribeSqlAuditTls
  - DescribeSqlConcurrencyControlRuleDetail
  - DescribeSqlConcurrencyControlRules
  - DescribeSqlFingerPrint
  - DescribeSqlKeywords
  - DescribeSqlKillRules
  - DescribeSqlReviewDetailList
  - DescribeSqlReviewDetailSummaryResult
  - DescribeSqlReviewList
  - DescribeSqlTask
  - DescribeSqlTasks
  - DescribeSqlTemplatesContrast
  - DescribeStorageCapacity
  - DescribeTable
  - DescribeTableColumn
  - DescribeTableIndex
  - DescribeTableMetric
  - DescribeTableSpace
  - DescribeTableSpaceAutoIncr
  - DescribeTableSpaces
  - DescribeTables
  - DescribeTaskExecuteRecords
  - DescribeTaskFlow
  - DescribeTaskFlows
  - DescribeTempCredentials
  - DescribeTempTableSpace
  - DescribeTicketDetail
  - DescribeTicketLogDetail
  - DescribeTicketRecordList
  - DescribeTickets
  - DescribeTopNFullSQLDetail
  - DescribeTopicDBW
  - DescribeTrigger
  - DescribeTriggers
  - DescribeTrxAndLocks
  - DescribeTrxDetailSnapshot
  - DescribeTrxSnapshots
  - DescribeUser
  - DescribeUserGroup
  - DescribeUserGroups
  - DescribeUserPrivileges
  - DescribeUsers
  - DescribeView
  - DescribeViews
  - DescribeWorkflow
  - DisableInstanceManagement
  - DisableSecurityRule
  - DisableSqlConcurrencyControl
  - DropDatabase
  - DropEvent
  - DropFunction
  - DropProcedure
  - DropTable
  - DropTrigger
  - DropView
  - EditCustomSecurityRule
  - EditSecurityRuleGroup
  - EnableInstanceManagement
  - EnableSecurityRule
  - EnableSqlConcurrencyControl
  - ExecuteAssistantPrompt
  - ExecuteCommandSet
  - ExecuteFunction
  - ExecuteMigrationTicket
  - ExecuteProcedure
  - ExecutePrompt
  - ExecuteTicket
  - ForgetConsolePassword
  - GetArchiveNextSql
  - GetKeyMembers
  - GetLogFiles
  - GetMetricData
  - GetMetricItems
  - GetSQLAdvisorProtocol
  - GetSqlAdvice
  - GetSqlAssistantProtocol
  - GetSqlConcurrencyControlStatus
  - GetTotalKeyNumber
  - GetUpgradeProgress
  - GetUserProtocolState
  - GrantUserGroupPrivilege
  - GrantUserPrivilege
  - IlmfHandleTradeMessage
  - InitUserManagementAdmin
  - KillProcess
  - ListApprovalAssociatedInstance
  - ListApprovalFlowConfig
  - ListApprovalNode
  - ListChatHistory
  - ListChats
  - ListSlowQueryAdvice
  - ListSlowQueryAdviceApi
  - ListSlowQueryAdviceConfig
  - ModifyAbnormalDetectionConfig
  - ModifyApprovalFlowConfig
  - ModifyApprovalNode
  - ModifyAuditLogConfig
  - ModifyAuditPodImage
  - ModifyAutoKillSessionConfig
  - ModifyDBAutoStorageScaling
  - ModifyDiskDBAutoScalingConfig
  - ModifyFullSqlConfig
  - ModifyInstanceClusters
  - ModifyLogCollectorImage
  - ModifySqlConcurrencyControlRule
  - ModifyTicket
  - OrderCreateAudit
  - OrderTerminateAudit
  - PreCheckCreateFullSql
  - PreCheckMigrationTicket
  - PreCheckTicket
  - PrivilegeTicketAction
  - RateModelReply
  - RemoveTagsFromResource
  - RenameTable
  - Report
  - RestartSqlConcurrencyControlRule
  - RevokeUserGroupPrivilege
  - RevokeUserPrivilege
  - SaveConsolePassword
  - SearchFunction
  - SearchLogDBW
  - SearchProcedure
  - SearchTable
  - SearchTrigger
  - SearchView
  - SessionKeepAlive
  - SlowQueryAdviceTaskHistory
  - SlowQueryAdviceTaskHistoryApi
  - SqlAssistant
  - SqlCorrect
  - GenerateSQLFromNL
  - StopDataMigrationTask
  - StopSqlConcurrencyControlRule
  - StopSqlKillRule
  - StopTicket
  - SubmitApproveSqlReview
  - SubmitMigrationTicket
  - SubmitTicket
  - SyncDBInstances
  - UpdateConsoleConnEnv
  - UpdateCopilotChatName
  - UpdateInstanceManagementConfig
  - UpdateMgrParam
  - UpdateMyFavouriteSQL
  - UpdateOnlineDDLSecurityRule
  - UpdateSecurityRule
  - UpdateSlowQueryAnalysis
  - UpdateTaskFlow
  - UpdateUser
  - UpdateUserGroup
  - UpgradeFullsql
  - UpgradeFullsqlTableAggr
  - UpgradeResource
  - UserGroupSync
  - WorkflowAction
  OpsActionList:
  - AddControlInstance
  - AddCustomSecurityRule
  - AddFullSqlCollectionFingerprint
  - AddMyFavouriteSQL
  - AddSecurityRuleGroup
  - AddSqlReview
  - AddTagsToResource
  - AddUser
  - AgreeDataCollection
  - AgreeSQLAdvisorProtocol
  - AgreeSqlAssistantProtocol
  - AgreeUserProtocol
  - AlterDatabase
  - AlterKVs
  - AlterRows
  - AlterTable
  - AutoScale
  - BatchOpenInstanceFunction
  - CancelCommandSet
  - CancelMigrationTicket
  - CancelTicket
  - ChangeDB
  - Chat
  - CheckConn
  - CheckMgrSelfHealth
  - CheckResHealth
  - CloseConnection
  - CloseSession
  - CopilotButtonGeneration
  - CopyTable
  - CopyTableSchemaOnly
  - CreateApprovalFlowConfig
  - CreateApprovalNode
  - CreateAutomaticInspection
  - CreateChat
  - CreateConnection
  - CreateCopilotChat
  - CreateDatabase
  - CreateDbExportTask
  - CreateDbImportTask
  - CreateEvent
  - CreateFullSqlExportTask
  - CreateFullSqlOrder
  - CreateFunction
  - CreateLogExportTask
  - CreateManualCCLInspectionTask
  - CreateManualInspection
  - CreateMigrationTicket
  - CreateOrderContentFromMessages
  - CreatePrivilegeTicket
  - CreateProcedure
  - CreateSQLAdvisorTask
  - CreateSession
  - CreateSlowLogsExportTask
  - CreateSqlAudit
  - CreateSqlAuditExportTask
  - CreateSqlConcurrencyControlRule
  - CreateSqlKillRule
  - CreateSqlTask
  - CreateTable
  - CreateTaskFlow
  - CreateTerminateFullSqlOrder
  - CreateTicket
  - CreateTrigger
  - CreateUserGroup
  - CreateView
  - DataCancelExec
  - DataCloseSession
  - DataConnectInstance
  - DataDescribeCommandSet
  - DataExecCommandSetAsync
  - DataExecCommands
  - DataGetCommandSetResult
  - DataSessionKeepAlive
  - DeleteApprovalFlowConfig
  - DeleteApprovalNode
  - DeleteChat
  - DeleteCopilotChat
  - DeleteCustomRule
  - DeleteDBInstance
  - DeleteDbTasks
  - DeleteFullSqlCollectionFingerprint
  - DeleteMyFavouriteSQL
  - DeleteSecurityRuleGroup
  - DeleteSqlAudit
  - DeleteSqlConcurrencyControlRule
  - DeleteSqlKillRule
  - DeleteTaskFlow
  - DeleteUser
  - DeleteUserGroup
  - Deploy
  - DescribeAZs
  - DescribeAbnormalDetectionConfig
  - DescribeAbnormalDetectionDetail
  - DescribeAbnormalDetectionInfo
  - DescribeAccountTlsStatus
  - DescribeActions
  - DescribeAggregateDiagSlowQuery
  - DescribeAggregateDialogs
  - DescribeAggregateSlowLogs
  - DescribeAggregationSQLTable
  - DescribeAggregationSQLTemplates
  - DescribeApprovalFlowConfig
  - DescribeApprovalFlowLogs
  - DescribeArchiveConfigs
  - DescribeArchiveTaskLogDetail
  - DescribeArchiveTasks
  - DescribeAuditInstance
  - DescribeAuditInstances
  - DescribeAuditLogConfig
  - DescribeAuditLogDetail
  - DescribeAutoKillSessionConfig
  - DescribeAutoScaleEvents
  - DescribeAutoScaleInstanceSpec
  - DescribeAutoScaleRules
  - DescribeAvailableTLSTopic
  - DescribeCharsets
  - DescribeChatMessages
  - DescribeCluster
  - DescribeCollations
  - DescribeCollections
  - DescribeCommand
  - DescribeCommandSet
  - DescribeConsoleConnEnvs
  - DescribeConsoleRecordList
  - DescribeCopilotChatList
  - DescribeCurrentConnInfos
  - DescribeCustomRule
  - DescribeDBDiagnosis
  - DescribeDBHealthScore
  - DescribeDBInspectionReport
  - DescribeDBInspectionScore
  - DescribeDBInspections
  - DescribeDBProxyStatus
  - DescribeDBs
  - DescribeDasOperationTaskList
  - DescribeDataBaseTables
  - DescribeDataSource
  - DescribeDataSourceTypes
  - DescribeDataTypes
  - DescribeDatabases
  - DescribeDbExportDownloadUrl
  - DescribeDbMigrationTaskDetail
  - DescribeDbMigrationTasks
  - DescribeDbTreeMountInfo
  - DescribeDeadlock
  - DescribeDeadlockDetect
  - DescribeDiagItemDetail
  - DescribeDiagRootCause
  - DescribeDiagType
  - DescribeDialogDetailSnapshot
  - DescribeDialogDetails
  - DescribeDialogHotspots
  - DescribeDialogInfos
  - DescribeDialogSnapshots
  - DescribeDialogStatistics
  - DescribeDiskAutoScaleEvents
  - DescribeDiskDBAutoScalingConfig
  - DescribeDmTasks
  - DescribeDownloadUrl
  - DescribeEngineStatusSnapShot
  - DescribeEvent
  - DescribeEvents
  - DescribeExampleSQL
  - DescribeExecuteLogs
  - DescribeFactors
  - DescribeFullSQLDetail
  - DescribeFullSQLFingerprintExample
  - DescribeFullSqlCandidate
  - DescribeFullSqlConfig
  - DescribeFullSqlExportTasks
  - DescribeFullSqlStatus
  - DescribeFunction
  - DescribeFunctions
  - DescribeHealthSummary
  - DescribeHistogramV1DBW
  - DescribeIAMUsers
  - DescribeIndexDBW
  - DescribeIndexs
  - DescribeInnerRdsInstance
  - DescribeInstanceChargeItemUsage
  - DescribeInstanceDatabases
  - DescribeInstanceDetail
  - DescribeInstanceFeatures
  - DescribeInstanceInfo
  - DescribeInstanceList
  - DescribeInstanceLogNodes
  - DescribeInstanceManagement
  - DescribeInstanceNodes
  - DescribeInstancePrice
  - DescribeInstanceShards
  - DescribeInstanceVariables
  - DescribeInstances
  - DescribeKeys
  - DescribeLinkTypes
  - DescribeLockCurrentWaits
  - DescribeLockWaitsDetailSnapshot
  - DescribeLogCollectorClusters
  - DescribeLogContextDBW
  - DescribeLogsDownloadUrl
  - DescribeManagedInstanceColumns
  - DescribeManagedInstanceDatabases
  - DescribeManagedInstanceTables
  - DescribeManagedInstances
  - DescribeManagedUsers
  - DescribeMgrParam
  - DescribeMigrationPreCheckDetail
  - DescribeMigrationTicketDetail
  - DescribeMigrationTickets
  - DescribeMongoDBs
  - DescribeMyFavouriteSQL
  - DescribeOnlineDDLSecurityRule
  - DescribeOpsList
  - DescribePgCollations
  - DescribePgTable
  - DescribePgUsers
  - DescribePreCheckDetail
  - DescribePrimaryKeyRange
  - DescribePrivilegeTicketDetail
  - DescribePrivilegeTickets
  - DescribeProcedure
  - DescribeProcedures
  - DescribeRealTimeInstances
  - DescribeRegions
  - DescribeResourceTags
  - DescribeReviewDetailActionList
  - DescribeRole
  - DescribeRuleConfiguration
  - DescribeRuleExecuteRecordDetail
  - DescribeRuleExecuteRecordSummaryResult
  - DescribeSQLAdvisorTableMeta
  - DescribeSQLAdvisorTask
  - DescribeSQLExecItemMetric
  - DescribeSQLExecNum
  - DescribeSQLExecTimeDetail
  - DescribeSQLExecTimeDistribution
  - DescribeSQLStatisticMetrics
  - DescribeSQLTemplateStatistic
  - DescribeSQLTimeElapseDistribution
  - DescribeSQLTimeElapseTotal
  - DescribeSampleData
  - DescribeSchemas
  - DescribeSecurityGroupDetail
  - DescribeSecurityGroups
  - DescribeSecurityRule
  - DescribeSequences
  - DescribeSession
  - DescribeSlowLogTimeSeriesStats
  - DescribeSlowLogs
  - DescribeSlowLogsExportTasks
  - DescribeSourceIPs
  - DescribeSqlAuditCandidate
  - DescribeSqlAuditDetail
  - DescribeSqlAuditExportTasks
  - DescribeSqlAuditStatus
  - DescribeSqlAuditTls
  - DescribeSqlConcurrencyControlRuleDetail
  - DescribeSqlConcurrencyControlRules
  - DescribeSqlFingerPrint
  - DescribeSqlKeywords
  - DescribeSqlKillRules
  - DescribeSqlReviewDetailList
  - DescribeSqlReviewDetailSummaryResult
  - DescribeSqlReviewList
  - DescribeSqlTask
  - DescribeSqlTasks
  - DescribeSqlTemplatesContrast
  - DescribeStorageCapacity
  - DescribeTable
  - DescribeTableColumn
  - DescribeTableIndex
  - DescribeTableMetric
  - DescribeTableSpace
  - DescribeTableSpaceAutoIncr
  - DescribeTableSpaces
  - DescribeTables
  - DescribeTaskExecuteRecords
  - DescribeTaskFlow
  - DescribeTaskFlows
  - DescribeTempCredentials
  - DescribeTempTableSpace
  - DescribeTicketDetail
  - DescribeTicketLogDetail
  - DescribeTicketRecordList
  - DescribeTickets
  - DescribeTopNFullSQLDetail
  - DescribeTopicDBW
  - DescribeTrigger
  - DescribeTriggers
  - DescribeTrxAndLocks
  - DescribeTrxDetailSnapshot
  - DescribeTrxSnapshots
  - DescribeUser
  - DescribeUserGroup
  - DescribeUserGroups
  - DescribeUserPrivileges
  - DescribeUsers
  - DescribeView
  - DescribeViews
  - DescribeWorkflow
  - DisableInstanceManagement
  - DisableSecurityRule
  - DisableSqlConcurrencyControl
  - DropDatabase
  - DropEvent
  - DropFunction
  - DropProcedure
  - DropTable
  - DropTrigger
  - DropView
  - EditCustomSecurityRule
  - EditSecurityRuleGroup
  - EnableInstanceManagement
  - EnableSecurityRule
  - EnableSqlConcurrencyControl
  - ExecuteAssistantPrompt
  - ExecuteCommandSet
  - ExecuteFunction
  - ExecuteMigrationTicket
  - ExecuteProcedure
  - ExecutePrompt
  - ExecuteTicket
  - ForgetConsolePassword
  - GetArchiveNextSql
  - GetKeyMembers
  - GetLogFiles
  - GetMetricData
  - GetMetricItems
  - GetSQLAdvisorProtocol
  - GetSqlAdvice
  - GetSqlAssistantProtocol
  - GetSqlConcurrencyControlStatus
  - GetTotalKeyNumber
  - GetUpgradeProgress
  - GetUserProtocolState
  - GrantUserGroupPrivilege
  - GrantUserPrivilege
  - IlmfHandleTradeMessage
  - InitUserManagementAdmin
  - KillProcess
  - ListApprovalAssociatedInstance
  - ListApprovalFlowConfig
  - ListApprovalNode
  - ListChatHistory
  - ListChats
  - ListSlowQueryAdvice
  - ListSlowQueryAdviceApi
  - ListSlowQueryAdviceConfig
  - ModifyAbnormalDetectionConfig
  - ModifyApprovalFlowConfig
  - ModifyApprovalNode
  - ModifyAuditLogConfig
  - ModifyAuditPodImage
  - ModifyAutoKillSessionConfig
  - ModifyDBAutoStorageScaling
  - ModifyDiskDBAutoScalingConfig
  - ModifyFullSqlConfig
  - ModifyInstanceClusters
  - ModifyLogCollectorImage
  - ModifySqlConcurrencyControlRule
  - ModifyTicket
  - OrderCreateAudit
  - OrderTerminateAudit
  - PreCheckCreateFullSql
  - PreCheckMigrationTicket
  - PreCheckTicket
  - PrivilegeTicketAction
  - RateModelReply
  - RemoveTagsFromResource
  - RenameTable
  - Report
  - RestartSqlConcurrencyControlRule
  - RevokeUserGroupPrivilege
  - RevokeUserPrivilege
  - SaveConsolePassword
  - SearchFunction
  - SearchLogDBW
  - SearchProcedure
  - SearchTable
  - SearchTrigger
  - SearchView
  - SessionKeepAlive
  - SlowQueryAdviceTaskHistory
  - SlowQueryAdviceTaskHistoryApi
  - SqlAssistant
  - SqlCorrect
  - GenerateSQLFromNL
  - StopDataMigrationTask
  - StopSqlConcurrencyControlRule
  - StopSqlKillRule
  - StopTicket
  - SubmitApproveSqlReview
  - SubmitMigrationTicket
  - SubmitTicket
  - SyncDBInstances
  - UpdateConsoleConnEnv
  - UpdateCopilotChatName
  - UpdateInstanceManagementConfig
  - UpdateMgrParam
  - UpdateMyFavouriteSQL
  - UpdateOnlineDDLSecurityRule
  - UpdateSecurityRule
  - UpdateSlowQueryAnalysis
  - UpdateTaskFlow
  - UpdateUser
  - UpdateUserGroup
  - UpgradeFullsql
  - UpgradeFullsqlTableAggr
  - UpgradeResource
  - UserGroupSync
  - WorkflowAction

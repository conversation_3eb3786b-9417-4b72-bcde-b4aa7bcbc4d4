module code.byted.org/infcs/dbw-mgr

go 1.21

require (
	code.byted.org/cloud-fe/cloud_config_center v0.0.5
	code.byted.org/gopkg/lang v0.21.8
	code.byted.org/gopkg/mockito v1.3.0
	code.byted.org/iaasng/volcstack-go-inner-sdk v1.1.15
	code.byted.org/infcs/ds-lib v0.0.0-20250629144307-cad87df1a1ac
	// TODO 如果有问题，将此版本回调 code.byted.org/infcs/ds-sql-parser v0.0.0-20250331101940-a72518cf8896
	code.byted.org/infcs/ds-sql-parser v0.0.0-20250509032818-71b8a309fe5b
	code.byted.org/infcs/lib-mgr-common v0.1.15
	code.byted.org/infcs/lib-upgrade v0.0.47
	code.byted.org/infcs/mgr v1.1.59
	code.byted.org/kite/kitex v1.18.1
	code.byted.org/luoshiqi/mockito v1.4.0
	code.byted.org/ti/disco_sdk v1.0.50
	code.byted.org/ve-arch/locationclient v0.1.1
	code.byted.org/videoarch/cloud-volc_sdk_go v1.0.533
	github.com/Shopify/sarama v1.30.1
	github.com/XiaoMi/soar v0.8.2-0.20210527021321-a5cb33b98f86
	github.com/apache/thrift v0.19.0
	github.com/bwmarrin/snowflake v0.3.0
	github.com/bytedance/mockey v1.2.14
	github.com/coocood/freecache v1.2.2
	github.com/gin-gonic/gin v1.9.0
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-sql-driver/mysql v1.8.1
	github.com/gogo/protobuf v1.3.2
	github.com/golang/mock v1.6.0
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/uuid v1.6.0
	github.com/hashicorp/golang-lru/v2 v2.0.7
	//github.com/pingcap/parser v0.0.0-20200623164729-3a18f1e5dceb
	github.com/pingcap/tidb v1.1.0-beta.0.20200630082100-328b6d0a955c
	github.com/qjpcpu/common.v2 v0.0.0-20231204091538-5bb4afb30440
	github.com/qjpcpu/fp v0.0.0-20220629083539-d0513673d296
	github.com/shopspring/decimal v1.2.0
	github.com/smartystreets/goconvey v1.8.1
	github.com/spf13/cast v1.7.0
	github.com/stretchr/testify v1.9.0
	github.com/tidwall/gjson v1.17.3
	github.com/volcengine/volc-sdk-golang v1.0.208
	github.com/volcengine/volcengine-go-sdk v1.1.8
	go.uber.org/dig v1.13.0
	golang.org/x/sync v0.10.0
	gopkg.in/yaml.v2 v2.4.0
	gorm.io/driver/mysql v1.5.2
	gorm.io/gorm v1.25.7-0.20240204074919-46816ad31dde
	k8s.io/api v0.26.5
	k8s.io/apimachinery v0.26.5
	k8s.io/client-go v0.26.5
	vitess.io/vitess v0.0.0-20200325000816-eda961851d63
)

require (
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-runewidth v0.0.14 // indirect
	github.com/rivo/uniseg v0.4.2 // indirect
	github.com/spf13/cobra v1.6.1 // indirect
	github.com/vicanso/go-charts/v2 v2.6.10
	go.mongodb.org/mongo-driver v1.12.1
	golang.org/x/arch v0.11.0 // indirect
	golang.org/x/sys v0.28.0 // indirect
	golang.org/x/term v0.27.0 // indirect
	golang.org/x/tools v0.23.0 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
)

replace (
	code.byted.org/gopkg/tccclient => code.byted.org/gopkg/tccclient v1.5.0-beta.11
	github.com/antlr4-go/antlr/v4 => github.com/antlr4-go/antlr/v4 v4.13.0 // indirect
	//code.byted.org/gopkg/logs => code.byted.org/gopkg/logs v1.1.12
	//code.byted.org/infcs/lib-log => code.byted.org/infcs/lib-log v0.0.11 // indirect
	github.com/apache/thrift => github.com/apache/thrift v0.13.0
	//google.golang.org/grpc => google.golang.org/grpc v1.26.0
	github.com/jackc/puddle/v2 => github.com/jackc/puddle/v2 v2.0.0
	//github.com/pingcap/parser => code.byted.org/infcs/ds-sql-parser v0.0.0-20240508112702-5bddcd3e1504
	//github.com/satori/go.uuid => github.com/satori/go.uuid v1.2.0
	github.com/siddontang/go-mysql => code.byted.org/infcs/dflow/third_party/go-mysql v0.0.0-20211222065014-989df44360f3
)

exclude github.com/siddontang/go-mysql v0.0.0-00010101000000-000000000000

//replace github.com/volcengine/volc-sdk-golang v1.0.50 => code.byted.org/volcengine/volc-sdk-golang v1.0.30-0.20221125075849-a481a350a143

replace (
	code.byted.org/bytedts/dflow_bd => code.byted.org/bytedts/dflow_bd v0.0.0-20240624115409-f31e868b89f7
	code.byted.org/bytedts/ds-mgr => code.byted.org/bytedts/ds-mgr v0.0.0-20240702070236-9433e85dc494
	code.byted.org/bytedts/dts-lib-bd => code.byted.org/bytedts/dts-lib-bd v0.0.0-20240516071722-904cf3394439
	code.byted.org/cloud-fe/cloud_config_center => code.byted.org/cloud-fe/cloud_config_center v0.0.3
	//code.byted.org/infcs/ds-lib => code.byted.org/infcs/ds-lib v0.0.0-20250428065157-9811c19bef3a
	//code.byted.org/infcs/ds-lib => /Users/<USER>/go/src/code.byted.org/infcs/ds-lib
	code.byted.org/infcs/lib-mgr-common => code.byted.org/db-inf/lib-mgr-common v0.1.29-0.20250717022920-1f805deddf02
	github.com/rogpeppe/go-internal => github.com/rogpeppe/go-internal v1.12.0
	//code.byted.org/infcs/tsql-parser => /Users/<USER>/go/src/code.byted.org/infcs/tsql-parser
	go.opentelemetry.io/otel => go.opentelemetry.io/otel v1.14.0

	go.opentelemetry.io/otel/exporters/prometheus => go.opentelemetry.io/otel/exporters/prometheus v0.26.0

	go.opentelemetry.io/otel/metric => go.opentelemetry.io/otel/metric v0.26.0

	go.opentelemetry.io/otel/sdk/export/metric => go.opentelemetry.io/otel/sdk/export/metric v0.26.0

	go.opentelemetry.io/otel/sdk/metric => go.opentelemetry.io/otel/sdk/metric v0.26.0
	golang.org/x/text => golang.org/x/text v0.21.0
	google.golang.org/grpc => google.golang.org/grpc v1.46.2
	k8s.io/api => k8s.io/kubernetes/staging/src/k8s.io/api v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/apiextensions-apiserver => k8s.io/kubernetes/staging/src/k8s.io/apiextensions-apiserver v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/apimachinery => k8s.io/kubernetes/staging/src/k8s.io/apimachinery v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/apiserver => k8s.io/kubernetes/staging/src/k8s.io/apiserver v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/cli-runtime => k8s.io/kubernetes/staging/src/k8s.io/cli-runtime v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/client-go => k8s.io/kubernetes/staging/src/k8s.io/client-go v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/cloud-provider => k8s.io/kubernetes/staging/src/k8s.io/cloud-provider v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/cluster-bootstrap => k8s.io/kubernetes/staging/src/k8s.io/cluster-bootstrap v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/code-generator => k8s.io/kubernetes/staging/src/k8s.io/code-generator v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/component-base => k8s.io/kubernetes/staging/src/k8s.io/component-base v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/component-helpers => k8s.io/kubernetes/staging/src/k8s.io/component-helpers v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/controller-manager => k8s.io/kubernetes/staging/src/k8s.io/controller-manager v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/cri-api => k8s.io/kubernetes/staging/src/k8s.io/cri-api v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/csi-translation-lib => k8s.io/kubernetes/staging/src/k8s.io/csi-translation-lib v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/kube-aggregator => k8s.io/kubernetes/staging/src/k8s.io/kube-aggregator v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/kube-controller-manager => k8s.io/kubernetes/staging/src/k8s.io/kube-controller-manager v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/kube-proxy => k8s.io/kubernetes/staging/src/k8s.io/kube-proxy v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/kube-scheduler => k8s.io/kubernetes/staging/src/k8s.io/kube-scheduler v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/kubectl => k8s.io/kubernetes/staging/src/k8s.io/kubectl v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/kubelet => k8s.io/kubernetes/staging/src/k8s.io/kubelet v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/kubernetes => k8s.io/kubernetes v1.25.0-alpha.2.0.20220714164509-e5f4f8d71b48
	k8s.io/legacy-cloud-providers => k8s.io/kubernetes/staging/src/k8s.io/legacy-cloud-providers v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/metrics => k8s.io/kubernetes/staging/src/k8s.io/metrics v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/mount-utils => k8s.io/kubernetes/staging/src/k8s.io/mount-utils v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/pod-security-admission => k8s.io/kubernetes/staging/src/k8s.io/pod-security-admission v0.0.0-20220714164509-e5f4f8d71b48
	k8s.io/sample-apiserver => k8s.io/kubernetes/staging/src/k8s.io/sample-apiserver v0.0.0-20220714164509-e5f4f8d71b48
)

require (
	code.byted.org/bcc/tools v0.0.21
	code.byted.org/bytedts/ds-mgr v0.0.0-00010101000000-000000000000
	code.byted.org/bytedts/dts-lib-bd v0.0.0-20250310130555-392981462b6c
	code.byted.org/eps-platform/message_client v1.1.4
	code.byted.org/eps-platform/tob-util v1.0.2
	code.byted.org/flow/eino-byted-ext/callbacks/fornax v0.1.4
	code.byted.org/flowdevops/fornax_sdk v1.1.13
	code.byted.org/gin/ginex v1.8.0
	code.byted.org/gin/ginext v0.0.0-20210709033238-576ca499be06
	code.byted.org/gin/ginext/binding v1.1.0
	code.byted.org/gopkg/context v0.0.1
	code.byted.org/gopkg/env v1.6.18
	code.byted.org/gopkg/gorm v1.0.5
	code.byted.org/gopkg/lang/v2 v2.1.3
	code.byted.org/gopkg/logid v0.0.0-20241008043456-230d03adb830
	code.byted.org/gopkg/logs v1.2.23
	code.byted.org/gopkg/metrics/v3 v3.1.35
	code.byted.org/gopkg/pkg v0.0.0-20210817064112-6fe00340bb36
	code.byted.org/gopkg/thrift v1.14.2
	code.byted.org/gopkg/tos v1.5.7
	code.byted.org/inf/metrics-query v1.6.21
	code.byted.org/infcs/dbw-mysql-driver v0.0.0-20250607040843-5907189f2388
	code.byted.org/infcs/lib-log v0.0.29
	code.byted.org/infcs/protoactor-go v0.0.0-20250729143336-61c1199e0658
	code.byted.org/infcs/tsql-parser v0.0.0-20241012073218-6519bacaed12
	code.byted.org/kite/kitex/pkg/protocol/bthrift v0.0.0-20250103083202-413e97b2b0fd
	code.byted.org/kite/kitutil v3.8.8+incompatible
	code.byted.org/kitex/apache_monitor v0.1.1
	code.byted.org/kv/goredis v5.5.7+incompatible
	code.byted.org/paas/cloud-sdk-go v0.0.235
	code.byted.org/sre/bytetree_go_sdk v1.0.35
	code.byted.org/ve-arch/volcengine-innersdk v0.1.1
	code.byted.org/videoarch/cloud_gopkg v1.1.158
	github.com/ahmetb/go-linq/v3 v3.2.0
	github.com/araddon/dateparse v0.0.0-20210429162001-6b43995a97de
	github.com/aws/aws-sdk-go v1.51.6
	github.com/cloudwego/eino v0.3.27
	github.com/cloudwego/eino-ext/components/model/ark v0.1.10
	github.com/cloudwego/gopkg v0.1.3
	github.com/cloudwego/kitex v0.12.1
	github.com/cloudwego/kitex/pkg/protocol/bthrift v0.0.0-20250124072755-047444c8e964
	github.com/deckarep/golang-set v1.8.0
	github.com/go-errors/errors v1.0.1
	github.com/go-logr/logr v1.2.4
	github.com/go-playground/assert v1.2.1
	github.com/go-playground/validator/v10 v10.11.2
	github.com/go-zookeeper/zk v1.0.2
	github.com/gobuffalo/packr/v2 v2.8.3
	github.com/gofrs/uuid v4.0.0+incompatible
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0
	github.com/influxdata/influxdb1-client v0.0.0-20200827194710-b269163b24ab
	github.com/jung-kurt/gofpdf v1.16.2
	github.com/kr/pretty v0.3.1
	github.com/larksuite/oapi-sdk-go/v3 v3.2.4
	github.com/olekukonko/tablewriter v0.0.5-0.20200416053754-163badb3bac6
	github.com/openark/golib v0.0.0-20210531070646-355f37940af8
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/pganalyze/pg_query_go/v6 v6.1.0
	github.com/pingcap/log v0.0.0-20200511115504-543df19646ad
	github.com/pingcap/parser v0.0.0-20200623164729-3a18f1e5dceb
	github.com/pkg/errors v0.9.1
	github.com/robfig/cron/v3 v3.0.1
	github.com/sirupsen/logrus v1.9.3
	github.com/stvp/assert v0.0.0-20170616060220-4bc16443988b
	github.com/volcengine/ve-tos-golang-sdk/v2 v2.1.6
	golang.org/x/exp v0.0.0-20240719175910-8a7402abbf56
	k8s.io/cri-api v0.0.0
	k8s.io/kubernetes v0.0.0-00010101000000-000000000000
	k8s.io/utils v0.0.0-20221108210102-8e77b1f39fe2
	sigs.k8s.io/controller-runtime v0.13.1
	sigs.k8s.io/yaml v1.3.0
)

require (
	code.byted.org/aiops/apm_vendor_byted v0.0.27 // indirect
	code.byted.org/aiops/metrics_codec v0.0.24 // indirect
	code.byted.org/aiops/monitoring-common-go v0.0.5 // indirect
	code.byted.org/bcc/bcc-go-client v0.1.46 // indirect
	code.byted.org/bcc/bcc-go-client/internal/sidecar/idl v0.0.4 // indirect
	code.byted.org/bcc/conf_engine v0.0.0-20230510030051-32fb55f74cf1 // indirect
	code.byted.org/bcc/pull_json_model v1.0.22 // indirect
	code.byted.org/bytedtrace-contrib/kitex-go v1.1.52 // indirect
	code.byted.org/bytedtrace/bytedtrace-client-go v1.3.1 // indirect
	code.byted.org/bytedtrace/bytedtrace-common/go v0.0.13 // indirect
	code.byted.org/bytedtrace/bytedtrace-compatible-client-go v0.0.14 // indirect
	code.byted.org/bytedtrace/bytedtrace-compatible-lightweight-go v1.0.1 // indirect
	code.byted.org/bytedtrace/bytedtrace-conf-provider-client-go v0.0.27 // indirect
	code.byted.org/bytedtrace/bytedtrace-gls-switch v1.3.0 // indirect
	code.byted.org/bytedtrace/bytedtrace-utils-go v1.0.3 // indirect
	code.byted.org/bytedtrace/interface-go v1.0.20 // indirect
	code.byted.org/bytedtrace/serializer-go v1.0.1-pre // indirect
	code.byted.org/bytees/olivere_elastic/v7 v7.0.34 // indirect
	code.byted.org/cloud-fe/agollo/v4 v4.0.0-**************-4f2a2df13238 // indirect
	code.byted.org/data-arch/gotbase v1.0.8-0.**************-b9d46a7dc975 // indirect
	code.byted.org/duanyi.aster/gopkg v0.0.4 // indirect
	code.byted.org/eps-platform/tob-util/account v1.1.0 // indirect
	code.byted.org/eps-platform/volcengine-innersdk-golang v1.0.39 // indirect
	code.byted.org/flow/eino-byted-ext/byted v0.3.13 // indirect
	code.byted.org/flow/eino-byted-ext/callbacks/metrics v0.1.2 // indirect
	code.byted.org/flow/eino-byted-ext/components/model/llmgateway v0.1.6 // indirect
	code.byted.org/flow/flow-telemetry-common/go v0.0.0-**************-2f2ee3f95cde // indirect
	code.byted.org/flowdevops/errorx v0.0.6 // indirect
	code.byted.org/flowdevops/errorx/code/gen/flow/devops/agent_server v0.0.0-**************-47d6baaffb45 // indirect
	code.byted.org/flowdevops/fornax/pkg/auth v0.0.0-**************-be429d5e6b0c // indirect
	code.byted.org/golf/consul v2.1.13+incompatible // indirect
	code.byted.org/gopkg/apm_vendor_interface v0.0.3 // indirect
	code.byted.org/gopkg/asyncache v0.0.0-**************-1df5611dba17 // indirect
	code.byted.org/gopkg/asynccache v0.0.0-**************-26f94f7676b8 // indirect
	code.byted.org/gopkg/bytedmysql v1.1.15 // indirect
	code.byted.org/gopkg/circuitbreaker v3.8.1+incompatible // indirect
	code.byted.org/gopkg/consul v1.2.6 // indirect
	code.byted.org/gopkg/ctxvalues v0.6.0 // indirect
	code.byted.org/gopkg/debug v0.10.1 // indirect
	code.byted.org/gopkg/etcd_util v2.3.3+incompatible // indirect
	code.byted.org/gopkg/etcdproxy v0.1.1 // indirect
	code.byted.org/gopkg/facility v1.0.14 // indirect
	code.byted.org/gopkg/localcache v0.9.4 // indirect
	code.byted.org/gopkg/localcache/base v0.8.0 // indirect
	code.byted.org/gopkg/localcache/contributes/freecache v0.7.3 // indirect
	code.byted.org/gopkg/localcache/contributes/gcache v0.8.1 // indirect
	code.byted.org/gopkg/localcache/contributes/vfastcache v0.2.0 // indirect
	code.byted.org/gopkg/logs/v2 v2.2.0-beta.9 // indirect
	code.byted.org/gopkg/metainfo v0.1.4 // indirect
	code.byted.org/gopkg/metrics v1.4.25 // indirect
	code.byted.org/gopkg/metrics/v4 v4.1.4 // indirect
	code.byted.org/gopkg/metrics_core v0.0.39 // indirect
	code.byted.org/gopkg/net2 v1.5.0 // indirect
	code.byted.org/gopkg/rand v0.0.0-20200622102840-8cd9b682e5b4 // indirect
	code.byted.org/gopkg/retry v0.0.0-20230209024914-cf290f094aa7 // indirect
	code.byted.org/gopkg/stats v1.2.12 // indirect
	code.byted.org/gopkg/tccclient v1.5.0-beta.11 // indirect
	code.byted.org/gopkg/thriftparser v0.0.0-20210804085251-0a74cb842d5f // indirect
	code.byted.org/hystrix/hystrix-go v0.0.0-20190214095017-a2a890c81cd5 // indirect
	code.byted.org/iaasng/volcstack-sdk-go v1.0.2-0.20220607125543-5561b37186ec // indirect
	code.byted.org/inf/authcenter v1.5.2 // indirect
	code.byted.org/inf/infsecc v1.0.3 // indirect
	code.byted.org/inf/sarama v1.5.1 // indirect
	code.byted.org/infcp/iaas-api-sdk v0.11.1 // indirect
	code.byted.org/infcs/ds-zk v0.0.0-20240819122453-0aec8f1546bd // indirect
	code.byted.org/infcs/dts-gateway v0.0.0-20240607143528-56107cab9d46 // indirect
	code.byted.org/infcs/glisp v0.0.0-20240617072235-c440e2cb8af8 // indirect
	code.byted.org/infcs/hbase-client-go/goclient v0.0.0-20210917062552-f16e3dfa9ce8 // indirect
	code.byted.org/infcs/rocketmq-go-sdk/v2 v2.1.1-release-fix3 // indirect
	code.byted.org/kite/endpoint v3.7.5+incompatible // indirect
	code.byted.org/kite/kitc v3.10.26+incompatible // indirect
	code.byted.org/kite/kite v3.9.30+incompatible // indirect
	code.byted.org/kite/rpal v0.1.22 // indirect
	code.byted.org/kv/backoff v0.0.0-20191031070508-5d868504e646 // indirect
	code.byted.org/kv/circuitbreaker v0.0.0-20200212034351-d3f51a5b9165 // indirect
	code.byted.org/kv/goredis/v5 v5.6.1 // indirect
	code.byted.org/kv/redis-v6 v1.0.29 // indirect
	code.byted.org/lagrange/viking_go_client v0.0.33 // indirect
	code.byted.org/lang/gg v0.18.0 // indirect
	code.byted.org/lang/trace v0.0.3 // indirect
	code.byted.org/lidar/profiler v0.4.4 // indirect
	code.byted.org/lidar/profiler/hertz v0.0.0-20230801111316-7e5562fd8659 // indirect
	code.byted.org/lidar/profiler/kitex v0.4.6 // indirect
	code.byted.org/log_market/gosdk v0.0.0-20230524072203-e069d8367314 // indirect
	code.byted.org/log_market/loghelper v0.1.11 // indirect
	code.byted.org/log_market/tracelog v0.1.5 // indirect
	code.byted.org/log_market/ttlogagent_gosdk v0.0.6 // indirect
	code.byted.org/log_market/ttlogagent_gosdk/v4 v4.0.53 // indirect
	code.byted.org/microservice/galaxy_gosdk v0.0.0-20201124122843-ef5aa7c18e23 // indirect
	code.byted.org/microservice/galaxy_gosdk_v4 v0.0.0-20210322082854-4fe38a25ae46 // indirect
	code.byted.org/middleware/eino v1.0.0-rc.3 // indirect
	code.byted.org/middleware/fic_client v0.2.8 // indirect
	code.byted.org/middleware/hertz v1.12.1 // indirect
	code.byted.org/obric/flow_telemetry_go v1.1.1 // indirect
	code.byted.org/overpass/data_aml_llmflow_engine v0.0.0-20241107145550-f2da45272e96 // indirect
	code.byted.org/overpass/stone_llm_gateway v0.0.0-20250212095743-a71beba3b8a1 // indirect
	code.byted.org/overpass/toutiao_ttregion_manager v0.0.0-20231211101957-46c9440bc361 // indirect
	code.byted.org/rocketmq/rocketmq-go-proxy v1.5.13 // indirect
	code.byted.org/rocketmq/rocketmq-go-proxy-mqmesh-interceptor v1.0.18 // indirect
	code.byted.org/security/go-polaris v1.12.15 // indirect
	code.byted.org/security/go-spiffe-v2 v1.0.8 // indirect
	code.byted.org/security/memfd v0.0.2 // indirect
	code.byted.org/security/sensitive_finder_engine v0.3.18 // indirect
	code.byted.org/security/zti-jwt-helper-golang v1.0.17 // indirect
	code.byted.org/service_mesh/shmipc v0.2.16 // indirect
	code.byted.org/ti/agw_sdk v0.0.32-0.20240530065740-30ef40b38dc0 // indirect
	code.byted.org/ti/dns_common v0.1.102 // indirect
	code.byted.org/ti/gadget v0.0.22 // indirect
	code.byted.org/ti/iam_sdk v1.0.97 // indirect
	code.byted.org/ti/infra v0.0.30 // indirect
	code.byted.org/ti/next_dns_sdk v0.0.51 // indirect
	code.byted.org/tiktok/buildinfo v0.0.2 // indirect
	code.byted.org/tiktok/region_lib v0.11.0 // indirect
	code.byted.org/toutiao/elastic/v7 v7.0.42 // indirect
	code.byted.org/trace/go-stdlib/nethttp v1.0.0 // indirect
	code.byted.org/trace/trace-client-go v1.3.7 // indirect
	code.byted.org/ttarch/byteconf-cel-go v0.0.3 // indirect
	code.byted.org/ve-arch/customer-label-sdk v0.0.54 // indirect
	code.byted.org/ve-arch/volcengine-go-sdk v0.0.79 // indirect
	code.byted.org/videoarch/golib v0.9.27 // indirect
	code.byted.org/videoarch/ttlcache v1.0.3 // indirect
	code.byted.org/videoarch/vfastcache v1.0.10 // indirect
	code.byted.org/volcengine/volc-signer-golang v1.0.0 // indirect
	code.byted.org/webcast/libs_anycache v1.6.7 // indirect
	code.byted.org/webcast/libs_anycache/plugin/cache/base v0.1.1-0.20221212082232-7c36e6844ac9 // indirect
	code.byted.org/webcast/libs_anycache/plugin/cache/objectcache v0.0.1 // indirect
	code.byted.org/webcast/libs_anycache/plugin/codec/base v0.1.0 // indirect
	code.byted.org/webcast/libs_anycache/plugin/refresh v0.1.3 // indirect
	code.byted.org/webcast/libs_sync v0.1.2 // indirect
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/CorgiMan/json2 v0.0.0-20150213135156-e72957aba209 // indirect
	github.com/DataDog/zstd v1.5.5 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/Microsoft/go-winio v0.4.17 // indirect
	github.com/PuerkitoBio/purell v1.1.1 // indirect
	github.com/PuerkitoBio/urlesc v0.0.0-20170810143723-de5bf2ad4578 // indirect
	github.com/ReneKroon/ttlcache v1.7.0 // indirect
	github.com/Workiva/go-datastructures v1.0.53 // indirect
	github.com/agrison/go-commons-lang v0.0.0-20200208220349-58e9fcb95174 // indirect
	github.com/andeya/ameda v1.5.2 // indirect
	github.com/andeya/goutil v0.0.0-20221115092640-94288b660c35 // indirect
	github.com/andybalholm/brotli v1.0.4 // indirect
	github.com/antlr4-go/antlr/v4 v4.13.1 // indirect
	github.com/antonmedv/expr v1.15.5 // indirect
	github.com/astaxie/beego v1.12.3 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bits-and-blooms/bitset v1.13.0 // indirect
	github.com/bits-and-blooms/bloom/v3 v3.6.0 // indirect
	github.com/blang/semver/v4 v4.0.0 // indirect
	github.com/bluele/gcache v0.0.2 // indirect
	github.com/bufbuild/protocompile v0.8.0 // indirect
	github.com/bytedance/go-tagexpr/v2 v2.9.2 // indirect
	github.com/bytedance/gopkg v0.1.1 // indirect
	github.com/bytedance/sonic v1.13.2 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/caarlos0/env/v6 v6.10.1 // indirect
	github.com/cenk/backoff v2.2.1+incompatible // indirect
	github.com/cenkalti/backoff/v4 v4.2.1 // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/choleraehyq/pid v0.0.18 // indirect
	github.com/choleraehyq/rwlock v0.0.13 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cloudwego/configmanager v0.2.2 // indirect
	github.com/cloudwego/dynamicgo v0.4.7-0.20241220085612-55704ea4ca8f // indirect
	github.com/cloudwego/fastpb v0.0.5 // indirect
	github.com/cloudwego/frugal v0.2.3 // indirect
	github.com/cloudwego/hertz v0.8.1 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/cloudwego/localsession v0.1.1 // indirect
	github.com/cloudwego/netpoll v0.6.5 // indirect
	github.com/cloudwego/runtimex v0.1.0 // indirect
	github.com/cloudwego/thriftgo v0.3.18 // indirect
	github.com/coreos/go-semver v0.3.0 // indirect
	github.com/coreos/go-systemd/v22 v22.3.2 // indirect
	github.com/cznic/mathutil v0.0.0-20181122101859-297441e03548 // indirect
	github.com/danielgtaylor/huma v1.14.2 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dchest/uniuri v0.0.0-20200228104902-7aecb25e1fe5 // indirect
	github.com/dersebi/golang_exp v0.0.0-20121005063734-b599a102a57a // indirect
	github.com/dgrijalva/jwt-go v3.2.1-0.20180921172315-3af4c746e1c2+incompatible // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dlclark/regexp2 v1.11.0 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/emicklei/go-restful/v3 v3.8.0 // indirect
	github.com/emirpasic/gods v1.18.1 // indirect
	github.com/evanphx/json-patch/v5 v5.6.0 // indirect
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/fatih/color v1.16.0 // indirect
	github.com/fatih/structtag v1.2.0 // indirect
	github.com/forease/gotld v0.0.0-20220117081630-c190e9a9a647 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/gedex/inflector v0.0.0-20170307190818-16278e9db813 // indirect
	github.com/getkin/kin-openapi v0.118.0 // indirect
	github.com/getsentry/sentry-go v0.12.0 // indirect
	github.com/gin-contrib/cors v1.4.0 // indirect
	github.com/gin-contrib/pprof v1.3.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-jose/go-jose/v3 v3.0.3 // indirect
	github.com/go-kit/kit v0.12.0 // indirect
	github.com/go-kit/log v0.2.1 // indirect
	github.com/go-logfmt/logfmt v0.6.0 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-openapi/jsonpointer v0.19.5 // indirect
	github.com/go-openapi/jsonreference v0.19.5 // indirect
	github.com/go-openapi/swag v0.19.14 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator v9.31.0+incompatible // indirect
	github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572 // indirect
	github.com/gobuffalo/logger v1.0.6 // indirect
	github.com/gobuffalo/packd v1.0.1 // indirect
	github.com/goccy/go-json v0.10.0 // indirect
	github.com/golang-jwt/jwt/v4 v4.5.0 // indirect
	github.com/golang-jwt/jwt/v5 v5.2.1 // indirect
	github.com/golang-sql/civil v0.0.0-20220223132316-b832511892a9 // indirect
	github.com/golang-sql/sqlexp v0.1.0 // indirect
	github.com/golang/glog v1.0.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/gnostic v0.5.7-v3refs // indirect
	github.com/google/go-querystring v1.1.0 // indirect
	github.com/google/gofuzz v1.2.0 // indirect
	github.com/google/martian v2.1.0+incompatible // indirect
	github.com/google/pprof v0.0.0-20240727154555-813a5fbdbec8 // indirect
	github.com/goph/emperror v0.17.2 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.11.3 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/hbollon/go-edlib v1.6.0 // indirect
	github.com/henrylee2cn/ameda v1.5.0 // indirect
	github.com/henrylee2cn/goutil v0.0.0-20221115092640-94288b660c35 // indirect
	github.com/hertz-contrib/http2 v0.1.1 // indirect
	github.com/hertz-contrib/localsession v0.0.0-20230912121050-49d165b95cbf // indirect
	github.com/huandu/skiplist v1.2.0 // indirect
	github.com/iancoleman/strcase v0.3.0 // indirect
	github.com/imdario/mergo v0.3.12 // indirect
	github.com/invopop/yaml v0.1.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a // indirect
	github.com/jackc/pgx/v5 v5.5.5 // indirect
	github.com/jackc/puddle/v2 v2.2.1 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jhump/protoreflect v1.15.6 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/juju/ratelimit v1.0.2 // indirect
	github.com/karrick/godirwalk v1.16.1 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/klauspost/crc32 v1.2.0 // indirect
	github.com/kr/fs v0.1.0 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/kuangchanglang/graceful v1.0.2 // indirect
	github.com/leodido/go-urn v1.2.1 // indirect
	github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible // indirect
	github.com/lestrrat-go/strftime v1.0.5 // indirect
	github.com/lib/pq v1.10.2 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/markbates/errx v1.1.0 // indirect
	github.com/markbates/oncer v1.0.0 // indirect
	github.com/markbates/safe v1.0.1 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.2-0.20181231171920-c182affec369 // indirect
	github.com/microsoft/go-mssqldb v1.6.0 // indirect
	github.com/miekg/dns v1.1.56 // indirect
	github.com/minio/sha256-simd v1.0.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/gls v0.0.0-20220109145502-612d0167dce5 // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/montanaflynn/stats v0.7.0 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/nikolalohinski/gonja v1.5.3 // indirect
	github.com/nikolalohinski/gonja/v2 v2.3.1 // indirect
	github.com/nyaruka/phonenumbers v1.0.71 // indirect
	github.com/olivere/elastic v6.2.37+incompatible // indirect
	github.com/olivere/elastic/v6 v6.2.1 // indirect
	github.com/olivere/elastic/v7 v7.0.32 // indirect
	github.com/onsi/ginkgo/v2 v2.13.0 // indirect
	github.com/opentracing/opentracing-go v1.2.1-0.20210726034734-bdbb7cc3a1c0 // indirect
	github.com/orcaman/concurrent-map v0.0.0-20190107190726-7ed82d9cb717 // indirect
	github.com/pelletier/go-toml/v2 v2.0.9 // indirect
	github.com/percona/go-mysql v0.0.0-20210427141028-73d29c6da78c // indirect
	github.com/perimeterx/marshmallow v1.1.4 // indirect
	github.com/philhofer/fwd v1.1.1 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pingcap/errors v0.11.5-0.20190809092503-95897b64e011 // indirect
	github.com/pingcap/tipb v0.0.0-20210425040103-dc47a87b52aa // indirect
	github.com/pkg/sftp v1.13.1 // indirect
	github.com/pkoukk/tiktoken-go v0.1.7 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20240221224432-82ca36839d55 // indirect
	github.com/prometheus/client_golang v1.12.2 // indirect
	github.com/prometheus/client_model v0.4.0 // indirect
	github.com/prometheus/common v0.32.1 // indirect
	github.com/prometheus/procfs v0.7.3 // indirect
	github.com/qjpcpu/channel v0.0.0-20231204091515-b2613ddf67d9 // indirect
	github.com/qjpcpu/filelog v0.0.0-20240130072449-09c1bf9d6a7e // indirect
	github.com/qjpcpu/qjson v0.0.0-20231204091656-1b4d3e030b1d // indirect
	github.com/quic-go/quic-go v0.42.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/rogpeppe/go-internal v1.14.1 // indirect
	github.com/russross/blackfriday v1.6.0 // indirect
	github.com/saintfish/chardet v0.0.0-20120816061221-3af4cd4741ca // indirect
	github.com/samuel/go-zookeeper v0.0.0-20200724154423-2164a8ac840e // indirect
	github.com/sashabaranov/go-openai v1.36.1 // indirect
	github.com/satori/go.uuid v1.2.1-0.20181028125025-b2ce2384e17b // indirect
	github.com/shiena/ansicolor v0.0.0-20151119151921-a422bbe96644 // indirect
	github.com/shirou/gopsutil v3.21.11+incompatible // indirect
	github.com/shirou/gopsutil/v3 v3.24.2 // indirect
	github.com/slongfield/pyfmt v0.0.0-20220222012616-ea85ff4c361f // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	github.com/spf13/afero v1.10.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.6-0.20200504143853-81378bbcd8a1 // indirect
	github.com/spf13/viper v1.16.0 // indirect
	github.com/stathat/consistent v1.0.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/subosito/gotenv v1.4.2 // indirect
	github.com/thedevsaddam/gojsonq v2.3.0+incompatible // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/tidwall/sjson v1.2.5 // indirect
	github.com/tinylib/msgp v1.1.6 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.11 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	github.com/vmihailenco/msgpack v4.0.4+incompatible // indirect
	github.com/vmihailenco/msgpack/v4 v4.3.12 // indirect
	github.com/vmihailenco/msgpack/v5 v5.4.1 // indirect
	github.com/vmihailenco/tagparser v0.1.2 // indirect
	github.com/vmihailenco/tagparser/v2 v2.0.0 // indirect
	github.com/volcengine/volcstack-go-sdk v1.0.3 // indirect
	github.com/wcharczuk/go-chart/v2 v2.1.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/yargevad/filepathx v1.0.0 // indirect
	github.com/youmark/pkcs8 v0.0.0-20201027041543-1326539a0a0a // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	github.com/zeebo/errs v1.3.0 // indirect
	go.etcd.io/etcd/api/v3 v3.5.9 // indirect
	go.etcd.io/etcd/client/pkg/v3 v3.5.10 // indirect
	go.etcd.io/etcd/client/v3 v3.5.9 // indirect
	go.opentelemetry.io/contrib/propagators/b3 v1.15.0 // indirect
	go.opentelemetry.io/contrib/propagators/ot v1.15.0 // indirect
	go.opentelemetry.io/otel v1.14.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/internal/retry v1.14.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.14.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.14.0 // indirect
	go.opentelemetry.io/otel/exporters/prometheus v0.26.0 // indirect
	go.opentelemetry.io/otel/exporters/stdout/stdouttrace v1.14.0 // indirect
	go.opentelemetry.io/otel/internal/metric v0.26.0 // indirect
	go.opentelemetry.io/otel/metric v0.31.0 // indirect
	go.opentelemetry.io/otel/sdk v1.14.0 // indirect
	go.opentelemetry.io/otel/sdk/export/metric v0.26.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v0.26.0 // indirect
	go.opentelemetry.io/otel/trace v1.14.0 // indirect
	go.opentelemetry.io/proto/otlp v0.19.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/mock v0.4.0 // indirect
	go.uber.org/multierr v1.9.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	go4.org/unsafe/assume-no-moving-gc v0.0.0-20231121144256-b99613f794b6 // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/image v0.0.0-20220302094943-723b81ca9867 // indirect
	golang.org/x/mod v0.19.0 // indirect
	golang.org/x/net v0.30.0 // indirect
	golang.org/x/oauth2 v0.17.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	gomodules.xyz/jsonpatch/v2 v2.2.0 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto v0.0.0-20240227224415-6ceb2ff114de // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240513163218-0867130af1f8 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240509183442-62759503f434 // indirect
	google.golang.org/grpc v1.64.0 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/vmihailenco/msgpack.v2 v2.9.2 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/postgres v1.5.4 // indirect
	gorm.io/driver/sqlserver v1.5.3 // indirect
	gorm.io/hints v1.1.1 // indirect
	k8s.io/apiextensions-apiserver v0.25.0 // indirect
	k8s.io/apiserver v0.0.0 // indirect
	k8s.io/component-base v0.25.0 // indirect
	k8s.io/klog/v2 v2.80.1 // indirect
	k8s.io/kube-openapi v0.0.0-20221012153701-172d655c2280 // indirect
	modernc.org/b v1.0.2 // indirect
	sigs.k8s.io/json v0.0.0-20220713155537-f223a00ba0e2 // indirect
	sigs.k8s.io/structured-merge-diff/v4 v4.2.3 // indirect
)

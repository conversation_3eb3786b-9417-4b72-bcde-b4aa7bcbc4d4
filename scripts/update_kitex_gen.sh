#!/bin/bash

ROOT=`pwd`

function gen() {
    DST_DIR=gen/$1
    IDL_DIR=$2

    echo "============== generate code for $1 ==================="
    (cd $IDL_DIR && git pull)
    mkdir -p $DST_DIR
    for file in `cd ${IDL_DIR} && find . -name 'dbw.thrift'`;do
        echo "generate code by ${IDL_DIR}/${file}"
        if [ `dirname $file` != '.' ];then
            REAL_DIR=$DST_DIR/`dirname $file`
            mkdir -p $REAL_DIR
            file=$ROOT/${IDL_DIR}/$file
            (cd $REAL_DIR && kitex -module code.byted.org/infcs/dbw-mgr  -disable-self-update --thrift-plugin validator -core -thrift scan_value_for_enum=false -thrift json_enum_as_text ${file})
        else
            (cd $DST_DIR && kitex -module code.byted.org/infcs/dbw-mgr   -disable-self-update -core -thrift scan_value_for_enum=false -thrift json_enum_as_text ../../${IDL_DIR}/${file})
        fi
    done
}
#gen dbw-syncer ../dbw-syncer/idl
#gen rds-mgr ../rds-mgr-idl
#gen vedb-mgr ../bytendb-mgr-idl/idl/bytendb-mgr-idl
gen dbw-mgr ../dbw-mgr-idl
#gen mysql-sharding-mgr ../bytescale-mgr-idl
#gen pg-mgr  ../rds-pg-mgr-idl
#gen web-infra ../web-inframgmt-idl
#gen monitor-mgr idl/monitor-mgr-idl
#gen redis-mgr ../redis-mgr-idl
#gen kafka-mgr idl/kafka-mgr-idl
#gen mongo-mgr ../mongdb-c-idl
#gen mssql-mgr ../rds-mgr-idl
#gen web-controller ../web-controller-idl
#gen mysql-sharding-mgr ../rds-mgr-idl
#gen meta-mysql-mgr ../rds-mgr-idl
#gen dair-mgr ../ai-db-api/idl
#gen $1 $2

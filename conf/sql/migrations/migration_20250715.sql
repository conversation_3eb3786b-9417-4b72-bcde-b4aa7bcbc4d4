CREATE TABLE `dbw_instance_history` (
`id` bigint(20) unsigned NOT NULL,
`instance_id` varchar(128) NOT NULL DEFAULT '',
`instance_name` varchar(128) NOT NULL DEFAULT '',
`instance_type` varchar(128) NOT NULL DEFAULT '',
`psm` varchar(256) NOT NULL DEFAULT '',
`tenant_id` varchar(128) NOT NULL DEFAULT '',
`user_id` varchar(128) NOT NULL DEFAULT '',
`region_id` varchar(32) NOT NULL DEFAULT '',
`create_time` bigint(20) unsigned DEFAULT NULL,
`update_time` bigint(20) unsigned DEFAULT NULL,
`login_time` bigint(20) unsigned DEFAULT NULL,
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
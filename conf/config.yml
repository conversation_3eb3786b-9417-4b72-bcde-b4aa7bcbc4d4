base:
  debug: false
  log:
    log_level: "INFO"
    #log_file_path: "./log/dbwmgr.log"
    log_file_path: "/opt/tiger/dbwmgr/log/dbwmgr.log"
    log_formatter: "json"
    keep_files: 72
    call_depth: 2
  mgr:
    ID: "1"
    GroupName: "DBW-MGR"
    Address: ":8088"
    EnableElection: false
    IsHighAvailable: false
    IsRecover: true
    ProductStr: "Dbw"
    WebControllerEndpoint: "cluster"

  db:
    meta:
      db: "dbwmgr"
      host: "{{ FIRSTVALUE (SECRET "run/" "PRIVATE_MYSQL_HOST") (SECRET "PRIVATE_MYSQL_HOST") }}"
      port: {{ FIRSTVALUE  (SECRET "run/" "PRIVATE_MYSQL_PORT") (SECRET "PRIVATE_MYSQL_PORT") }}
      user: "{{ FIRSTVALUE (SECRET "run/" "MYSQL_USERNAME") (SECRET "MYSQL_USERNAME") }}"
      password: "{{ FIRSTVALUE (SECRET "run/" "MYSQL_PASSWORD") (SECRET "MYSQL_PASSWORD") }}"

      timeout_ms: 15000
      read_timeout_ms: 15000
      write_timeout_ms: 15000
      max_idle_conns: 40
      max_open_conns: 100
      super_tenant_id:
        - "0"
        - "1"
  actor:
    default:
      zkaddr: "{{ FIRSTVALUE (SECRET "run/" "ZK_ADDR") (SECRET "ZK_ADDR") (ENV "BDC_ZK_DOMAIN") }}"
      ip: "{{ENV "POD_IP"}}"
      port: 8188
      ns: "/dbw-console"
      cluster: "{{FIRSTVALUE (ENV "ACTOR_CLUSTER") "default"}}"
      storage:
        type: "mysql"
        name: "meta"
        table: "{{FIRSTVALUE (ENV "ACTOR_TABLE") "dbw_actor"}}"

  gin:
    address: ":8388"


iam_profile: "{{ENV "IAM_PROFILE"}}"
service_link_rule: "{{ENV "SERVICE_LINK_RULE"}}"
service_link_rule_sql_audit: "{{ENV "SERVICE_LINK_RULE_SQL_AUDIT"}}"

cloud_monitor_endpoint: "{{ENV "CLOUD_MONITOR_ENDPOINT"}}"

rds_mgr_address: "{{ENV "BDC_REGION_WEB_CONTROLLER_SERVICE"}}:{{ENV "BDC_REGION_WEB_CONTROLLER_PORT"}}"
meta_rds_mgr_address: "{{ENV "BDC_REGION_WEB_CONTROLLER_SERVICE"}}:{{ENV "BDC_REGION_WEB_CONTROLLER_PORT"}}"
vedb_mgr_address: "{{ENV "BDC_REGION_WEB_CONTROLLER_SERVICE"}}:{{ENV "BDC_REGION_WEB_CONTROLLER_PORT"}}"
redis_mgr_address: "{{ENV "BDC_REGION_WEB_CONTROLLER_SERVICE"}}:{{ENV "BDC_REGION_WEB_CONTROLLER_PORT"}}"
mongo_mgr_address: "{{ENV "BDC_REGION_WEB_CONTROLLER_SERVICE"}}:{{ENV "BDC_REGION_WEB_CONTROLLER_PORT"}}"
infra_mgr_address: "{{ENV "BDC_REGION_WEB_CONTROLLER_SERVICE"}}:{{ENV "BDC_REGION_WEB_CONTROLLER_PORT"}}"
upgrade_mgr_address: "{{ENV "BDC_REGION_WEB_CONTROLLER_SERVICE"}}:{{ENV "BDC_REGION_WEB_CONTROLLER_PORT"}}"

init_image: "hub.byted.org/rds-tob/init-packetbeat:dh"
packetbeat_image: "hub.byted.org/rds-tob/pb:dh"
internal_users: "{{ENV "INTERNAL_USERS"}}"

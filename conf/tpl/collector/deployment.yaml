apiVersion: apps/v1
kind: Deployment
metadata:
  name: dbw-collector
  namespace: dbw
  annotations:
    {{if .ResourceID}}
    vs-resource: "{{.ResourceID}}"
    {{end}}
    inf-webhook-server.infcs.tob/product-protected-by-inf: "dbw-collector"
  labels:
    {{if .CollectorVersionSetID}}
    vs-id: "{{.CollectorVersionSetID}}"
    {{end}}
    app: dbw-collector
spec:
  selector:
    matchLabels:
      app: dbw-collector
  replicas: {{.CollectorReplicas}}
  template:
    metadata:
      labels:
        app: dbw-collector
    spec:
      tolerations:
        - effect: NoExecute
          key: node.kubernetes.io/unreachable
          operator: Exists
          tolerationSeconds: 5
        - effect: NoExecute
          key: node.kubernetes.io/not-ready
          operator: Exists
          tolerationSeconds: 30
      serviceAccountName: dbw-collector
      terminationGracePeriodSeconds: 10
      {{if .NodePool}}
      nodeSelector:
        node-pool: "{{.NodePool}}"
      {{end}}
      volumes:
        - name: log
          hostPath:
            path: "{{.LogDir}}/svc_logs/dbw-collector"
            type: DirectoryOrCreate
        - name: config
          secret:
            secretName: "dbw-c3-config-secret-env.private"
      initContainers:
        - name: init
          image: "{{.CollectorImage}}"
          command:
            - chown
            - "-R"
            - dbw:dbw
            - /opt/tiger/dbw-collector
          volumeMounts:
            - name: log
              mountPath: /opt/tiger/dbw-collector/log
      containers:
        - name: dbw-collector
          image: "{{.CollectorImage}}"
          imagePullPolicy: IfNotPresent
          workingDir: /opt/tiger/dbw-collector
          command: [ "gosu","dbw" ]
          args:
            - /opt/tiger/dbw-collector/bin/dbw-collector
          resources:
            limits:
              cpu: "4"
              memory: 16Gi
            requests:
              cpu: "2"
              memory: 4Gi
          ports:
            - containerPort: 8088
              name: rpc
          volumeMounts:
            - name: log
              mountPath: /opt/tiger/dbw-collector/log
            - name: config
              mountPath: /var/run/c3_env
              readOnly: true
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: POD_HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            {{- range $key,$value := .CollectorEnvs }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
